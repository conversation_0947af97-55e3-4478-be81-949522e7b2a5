FROM node:18.18.0-alpine3.17 AS node

FROM node AS base
RUN apk --no-cache -U upgrade

FROM base AS server-builder
RUN apk add --no-cache python3 build-base libtool autoconf automake
WORKDIR /app
COPY src ./src
COPY package.json .
COPY package-lock.json .
COPY .wasp/build/server .wasp/build/server
COPY .wasp/out/sdk .wasp/out/sdk
RUN npm install && cd .wasp/build/server && npm install
COPY .wasp/build/db/schema.prisma .wasp/build/db/
RUN cd .wasp/build/server && npx prisma generate --schema='../db/schema.prisma'
RUN cd .wasp/build/server && npm run bundle

FROM base AS server-production
RUN apk add --no-cache python3
ENV NODE_ENV production
WORKDIR /app
COPY --from=server-builder /app/node_modules ./node_modules
COPY --from=server-builder /app/.wasp/out/sdk .wasp/out/sdk
COPY --from=server-builder /app/.wasp/build/server/node_modules .wasp/build/server/node_modules
COPY --from=server-builder /app/.wasp/build/server/bundle .wasp/build/server/bundle
COPY --from=server-builder /app/.wasp/build/server/package*.json .wasp/build/server/
COPY --from=server-builder /app/.wasp/build/server/scripts .wasp/build/server/scripts
COPY .wasp/build/db/ .wasp/build/db/
# Copy Lighthouse reports for admin dashboard performance monitoring
COPY lighthouse-*.json ./ 
EXPOSE 8080
WORKDIR /app/.wasp/build/server
ENTRYPOINT ["npm", "run", "start-production"] 