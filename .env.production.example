# CareerDart Production Environment Configuration
# Copy this file to .env.production and fill in your actual values
#
# IMPORTANT: Never commit this file with real values to version control!

# =================================================================
# SERVER CONFIGURATION
# =================================================================
# Your deployed Vercel URL (update after first deployment)
WASP_WEB_CLIENT_URL=https://your-careerdart-app.vercel.app
WASP_SERVER_URL=https://your-careerdart-app.vercel.app

# =================================================================
# DATABASE CONFIGURATION
# =================================================================
# Neon PostgreSQL connection string
# Get this from: https://console.neon.tech/ -> Your Project -> Connection Details
# Format: ****************************************/database?sslmode=require
DATABASE_URL=postgresql://username:<EMAIL>/neondb?sslmode=require

# =================================================================
# AI SERVICES
# =================================================================
# OpenAI API Key for AI-powered features
# Get this from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-proj-your-openai-api-key-here

# =================================================================
# AUTHENTICATION
# =================================================================
# Google OAuth Configuration
# Get these from: https://console.developers.google.com/
# Make sure to add your Vercel domain to authorized redirect URIs
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-your-google-client-secret

# =================================================================
# EMAIL SERVICES
# =================================================================
# SendGrid API Key for transactional emails
# Get this from: https://app.sendgrid.com/settings/api_keys
SENDGRID_API_KEY=SG.your-sendgrid-api-key-here
SEND_EMAILS_IN_DEVELOPMENT=false

# =================================================================
# PAYMENT PROCESSING
# =================================================================
# Stripe Configuration (use live keys for production)
# Get these from: https://dashboard.stripe.com/apikeys
STRIPE_KEY=sk_live_your-stripe-secret-key-here

# Stripe Product Price IDs
# Get these from: https://dashboard.stripe.com/products
PRODUCT_PRICE_ID=price_your-subscription-price-id
PRODUCT_CREDITS_PRICE_ID=price_your-credits-price-id

# =================================================================
# ENVIRONMENT SETTINGS
# =================================================================
NODE_ENV=production
WASP_ENV=production
CI=true

# =================================================================
# BUILD OPTIMIZATIONS
# =================================================================
# Production build optimizations
GENERATE_SOURCEMAP=false
SKIP_PREFLIGHT_CHECK=true
DISABLE_ESLINT_PLUGIN=true
TSC_COMPILE_ON_ERROR=true
FAST_REFRESH=false

# =================================================================
# SECURITY & PERFORMANCE
# =================================================================
# Security headers and CORS
HELMET_ENABLED=true
CORS_ORIGIN=https://your-careerdart-app.vercel.app

# Performance monitoring (optional)
# SENTRY_DSN=https://<EMAIL>/project-id
# ENABLE_ANALYTICS=true
