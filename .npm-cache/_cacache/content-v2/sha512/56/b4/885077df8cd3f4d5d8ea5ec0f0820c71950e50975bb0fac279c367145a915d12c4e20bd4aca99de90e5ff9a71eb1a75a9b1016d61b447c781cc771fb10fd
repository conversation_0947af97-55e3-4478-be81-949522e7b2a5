{"name": "bolt11", "dist-tags": {"latest": "1.4.1"}, "versions": {"1.0.0": {"name": "bolt11", "version": "1.0.0", "dist": {"integrity": "sha512-roeQzJPoOtVpefXna46k7ZcKUgYdn+zydlG6fWSW3ZG3kxmiDxiKc34AWiZ3otMzjIua9EnHgwobOd7RmzPzeg==", "shasum": "8baca5fa717f335d50179d46fd5642252421bbb4", "tarball": "https://registry.npmjs.org/bolt11/-/bolt11-1.0.0.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCTtl42X79sF4XgJlp2Q9od+hIwx+aj8xV3Ka7rD5zafQIgFG0SDsH6OWnPV5Ljq99TSwhyTDAiZ+YX9GFGaLYkQEM="}]}}, "1.1.0": {"name": "bolt11", "version": "1.1.0", "dependencies": {"bech32": "^1.1.2", "bitcoinjs-lib": "^3.3.1", "bn.js": "^4.11.8", "lodash": "^4.17.4", "safe-buffer": "^5.1.1", "secp256k1": "^3.4.0"}, "devDependencies": {"nyc": "^13.0.0", "standard": "*", "tap-dot": "*", "tape": "^4.8.0"}, "dist": {"integrity": "sha512-+knKMZNyuiPoGyuuftUGIsZFdLooVSpSIs/p9IXVaHqD0a2Nwph26Csan0gi8z6gvmq8K/K2heI5c7dAf5K5UQ==", "shasum": "af72376f5278215e214c3b7bb5140bb6d3e1e41e", "tarball": "https://registry.npmjs.org/bolt11/-/bolt11-1.1.0.tgz", "fileCount": 8, "unpackedSize": 78701, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFymzCRA9TVsSAnZWagAAdjwP/1nCu+3ntMk9N9nwmKA9\nPQVFc1/LIn2Rg2s7O6WiXoDdH8tTkiSFVzzfdyVwA0jZpJhOqWYweOXZx6pK\n9P+E50FyZ/V3US4WUAyhEs9v+vDntuWf0kv9Ez+Yzy+kPKKM0dsiXYltexkc\nbt9rSfe6QXxUl44c1WEgTZx4eCVP9lbdraXasWWb4iZWAAS7yKCGQ2bqeFd2\nIpUOe274ajAXwlIWFWU1SfTjjMm/t7dFgx4bHbHSp6QiZQbWhtPs9uiq4bkK\nqrJKp77/v0d0Bx8378sCXqap0QGQ1OuztmfiVs/9jGGgt3Gr3vygcyK7kGjN\nAaec1/NnlhQpR7fjOPAfJBSjWbqrvlJQI3SmrGkOTClOxs22ebVeJ9Y3ZZju\ndzJ2SyUtsHD5logozt4v+onxwugsyIelDJb48PDyALxuBwyWuVIXMYa+Sq57\nudPclNA/DGKu5d3pULFgDE5cY5VB4pkYpE52TUpaBDlFVVR81dSHDo4LUqD4\naf21w+YuyjXpyTT6xTCXUPC92BdCZCogSe2C5Eu+Z64jJJupUSEM+r1Vglvu\nvOui26/1PMab+cyNsd2bmBuwyq6+J8+1Fl+erxdVTwhDkVxTOC9nsQa+14uS\nyC50AMDDxAD/JWVWBWoR0+7cXN5bljUmrtA22dVX25EjLeHAz4VlQVgKeIdP\nysJW\r\n=Uhbw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB0qAhPjjYoZFKRsz0RjV5yGzV4U06iN45qF+Z5CmQTYAiEA4lzlXBLkFnInB+fwIOcXiyPvFljkgKFWTXaza/q0SsM="}]}}, "1.2.0": {"name": "bolt11", "version": "1.2.0", "dependencies": {"bech32": "^1.1.2", "bitcoinjs-lib": "^3.3.1", "bn.js": "^4.11.8", "lodash": "^4.17.4", "safe-buffer": "^5.1.1", "secp256k1": "^3.4.0"}, "devDependencies": {"nyc": "^13.0.0", "standard": "*", "tap-dot": "*", "tape": "^4.8.0"}, "dist": {"integrity": "sha512-VbwyaLaxSIT0MvRnlLtzTLw25HQZo8mkobHbS2tOFcHEmmJSgGeYq0mzvO793vwYDUb2z6I/WFm/fLLq9jh7Nw==", "shasum": "cdacd8e32dc25b2a73a5374b343867b9073c5d80", "tarball": "https://registry.npmjs.org/bolt11/-/bolt11-1.2.0.tgz", "fileCount": 8, "unpackedSize": 84147, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcF6XYCRA9TVsSAnZWagAAaZgP/1LPbVXegHP4/YlCS2Jf\nGDLQ6YoFkQVsGDhf5Ql/rLc7SdNXFw50omS6R833SDBPw/RJWXwWitDymKLM\n6iOvkiJjbe0IY/OgHyh82l6RnyCOtVVrXnfbDfkb4V+tK5lkmWb9EUJ+HvOP\nuwsIjE6eOzmEJLRTwyfvxh01lEbprBNigATzgc3IikPabb9vcNsrKUbD5dMX\nUVKkhxUBa317O9vT+mqdFmJ4LvCqafmA7FPs/fYtU2vLAQdq69ZcCquwhlAL\nea9pmHG7Q7T28pW5x8a92w3HBAMiucdI+1px+NTSK8IQWeGt0sPf7DdNwAwO\n77x2Fa8cHYdRCXtPuivlcSnEfAqONRlkt74A2fANfx0G7yOse/nLnVOEceMF\nrrQEtVbit6REnM7G6AMuH495nax/O7mXHrklazr0dZV2XPZTcO3hj4pqfHG/\nEpW2xVAuJhIEHB7jNx53isWLgkt+FM/QwCbw0kisN1cog1NF9HbBI+lmWf/M\nUdvkzizkgie7wq7IX0x0HyiWkNyKyVfUtOUR9FiM5Wdj3T3Tq0+5HuISE/nX\ngWd6DIRLjQHIyl/Qc0TgZJfKnThwnaR92m+3Noh506m5maqakpx68IqHCE+j\ns29MAZtRH4+Q5F3nLenGicjOF4Mr4G2Osflx8Q2jj7Xqw1iTR+AW6j7oHEOn\nBKvK\r\n=OHtz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCeJM4miRmGXxQOGHvX3F14EyH3upKmndedCAHYSfC19QIgPyAXj6qMjJjWf9yQwz48aBi3Lxa+aun2vrQWUEyc9Ns="}]}}, "1.2.1": {"name": "bolt11", "version": "1.2.1", "dependencies": {"bech32": "^1.1.2", "bitcoinjs-lib": "^3.3.1", "bn.js": "^4.11.8", "coininfo": "git+https://github.com/cryptocoinjs/coininfo.git#c7e003b2fc0db165b89e6f98f6d6360ad22616b2", "lodash": "^4.17.4", "safe-buffer": "^5.1.1", "secp256k1": "^3.4.0"}, "devDependencies": {"nyc": "^13.0.0", "standard": "*", "tap-dot": "*", "tape": "^4.8.0"}, "dist": {"integrity": "sha512-7y2FmvBH1DOLFkygSngZpBYSzkS5IfSfxY4Zt06T4nwnZilt0pkQrFMeKDXzgIf+XnTGZzif3UznZ6GRzwWwtg==", "shasum": "67ee574385ec4143a40112ad9ad093a26309b545", "tarball": "https://registry.npmjs.org/bolt11/-/bolt11-1.2.1.tgz", "fileCount": 8, "unpackedSize": 84995, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcLLdHCRA9TVsSAnZWagAAj+gP/3b6EmCMxzZ7wHjXTHW0\nmtwq6H7p+7TIZrFDjsOHhBAQrSAIAnZqis8OjjqR3/5PX/uq2ZEXXyS9xRTQ\nqSbgJOptApDdvxjfFpDxUf3nN4EYOivNZgXc0ptEGyoRiz12JqAS1ottEJXO\ntipACYa3AdxvmALDFXzB60FnMXobrB3bpZfnu+ztsowLKSJG66oftkACX2Xy\nBrnF0THMvso5iV2OB1aT1t517pncun2ct8QDFU6MoV8IMC694Rr/XbP9PJ7k\ncUJDKXEjjc33K2ljrBkFH+rphMUkfFl4UKmoPGgnWtDHXUwJY+0iph/FHpL/\nPP6j/vQvwvHJiZWPWsYdodCovj9Od8ZoVpJl4xb4jRnxtb5P/haWUK7ZUT3k\nKAk3wf9AI8xdi/v9Ov05MiMCyRZVi2HQ8ZNnTUjEnvHYGTKyNCNjfbyf6brJ\nV8vbefYaEoJTLkh0XYpY0Fp0TqQ0VyDm2mBAg9fVXwYSTqbkFInQFtiqbM0I\niZa8lwqZ/IaBTArgfzhW7UrV8skr0FkVuSyUyOlOgcO/sMru3Wu4s5XSc8zg\nFYY4hdv7uoJbMSXtN1yyO2viK7BvQFndlU1hdkmj+BBWuB5/s8A0w6hVwFS6\nQPeipmNQkMDrbZZZtgvMHx4iKmKJ5wPV1D+n9Piuz8uVTHJrqa6N4hUHRmE6\nkUgn\r\n=wb/p\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBw2kcO+88t5yNHDLs33jwccZDDH4uUDcN/5aTQiER15AiEAvZW92eVMIshHMfMACpvYtub0lW5f8iQGciVOEIIWxBc="}]}}, "1.2.2": {"name": "bolt11", "version": "1.2.2", "dependencies": {"bech32": "^1.1.2", "bitcoinjs-lib": "^3.3.1", "bn.js": "^4.11.8", "coininfo": "git+https://github.com/cryptocoinjs/coininfo.git#c7e003b2fc0db165b89e6f98f6d6360ad22616b2", "lodash": "^4.17.4", "safe-buffer": "^5.1.1", "secp256k1": "^3.4.0"}, "devDependencies": {"nyc": "^13.0.0", "standard": "*", "tap-dot": "*", "tape": "^4.8.0"}, "dist": {"integrity": "sha512-hvmXNrNMrB7V2km3yotI/f23jodSnWVyoJqUsySFvLDcieDJf5Nox0+cleviSExM2YJt33PzxF44UFha/lwCJw==", "shasum": "1d8ddaa46f5d4085efee3db4d84dd43756f028b8", "tarball": "https://registry.npmjs.org/bolt11/-/bolt11-1.2.2.tgz", "fileCount": 8, "unpackedSize": 86105, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOpWjCRA9TVsSAnZWagAABXgQAJGo5ly/avEE1MqByFhC\ngjIjldh8Bvqsenqb7X17ZlrhqOOhSCawnxkafCCzO5mgRuvZm04+MfNgf4hf\nI3oW6hPP7RtmljyPJ+Gt7U3e3HmPQck1NIZ/UJYfLiF/3vjjMDwWjjooCfuR\nLl4VxQRvvTuXrMBIsW3Md7eIdY+0EzNYQ6724sBIBl/KYSZ5E3xS3X1lES1V\nmA59eBsNyMvxgqGc5jzpGLrF1w54ytF6Bs4Vn9P7Xj1KETptCb1XX9qzbWHT\n1Q8GdBM70musuXH73ENeoFv7pAQ2F4AUvzihh6f7A7eAPDSLg9xuzpWrJKZK\nCqLzsV8PuQxjn4O4Jc608/8iEinLa+HoQX23Y2Sd9fGQEOd/L4kYU3Ify7An\nYnszdlOX4pyb1YjDeVpiasLukKm6XUtFO2kT7w85WZV13+7gIwUaLYMyAZl3\nX26pTit95vHyqD2da7DNEqVeQgUOzIJ6ybO52lfzI9I+J3grpPq+KwtsB/85\nBeLFVWGqMIlepguobwFeI5wKepikaMBfd+IstUSJODPAUa5aNwoVmX3/hw6D\nJRpBU5y+a2QWuiFmUAzskWS7HoL1gFQAHYmpdLhd0ocdEfsyWZtmfBd6S99v\nbEwN1JPdxE/rSp/OSsTNzXJUydtd8UmJ8rMjDZ3bXD5MFMuqRMUaMU3z3RGk\nqbCD\r\n=JZjl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDcopJZfc8vxAB4PwwUrhufdpfCM+B7YQKVFQhNPWdWwIgV4+kZJENyvngMzB2re4yiVPqDxZGz0Q8MtejTCrr4lY="}]}}, "1.2.3": {"name": "bolt11", "version": "1.2.3", "dependencies": {"@types/bn.js": "^4.11.3", "bech32": "^1.1.2", "bitcoinjs-lib": "^3.3.1", "bn.js": "^4.11.8", "coininfo": "git+https://github.com/cryptocoinjs/coininfo.git#c7e003b2fc0db165b89e6f98f6d6360ad22616b2", "lodash": "^4.17.4", "safe-buffer": "^5.1.1", "secp256k1": "^3.4.0"}, "devDependencies": {"nyc": "^13.0.0", "standard": "*", "tap-dot": "*", "tape": "^4.8.0"}, "dist": {"integrity": "sha512-Cyvp/4fpF8ppXt/z0JW300FSy2wliV/GkSrlBcYiz3ycUYGeH3zIxFbSeHQrX0rn90gj9p+CZHVm1l/ZPjd35g==", "shasum": "48efd088052c08579ae0d38031d9e588375651d6", "tarball": "https://registry.npmjs.org/bolt11/-/bolt11-1.2.3.tgz", "fileCount": 9, "unpackedSize": 87640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZPZtCRA9TVsSAnZWagAAlDcP/20aEavx0lkMDqXQDsC5\n+2U2Zo6Zamgm088SecHjcfUFtQ6few5p/MpW/okiiwbyjq27VGY03SeIc6x5\nnlNPyC2OVBmVQWFpF4R196XP39Q+r4Ci374kEtSYFp95cC4FTDN/4DTqRvBJ\n1iDZoNec2yyA8soZqqXlgQU39T5GJq9bRTQuR0/EVdjsMe7JNTY/+zNCiCpL\nejs3h492YbA8tLTGL8prjepObMsg5DVJrloE4o7l08tp2y/RaxIsVgV5AaQN\nI/iK42h2i8NxR7/FqGgFzAeZhIlH23EUGC7UC45XMJ8ebqQgv8mnu+gQLNuj\n+przvBdENfy5yGFsCYZ/I7B0ERvoN47d/hiSHfGdHIWfp+GZPv2eoUi/g8zv\n/OshwBQ7AVnmpgPlOC+OzfqKFqynERKQIoE3kpIZtoDWixvA/AWQYPT6kQw/\nkKG7gCbrIc6t6W/2YREKWKtxzrSTAO9bA7OuQJcbyjRJbV9mvIN1tD9p0kJw\nshi1JhGCWodh/rtT1EWzsffna6++QcahGKKODh1+FCdy1/FeiKx+4sx8TlZ3\nXCdhRsWorV6JXHb7JXRJyKGEfNnFwoiuyDYhsHRbJeZUAz4sOoPEnilq2L6N\nZ+BTyq1DqxTovPTj5hbDPZQ/9QUtRI8mHFl1ppVoaS/c28dvtvwFZ8G59CVH\nzxPY\r\n=KaZB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDM2wJCME23PyHasFiRZqyxpE0OGvO8WAc/E1gdCkDCvAIgcYhjIHq9vzTJ8T4NM74m6d/SQknyPBR7bcNjyhoCxkk="}]}}, "1.2.4": {"name": "bolt11", "version": "1.2.4", "dependencies": {"@types/bn.js": "^4.11.3", "bech32": "^1.1.2", "bitcoinjs-lib": "^3.3.1", "bn.js": "^4.11.8", "coininfo": "git+https://github.com/cryptocoinjs/coininfo.git#c7e003b2fc0db165b89e6f98f6d6360ad22616b2", "lodash": "^4.17.4", "safe-buffer": "^5.1.1", "secp256k1": "^3.4.0"}, "devDependencies": {"nyc": "^13.0.0", "standard": "*", "tap-dot": "*", "tape": "^4.8.0"}, "dist": {"integrity": "sha512-Sz8H7aKDA9mksqg1I9divIkTVco6IEo9ATJwPJFaQhxVOB/OiRCaAnj5n7Tj9jbIQhkqadR1hZPQwFVQFfwZ9Q==", "shasum": "a6ec990fefa92d835280126bb5f715e2ce7c8374", "tarball": "https://registry.npmjs.org/bolt11/-/bolt11-1.2.4.tgz", "fileCount": 9, "unpackedSize": 87635, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZQXJCRA9TVsSAnZWagAAxaIP/3UX+KFMNceZzD8LVn+W\nb4rpFgt+h/sdMDRxCeoWZLsy86jRdw4H9uCF9iZZo2Z5wTO9lNgPjczWaDiH\nauNsQu8eyvMqNa+uP2OAeyuG2sxwpgH6agrRx6jQVcVxEFz2e7gueFnusegU\nj0/KTdJhNzWxpJmMCRbPqqVeWxd/92VObbya0SMhLgkEbD1CfekJkofJ9wK2\nLD5xgggofElZJjk7PjRArCYa4NmevCuv2YDppYfhr/WlmGEgB4LMZuqCNfNN\nQgHJ0N5S5D1bpBj7HnFWejrF0I/JoW4f881ujb6VAZK4EVr06Yyv93p8EBrP\nyDKdk6Ye155J25b7wb8d9qwhZzzKpXZ8hB0MC0KlZeTvgILtQZWtv8gmZUau\nOllsGY92bYJ6bfp2tvMScE6mj6QlCJ8cwfKodgIpDgBQG9lKpkXMiH7XPz4v\nMlDsEosEoO0Y3ZkJcDHXA8tnL7jtvs/vr5wzBG6znSR8bZpORRnUfeDtwtlZ\nvKdYu7kctDLZeclCH3kqQ9cZokAcwroShYA3/qbr8T+VtSWwv7b3EKyQ68kz\ny99D1hIIrWoF/4bRH2ZC3bZlaYBFCYkdr7K8J+PsU1tssGliUaV855FRYZq5\n247shZVQz0ZfJ6OIM4sQrGDbM4+C74SkE465XXL1wyv+UTQQyx+vbCwpgWw2\nVxw6\r\n=HQ/Y\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCEASxvAi9RvfKEh8rzJMIiC6LTg2NVCjz6gd7HdULYnwIgBo4NlWEUINa2oyGkhWUe8RnIAoHda6N/peOI/vQmCh4="}]}}, "1.2.5": {"name": "bolt11", "version": "1.2.5", "dependencies": {"@types/bn.js": "^4.11.3", "bech32": "^1.1.2", "bitcoinjs-lib": "^3.3.1", "bn.js": "^4.11.8", "coininfo": "git+https://github.com/cryptocoinjs/coininfo.git#c7e003b2fc0db165b89e6f98f6d6360ad22616b2", "lodash": "^4.17.4", "safe-buffer": "^5.1.1", "secp256k1": "^3.4.0"}, "devDependencies": {"nyc": "^13.0.0", "standard": "*", "tap-dot": "*", "tape": "^4.8.0"}, "dist": {"integrity": "sha512-NbvmUxuMqhnqprIHAdaruC5zQIJQN9gLXUgN+avPn8YLlW3+/Fb3uSyXVImK49+aSrDEIbv3FcixZoTxE5jYnw==", "shasum": "47de493954c488789abe90eacd694d9dfe770647", "tarball": "https://registry.npmjs.org/bolt11/-/bolt11-1.2.5.tgz", "fileCount": 9, "unpackedSize": 89578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcuFbXCRA9TVsSAnZWagAAXBIP/0hU+2uBQTZhfZ5O+6jM\ndQBcVBrCGyoLqIlvZR1wGjc8V/II6/NxilD5qfyds0l6NIBJpJF5VxT0tnM1\n68/tRx+S1WjRq0wsDRILpb0eUKSRp3e71QI4E0rhMZ62MA467Hmxowi4s+E+\nDQHLJ/iuQNl/dY5pkivfZe4BiNEeNiSmjUD9WoKJe17iaKN0WGiUUEx4z/1w\nXuhxGiLALSJ1cARjA5TKrfU9+7EG7eK9shXku5nsjmw0TrhFcgINC9uGH0xu\nTBE69slL4W7LSaQJtDC+BtTaL/k0BSss+8GpWcRhHmgvkE86BLxFfD9sqjpQ\n2XuaS9REYz0M9mLQjoEGDJ0At1QdkBztg3BsN8sJ1VwKqcfYbZETQF0X2VMM\nljtOZ9dLbJ7XoMvCX6dfBfgPZb1dgkpaS5jRDhQ9oAF2Q1crO75yv5ZyfxAU\nkljK86WfMA0wkTX6TztlGbm788U8kev0RX3UShale25eIAJiA5Ad4S0BM1dL\n9W6ARgNJPl3zMFnP3J/D6VOF2FViDvgul/e9NossbC0Or6XessHJGyKsdMfw\nBSAeePiu2K3VQOu2ALmIRxLvPodYLLvRnEJUiPqtuXg6Em94zq/mPlQJb1qU\nysFvDHpR/M6JUYP1N+lFKW4lRNKhw/8XHAsYwTB9s3ckvi86t9S5lRTguf7y\nZmRK\r\n=lJK8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC11jsC98aBeGmgvDi+majPB63q2tPnVZiImkC0Bt7grAiEAiK5DxZnsjIqdZGbgBTEFt3FjhNSoREOfGIb37lH2Z0U="}]}}, "1.2.6": {"name": "bolt11", "version": "1.2.6", "dependencies": {"@types/bn.js": "^4.11.3", "bech32": "^1.1.2", "bitcoinjs-lib": "^3.3.1", "bn.js": "^4.11.8", "coininfo": "^4.5.0", "lodash": "^4.17.11", "safe-buffer": "^5.1.1", "secp256k1": "^3.4.0"}, "devDependencies": {"nyc": "^13.0.0", "standard": "*", "tap-dot": "*", "tape": "^4.8.0"}, "dist": {"integrity": "sha512-y4ctQaMgslIPbgfQ3UjDpoa7foURai5E+wBTYZ0gUHGjuCVUK/xU968R15UUyFnwEcEcomGeyE1vLrgSFnzj6A==", "shasum": "ba8eed12640a9c6e1e62b8851cb998ec956f3418", "tarball": "https://registry.npmjs.org/bolt11/-/bolt11-1.2.6.tgz", "fileCount": 9, "unpackedSize": 90253, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIVbbCRA9TVsSAnZWagAAygYQAJrvOhse3xqpjxJ/woD2\nfxtzqyxF32fgaAiZ3nDz6W6s0K8DYz2iPWNt8DHxpKBPjuvmIm1L7eWDzmdy\n5efZXQvKy9Uo2LQRPSA1ZCF8Qqm33uRB1FUzHgN3+72nYhIFLdISnQvTtPqR\nGZ5Pa24fmJCtc/lVEQ1pr0IvrwZFiLfHPkCSX4Nl9ce21spBNp/LaQw99A3J\nzbdFaAEdgrvnHnQMXPfYUjpqPp1Vkm0KddKU9qq+VswWovCgFdEHnhSbbQZZ\nJL155dBAQ1rJaZat7VLiAKXGWIuCBq2hAxJq/6ZkKMc9BWpANZdtgGCrTrrz\nu9VBEnXeFZ9SWdW9n0rvryD0fXNke5tzab6Wgu0hawEW2J8k2RQ2adjdUniz\nM40Fb8ZF2FbrhUuQS2x02C3t8WDBGZm050cPpG1uxdvIH2raAJljkHXJW5H+\nsLcCYilKHv2/uB9xdHpmts1ttA/AC1j2Rl1U+3+do3AHWASJ9hnmBEYFRSY4\nOB6yCpTIpr2k81AxGPpy8vfVr778H1bV7ikeUu710Ga0zvuqNnMON9w77+Y0\n5yX3yDDjQwgUxeVApKfNt6kA0p5rqt8kw8U3sRWOkMLc7Gr7VbguHFkmwUr5\ne/rGTcEMIxgqVcBoiVkA+fSb6c/7kb20gLeozbfZRVmuE/rxasbgiB7xYL8r\nCpeC\r\n=2srI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCbiID5nX2oswgB0zAJJ5/J6M1MnX3kt5/jz72bxQp6GQIgIbPJZKa9j/cBuq2MGZRXKaDQ2cQ+aDO1+R/a3I9ff1w="}]}}, "1.2.7": {"name": "bolt11", "version": "1.2.7", "dependencies": {"@types/bn.js": "^4.11.3", "bech32": "^1.1.2", "bitcoinjs-lib": "^3.3.1", "bn.js": "^4.11.8", "coininfo": "^4.5.0", "create-hash": "^1.2.0", "lodash": "^4.17.11", "safe-buffer": "^5.1.1", "secp256k1": "^3.4.0"}, "devDependencies": {"nyc": "^13.0.0", "standard": "*", "tap-dot": "*", "tape": "^4.8.0"}, "dist": {"integrity": "sha512-og00WBenEDUeMOVLoiWIiA0i14+tslzqhk/fJIYt5L7GWFFvY88kl0J1ib5DqMXp7mQKez4E8/XI6nrG0Rek4g==", "shasum": "75852e215aeb28d6ccd6dd7c721c09d8ccb40a88", "tarball": "https://registry.npmjs.org/bolt11/-/bolt11-1.2.7.tgz", "fileCount": 9, "unpackedSize": 90539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeEqA7CRA9TVsSAnZWagAA4hMP/jSL82RfBwh+w4FnnyWA\nPsyWFjIGOQjk6ilgHrVPDDxC4S2RfdDhi5sZ2dSfKsjJPRpIQN8TrgxZAaza\n0N+14GedpFdE8vkmUtRAD0X8BdbC2gAWLRaQh1DRU5NZxhXQozyxgjpwX8Gf\nMss6n9kx3XPjMccMf+/YBi/LtV3dQo67VxHz3/PYnhtbZDeKpJZH7v0iJWU3\nTAF+gn8kfOGCcny4zF+gzAqNOAS0ApfVLLrPt4JO+FHcQVhoxAvbrNiiGWqE\nWGV10nn8ub3hO+kDLcCRxkPNv2+/tdNpprY3TBklLxlDgqWylhe7Ny6aZpX1\nyrlYMM11x8VeKTyoq/y/FTjvnnirZAIQVVRWC4aJcECUzMNwc02//ElwV+Tn\nhE1sxlP974Trp/fMe7Zop5CKP2wSYMpugtOxUDT7rYWzf3aUwtp7pI0xjTcB\nakWyBGAzcbc3ZPKCKTD051tI+SJdM1Ilmt9t/yZtJu7MDuQV3aJqW6H+I36i\n3ncSglgqh0kOZIocY43jw+JmH/BsdB3U4+yzcDLt/LK5fDopQUN+T8kVSdDz\ntjpDasjwdgx6MQGY3TIEPwJwjm5rSr3rGz6kvpaJfyYI4kDBvJjosFdDKDbu\nJG/CUz48Dsd6YAt8Apfn3Xq2tKBWCL/oqCPeTst4Pif0JHA0znXtSWgfK0vm\njtHG\r\n=URsf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC+l5b5kTdjbzm53zZa3fXAhN6J8ai395mrJQ/WNyPy6wIhALc0W/DD4bDUYJf+e/1IddUr0URArBhOsWErDD0UyOGa"}]}}, "1.3.0": {"name": "bolt11", "version": "1.3.0", "dependencies": {"@types/bn.js": "^4.11.3", "bech32": "^1.1.2", "bitcoinjs-lib": "^3.3.1", "bn.js": "^4.11.8", "create-hash": "^1.2.0", "lodash": "^4.17.11", "safe-buffer": "^5.1.1", "secp256k1": "^3.4.0"}, "devDependencies": {"nyc": "^15.0.0", "standard": "*", "tap-dot": "*", "tape": "^4.13.2"}, "dist": {"integrity": "sha512-gkrCA8G7p4RjILQ6KaymNg4t58NGoI9iE+KBN3Sze+tYeBJW7EC/Z1DPiX0YqcrzUbC3O0UZqh/uYTujop+t4Q==", "shasum": "5a264a690b051db6c295d0d3a0e8f69f62c64e5e", "tarball": "https://registry.npmjs.org/bolt11/-/bolt11-1.3.0.tgz", "fileCount": 5, "unpackedSize": 47697, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRufPCRA9TVsSAnZWagAAdcYP/1+JmKRxzhq5wtRMLnGK\n3uJ1haSMwp70vkZQUTDxJfrMbPAVIDHx8e3qqkOvv/wqnER9kwxlkrpefOOz\ngQkqBm35B6kp7QcEbfcdYCloQu0DpuCG+7MPoIzHCXNv9XUE1mkQfzkGT68Y\n3L8o9wJoGsygEflILDvv2I7JCNZw1UXBYhY/6kko/DXMz3qplJvimJfgbd93\n6iVegRX4d/NCZbRS3YWvirH25miNcG8sAITRtGGBbGhKjBmCN8Fvf/FlUKTu\nQN30PvXhsN5uCINtKnL+RUwXFe4tTJiYwBYmm+l58ZVQxLDOYbUTu56SGg+S\n4Ygc2yaxwk9SOzHu7OVMMRTkdQMn0IGoghTPr3GUu3qFGfJLusUApF95Yv4o\nfXoI4CfYARYw6L0hIExQ2dkVA5uxgr+J2+mGNmp760Mfp1qum0d+MczE/Wn6\nJwTgRCij9QAvNOK+CcftFRh+V4uda3v7ueLLmKZKBaL4LjgO4YWqJ7tX0zqn\nrnawHDeAOnyz7WY4jDP2yU0dbFqcpUi6m8EhpgTrJ2rrpiJfOggyH2RI5dYK\nhi9MCeCR1mpl+K1wps84QWkUXrNHfadE+HuEiHvJizXxcllK9XvpyCq/Pd7Z\nfZ4yUNwmLDGwpDEQJZsU6oQ7U4k8C6iVAyl8UEjG9QfM0CWTZp5CPIV/TPcm\n59Q3\r\n=ii68\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFqzzU0a8EQTLW+gCXcyOMgZ0/cVm/OjtIdc7dus4dcdAiAlhtV6M33zPCDwiKOkm9WZyvLrvsccAxGlO0k523QBJw=="}]}}, "1.3.1": {"name": "bolt11", "version": "1.3.1", "dependencies": {"@types/bn.js": "^4.11.3", "bech32": "^1.1.2", "bitcoinjs-lib": "^3.3.1", "bn.js": "^4.11.8", "create-hash": "^1.2.0", "lodash": "^4.17.11", "safe-buffer": "^5.1.1", "secp256k1": "^3.4.0"}, "devDependencies": {"nyc": "^15.0.0", "standard": "*", "tap-dot": "*", "tape": "^4.13.2"}, "dist": {"integrity": "sha512-dmTcJGIQ4RcSDgXMijhUO4JWskCelXhbpcedWsF7YoFcPw3QYzYWCowO0+GJFT2TvrHXtlYdbnMBT85Se8IJSA==", "shasum": "583af1e3af1f0456be0ebeb0470af023ddfcde9b", "tarball": "https://registry.npmjs.org/bolt11/-/bolt11-1.3.1.tgz", "fileCount": 5, "unpackedSize": 47697, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRulqCRA9TVsSAnZWagAAit4QAJarO6vIucbXUHP+fw/7\nMy6dP5XKOdLOl+ND+oaG4eMuBaqChz6nQ1ZrFwCtXKgcQRX7bmvhZAQlQGrM\nacwdyWVx8Er8mA0vSDZXFT6qYcXGUFPOU3DM+h1xjBLEPLPndVBasqjhCY/S\neyZXRy16WKmdxABTQssISbQCrgapKOe2GISmeDT0Y2Azs/ppVDPiSThEO/EQ\ntCgddBGxchus0Yb4jErwSfEblTA43g/Fe9QzzXEc3E6yHYyfkDl28YDXRtJh\nCVrvYIn8CeY4VmsP2Cbd1Ik9iNM/bRZqJJcuumsRLCKUwMD7S4wnJsaKcR/U\ndl39p4LtwU5nXrV2LT64wk2TkTwBx3+xLZJPsyyMS/nDfBikbzXT+s1qaUe8\nP1m+kbUVdmQXWUET4nyETJtesYKeGfPc2FQcBWoMZKBqh9Fz2x+L16qfjBaH\nWIdfoZ36c9Y4jOPFBJ7ALIhEgKPd7jflN0Iz3A67y7ulCrFhas62+prUVRaz\ntzFeStdAtt2BJT4or+jizn/pjMvvNJDpoHXMPdtftQNc4+INpXk3U8FGPqFM\nSBON6H31m5+PpW0jvfAqGFW5anrYcQvYOFe+uIfm/Sg6K4fPlcms9dp4ZLnz\noGedc9HdU2P/5hLBQIT38hQJeWEdTqelnqsFgjmkK6ewyQIq0iof5po24wyz\nPQRH\r\n=skkR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCqQbFwlxQ803vilMz9d5R6by/G7OT6Xo450ZzkgYIz9gIhAKYNwodFDsY0SAMq+UPuPUkoxvxbOEuew3MCWBFUR2Aa"}]}}, "1.3.2": {"name": "bolt11", "version": "1.3.2", "dependencies": {"@types/bn.js": "^4.11.3", "bech32": "^1.1.2", "bitcoinjs-lib": "^3.3.1", "bn.js": "^4.11.8", "create-hash": "^1.2.0", "lodash": "^4.17.11", "safe-buffer": "^5.1.1", "secp256k1": "^3.4.0"}, "devDependencies": {"nyc": "^15.0.0", "standard": "*", "tap-dot": "*", "tape": "^4.13.2"}, "dist": {"integrity": "sha512-WW86n1QdusD6t3j64B+GZLztj4skwoH/LBiiIrt7zpQvtaGJsRMavv22tDwoDBF/Yb143omPIq+R6sJaRRUP9w==", "shasum": "7fc13ed7b1c86fab2e57927af057df55d7841b10", "tarball": "https://registry.npmjs.org/bolt11/-/bolt11-1.3.2.tgz", "fileCount": 5, "unpackedSize": 47895, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2lsaCRA9TVsSAnZWagAAXXIP/iBzvwkhNntDIUDhjjSi\nclyl4nkMsiZ9GlsAX3SnAQjSUDI5wZE9FBF0rywwxkpttRSd1Z+jBr9DxE//\nMu6KkdGjcGmzz5NMf6mACqDLvZQQPGyvk6wB7DPOd1eYIDWJta8tfQH+WIsO\nyoLgLsXBVWXGp8Aero7E0X5uI2YTPyI3GObF1sDFw1pBgmfmC57Zd417TbDc\nMw5HuWGhRmeABC6+gY8LrWgQG1hxX04m4ySO7roou6UfVKYb/E2xq7hHNc+t\nME5XDXXL3/Rw48Cv/mTGmTa46QchUE51DGWr9NLqc70ftSmQnHBr3aO5FFMV\n1+g1jmilZDSIFj/xxMxzbNzvVDxWwk1b9acYZJV2cWYR9zAaj0tgHuApEPdL\n7wvd2FmcVK6x9kfJXKG6/6UYYh/+Gmb4TvoLHgB8DoYbda4sRJn7WYAfdTGV\nMgoBU7WxeVRIQ0Vj4CoNu1E+xe7zyc9FSMmxtjbyco23xYR3SlfUBjmasRQa\n3+1iRhAMbmVjv8UWT1TjwedxHeE50pdTX3KkmsqLjgel/qxdLndY7jOkfTK2\nL6Zbx4B4FZ91jTg0wavPfn4OCfNXSvQ130SDcFYmXzxcV8tgt1VK+oNtyMEG\nEjJ9YVbzHx25W81wZKBMkYsgtewjzM9DMYsPNL6vsDD+bDKexcgK3mJ/nd1p\njm0Q\r\n=OZ8C\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB1hciW02JQq/F16JvVbnEZ27rO4Cy7migf2StUTT0r3AiEA+UDgerg4tnT1shr36cd3hdfs6mW3aS0NRRujtIyp/b4="}]}}, "1.3.3": {"name": "bolt11", "version": "1.3.3", "dependencies": {"@types/bn.js": "^4.11.3", "bech32": "^1.1.2", "bitcoinjs-lib": "^6.0.0", "bn.js": "^4.11.8", "create-hash": "^1.2.0", "lodash": "^4.17.11", "safe-buffer": "^5.1.1", "secp256k1": "^4.0.2"}, "devDependencies": {"nyc": "^15.0.0", "standard": "*", "tap-dot": "*", "tape": "^4.13.2"}, "dist": {"integrity": "sha512-9L4C9kfA0NEEBsE/rRXTLofCP+G9V1H+YrQZ5H6UUEkeIG+oTBhrDfOK00Xh61DTvliL9Fs0Of+4eiRIh/cSAQ==", "shasum": "149e5ff6303c70d2f24572adc0ea39e5096254a6", "tarball": "https://registry.npmjs.org/bolt11/-/bolt11-1.3.3.tgz", "fileCount": 5, "unpackedSize": 47920, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhljE4CRA9TVsSAnZWagAA7IYQAIVdz0OXJWr7tkEaPIQ8\nvftTGFDrKQFxR7Y5pe3ohdIpsJQsjSQTmL6pXjAmFCegAzd2yNFVC99Bm5yd\nNTX8JGLbMPMySDvNy7BnlQ+A7H3Vij3UosMKc6q+quzeJzk8mrmHXaiAG1EJ\n4M6rzi61t8/G/onWYdUi35AqrBdyMmv5odwLMSO0fr313Dhd++O/yctVyT5g\nT2l/MLLDMH7TdjNlAJCY486azLAAP0Cgw/6Jek2WMTVJbOQpLvXLGriQhxpG\nqn1yGZegUUdUPfw8QY9gq6ZqmMBzf6uqw0cyB3hyWzE556IXuX8TWpfFfDjp\n0aHIsOcCP/GMHqf/y+YrjxFSar09Xz9XJ7JBkmHd7tpPXbPMJGFI3TXXdHq7\nolsWn5wfI/A6pWEyAWrwnZHl/VcaY7H1/3IkQgY7+ITVRzGuGh1Fvys4dgnm\nYiBg/vlx+s0he3I7MY3fIfUpd75aRA2zLlyKFPwB5i4N8F9kAuTuWTp2FC+E\nO+1d2NQIM8pTAeKE1EDcIRdlxSbASvzFb64qkwXgefiWO4XoIWvKLGTOCISO\nWvck6E2LmOsR1it1OpA0PqVXkvPdpOXwNX+fc5vtY47eCD8JFo+2HimWqwNB\nf7UsVxCAxRdx8cylJaTa2QbDap5rdfLiVthn0KJ9/rN/K+DBa5d5u3nBI10g\nIo6v\r\n=jAhp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHCrnAxIjPvkBaUC7VQ9E1AG2DjJobMHm25Ry9zAnMJ3AiA59nmeec6z5+OlxSS0Zssab9eaRhUV+rfYSgUirrq5EA=="}]}}, "1.3.4": {"name": "bolt11", "version": "1.3.4", "dependencies": {"@types/bn.js": "^4.11.3", "bech32": "^1.1.2", "bitcoinjs-lib": "^6.0.0", "bn.js": "^4.11.8", "create-hash": "^1.2.0", "lodash": "^4.17.11", "safe-buffer": "^5.1.1", "secp256k1": "^4.0.2"}, "devDependencies": {"nyc": "^15.0.0", "standard": "*", "tap-dot": "*", "tape": "^4.13.2"}, "dist": {"integrity": "sha512-x4lHDv0oid13lGlZU7cl/5gx9nRwjB2vgK/uB3c50802Wh+9WjWQMwzD2PCETHylUijx2iBAqUQYbx3ZgwF06Q==", "shasum": "4f2c2fa529f2dc1d60ee7c932aa47d90a0c17ed9", "tarball": "https://registry.npmjs.org/bolt11/-/bolt11-1.3.4.tgz", "fileCount": 5, "unpackedSize": 48019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlkPqCRA9TVsSAnZWagAAJUYP/iPBctZ2D+r5XMz/JV44\nAnek+X8lQIGffQ0i3jzg63ZDY4jwcuI/zWWM/bthNP5wKzMUS/361/KPq0B4\np5XOINBqejQrUU0fn1nQfyMI9wJZjqt0SmVw2UXXWALorhJR/jpqO8EqaFTJ\ndfiM4PQMsBDLowgj0Lw0KUnmWQ69WWQSdH21y1PG0Su1L6bLJm1ULzBlEtUg\nzCOKglLrOfE0Q0h7j7Gr9g2qU4QyqrSkgii+ttnFCV3OUN33UzLzQO4dWvR3\nUw1CBQit26OzgcI6Lqb1dFcBi4Nc/EpA0HaNYaNN6V2Xs3jqUKSigEXNpL8J\nThi4zloqOiOzt4shemB4fENe03u3ZB3E7eHX24shWN3zMpaqawAD4MrTZ5bh\n+FucznxSTmnSjUa8KAFlSwchJl5w3OsRbN/miWCo52UM8MoZpzlKor7vwOom\n+pOZCdQtwFf4eX4ANlha4Qhma9Mng9IpFe3771hpPazh690YOWlOUeR9lYUw\nElY73LlwcnPQcleOXV1YMUFUdh1tYy/Z1CW9n9adCSGtJ9h5xIAngpNVOuJO\n8ePtUY5qmf6t7DzjNyU4FrbDzCXbkg7CqWxtCwG8+PPCPoKL/Bx1iowFGBXi\nrTUUCZcm2hLgy3OhFG8bM7ikCrLswdf4deU3dywVcjeZ3RSYjefEZGUfRlzk\nZpgr\r\n=4Beb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEZarYJede3rhzq10e7d3Ee/7Dx1aPSq+o57YZyhVF1rAiBSqKvosEZdioWZ+Zfpx4VNSPGiVCBG2scl9MeCNPcV2w=="}]}}, "1.4.0": {"name": "bolt11", "version": "1.4.0", "dependencies": {"@types/bn.js": "^4.11.3", "bech32": "^1.1.2", "bitcoinjs-lib": "^6.0.0", "bn.js": "^4.11.8", "create-hash": "^1.2.0", "lodash": "^4.17.11", "safe-buffer": "^5.1.1", "secp256k1": "^4.0.2"}, "devDependencies": {"nyc": "^15.0.0", "standard": "*", "tap-dot": "*", "tape": "^4.13.2"}, "dist": {"integrity": "sha512-TeGIOpFNrvNA1XGvwBr+lYmzKfoJeyMRt7nsGmIci+5gbmSz7s46jwKLNYXH3IoxV7fjmYUEUu2NM19n9w0msg==", "shasum": "d8ff0f6b8f89aaa07ed8c02e330a344b8c158263", "tarball": "https://registry.npmjs.org/bolt11/-/bolt11-1.4.0.tgz", "fileCount": 5, "unpackedSize": 50833, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDRwUene1nrjnKFmDnOBvcM1x8faZDF+N2x9sjktveDywIgTCXuO7DrbhxL2SXEvu2Au/NO/1lUHEQRoy/IybLPSCg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia9U1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOQg//Qb3VEQWFHX7m7p0GxgtsxUjG+eR7N6YHwraOLE/AuJxLoI6U\r\nzLg4TkXSW7tkNiNvb0ajRTSw0CHCNT7Tep7x3XP7UM2Eudu0Ud2wlmqLL46Q\r\neuZ+KB7vrl02Jza9JIMlHfWJ7VqxuGQUwvll4sqV1mrcjc0fVb11STLw8k+R\r\nXlG071xbXTpWnQ/Ry11u+StjV0ASJ7WEcap/neEf+IEbzQAPYfenotm1fDY+\r\nebVsSzAohH2tcOdnNneyIl3pubEpqMuMdl8GCPC0Zo7HpqYJxzequVLH916y\r\nIoqw7FPG2Lf2YVtfP3KtbDMFXVOkWtbRAkVVLTqaOgSzijBl/gc7Q5lQgtAU\r\nIM0me9plR8bUDPBpANPdnvowAFhIm/Vn+IqeeXCyjr1rEh8PxonB7tOacxTU\r\nnNp1Z3IO3KPW+rsCY1zQtC2TH6M5HkcS5RQunNwgKhvgeJ6bcZ4u+hV/5+sM\r\ny1MTLjTDac8RBJ6OBBTQKgePSokSl9eM5VIA5YQk3KkHZPvtuoUFkk4oLhmd\r\n3CQjIWjcS7dzgRCbiiiWZMoAP2rff8NuAB6KpuCukz/P9H6/R9oy3wFGeiT5\r\n53MXllQGyGAveYWVBW8c51YSLGE8m5RMxtdQbLmhqJKOEZVsZS/bpQF3h5Q8\r\nbEi8WeSkkvy3IYM8Lo1h05e3ZTyd30JO8TY=\r\n=P3ze\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.1": {"name": "bolt11", "version": "1.4.1", "dependencies": {"@types/bn.js": "^4.11.3", "bech32": "^1.1.2", "bitcoinjs-lib": "^6.0.0", "bn.js": "^4.11.8", "create-hash": "^1.2.0", "lodash": "^4.17.11", "safe-buffer": "^5.1.1", "secp256k1": "^4.0.2"}, "devDependencies": {"nyc": "^15.0.0", "standard": "*", "tap-dot": "*", "tape": "^4.13.2"}, "dist": {"integrity": "sha512-jR0Y+MO+CK2at1Cg5mltLJ+6tdOwNKoTS/DJOBDdzVkQ+R9D6UgZMayTWOsuzY7OgV1gEqlyT5Tzk6t6r4XcNQ==", "shasum": "4363041b8c9f477b7f42c12d96e771fec39a00f1", "tarball": "https://registry.npmjs.org/bolt11/-/bolt11-1.4.1.tgz", "fileCount": 5, "unpackedSize": 50905, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCqOu8nO4so4sY6mUlTxrK5o32J/t8pHN17lVbtARAtBwIhAMWGRECtR9Bzi0CeHbNZ+1peIWPzsqRD9fD4HQPdaEdF"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkG19+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLWw/8CYhrSAbDYzhVFANx3nSMD048JQIfnHl5+E8UrkNeQnndpw/+\r\njT4jDNdKey6yMJqwSSgPlpKAHLASGcTvz62PPaLEQUnDJYQGYH31pz95oPiU\r\npNhewtQzV+LtiJUGFp7tip9ahFYtFuMFhvD1plPfm20HPn86418oOj7X7g/Y\r\nqQ0np/uk/5GNWKYJp/xn8sP+DSWZ6QC6EuGtse/qrsCVBHObUcZbBoV7WBVH\r\nsUhr6vinHaBytIBAwP3Ree1sCYopq46Ek0xYEbtTGWbC/SzdNi9WiBWT4XDL\r\njbaD+wzZ0y9rwXz3MZYXnxkrrnrGi+DcuEMAPhuQPal+CMZ7mXbRUPbSTtLx\r\nqxJ3F13SB0RvzqM4D0zER5Ztpa+cnh1JN0ubNFHpW7XGFIJofjhi69rrc0Bx\r\nh/3qHy9Wku335JJ1fcSSR7dehsHJ/WXM+q5YDzUdPkZYZA4GyQ0TtOjaoybt\r\nr/S1g8w9qcx+4JuLdn3XtZZNFwqSK+Yx0fdbydkHy5tOwhHmyv6s/0kT6KMs\r\nHJXXYbW4njQeg1aLtidUFwrX0n5I6eqf1vJLRQtGNr58WCyTdMeqPp5UiNU9\r\nqQfW2osFWmBjjFbt2NxFIgHmRQEl1VnXj1RU67w/r2SvV5vd8J0d2ZuvHz48\r\nyth6/2gLpMDv7fVKftwKoosjv6m5qERYRGg=\r\n=kMiT\r\n-----END PGP SIGNATURE-----\r\n"}}}, "modified": "2023-03-22T20:05:19.039Z"}