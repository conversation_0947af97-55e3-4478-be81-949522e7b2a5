{"source": 1099520, "name": "body-parser", "dependency": "body-parser", "title": "body-parser vulnerable to denial of service when url encoding is enabled", "url": "https://github.com/advisories/GHSA-qwcr-r2fm-qrc7", "severity": "high", "versions": ["1.0.0", "1.0.1", "1.0.2", "1.1.0", "1.1.1", "1.1.2", "1.2.0", "1.2.1", "1.2.2", "1.3.0", "1.3.1", "1.4.0", "1.4.1", "1.4.2", "1.4.3", "1.5.0", "1.5.1", "1.5.2", "1.6.0", "1.6.1", "1.6.2", "1.6.3", "1.6.4", "1.6.5", "1.6.6", "1.6.7", "1.7.0", "1.8.0", "1.8.1", "1.8.2", "1.8.3", "1.8.4", "1.9.0", "1.9.1", "1.9.2", "1.9.3", "1.10.0", "1.10.1", "1.10.2", "1.11.0", "1.12.0", "1.12.1", "1.12.2", "1.12.3", "1.12.4", "1.13.0", "1.13.1", "1.13.2", "1.13.3", "1.14.0", "1.14.1", "1.14.2", "1.15.0", "1.15.1", "1.15.2", "1.16.0", "1.16.1", "1.17.0", "1.17.1", "1.17.2", "1.18.0", "1.18.1", "1.18.2", "1.18.3", "1.19.0", "1.19.1", "1.19.2", "1.20.0", "1.20.1", "1.20.2", "1.20.3", "2.0.0-beta.1", "2.0.0-beta.2", "2.0.0", "2.0.1", "2.0.2", "2.1.0", "2.2.0"], "vulnerableVersions": ["1.0.0", "1.0.1", "1.0.2", "1.1.0", "1.1.1", "1.1.2", "1.2.0", "1.2.1", "1.2.2", "1.3.0", "1.3.1", "1.4.0", "1.4.1", "1.4.2", "1.4.3", "1.5.0", "1.5.1", "1.5.2", "1.6.0", "1.6.1", "1.6.2", "1.6.3", "1.6.4", "1.6.5", "1.6.6", "1.6.7", "1.7.0", "1.8.0", "1.8.1", "1.8.2", "1.8.3", "1.8.4", "1.9.0", "1.9.1", "1.9.2", "1.9.3", "1.10.0", "1.10.1", "1.10.2", "1.11.0", "1.12.0", "1.12.1", "1.12.2", "1.12.3", "1.12.4", "1.13.0", "1.13.1", "1.13.2", "1.13.3", "1.14.0", "1.14.1", "1.14.2", "1.15.0", "1.15.1", "1.15.2", "1.16.0", "1.16.1", "1.17.0", "1.17.1", "1.17.2", "1.18.0", "1.18.1", "1.18.2", "1.18.3", "1.19.0", "1.19.1", "1.19.2", "1.20.0", "1.20.1", "1.20.2"], "cwe": ["CWE-405"], "cvss": {"score": 7.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H"}, "range": "<1.20.3", "id": "uoEU0GS6gk35ZdcQ5nagZYzZcDz5Th0PEvPIVa8rzwbO20pRdxYbAhpIm6Wc5MALXoWb1C4X8WqCtH02U0Wo1A=="}