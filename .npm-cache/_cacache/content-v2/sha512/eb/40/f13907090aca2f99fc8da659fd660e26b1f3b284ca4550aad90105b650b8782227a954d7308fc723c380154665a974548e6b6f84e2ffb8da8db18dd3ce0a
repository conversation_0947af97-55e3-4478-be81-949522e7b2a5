{"name": "body-parser", "dist-tags": {"latest": "2.2.0", "next": "2.1.0"}, "versions": {"1.0.0": {"name": "body-parser", "version": "1.0.0", "dependencies": {"qs": "~0.6.6", "raw-body": "~1.1.2"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "*", "supertest": "*"}, "dist": {"shasum": "95c8a2861cd150dc195d50840ea4614149455e80", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.0.0.tgz", "integrity": "sha512-nBv25q4Mmz/ObizPi2xoi5elHpvaQheNWYMfAziwEbjnY6taSY50AAtSt+ZUdFJxU7PI6vTZLsuchQRE4epcuQ==", "signatures": [{"sig": "MEUCIQCjm1sSxADhYNoeGaLSseRF6CnHSB1YcD18aeQrn3oAdgIgQDKOzne2UkSpeUpDA61iZnV7/FHjeXfJmXrQLl5Qga4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.1": {"name": "body-parser", "version": "1.0.1", "dependencies": {"qs": "~0.6.6", "raw-body": "~1.1.2"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "*", "supertest": "*"}, "dist": {"shasum": "08a2d025ea286f982d5107ea8a2ba953708620e3", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.0.1.tgz", "integrity": "sha512-JNr7xm9WN153w4il8wiWv4w86F0VYhlkOdAYKLrfJvviT7IIJgL9U7JpGn173TaFB4sWy+/6Q/4LP3+bK6Ki1g==", "signatures": [{"sig": "MEQCIAwzqftmrTrwL8paGNt3Q6OtNDhciVddjWMZ2Xwv6XBOAiA1vBIxP9/e02PbEcMXjHBoUKwytynuquwtIiS1zkSCaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.2": {"name": "body-parser", "version": "1.0.2", "dependencies": {"qs": "~0.6.6", "type-is": "~1.1.0", "raw-body": "~1.1.2"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "*", "supertest": "*"}, "dist": {"shasum": "3461479a3278fe00fcaebec3314bb54fc4f7b47c", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.0.2.tgz", "integrity": "sha512-/VlRWmVVwfh8rYG/MHGIwesdJYdxBgeddggFG5ssLfX/Qhwt4NKEQhvzwdzVRcy2RhLxni1MKe0uuPfP/unjyw==", "signatures": [{"sig": "MEUCIAHoDhLSb14ld9+5KQLZFkyEGN3QeK8rCDg4IJQ4KBpZAiEAuxCRXr5Lh7qUY729uacI8/moS1eLQ/9+jKWCmMpCbuE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0": {"name": "body-parser", "version": "1.1.0", "dependencies": {"qs": "0.6.6", "type-is": "1.1.0", "raw-body": "1.1.4"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.1", "supertest": "~0.12.1"}, "dist": {"shasum": "e6a3c46063b329dab0eb7a31bdc1dca3b3185ab9", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.1.0.tgz", "integrity": "sha512-9yrm354GLsytztMuoUf8uRI0wZ7TJQnLBAfSEVxy6KYe7Kp7A6917RLn80EwLKH8qb99pK2azVOMwu73hKHFZA==", "signatures": [{"sig": "MEQCICS/vg8M35iv6k15KCfTf5htfzgPTtmyNExG6x0Q8FpgAiA+d2XvIbdcUuZhvrJ+P4QHYWUBmK+S1AGIOqv7HJSDEQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.1.1": {"name": "body-parser", "version": "1.1.1", "dependencies": {"qs": "0.6.6", "bytes": "1.0.0", "type-is": "1.1.0", "raw-body": "1.1.4"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.1", "supertest": "~0.12.1"}, "dist": {"shasum": "cf3cc10d885e91fc0ffa35a47ecad858238fb880", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.1.1.tgz", "integrity": "sha512-7C9Yj+601JYEnumC32wfIqIqkrUsXkxE2gssCioDb7NCLgXVyVMRIZ1RM0fajzRNRYW08hOxP05JGXdVFhIieg==", "signatures": [{"sig": "MEUCIE55l1ceYO8CKnKnFMfmIpOo8hOwlSPpOcrA148svmufAiEAq3uOYyhi7Y+/tpeg5cnA7NLctC7QXM467XJX7/C3638=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.1.2": {"name": "body-parser", "version": "1.1.2", "dependencies": {"qs": "0.6.6", "bytes": "1.0.0", "type-is": "1.1.0", "raw-body": "1.1.4"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.1", "supertest": "~0.12.1"}, "dist": {"shasum": "c943b64c4cd3c44dc96a4681b02cd54ff29e8cd7", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.1.2.tgz", "integrity": "sha512-A7tD5EdJ/tZtZkeIUYEWKiXZInTNWyh+2lBL5s0ItyO3OVosTGTeoKI1d84qsP84CpZoNdeZ5bpPPNtb/Nx0Sg==", "signatures": [{"sig": "MEYCIQCd4T+SFiw8gqHR/Bxgz2klFHq8HMJN1HhAnRfgqKYIHwIhANoKYLEhvExYqxl99YV2NdsiXxPOJByGdz1BW6qddhK2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.2.0": {"name": "body-parser", "version": "1.2.0", "dependencies": {"qs": "0.6.6", "bytes": "1.0.0", "type-is": "1.2.0", "raw-body": "1.1.4"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.1", "supertest": "~0.12.1"}, "dist": {"shasum": "f6247cc88d4c673c30a926d74fe36c177b9846e0", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.2.0.tgz", "integrity": "sha512-p5oYikf6X7c5NtXzYQd4T3m7a0r4x6baDEHOvPIaRQGI76S4mpvvdPvQDulXjwAULXZ2s/pLNwAal7TC4gEcwA==", "signatures": [{"sig": "MEYCIQCDQn3pHZfAe0m8l1imNI3/m8pyNwkonaou3+msviiaXgIhAOttW6h5KG2IcX8m0xnic4EOjjCjdqlC1HHXVg8i4t+v", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.2.1": {"name": "body-parser", "version": "1.2.1", "dependencies": {"qs": "0.6.6", "bytes": "1.0.0", "type-is": "1.2.0", "raw-body": "1.1.4"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.1", "supertest": "~0.12.1"}, "dist": {"shasum": "917beee35a88e9f6893728bf1a542111d7d1eb28", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.2.1.tgz", "integrity": "sha512-0BrQaF9eVPjhT+4rg0hjVePQVtemcT7/Anc7DANq3KW99Yxp7J73DN3AqowV70XQx6hz+cOVZhulNmLC6KZ6uQ==", "signatures": [{"sig": "MEYCIQDWbMpJGK2g0c2g+Ly5JvCahEDq1NhyenPYtRKppoGCLAIhAJSHCnmzj6sUmXOXN/YEz5iqzIsFOwKFdlRQB8hZS1Te", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.2.2": {"name": "body-parser", "version": "1.2.2", "dependencies": {"qs": "0.6.6", "bytes": "1.0.0", "type-is": "1.2.0", "raw-body": "1.1.6"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.1", "supertest": "~0.12.1"}, "dist": {"shasum": "6106373cc1d34d559ebcfdb582e4e37d4312acfb", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.2.2.tgz", "integrity": "sha512-msu39zjr4/jhj9am7DUYXy0frYS6LfdJ7VFvefqJgM0x+1sBwLrIPpRf0anLr+wjdS6AHk5SQyCaZMQ1ZDvTmA==", "signatures": [{"sig": "MEUCIQD/C/5mkM0OrWTNlrLe/HHf/Xzb0QuSs8It1RH1dOta1AIgEEZkC+T5gYOwN2hNkLnzYFBZtwhM6Mo+sZSYlOpjCnw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.3.0": {"name": "body-parser", "version": "1.3.0", "dependencies": {"qs": "0.6.6", "bytes": "1.0.0", "type-is": "1.2.0", "raw-body": "1.1.6"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.1", "istanbul": "0.2.10", "supertest": "~0.12.1"}, "dist": {"shasum": "1a651cb9993a01a65531ae38395ceb0199dd7e3c", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.3.0.tgz", "integrity": "sha512-is7gFt5CcTO6f1z1tAYztWNtqBLCBIqYUVjEcwRdOIi7LipX65mAglF4ChMAYBUjrF06v39d9PnPGnwjv3Yg8w==", "signatures": [{"sig": "MEYCIQDkUcraw4M+R9ncRwo7+FRz81Lapl2nFDd/k/TacPIToAIhANfbm1FMjMFdHOn7exA35wotmTMyYSn9l/av4tD0kJpy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.3.1": {"name": "body-parser", "version": "1.3.1", "dependencies": {"qs": "0.6.6", "bytes": "1.0.0", "type-is": "1.2.1", "raw-body": "1.1.6"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.4", "istanbul": "0.2.10", "supertest": "~0.13.0"}, "dist": {"shasum": "1a74513fc7897d70db56589e0d03f0a13f1bfa94", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.3.1.tgz", "integrity": "sha512-89/x3K5hk+ZTWDeuT2K0omjSaA8uzT15raeGH65ZktRs+odBKtyjkB2Gw+eIYSqOW9YFDk8wA47y78kVJ/Prlg==", "signatures": [{"sig": "MEUCIQDuDqUPThFhmTZrNAIC9bO+Y6/5beiE0IER39v5jxXw9gIgLOnnkSZLQVvFKM52KuM3sjQsU6z27+Ht6hhGcFxqz78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.4.0": {"name": "body-parser", "version": "1.4.0", "dependencies": {"qs": "0.6.6", "depd": "0.3.0", "bytes": "1.0.0", "type-is": "1.2.1", "raw-body": "1.2.2", "iconv-lite": "0.4.3", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.4", "istanbul": "0.2.10", "supertest": "~0.13.0"}, "dist": {"shasum": "31274668441c2b00bab6ca50a173442d8bac1382", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.4.0.tgz", "integrity": "sha512-K6o4TywK+OOelRFOdTc98PFMj8Y5L0iMQEaoQZpk7GMnbf9yffraTO99efrCJ8FMJO1Y7XDfqgZFWIsCodYDKA==", "signatures": [{"sig": "MEQCIGx4EjxbpVPx7OBYynUOlJu9x45OMY+6YCwE8579rGH0AiBf0S5jDDel3B8qCpNbyHAOUnR+HRNtrEGTEKoRr73dUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.4.1": {"name": "body-parser", "version": "1.4.1", "dependencies": {"qs": "0.6.6", "depd": "0.3.0", "bytes": "1.0.0", "type-is": "1.2.1", "raw-body": "1.2.2", "iconv-lite": "0.4.3", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.4", "istanbul": "0.2.10", "supertest": "~0.13.0"}, "dist": {"shasum": "29146acc104a353e8cb07b7b3666d2d829bed6b0", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.4.1.tgz", "integrity": "sha512-mOVgsLYLS37pa2Ya6XKjr/i8An2kbxG9SOT3yCZCQJv5P9FAwrdQkyRRlYGwAqPawgwuifSNAo/71NqrEBADzw==", "signatures": [{"sig": "MEUCIQCT4VnNFrLXu3ajXYrsukTiBmOlgOl8/8FPv/2waDyeWQIgWnZ7hXACGqUp5uxDWiIn73dyJQwX3ejoGn0a5hYQDw0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.4.2": {"name": "body-parser", "version": "1.4.2", "dependencies": {"qs": "0.6.6", "depd": "0.3.0", "bytes": "1.0.0", "type-is": "1.3.0", "raw-body": "1.2.2", "iconv-lite": "0.4.3", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.4", "istanbul": "0.2.10", "supertest": "~0.13.0"}, "dist": {"shasum": "e748603c5f79eb06bd75434e219258986328aae7", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.4.2.tgz", "integrity": "sha512-nBUUDXD1nofz/J4ZBdY/QklJqPE/ooXJZqShnCqf/lsgz6DuRFjXGuWCT+z9pr3RsvDHUypIyL4TMsSIfn0cNg==", "signatures": [{"sig": "MEUCIA+novnODgOry3Ah09z1zI36ExYmIpIodAM8nbBBP4mGAiEAoEVhS3V5zq/mn5gKxep80erdM2F4//q+db0en2jUVlQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.4.3": {"name": "body-parser", "version": "1.4.3", "dependencies": {"qs": "0.6.6", "depd": "0.3.0", "bytes": "1.0.0", "type-is": "1.3.1", "raw-body": "1.2.2", "iconv-lite": "0.4.3", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.4", "istanbul": "0.2.10", "supertest": "~0.13.0"}, "dist": {"shasum": "4727952cff4af0773eefa4b226c2f4122f5e234d", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.4.3.tgz", "integrity": "sha512-+/wGpsrfMR0d7nPNnmpKAPQVXg37cU3YVvR/hThORfbiJYvzmGHf+A/x0QWtE/s2XMdj2/UTQUweVqNPlkZlEw==", "signatures": [{"sig": "MEUCIQC8D9QpR0WxXM/CrwhMHU5QiuQF7Thi11K9OzvoBqTlvwIgLjZh52OMLNxb4YLy6u163ZjkJT2BvJfYcu/F9UMKirQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.5.0": {"name": "body-parser", "version": "1.5.0", "dependencies": {"qs": "0.6.6", "depd": "0.4.2", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.20.1", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "c6fce2483c9eeb49ab349ff25a92d336d91055b9", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.5.0.tgz", "integrity": "sha512-UJfZike68QN1mdo0mA+Z0y+0qi10oxOrCPw2CZpP73O/LIfEWHDy9SHhwsME1mdk1WmnltBLddUkfBpuKPH4Ng==", "signatures": [{"sig": "MEUCIQCgrwSUCfpV8Vnagxv19C2hFslppJQiEhuDryHeLpfeTAIgCmwN8JjbFzjK3KOpVG3sHsNz+TaI9G3EJ1WZUWXoc5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.5.1": {"name": "body-parser", "version": "1.5.1", "dependencies": {"qs": "0.6.6", "depd": "0.4.3", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "8d2eb95e987d274ef02fcf56567b3f3a31749c51", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.5.1.tgz", "integrity": "sha512-d2wC2Wt8e4l+K9DRDEgDExujLogpcZHh5tzt3UTl5+4bLQs8na+IZ1ECsQJVcWPDIkyZMWm2YjJDQFGzDDGoeA==", "signatures": [{"sig": "MEUCIQC54SsvTrmOYIVrg90WiN7/a0iUYI5uAzpgBxfslJmm6gIgCtVHhsV0XZIi/Q57hKycMJALpCFKubEluUdaUEwKHhA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.5.2": {"name": "body-parser", "version": "1.5.2", "dependencies": {"qs": "0.6.6", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "beebacac741b83f62c9137d5685196e1a44304ab", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.5.2.tgz", "integrity": "sha512-e4V5PN/KJTx381nRvKBSFaoRxS/6IImUMy6kouZPHPKan8RZoDPFE+GeT8RdtbUFNIyhNUW2C4vj4lLbVZiodA==", "signatures": [{"sig": "MEQCIAsKHXrsGEMe6WwYbAPH7xA4E24Oa5IhgV5rRrB1ZK6/AiAElVYt0Z+KRji3P5R/BCyhVN8pdO2m3Rnhv7EnNRPB/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.6.0": {"name": "body-parser", "version": "1.6.0", "dependencies": {"qs": "1.0.2", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "d02a9d373c7349c281a8b76b41d6bbf60ef2d3f6", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.6.0.tgz", "integrity": "sha512-2IP/Pq5mKUFyMEEB40i4LOTrqH+CPwUEUC4uacrhYQ2PeoNk+KEuKhyv+eGJeCn9XL5J6MplG0TWfviYsAiBbA==", "signatures": [{"sig": "MEQCIDe3l/mH6Z7LtHCDiX1YKtcTpzZ4CmRGqjCLwKtuGOtZAiAlEQyDKG0NpSivAcxCAX45sLm1ZyH98B1Fav5yivjK/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.6.1": {"name": "body-parser", "version": "1.6.1", "dependencies": {"qs": "1.1.0", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "3894580ab743e2c2611fec695bae60a883ea6f3b", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.6.1.tgz", "integrity": "sha512-r9w2z83VWMDUP0Frhyfc53lfUJOyXW1SF3SANuW3OZV5g/D/NGVBsLHO0bkeLtxhaD6Ayud8VhkaKbiHME4uSQ==", "signatures": [{"sig": "MEQCIB4jQ3ssMUBXwO1Icr41HjTCen11SVUf9VDUQnerB5j4AiBdDPjnUnj3WcE+2gOufZDalfgfv6Ee6l1YpKv1kYVbXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.6.2": {"name": "body-parser", "version": "1.6.2", "dependencies": {"qs": "1.2.0", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "38952b4fd534395ab3034e9bb40bbdf3dd99c4ce", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.6.2.tgz", "integrity": "sha512-Qg/WrLDE5NBX95rzpnGjTwy/CYBTDHG+nuuFEIoQ/ZWIpDxfG1XDB0gER6xQVC8jowbyas9wzbPwSZrEFLfS0w==", "signatures": [{"sig": "MEUCIGbUqpMwd/8KD6/oKZSDUIP5h2+3t4RR8zpPXW7Tra+tAiEAwX8+Y8u8u/DpGBZbVE9qBaOLfTqBvwzGKr0FviSUGIk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.6.3": {"name": "body-parser", "version": "1.6.3", "dependencies": {"qs": "1.2.1", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "db3b270bd3ebce5da4d2d2021653454b24861a79", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.6.3.tgz", "integrity": "sha512-4JfbwJryDlXYLwwBbrFWy/gMbxagpjoB8+EbfJ6bKXnohtLVilFkA/sv/2aRUbGid/n704Vp9+wrwq/SKBie5A==", "signatures": [{"sig": "MEUCIBJj1E0BuCgPgBR7mhnqhCHDxrqmvVYA6YqF9iTKGYowAiEAwgW6gcz9CIMSSfWVUv1/OC+PpaYz3aQbFXMjswn78PE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.6.4": {"name": "body-parser", "version": "1.6.4", "dependencies": {"qs": "1.2.2", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "befd799cc361a46d34e181f5f881f421a1f3b4c1", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.6.4.tgz", "integrity": "sha512-RluKYJFDExCfZT4MeW9vtKUiCtWgmUYbb7jeKcTUn/9DrsosDNylt8WdQFQgerxBXv9eAz3XDzV/pls2+6DcxQ==", "signatures": [{"sig": "MEQCIHk8VwFHuqPF3xe1SDYzHXs9AFEj1D5wrlkWlKQdFrzwAiBP5GUUv0cqngwMMI5Hsl3xC7GyHdqTz+SqRfo6ZhrQxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.6.5": {"name": "body-parser", "version": "1.6.5", "dependencies": {"qs": "1.2.2", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "536f01e08ee2b6df6a941d6c8c9647ee99ee4de7", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.6.5.tgz", "integrity": "sha512-hNuwcxA9J9d5Go6QEg8Go8eiJd3r6SeN5+TogxvmI5xWg5GrSWmLUGWq41WhPYNeF7HzzVd+C9IL29wpRXSrRg==", "signatures": [{"sig": "MEUCIEtJNwQGDEL2FbPWEg1HcsgOYYM6Bn7ohfL9QE1kWLQYAiEAhjNDrs/BlEV7AQcXRNvnsyR5bKy4V+LDdBOu+QgZi8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.6.6": {"name": "body-parser", "version": "1.6.6", "dependencies": {"qs": "2.2.0", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "abfead725f1983631ce94b8e3e9a297d1ab703fb", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.6.6.tgz", "integrity": "sha512-jeON7/5++VNhI6GMPOtEuJwlXoP5aLS8XNRj3YURQqDXRCq2Pq7p8hUyFFR3sWq2AhpELOKaB042XRxXgAYglw==", "signatures": [{"sig": "MEYCIQCsyKQ9GtBVK1RVr6R5oSd6IY7GtRk3GlwGnskBKBR4zAIhAPYXc9esZsokuGsn/5HwoeuSSWMrHA7qj4YC9/oVmU3u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.6.7": {"name": "body-parser", "version": "1.6.7", "dependencies": {"qs": "2.2.2", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "82306becadf44543e826b3907eae93f0237c4e5c", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.6.7.tgz", "integrity": "sha512-J6HSnbgUPZMhDuqsTyGdpEy6PavO/05c7bKpqXVbYnLUBdH9oM1DvMTC27I3x9F0/tjfvbpuyedP/uUinj+Veg==", "signatures": [{"sig": "MEUCIBfi/eylNrltKrRb6oT7ODRIMMuWse1NGo2s14Fs6hJ1AiEA8XgGfwcg4qBcsBWgKvLRQbkIrZNqY3FKWx0mU6BKZvs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.7.0": {"name": "body-parser", "version": "1.7.0", "dependencies": {"qs": "2.2.2", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.3.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "6a245ea5b32d8e1e0d43bec8344b264ba4b36541", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.7.0.tgz", "integrity": "sha512-SLLySe3OQZk4BVED3cPCTrr9n1xlCpOo6u79RumwySzprOtQ1Y1V6tAoBg/PJOsUYKedRPK8gdAAXq1hsctqMA==", "signatures": [{"sig": "MEUCIQCulx9tNEbmjk2StW0OUjliFPF7LydMs8VaBjxaXmWAGwIgC6J4hEwt/14286PLNfPbIdsJw3PeK0XJ1/3Y3PYM4Xg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.8.0": {"name": "body-parser", "version": "1.8.0", "dependencies": {"qs": "2.2.3", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.5.0", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.2.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0"}, "dist": {"shasum": "20b3a3d3553a6835d7373456dd9da8720759b306", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.8.0.tgz", "integrity": "sha512-h2luv+SU8Xm34vKqxoJ7QEDaZStDrzQ7eizLOJbc91IF2SRcOAhPB63FRLbF9J06H7lzPQIZ+cqBOpA81Rn4uA==", "signatures": [{"sig": "MEUCIQCbOmOrj2WAyMQuzERn4OMrak9jwI9DJZcAi/xRC+XJFAIgY/0OAlipqoLBsAjZ82iPTzlK4Z6GtTw6oGeOYeDYOH4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.8.1": {"name": "body-parser", "version": "1.8.1", "dependencies": {"qs": "2.2.3", "depd": "0.4.4", "bytes": "1.0.0", "type-is": "~1.5.1", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.3.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0"}, "dist": {"shasum": "f9f96d221c435c95d18aeaad2bcdea1371902aad", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.8.1.tgz", "integrity": "sha512-A6jnnZafw0xLopdb8B9VXEoOZsD9hTf5TPHmE5HLWQs5HKPPHLu8aLsVN0KovaO7jbCAlC3D5+5HWVDp2rwLvQ==", "signatures": [{"sig": "MEUCIQDb3H3HW1pwLc8mGmti1o4YITYVJuk08GYwfrIotiK71gIgFOKLen337ATO2lwst3yjsR+ewLdfbM5cceosI7MFezY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.8.2": {"name": "body-parser", "version": "1.8.2", "dependencies": {"qs": "2.2.3", "depd": "0.4.5", "bytes": "1.0.0", "type-is": "~1.5.1", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.3.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0"}, "dist": {"shasum": "cb55519e748f2ac89bd3c8e34cb759d391c4d67d", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.8.2.tgz", "integrity": "sha512-AcQvhb15p8TtusgucXzASXBAdWfB/eqZxZm5aytKhu8C93xHBfH4J/2nQNEElPnMFu8+lxzOIaxCYI+beFCfZw==", "signatures": [{"sig": "MEQCIHboNc/kehfCD6nGXzuy/+0Kt6EHik/ER+n6H7qRpqRFAiBzejwiZfITYIcWxiyDE+uJ8UYDfdANFOdI8d452Tm+Hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.8.3": {"name": "body-parser", "version": "1.8.3", "dependencies": {"qs": "2.2.4", "depd": "0.4.5", "bytes": "1.0.0", "type-is": "~1.5.1", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.3.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0"}, "dist": {"shasum": "922b82e6448d654f2f5197574ceacefc04a6a8af", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.8.3.tgz", "integrity": "sha512-uYU8Yg23GXkwrZ8im2ZmnwooBtqm1iyO8H0eRW/0Mp+2GyG7PJFIdlhFrxaNPfbMQEKwaQPIz53HIU4iCnmGeg==", "signatures": [{"sig": "MEQCIGkBf1AJIAj2aTy6ijiGN/5iOmCagRdAr+YvRFQfBoONAiATJt+8rW/4Q6nO2bPw+n18d1PdB+y0zC/n3ZEKosxuNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.8.4": {"name": "body-parser", "version": "1.8.4", "dependencies": {"qs": "2.2.4", "depd": "0.4.5", "bytes": "1.0.0", "type-is": "~1.5.1", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.3.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0"}, "dist": {"shasum": "d497e04bc13b3f9a8bd8c70bb0cdc16f2e028898", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.8.4.tgz", "integrity": "sha512-jTeWaZdC6r5o7FUSWNTPtxeudzg3cybUEgT56clWiW3FOpZ0fbQAUoD2k/BqmQlyEI2sK3TBqs9Zp6p6Fsv/sQ==", "signatures": [{"sig": "MEYCIQCvsA/vsScaVMHX/dCvGehP2ZRmmgTsBSbcWiJVQnrTMAIhAIXPTihs+6IsOkzxJzhpgetDRcvlYlwGmdMfiw1gQDfu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.9.0": {"name": "body-parser", "version": "1.9.0", "dependencies": {"qs": "2.2.4", "depd": "~1.0.0", "bytes": "1.0.0", "type-is": "~1.5.1", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.3.0", "on-finished": "2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "should": "~4.0.4", "istanbul": "0.3.2", "supertest": "~0.13.0"}, "dist": {"shasum": "95d72943b1a4f67f56bbac9e0dcc837b68703605", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.9.0.tgz", "integrity": "sha512-fCQmijfF8stcsUxUU0r8mDo6yXPggOBDFVR0WAF85DxFwpdmtFA36oEqz6XOHKhfUmf4dIvfHXkyR7azyN/fVA==", "signatures": [{"sig": "MEUCIApjEYnJOkEyJMZQpA4sHzGknSeCo6vXOsEjnJj3/4ZUAiEA4NH16wNr+uhu9CDacOkFfAhg6ymPxgLKDqu2Tzj5Xs8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.9.1": {"name": "body-parser", "version": "1.9.1", "dependencies": {"qs": "2.3.0", "depd": "~1.0.0", "bytes": "1.0.0", "type-is": "~1.5.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.3.0", "on-finished": "~2.1.1"}, "devDependencies": {"mocha": "~2.0.0", "should": "~4.1.0", "istanbul": "0.3.2", "supertest": "~0.14.0"}, "dist": {"shasum": "650a3047591fa9bb3cec191cb53933a468aa57aa", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.9.1.tgz", "integrity": "sha512-0gKh9KCeBRvtDczZnlQmivCO4+xh0Ji+jVN26/aODd5nMI3OZjpdbwK+LNWh9fS66RrQAkTrky+SB6vLRM3caw==", "signatures": [{"sig": "MEUCIQCocis9QTiBuFwWnTncmWpNdfRI97I4QDrDp36fBL38OwIgTmjXOayt5q2ftRAijb27XTqK0SB3O5aoL4hPuDRPNFY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.9.2": {"name": "body-parser", "version": "1.9.2", "dependencies": {"qs": "2.3.2", "depd": "~1.0.0", "bytes": "1.0.0", "type-is": "~1.5.2", "raw-body": "1.3.0", "iconv-lite": "0.4.4", "media-typer": "0.3.0", "on-finished": "~2.1.1"}, "devDependencies": {"mocha": "~2.0.0", "should": "~4.1.0", "istanbul": "0.3.2", "supertest": "~0.14.0"}, "dist": {"shasum": "07f52cf104939118bedcba689002017271ef3c0e", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.9.2.tgz", "integrity": "sha512-TuLvgBQBqGryJoo7Wuun5Oqg0ytLn+IcgXJOMiLvTNPsCaSzTDgYIB6oVxYXV6Qq3JweltuIh3C1WAW2uxgBiA==", "signatures": [{"sig": "MEQCICrnYF6ay9/O9DSbPlMZeAODQfKiJ4b/LS8ePMsH3SyNAiB/7ErZJR6HOTZqzkq3Qn+XGQDo9PBJ5bZY8yUiS8Fqzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.9.3": {"name": "body-parser", "version": "1.9.3", "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "bytes": "1.0.0", "type-is": "~1.5.3", "raw-body": "1.3.1", "iconv-lite": "0.4.5", "media-typer": "0.3.0", "on-finished": "~2.1.1"}, "devDependencies": {"mocha": "~2.0.0", "istanbul": "0.3.2", "supertest": "~0.15.0"}, "dist": {"shasum": "edfacd4fcfad87dfe74f861a5cc712900aef2623", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.9.3.tgz", "integrity": "sha512-nVSZlzCeMgePYRfXhLbmkzP9NDTbLwNnMtSD82hx97swlLWZeD7Aw30ffhyET2sEGn6+mn0uWcUHiBOcVF1VOQ==", "signatures": [{"sig": "MEYCIQCw1rl9KBpd7BT0aw9Jxk/+0LdMXYC9Ve1QfdIUWVmJAwIhAOeSbQbk7Zb9w7IrH9Lhc4dh5p02P7XVsKufOBEGQJAE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.10.0": {"name": "body-parser", "version": "1.10.0", "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "bytes": "1.0.0", "type-is": "~1.5.3", "raw-body": "1.3.1", "iconv-lite": "0.4.5", "media-typer": "0.3.0", "on-finished": "~2.1.1"}, "devDependencies": {"mocha": "~2.0.0", "methods": "~1.1.0", "istanbul": "0.3.2", "supertest": "~0.15.0"}, "dist": {"shasum": "f884d11839af09e3c61e5011059e29cbfe452085", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.10.0.tgz", "integrity": "sha512-VEoXZduApGvhp4tSaYbLTb8BpFM/uhAS2aTclS5U8O2Usu0LiCMwq7z1axhc47KsvCcWGxijAfbKfAXjJZRxPw==", "signatures": [{"sig": "MEUCIFWDpgsVr2kL/gkprOkny+mLIwAcpcZVI3Y9Fl6Dz5NFAiEAr1H7MT5FH5/MwBH6bBAzLq2Moii7PknCErcPPyApBz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.10.1": {"name": "body-parser", "version": "1.10.1", "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "bytes": "1.0.0", "type-is": "~1.5.5", "raw-body": "1.3.1", "iconv-lite": "0.4.5", "media-typer": "0.3.0", "on-finished": "~2.2.0"}, "devDependencies": {"mocha": "~2.1.0", "methods": "~1.1.1", "istanbul": "0.3.5", "supertest": "~0.15.0"}, "dist": {"shasum": "af0c7156b128d946f3c43f5fe0364da00cfa7391", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.10.1.tgz", "integrity": "sha512-0HS6HIsf/8hYcKGj0WWrDs8lmZqRKpACercXflJ6iRHiwLB0S/i3DfqXCnqTeFJlBfRUOkq5CN9/+EnGuuHZjQ==", "signatures": [{"sig": "MEUCIEsBQOe/Bl/rgdH3IRw6vh/u5TtPDt6GCDquS8E8GGbRAiEA92/nWVgqG7rYZpDhDgXdpSDYLN/SNqHYeFAAWLibWpc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.10.2": {"name": "body-parser", "version": "1.10.2", "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "bytes": "1.0.0", "type-is": "~1.5.5", "raw-body": "1.3.2", "iconv-lite": "0.4.6", "media-typer": "0.3.0", "on-finished": "~2.2.0"}, "devDependencies": {"mocha": "~2.1.0", "methods": "~1.1.1", "istanbul": "0.3.5", "supertest": "~0.15.0"}, "dist": {"shasum": "405d465fcd3ccf0ea8a35adbf1055f6e98316bd1", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.10.2.tgz", "integrity": "sha512-jvUDKORaxvvm3A4QlCxnVVACN/xamCrif3T6Bk6f+41sWmYokg003LMCLJXgeDH63sl0WjHAfGeTEOBbzy4vqA==", "signatures": [{"sig": "MEUCIQC9oJ8m3CNqwVrstr6z8CHVwCtRJN4JRwqWvBdUoZQPHAIgVhhpkZnh3YOvXMSTioJTbymw4toqDebtkG3j7VHemac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.11.0": {"name": "body-parser", "version": "1.11.0", "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "bytes": "1.0.0", "type-is": "~1.5.6", "raw-body": "1.3.2", "iconv-lite": "0.4.6", "media-typer": "0.3.0", "on-finished": "~2.2.0"}, "devDependencies": {"mocha": "~2.1.0", "methods": "~1.1.1", "istanbul": "0.3.5", "supertest": "~0.15.0"}, "dist": {"shasum": "29f876cb608efa54e9b2185fe8105efc9219a7f3", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.11.0.tgz", "integrity": "sha512-TD5oybtZnb48JSGTSiBRc3shYVdUJ6hsALEDLp37UMJEXOpJwOhPJ2zgkcti2fPqyzmj3UDZN7TMrQb3wJe59A==", "signatures": [{"sig": "MEYCIQCR/oOQUPGv9sb0S7PE/x1V//wZklEuVRmZK1wL3CCmkAIhAKMGPHkDEqr7pThb/yLo5KC2m2TCwT84YGm1LqWZN2WH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.12.0": {"name": "body-parser", "version": "1.12.0", "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "bytes": "1.0.0", "debug": "~2.1.1", "type-is": "~1.6.0", "raw-body": "1.3.3", "iconv-lite": "0.4.7", "on-finished": "~2.2.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "~2.1.0", "methods": "~1.1.1", "istanbul": "0.3.5", "supertest": "~0.15.0"}, "dist": {"shasum": "9750fc3cc1080b34a13d18c79840cd559979fce5", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.12.0.tgz", "integrity": "sha512-tbWYo1REQtmLKUxXm19eia6YxNW3paXWUzV8dAjfEnOiQjCD3Tnkpm1g7yFk/WFioHNCYEp+BWPAeP1iUFC4EQ==", "signatures": [{"sig": "MEQCIFRzjTO/MueIFs0s6hZs8h5o0aI/1IFvv7/kECK4NeP3AiBCfhDyKyZ5QzgJkbSTdbceEcX/kcp9lI6QZZf/9ChDfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.12.1": {"name": "body-parser", "version": "1.12.1", "dependencies": {"qs": "2.3.3", "depd": "~1.0.0", "bytes": "1.0.0", "debug": "~2.1.3", "type-is": "~1.6.1", "raw-body": "1.3.3", "iconv-lite": "0.4.7", "on-finished": "~2.2.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "~2.2.1", "methods": "~1.1.1", "istanbul": "0.3.8", "supertest": "~0.15.0"}, "dist": {"shasum": "4b9b4c67e8eb5ccac7c9eef3fbd6694e721ae002", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.12.1.tgz", "integrity": "sha512-T7YcXR7Dm3JkVl+/vQpZK3c/cMew02KIo0dhW7j8LH+E7hE5VQoNfb5aby4KhpOhKGsso90oCHMJ7Vl0vmQWuA==", "signatures": [{"sig": "MEQCIAfRHoug1VwrasHtKiAW6CD/o6vs4DYPB0dVz5QiNEY0AiAxqM1QuxSsVo2azzv6uH0bNG5RDaJAVH4xnSCXdAJFZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.12.2": {"name": "body-parser", "version": "1.12.2", "dependencies": {"qs": "2.4.1", "depd": "~1.0.0", "bytes": "1.0.0", "debug": "~2.1.3", "type-is": "~1.6.1", "raw-body": "1.3.3", "iconv-lite": "0.4.7", "on-finished": "~2.2.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "~2.2.1", "methods": "~1.1.1", "istanbul": "0.3.8", "supertest": "~0.15.0"}, "dist": {"shasum": "698368fb4dfc57a05bff1ddb1bebeba3bd2c0e87", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.12.2.tgz", "integrity": "sha512-uLyYuZM0WqAk43P09+i8Zh5IUB8PCUBlEWMMXekWMXUJs6TYuRDy5/mA95L/AAt3/6jRejU5mOTRnlOT/n9k6g==", "signatures": [{"sig": "MEQCIEUOKKcBt7BHvT/zWVm2JVCDe4/kE/zB6FgTnj3AaRitAiAiF+Dv9uJef9mkqh+nc8mxQdrXOEkVp5qShoVQaVF30g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.12.3": {"name": "body-parser", "version": "1.12.3", "dependencies": {"qs": "2.4.1", "depd": "~1.0.1", "bytes": "1.0.0", "debug": "~2.1.3", "type-is": "~1.6.1", "raw-body": "1.3.4", "iconv-lite": "0.4.8", "on-finished": "~2.2.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "~2.2.4", "methods": "~1.1.1", "istanbul": "0.3.9", "supertest": "~0.15.0"}, "dist": {"shasum": "5f40bf17e7823be6895d4d35582752e36cf97f71", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.12.3.tgz", "integrity": "sha512-OsEhm5Qi9bLox2dRfYiKZsqwOTzVQYxFlkA2f139F670i6Nv23DV3yHiG+8K+RRqQqDuJfPHv2yH12Lv2Yh7EA==", "signatures": [{"sig": "MEUCIQDEmaGfsk3DiM35rM/GcD2cjprHi/g+OebeUC0MnX3w7QIgIW+oKmKGxjSPMK65CEWgxnx+N/zqbVn389FkqOj1xc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.12.4": {"name": "body-parser", "version": "1.12.4", "dependencies": {"qs": "2.4.2", "depd": "~1.0.1", "bytes": "1.0.0", "debug": "~2.2.0", "type-is": "~1.6.2", "raw-body": "~2.0.1", "iconv-lite": "0.4.8", "on-finished": "~2.2.1", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "~2.2.4", "methods": "~1.1.1", "istanbul": "0.3.9", "supertest": "~0.15.0"}, "dist": {"shasum": "090700c4ba28862a8520ef378395fdee5f61c229", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.12.4.tgz", "integrity": "sha512-fueabp0EDZKvebbSI94mGzVlJr3vViXA7q+W+52MFZCrcJjRlnTkPQjpua8+6M6WOh1swnw+DJiUrETWRIQn9g==", "signatures": [{"sig": "MEYCIQCB0oh/G4thwZ/0DISn0SKNLO/EGCre+lx6GdtHFkXo3gIhAP3hX1/s2dBMdtmjLiqzb49DltBthAIzIBste1PgA9St", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.13.0": {"name": "body-parser", "version": "1.13.0", "dependencies": {"qs": "3.1.0", "depd": "~1.0.1", "bytes": "2.1.0", "debug": "~2.2.0", "type-is": "~1.6.3", "raw-body": "~2.1.1", "iconv-lite": "0.4.10", "http-errors": "~1.3.1", "on-finished": "~2.3.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "2.2.5", "methods": "~1.1.1", "istanbul": "0.3.9", "supertest": "1.0.1"}, "dist": {"shasum": "b6dca73da8c4a9f68b0e64d29acac39dd3ad9a9e", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.13.0.tgz", "integrity": "sha512-siOxX30eNn7A1g85gsWNj08LAwrElDxlVtMOidHqFi+JCgjrKCJC+SJsLd8n/vDWWg6DUve0eF454gBHs/1p7w==", "signatures": [{"sig": "MEUCIQCwU9EelJvRtTKl2jqZwNBD7/9d7xXx6+MSzpTivknxAgIgVya9+Og0hW3sBLIGnZcEodC+RQRF6ubrogtsFwk7eY4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.13.1": {"name": "body-parser", "version": "1.13.1", "dependencies": {"qs": "2.4.2", "depd": "~1.0.1", "bytes": "2.1.0", "debug": "~2.2.0", "type-is": "~1.6.3", "raw-body": "~2.1.1", "iconv-lite": "0.4.10", "http-errors": "~1.3.1", "on-finished": "~2.3.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "2.2.5", "methods": "~1.1.1", "istanbul": "0.3.9", "supertest": "1.0.1"}, "dist": {"shasum": "f07218bc2c4b5e36ca261557c9465481b29ecdcd", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.13.1.tgz", "integrity": "sha512-Fi98hAzHXZuEzK3zI3dJ87Huk1QCA6ynPFSAzEQ1YYK2N1mEtpOEW+PITWd+gOz5ycLgyBVYSMEOOrZy2YFpaQ==", "signatures": [{"sig": "MEUCIQCHe4SqtMb2ZB5TYB9oiEBOSIEc8m+iqwuzCWkjbsZrdQIgZKAtvGASisykorBQWIncwivP/sGUd4uXKqKuuOJdZZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.13.2": {"name": "body-parser", "version": "1.13.2", "dependencies": {"qs": "4.0.0", "depd": "~1.0.1", "bytes": "2.1.0", "debug": "~2.2.0", "type-is": "~1.6.4", "raw-body": "~2.1.2", "iconv-lite": "0.4.11", "http-errors": "~1.3.1", "on-finished": "~2.3.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "2.2.5", "methods": "~1.1.1", "istanbul": "0.3.17", "supertest": "1.0.1"}, "dist": {"shasum": "229262a4fd2e402dfb88d99bc27d8be31307e7e9", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.13.2.tgz", "integrity": "sha512-Jt0zENT15Ba22HOvi8oxBV5eAXgit7pQQxy133Bz+RlWy9ZeKXrY+HOpdHebP1B3aOgxRd4W4LklrihWdgnLig==", "signatures": [{"sig": "MEUCIHygN7KjivEaOMts2XmRmbFC/fAEMkEHJQLbj3wvS2EnAiEAw1EDQ/do8V34cWG01ZJ6PQcRlOQ5Be5t6YaJbWJPfdo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.13.3": {"name": "body-parser", "version": "1.13.3", "dependencies": {"qs": "4.0.0", "depd": "~1.0.1", "bytes": "2.1.0", "debug": "~2.2.0", "type-is": "~1.6.6", "raw-body": "~2.1.2", "iconv-lite": "0.4.11", "http-errors": "~1.3.1", "on-finished": "~2.3.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "2.2.5", "methods": "~1.1.1", "istanbul": "0.3.17", "supertest": "1.0.1"}, "dist": {"shasum": "c08cf330c3358e151016a05746f13f029c97fa97", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.13.3.tgz", "integrity": "sha512-ypX8/9uws2W+CjPp3QMmz1qklzlhRBknQve22Y+WFecHql+qDFfG+VVNX7sooA4Q3+2fdq4ZZj6Xr07gA90RZg==", "signatures": [{"sig": "MEUCIGbUUwkeTZufW1aAocpCofR5g/pEOiocyPiYjuO18MODAiEAuzcphsP8GLSxz2KLORYQDEOc15CVheqr1y3dvQPjAFs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.14.0": {"name": "body-parser", "version": "1.14.0", "dependencies": {"qs": "5.1.0", "depd": "~1.1.0", "bytes": "2.1.0", "debug": "~2.2.0", "type-is": "~1.6.8", "raw-body": "~2.1.3", "iconv-lite": "0.4.11", "http-errors": "~1.3.1", "on-finished": "~2.3.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "2.2.5", "methods": "~1.1.1", "istanbul": "0.3.20", "supertest": "1.1.0"}, "dist": {"shasum": "a7a10138547a75bfcacc20472404630c2fa6b0ff", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.14.0.tgz", "integrity": "sha512-gduSv8yKw/TWfNE0npoXyFU0J0Lk8oYQAEW9gDI8z6AfbrWsu8kIvvZJz/uwPhsXggiwrggjqObbKSIhmfXc3g==", "signatures": [{"sig": "MEUCIQCNlgMs588ZNajgORubxb12OYwAjj6D/znQIfzmA8ay9AIgSbmUqoHa6zfdfQoKS8YPJlFNE7PD9d8QKiM3dAwbcKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.14.1": {"name": "body-parser", "version": "1.14.1", "dependencies": {"qs": "5.1.0", "depd": "~1.1.0", "bytes": "2.1.0", "debug": "~2.2.0", "type-is": "~1.6.9", "raw-body": "~2.1.4", "iconv-lite": "0.4.12", "http-errors": "~1.3.1", "on-finished": "~2.3.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "2.2.5", "methods": "~1.1.1", "istanbul": "0.3.21", "supertest": "1.1.0"}, "dist": {"shasum": "ffe921eba3ce8f191e2a8a8803844bd025f3c6dc", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.14.1.tgz", "integrity": "sha512-5zDL2GGbhBN1zyJZVMp/tysRNk+tT2xnsKJMhzHWwn3EWA8ASdG5+eNR7nJw3aicgD4rdY3Q9er0j9c7is8Z+w==", "signatures": [{"sig": "MEYCIQCnZiU/woqZsgsW5AFJWXA3bT98xf+OxqT4LrjL2rBHVwIhANEMNN8OXs4sA+fvVTr4qCH21Lyj5tG7op3d3IHs9ez9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.14.2": {"name": "body-parser", "version": "1.14.2", "dependencies": {"qs": "5.2.0", "depd": "~1.1.0", "bytes": "2.2.0", "debug": "~2.2.0", "type-is": "~1.6.10", "raw-body": "~2.1.5", "iconv-lite": "0.4.13", "http-errors": "~1.3.1", "on-finished": "~2.3.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "2.3.4", "methods": "~1.1.1", "istanbul": "0.4.1", "supertest": "1.1.0"}, "dist": {"shasum": "1015cb1fe2c443858259581db53332f8d0cf50f9", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.14.2.tgz", "integrity": "sha512-6D9uiWn7dbnDAhlDikccybuqKCmsoest0es3VSQO8Doz/fzx6Ls7kJNxKBYTjbzu4/RzNsf9zuACnS3UYjVH8Q==", "signatures": [{"sig": "MEUCIEZkm3WRXTuhH3xWw/j4d3jzBraiwI7Uf6QFihW0HMwNAiEAkTSgaSaqTO6yG2404Lda6JBQSX4cNe/Ycc+dZMndwpE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.15.0": {"name": "body-parser", "version": "1.15.0", "dependencies": {"qs": "6.1.0", "depd": "~1.1.0", "bytes": "2.2.0", "debug": "~2.2.0", "type-is": "~1.6.11", "raw-body": "~2.1.5", "iconv-lite": "0.4.13", "http-errors": "~1.4.0", "on-finished": "~2.3.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "2.4.5", "methods": "1.1.2", "istanbul": "0.4.2", "supertest": "1.1.0"}, "dist": {"shasum": "8168abaeaf9e77e300f7b3aef4df4b46e9b21b35", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.15.0.tgz", "integrity": "sha512-kUw7+wBWR57kY1buA9lHZFKSjGX+6FQ2kZm/jl3vSMBsUUlBewvexq9nSu5sVb+QOM19bVPW10Lt0mfR1GgstQ==", "signatures": [{"sig": "MEUCIBqmCnmH2y8t6FG4pqlmorRiNbAhBxhZkuVS3BlT7kbEAiEA1/LDjyv6zWjoRhfL4MRmijLMia7n/4VTebIGdCCEAeg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.15.1": {"name": "body-parser", "version": "1.15.1", "dependencies": {"qs": "6.1.0", "depd": "~1.1.0", "bytes": "2.3.0", "debug": "~2.2.0", "type-is": "~1.6.12", "raw-body": "~2.1.6", "iconv-lite": "0.4.13", "http-errors": "~1.4.0", "on-finished": "~2.3.0", "content-type": "~1.0.1"}, "devDependencies": {"mocha": "2.4.5", "methods": "1.1.2", "istanbul": "0.4.3", "supertest": "1.1.0"}, "dist": {"shasum": "9bceef0669b8f8b943f0ad8ce5d95716bd740fd2", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.15.1.tgz", "integrity": "sha512-pufcv4+P49d+t01Slfbkhm9td5DWqh3VV+hooMhcu3k4t38R7UFW6HIW3WTOPHiM16CDTXulfcPZ1GWSgTw/Ig==", "signatures": [{"sig": "MEUCIH3QjEzk77JD8DzMp0zK6AOtDVwOOnXyjdObEpbe7uU6AiEAj/Ci/*****************************++hDJFyXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.15.2": {"name": "body-parser", "version": "1.15.2", "dependencies": {"qs": "6.2.0", "depd": "~1.1.0", "bytes": "2.4.0", "debug": "~2.2.0", "type-is": "~1.6.13", "raw-body": "~2.1.7", "iconv-lite": "0.4.13", "http-errors": "~1.5.0", "on-finished": "~2.3.0", "content-type": "~1.0.2"}, "devDependencies": {"mocha": "2.5.3", "eslint": "2.13.0", "methods": "1.1.2", "istanbul": "0.4.3", "supertest": "1.1.0", "eslint-plugin-promise": "1.3.2", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "dist": {"shasum": "d7578cf4f1d11d5f6ea804cef35dc7a7ff6dae67", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.15.2.tgz", "integrity": "sha512-w+PHzLDPwR2csqaHDt/oKTD5XVieOWaa1OV0VcX+agasefEZI3jK6iBabpOcM3ovd3YvIg+sK9sX437wvYjUSw==", "signatures": [{"sig": "MEYCIQDtXKE52XxUz3N4qOQvfA4vBf/Vo0CkqPzNEWVLR2lowQIhAJqGJVxfnhoWf6Lf4wL4YfTcU//1bOr4wcHG3ubQRmHt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.16.0": {"name": "body-parser", "version": "1.16.0", "dependencies": {"qs": "6.2.1", "depd": "~1.1.0", "bytes": "2.4.0", "debug": "2.6.0", "type-is": "~1.6.14", "raw-body": "~2.2.0", "iconv-lite": "0.4.15", "http-errors": "~1.5.1", "on-finished": "~2.3.0", "content-type": "~1.0.2"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.13.1", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.4.0", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "dist": {"shasum": "924a5e472c6229fb9d69b85a20d5f2532dec788b", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.16.0.tgz", "integrity": "sha512-zFgl6vVxlI5afDTqrzEbxKTVX5lOL0/M0VeNHK4Nka7cYwmJfwjAW1GqoDqhlXyVE+aZEG5aGfEZDWhW/v+6Ow==", "signatures": [{"sig": "MEUCIQCbX08OISdwLzIy90zvz4TBQSiJnUO3JQaHx2NA67OFMgIgbV6DyFeR9tvF5PF8pj0UmU6RXXoVu8pxujOQdV2DZ8Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.16.1": {"name": "body-parser", "version": "1.16.1", "dependencies": {"qs": "6.2.1", "depd": "~1.1.0", "bytes": "2.4.0", "debug": "2.6.1", "type-is": "~1.6.14", "raw-body": "~2.2.0", "iconv-lite": "0.4.15", "http-errors": "~1.5.1", "on-finished": "~2.3.0", "content-type": "~1.0.2"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.15.0", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.4.0", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "dist": {"shasum": "51540d045adfa7a0c6995a014bb6b1ed9b802329", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.16.1.tgz", "integrity": "sha512-7MTv2fVPg107awTG4ww4GDF5RrU/Z+546C8jsVJ0N8+KJu0SqABmjfRsh4e3nWryxZGF+yOZhGMMYDa/OAoGWg==", "signatures": [{"sig": "MEQCIH1eHUdHN6VlZkNvE7DD5i4I7eOR1oNB55Jo6NE9X5JsAiBLovLFkoaRRe9hUrVXCse2LRsmtk8iY2dzzpl1DOFXfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.17.0": {"name": "body-parser", "version": "1.17.0", "dependencies": {"qs": "6.3.1", "depd": "~1.1.0", "bytes": "2.4.0", "debug": "2.6.1", "type-is": "~1.6.14", "raw-body": "~2.2.0", "iconv-lite": "0.4.15", "http-errors": "~1.6.1", "on-finished": "~2.3.0", "content-type": "~1.0.2"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.16.1", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.4.0", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "dist": {"shasum": "d956ae2d756ae10bb784187725ea5a249430febd", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.17.0.tgz", "integrity": "sha512-64UNLtNOgTZ7ybVuGo7B3msRPTkXiGqn2t2k9KrqisBVhnexT9UarrgcerIo0qeKWHU7sZWflRubuCS2YXdmGA==", "signatures": [{"sig": "MEYCIQC9jImYdpZnYDBX2z4bBjD8EmdU+ZwXqqyPkql6Mt0YUAIhAOwbKC7r9e3QkOmFJrmc7hBmtT/JaZy73zWxN1nTgP8y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.17.1": {"name": "body-parser", "version": "1.17.1", "dependencies": {"qs": "6.4.0", "depd": "~1.1.0", "bytes": "2.4.0", "debug": "2.6.1", "type-is": "~1.6.14", "raw-body": "~2.2.0", "iconv-lite": "0.4.15", "http-errors": "~1.6.1", "on-finished": "~2.3.0", "content-type": "~1.0.2"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.17.0", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "7.0.0", "eslint-plugin-markdown": "1.0.0-beta.4", "eslint-plugin-standard": "2.1.1"}, "dist": {"shasum": "75b3bc98ddd6e7e0d8ffe750dfaca5c66993fa47", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.17.1.tgz", "integrity": "sha512-yMi6cDvaQLniK4bQATEZWYVn52TqcFt5fr8mP5xMI+1S5Kk6ouSF90ncoorYXCjsPLN9ilJ/rAbmpNKfj93gkQ==", "signatures": [{"sig": "MEUCIELIex1+IqOrCdnPKBVzjZLKMi41n7C3iLYz0k5+ZVtjAiEAqlLnYN349hfqQ6ZdS0f+9kSX29AW6dBjX2CdggxXcg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.17.2": {"name": "body-parser", "version": "1.17.2", "dependencies": {"qs": "6.4.0", "depd": "~1.1.0", "bytes": "2.4.0", "debug": "2.6.7", "type-is": "~1.6.15", "raw-body": "~2.2.0", "iconv-lite": "0.4.15", "http-errors": "~1.6.1", "on-finished": "~2.3.0", "content-type": "~1.0.2"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.0.1", "eslint-plugin-node": "4.2.2", "eslint-plugin-import": "2.2.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "f8892abc8f9e627d42aedafbca66bf5ab99104ee", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.17.2.tgz", "integrity": "sha512-pLoAUdkGfkp6VGbhhNWLtvF5dkWYVY7oSwZa45ZJeTxAipUB9L7n1cx0a/leni10VflrjorZM7Jb5jKxpIYniw==", "signatures": [{"sig": "MEUCIGP5JRxvhvVpnm8IyL6MFmabEBckAY3eEubp6zUL3RdRAiEAoBzpaL3rhymRrRwndzrLKhxqlfs/e326XDzds/1TU5Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.18.0": {"name": "body-parser", "version": "1.18.0", "dependencies": {"qs": "6.5.0", "depd": "~1.1.1", "bytes": "3.0.0", "debug": "2.6.8", "type-is": "~1.6.15", "raw-body": "2.3.1", "iconv-lite": "0.4.18", "http-errors": "~1.6.2", "on-finished": "~2.3.0", "content-type": "~1.0.2"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.1.1", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "d3b224d467fa2ce8d43589c0245043267c093634", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.18.0.tgz", "integrity": "sha512-iKF4dMoQmzJDfjIUx/uqWjUs6wUqpD/IiKR+kfTng2zDhBBXLa7gji72a50pa9EGtdTKzjNWUw3PphxtJk6ejA==", "signatures": [{"sig": "MEQCIFmBTDWJoSDrnwNucduioI8q7jFI+PxlgDW3T/RQSrlAAiBPmyc1Toa980nO+YO6EDUG6gauuueSGzus2hB50xnw+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.18.1": {"name": "body-parser", "version": "1.18.1", "dependencies": {"qs": "6.5.1", "depd": "~1.1.1", "bytes": "3.0.0", "debug": "2.6.8", "type-is": "~1.6.15", "raw-body": "2.3.2", "iconv-lite": "0.4.19", "http-errors": "~1.6.2", "on-finished": "~2.3.0", "content-type": "~1.0.4"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.1.1", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "9c1629370bcfd42917f30641a2dcbe2ec50d4c26", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.18.1.tgz", "integrity": "sha512-KL2pZpGvy6xuZHgYUznB1Zfw4AoGMApfRanT5NafeLvglbaSM+4CCtmlyYOv66oYXqvKL1xpaFb94V/AZVUnYg==", "signatures": [{"sig": "MEYCIQC8s9lNNafjwMPSKrGcVFHtFB5yS4gusrPS3Ajt99a8RgIhALIobQtYlX12OEgEoGGtQ/EUFxmkJvJyo+ccvpma6FIF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.18.2": {"name": "body-parser", "version": "1.18.2", "dependencies": {"qs": "6.5.1", "depd": "~1.1.1", "bytes": "3.0.0", "debug": "2.6.9", "type-is": "~1.6.15", "raw-body": "2.3.2", "iconv-lite": "0.4.19", "http-errors": "~1.6.2", "on-finished": "~2.3.0", "content-type": "~1.0.4"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.1.1", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "87678a19d84b47d859b83199bd59bce222b10454", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.18.2.tgz", "integrity": "sha512-XIXhPptoLGNcvFyyOzjNXCjDYIbYj4iuXO0VU9lM0f3kYdG0ar5yg7C+pIc3OyoTlZXDu5ObpLTmS2Cgp89oDg==", "signatures": [{"sig": "MEUCIF86iMTOB2wffH3rYvvRoaVpaDhE4TFkMEF2Rj9i251UAiEAuDjo6cy7FDPm7ihWa+ARsEXelPvAkeSGojtpt7rTYRw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8"}}, "1.18.3": {"name": "body-parser", "version": "1.18.3", "dependencies": {"qs": "6.5.2", "depd": "~1.1.2", "bytes": "3.0.0", "debug": "2.6.9", "type-is": "~1.6.16", "raw-body": "2.3.3", "iconv-lite": "0.4.23", "http-errors": "~1.6.3", "on-finished": "~2.3.0", "content-type": "~1.0.4"}, "devDependencies": {"mocha": "2.5.3", "eslint": "4.19.1", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "1.1.0", "safe-buffer": "5.1.2", "eslint-plugin-node": "6.0.1", "eslint-plugin-import": "2.11.0", "eslint-plugin-promise": "3.7.0", "eslint-config-standard": "11.0.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.1.0"}, "dist": {"shasum": "5b292198ffdd553b3a0f20ded0592b956955c8b4", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.18.3.tgz", "fileCount": 10, "integrity": "sha512-YQyoqQG3sO8iCmf8+hyVpgHHOv0/hCEFiS4zTGUwTA1HjAFX66wRcNQrVCeJq9pgESMRvUAOvSil5MJlmccuKQ==", "signatures": [{"sig": "MEUCIAiAWWr4AuCCgPB9fNk9hyO6ipAuWJWDsgvzRrr7o22JAiEArJYScAkCJ4N6Wf5d9+ennuxeFHhIaES5nGMOhBM9Dkk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55897, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+cRxCRA9TVsSAnZWagAAr+wP/jRF3kZaSUPcjTUOxoqN\nlSEgHs4ISF5j2kqTPeArKhG5cgmvygc9gRtNdjtfLGHbQAjyZtA8tzBdsXZG\n3KuehVC9hf/eUIeQbVTjaxrgX0jevrF4igcmM4tRI17JzFvq1+oCz9aAk99e\n3SljKbwvFPopbM5F5BraVbOpIYCO140dVVBrV9gHOBb+65hme0PzfBdru/Bg\nZ1UnDi9l/lEGhCy+HFlSGw+T9ev0KcgzcslA/9vJBAFyYMXVfFFI+jY2+O4P\nJ35s40gbQ1V/idGM4IfmCg5IhwmvpJ902bpXJEokVgGKcw/mMv3TQpBKov2I\nOxAIIRLr2w/1Kl2d8+jMLcbkIoSD7wutdV5i+rsUR5XJURbsDuitYJbcRvJw\n5MS0bQ5aVI83TOr/35z8671ciWqpM+Ru+7eiMjQzgUOWt2qKuOB0Bi/xhr8J\nXGWbqLwl1PWxJXgGJqAJ7rvXNOmAexqJmyk1mPHDJnpjaFRKnCpDrUbuX4Cm\nw52bjMIW+wYC/zKnUMCJ+5n4gBw0jWzFUsoTCses91YZHMDVNb1tOo2Sjbwp\nxLKIWDuNDQC3lxfuCSxC7Qe3PayQGnOCwoP3o6GM89YFAhFOWgPvgBpvjfWm\niJlgG/dUsGIAY3Gz0i7abBGyeJJAuA+CwFpfERMcEE4HwcJhzOrG7g0prSD9\n3nNl\r\n=47oB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}, "1.19.0": {"name": "body-parser", "version": "1.19.0", "dependencies": {"qs": "6.7.0", "depd": "~1.1.2", "bytes": "3.1.0", "debug": "2.6.9", "type-is": "~1.6.17", "raw-body": "2.4.0", "iconv-lite": "0.4.24", "http-errors": "1.7.2", "on-finished": "~2.3.0", "content-type": "~1.0.4"}, "devDependencies": {"mocha": "6.1.4", "eslint": "5.16.0", "methods": "1.1.2", "istanbul": "0.4.5", "supertest": "4.0.2", "safe-buffer": "5.1.2", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "dist": {"shasum": "96b2709e57c9c4e09a6fd66a8fd979844f69f08a", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.19.0.tgz", "fileCount": 10, "integrity": "sha512-dhEPs72UPbDnAQJ9ZKMNTP6ptJaionhP5cBb541nXPlW60Jepo9RV/a4fX4XWW9CuFNK22krhrj1+rgzifNCsw==", "signatures": [{"sig": "MEQCIE6oHUR+zCoLXnukUI+I14kmNMZ2yQCSpnqxynyl7ClSAiAeARDI3r97Fapg8nv1+y0KxR/GIN4c95AQFKS0ka/otA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcwnuMCRA9TVsSAnZWagAA2zMP/3i2Q8pQBJx4azFOeuub\n/s3F445wJrDoAKA+6zSOLFMYYasZ0iF60NoE4taDupDF1hzpC4gCYgy9ZezQ\n75kKKBC48jCQP6Urx1tj6VUPzWqG6xdQMjhZpXrkK+EF5XYtAspb8+YSxaw4\nzf4atEm+7Q3N1qwvyfi8T/KQaK7WV6wC513pXTZv8SCtetX/4jBJwA4uUqLh\nXbuO5GcsjNEDmfX91YFKbb2+TvL2kuJkxVVdjeVv+UDLAs8AL+6afVJTe2vB\nmY+9CmSN2egWYDEXgpIowRTXzvasLJ8kQQH0dhseRrnF/k8cxX61VsT0MYEB\nd7mVyXFJE2WrN/HgiVCa9XSzLNn2bp/tyoz3W8TTSCqWOaY2cgbpFBUcBqWY\nmZSkqGqBj0lAJ3qMJw9tfIKiGtLEqsBwRoHTt6yQRsPTTD0wY3WzQTzedpS7\nPKEPDqrqMhDJpjv7vHZyP0E85lSYoDAMYPQ33fYvNbiuIMU4eDxoNJWUImXJ\nTN3uRKDn9QeE8mLTeglLVIu5+4FrDQNNjK6HHcetM89H8F4FGxGl090/H07x\nqc9A2Fe2yCeM6BICsO3BIRt0eClHS6jD15tMDbx9hx4Z4Qt+IgTn0NS4Ebj7\nW1V7qu/d6ajepEVd2kCXQkvJvslxzIGDxXo6OvTN757kROAWoYZNyGefqnou\nHXEH\r\n=IvFh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}, "1.19.1": {"name": "body-parser", "version": "1.19.1", "dependencies": {"qs": "6.9.6", "depd": "~1.1.2", "bytes": "3.1.1", "debug": "2.6.9", "type-is": "~1.6.18", "raw-body": "2.4.2", "iconv-lite": "0.4.24", "http-errors": "1.8.1", "on-finished": "~2.3.0", "content-type": "~1.0.4"}, "devDependencies": {"nyc": "15.1.0", "mocha": "9.1.3", "eslint": "7.32.0", "methods": "1.1.2", "supertest": "6.1.6", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "1499abbaa9274af3ecc9f6f10396c995943e31d4", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.19.1.tgz", "fileCount": 10, "integrity": "sha512-8ljfQi5eBk8EJfECMrgqNGWPEY5jWP+1IzkzkGdFFEwFQZZyaZ21UqdaHktgiMlH0xLHqIFtE/u2OYE5dOtViA==", "signatures": [{"sig": "MEYCIQDX0pv+zrP54j0jwPRztnMBkxHadKdw0GPHw1PeFkxjwwIhAJGo0yU4nrpwGKmmKol3RbYqHQGE0orPKAeKEi0jYylz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhs6/iCRA9TVsSAnZWagAAXOYP/3fnrwpITlNiTT9so6bV\nbwbT8eAGY7ugyp71iqgswH4ZyHPRMdcruxTfBeVWc6XOoRp0LIWv4H+pmlrf\n4RsR/KcPlUbdnvwQ0bANpd4jdEOhIIG1RmuZ1VsVWxiI6lxuyhQJ0aGc8aJJ\nTBEPBL89v5jQr8jVFZHqguUWY+9iJ7a8A5oGL/6vYAxWr3ayAvAg7b2CN3VO\n7w1XjtCim+W6f4yPgJdOf6T+FeZ6C4Dsvvz8ouYpXLdcVe2wMIx4eSsW1BUo\nZ2cfIir2CxnjV5dkxSUDQEZfpoB+LD2ylgOLFpCGG5qI+LFBRMVALmbnBYDo\nGUM35eQ7q9V26lVYBImWIom9N2vfjyGk7ijLYHhx78W+7nRhZi8CO54PjGIA\nqj0bEkMTqwdvHx0jJ8owJlNYpteDAys7qfdBPRkq29y0T6QJRoLj8pnu5sfq\nvqaM1OirWxcLJcwMD0Qj5B9MCWTsrY7ZHIYYmGfOge63XmZh7gHglDCP0Col\nREl+cq/E+usBoqwHeq2TJl3BCkc1U1QP3JbAWdj8ahMWpJpBiygNWWARxGvh\n7qTisdsrx3YNz5oemPv9yL9a3gpRDSbPpPbPvZte0F0pyArpWibuNXmxf9bz\nW1DjWMVpvBX54qkBPqrD4ZTjPEEns90cq0leUIjs8cBo7bG3EKWE/cbIu4wW\nanUs\r\n=nYJJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}, "2.0.0-beta.1": {"name": "body-parser", "version": "2.0.0-beta.1", "dependencies": {"qs": "6.9.6", "depd": "~1.1.2", "bytes": "3.1.1", "debug": "2.6.9", "type-is": "~1.6.18", "raw-body": "2.4.2", "iconv-lite": "0.4.24", "http-errors": "1.8.1", "on-finished": "~2.3.0", "content-type": "~1.0.4"}, "devDependencies": {"nyc": "15.1.0", "mocha": "9.1.3", "eslint": "7.32.0", "methods": "1.1.2", "supertest": "6.1.6", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "d4ed97d6ed51f6040b967db0db2252a0b235a661", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-2.0.0-beta.1.tgz", "fileCount": 10, "integrity": "sha512-I1v2bt2OdYqtmk8nEFZuEf+9Opb30DphYwTPDbgg/OorSAoJOuTpWyDrZaSWQw7FdoevbBRCP2+9z/halXSWcA==", "signatures": [{"sig": "MEUCIBzjqd8yWc7WTxePUyegrQY86SNSCw/2BbgjMMsB78ZjAiEA0LXwESY4mKMNtCxk5UdwSDNaeEIRxQjyl0ay3+Ju428=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57521, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhvVruCRA9TVsSAnZWagAAOc4P/jrQSQ5PDt9wMSVzN8b3\nrEXm9qu/E026If+672WnIFLjUPcFv0zc9Bk06JNEd9BPeVK9Tt+nEeCHdwrR\n+lGo2WoG3f/rEpkF0fZsux0SuRFn+mZv+v9SyLDdBlLOpvPAF4AC4o8ghsvG\nem5PWgDpQfKiRX+CN9e7rdzXnZ/7MOR0j3Rwu5n7W98huhVctmFHH0w235pe\njUQ3OJLvNgQ/+PDfdObbHDoDWEs7JSRYvUIez1wKlRIDwaT3Wju6OPKCbWuC\n2l6abH7cwFCxmt7NJQNu/uk6Mus+X60hrx8YZygvHo8fs2Bn5wR5X94T+7oN\n771Pf72pYC/cunpsc43yKiU1UFUDQeEWKQXSLX1J9hSe34WB/ABka1gFLs7v\n81ekg9+lasQXh3iBPzSTW1thwNdolQu8i/i34IpAaJwuAK2cXqJjRSBCeWZk\nzdpKnCXse1NNgr2xuxlLz0kd5In+Xj0WRAMu/svBt1V4vFLWQY67GqhfmTJw\n/FD6DN3dfNfNf7npggoLYbQiv7w1Z1BLCpyj6AWosWxqUDhrIEd1hsTMhyBF\ngPLZnnCsEvZxgUlKCEGV47KwZoPzmAJkkyMXYOVpfVN7eC56aNeTkCBDWVZP\nv6UNdkDinBnqZIN0gYnlACr9zk8lUNssjJ2jxFSA1cbxtuWP9gdXs9x3lNnd\nnU7R\r\n=b7Qq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10"}}, "1.19.2": {"name": "body-parser", "version": "1.19.2", "dependencies": {"qs": "6.9.7", "depd": "~1.1.2", "bytes": "3.1.2", "debug": "2.6.9", "type-is": "~1.6.18", "raw-body": "2.4.3", "iconv-lite": "0.4.24", "http-errors": "1.8.1", "on-finished": "~2.3.0", "content-type": "~1.0.4"}, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.0", "eslint": "7.32.0", "methods": "1.1.2", "supertest": "6.2.2", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "4714ccd9c157d44797b8b5607d72c0b89952f26e", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.19.2.tgz", "fileCount": 10, "integrity": "sha512-SAAwOxgoCKMGs9uUAUFHygfLAyaniaoun6I8mFY9pRAJL9+Kec34aU+oIjDhTycub1jozEfEwx1W1IuOYxVSFw==", "signatures": [{"sig": "MEYCIQDB/gljkDFGXD26LAQjdpTBri3yNbufb8cEQHRX7uuJSQIhAMhLAWXtuWSnnTV6INHf3qgXmPNlsmTggU+qYx039B8f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDHMdCRA9TVsSAnZWagAAiwIQAIaiKW4cSAAxYl21hS3P\nfIOZykpSmJ4VvM+XKLxQZR3t8uaLA0yga9XK/zAeHEOjJbHQpw9uQWgPnOJj\ns5+x1NPYGwZzwPcmP0/LnO1ArEXD1LdaK9u3IZKEyCmGPFvnGvqJnxandiwH\neSaj6SA20/AlwG9XBO+iStgnV8ubU0UgOgUK1wfcJNITp7KvQY+WFhyFIqzH\n3xyrQAysqI53cyUvxzpvj/QrA2kDkgt3D9UgXZS3w3phSTRMiCRtOks+rP+w\nYxJ/4KgEHy+s+Ut4MBt4Lit8VJvzyPA7fFWKS+2Jzvwq1r4hk3j9+j+m+Nla\nw6AXD41WwjF2PvyB3+PlsGvpueaIJOREI84vHTNUuwlOFhcg0yvCUAzwcMx3\nKdWTrft36l4CXO7AX1OiXLfSq0jti1ifx88dJ+LsmjnjZzOiRRod3j8wFaGN\nv7GZmxsbQybwI1Vwxv/ogZKws8NEtMxRDPwvaLJxW+PJpI5ULsmkcI2ppqQj\nHS5fcWn9+VFYr8IZzDnXKNEbwKAdRsX9HrPD7EftIOKLdX8ekeV0iNTIh8aJ\n1dDsP/wpeLuZtDPjWw5SMYYwkB22/LZ3ZzJt81qR11sdxhjEPWNxts0m1bru\n1aCZNSEBO9uR3CXfDgBXMFfm8UdTU5MrgpLEv8AgfbqwU1CX+xnK9tL7qexJ\n4OzG\r\n=d7sF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8"}}, "1.20.0": {"name": "body-parser", "version": "1.20.0", "dependencies": {"qs": "6.10.3", "depd": "2.0.0", "bytes": "3.1.2", "debug": "2.6.9", "unpipe": "1.0.0", "destroy": "1.2.0", "type-is": "~1.6.18", "raw-body": "2.5.1", "iconv-lite": "0.4.24", "http-errors": "2.0.0", "on-finished": "2.4.1", "content-type": "~1.0.4"}, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.2", "eslint": "7.32.0", "methods": "1.1.2", "supertest": "6.2.2", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "3de69bd89011c11573d7bfee6a64f11b6bd27cc5", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.0.tgz", "fileCount": 11, "integrity": "sha512-DfJ+q6EPcGKZD1QWUjSpqp+Q7bDQTsQIF4zfUAtZ6qk+H/3/QRhg9CEp39ss+/T2vw0+HaidC0ecJj/DRLIaKg==", "signatures": [{"sig": "MEYCIQC1OSkOnv+96zl/Q+rmfrOAZUHqzW95lhcVtlbTkzWYvQIhAISA01nS8WBaE3hIQgonGwA9xu+0xtp1wU9mBFqRXrBP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiSPKQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSrg/+NDcjgvgB1yS8/N+wfk4Ph7ddxpY2Lc8Ww5RM7VU1Kk8FmUiM\r\nZYKpAGXVhvOWZwANhF4xqTwjC3rychMkmVeUJuxFbgWIy3ittJ4ntb9wx+3x\r\n6k6H5bDyIce23M+a6wPcBnwPeHIjPHIgLJfU6AS/HDF6qXpNdzV10xy3BNYv\r\ncoaisnMketbEmBJ60LE6zAjxBLvRr54hA4wOXDqvhJ1ROUWCwEVGU4z7AczJ\r\ne4YWn3rj/elSDKbESvw3XYzAcr6Wk6pP7lCMY0xprj+OhHwiCOe9TEQQaxPm\r\nq9Se7jvpNz9T0ikWA20cnRGtRFOzshVGHAsdhqhUPRe470hfteP03GmCt0Rz\r\naK9nkj6EnkxGotTXIa+Fx/G8XT2i/rYfCF5p1Ewrw4X/X+OHlqKjTB3/JwbS\r\nnTfHlhAl8ixj5Zn8PMXHyUDswyQPFo6PNNK6+7UByAGp3Uez1Zo5hV5aPXyF\r\nYeSMPo7POCoxx4v6u79AwQYuQUmwHQWkJhM1KIumSf/l/aJU8PFVA0OL7t6p\r\nwk2/mWT9r1gc8/YPIKoYlAq44jVBaVJIEdNd7BwxKUxMqjJg1+GiR+Pwg1K6\r\nVaZpxAYDr+pSEGaxG/uu6tUzAkVuAwEW2Sg/pdJ4OSEZF4ZMuDlecEhDlfuF\r\nIGZzkDPPHS81u71NqIRPdAB5FxSGrJkZ1t4=\r\n=08QC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"npm": "1.2.8000 || >= 1.4.16", "node": ">= 0.8"}}, "1.20.1": {"name": "body-parser", "version": "1.20.1", "dependencies": {"qs": "6.11.0", "depd": "2.0.0", "bytes": "3.1.2", "debug": "2.6.9", "unpipe": "1.0.0", "destroy": "1.2.0", "type-is": "~1.6.18", "raw-body": "2.5.1", "iconv-lite": "0.4.24", "http-errors": "2.0.0", "on-finished": "2.4.1", "content-type": "~1.0.4"}, "devDependencies": {"nyc": "15.1.0", "mocha": "10.0.0", "eslint": "8.24.0", "methods": "1.1.2", "supertest": "6.3.0", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-promise": "6.0.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "b1812a8912c195cd371a3ee5e66faa2338a5c668", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.1.tgz", "fileCount": 11, "integrity": "sha512-jWi7abTbYwajOytWCQc37VulmWiRae5RyTpaCyDcS5/lMdtwSz5lOpDE67srw/HYe35f1z3fDQw+3txg7gNtWw==", "signatures": [{"sig": "MEUCIG0jCuvvVGjV+GGAmGEZeQeFMhZWZnJ9qP6Ch4Tc90CZAiEAqfZBKAMguRwJF5saPpkusWoe72ZcQdMGy1er8viK27I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60314, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPt96ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpmpg//ZbJnafsaEqlDgyIUiLP0VteSrs+e3HvkXuCewU6EfD4HkNKm\r\natWspIWQ0UE8WU3jyjy8dwPwvyUZVsLvOYrEndaNNxmQjHauwk2iKWhbgS5m\r\npC1yK1UUmogVgo9wUTinUjuF4r2AFSPKMxq9o3Ms6vnu8i+Svb60TqYpDL66\r\nxorj0ivXlpUnPcfh6iecA3x02rdEM57O1eG51KYwSd4+mCfjM3UVSiqycivV\r\n9ZIZhsEKchgwH13544CV5ZbssNZqqajIepCTTPQGhWCxpA8tVTKoGTjQPaNd\r\nie+ZLnw5Hm7ddGZj5ywDMi9in9FpycAYospPJK0+pJuMEWp+Az8ROyLku92q\r\nqP/rzc9lJjSQI4g8iUDKgA9T6jU6YGPmCSccoaotBfw7RS/3BJ8vjAEsl2jP\r\n2mN/RrNcLPKBjULS4tTd6Azh6jHL1ymUhoU4dVqMuPAe6DljSQXjHYTr+KyQ\r\n+Soby9jdKSGvMKkvA33MrI0WHej06ZIz1AmMGUAnkP+GD+ozlLXDAa7Y4CsN\r\nG4a5L7pxtaMfnwOZer0mfhJgxf4i/Nqly0NMWnJoXk91guehHemPh7qPXYby\r\n/ouAila3muEJ9LkGwRZDq6uTnWG0bBAFrHA8fh6la6WJcTnLBRY9VZ9RwnuP\r\nO2k558FDW97pjISrvgM/0qyc9867O38QlD8=\r\n=ZP3f\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"npm": "1.2.8000 || >= 1.4.16", "node": ">= 0.8"}}, "1.20.2": {"name": "body-parser", "version": "1.20.2", "dependencies": {"qs": "6.11.0", "depd": "2.0.0", "bytes": "3.1.2", "debug": "2.6.9", "unpipe": "1.0.0", "destroy": "1.2.0", "type-is": "~1.6.18", "raw-body": "2.5.2", "iconv-lite": "0.4.24", "http-errors": "2.0.0", "on-finished": "2.4.1", "content-type": "~1.0.5"}, "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.34.0", "methods": "1.1.2", "supertest": "6.3.3", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "6feb0e21c4724d06de7ff38da36dad4f57a747fd", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.2.tgz", "fileCount": 11, "integrity": "sha512-ml9pReCu3M61kGlqoTm2umSXTlRTuGTx0bfYj+uIUKKYycG5NtSbeetV3faSU6R7ajOPw0g/J1PvK4qNy7s5bA==", "signatures": [{"sig": "MEQCIHFWkBQ9SNwui362JRxszbvNRiYLO62bIMqSXg0xctAIAiB07wAQALxzWBax4N2eg8AUI8ltUmCbogDn8TBIzCpyPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60781, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9XG7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTLA/6A7GxZJclDS15GRJB0Sqaorjhc2xTnVhoHSwa7rzKzN6XU5Iw\r\nPEMoBc5AvFoF1OLT0CTOkPmAdaTxPPBV7Y1QNvYow6yY3P6hDvwvFgXsecK/\r\nX5UAYBiQFEjxJ9oWo1j1iIufBvUoM6QL4IPq1SSChqpm6KzUtUCCdRPL3GGS\r\n5cw7BLNWR4V8bnQmncs5PqxeYAZ7vJ3wICmJ1ZVnNC3rqxURZXlODblKYZaE\r\nLmwuhfYeeQwe1+v1JXfSyRg4b/n07/bu9WKXU9bQK51rCBrwMCQFCEO4nvHB\r\nB1VAJK8rZIkt3Pi7C2R5Rs/orM1wrJODcGzzGE5nGctJDw51lcCe0qNbI8MR\r\nqkYjQgKpnUUtxN3xsH4jSJJ5q8KLZvwIIxlAAffWXieBRn1zqOF4mQ7Hypvz\r\n3snLSti3XA3V6rIs27kKd8YY6rY7P5S7OXxKc4fz9LdCfpWn2Z1HSTdJyCqo\r\nYebfL2T9E9XjfVAWMEt+mbqMJypAIVTKRzeTib9PjddpBWq59bURPjVp9PKU\r\nK5vb6qwWmH3h8VFngw5elsZSCGU1HtyNiEXqhpTqRqNzvTrCHHVArg8JUaXh\r\n5ys0b44lof0W9aEH5wHwRMdM7s3+2SrC/4bxz9pnV5FVDfkeZpYG3qPLDcUR\r\n7RY9pWpZzmrAhhoNdPvQlnrmT06ZN7v3p/k=\r\n=TVwV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"npm": "1.2.8000 || >= 1.4.16", "node": ">= 0.8"}}, "2.0.0-beta.2": {"name": "body-parser", "version": "2.0.0-beta.2", "dependencies": {"qs": "6.11.0", "bytes": "3.1.2", "debug": "3.1.0", "unpipe": "1.0.0", "destroy": "1.2.0", "type-is": "~1.6.18", "raw-body": "3.0.0-beta.1", "iconv-lite": "0.5.2", "http-errors": "2.0.0", "on-finished": "2.4.1", "content-type": "~1.0.5"}, "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.34.0", "methods": "1.1.2", "supertest": "6.3.3", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "46081eb83ab83e436c2bce8fa71464b2385d61fb", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-2.0.0-beta.2.tgz", "fileCount": 11, "integrity": "sha512-oxdqeGYQcO5ovwwkC1A89R0Mf0v3+7smTVh0chGfzDeiK37bg5bYNtXDy3Nmzn6CShoIYk5+nHTyBoSZIWwnCA==", "signatures": [{"sig": "MEUCIA2W6LBbuT0mTqzMuaiX1bmDOVgyjEruDQWrkol3MlYEAiEAzKM70h4aVJzbNlFGFSeH1gvQ3YrTsaGHuMS5348Cs7E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9+IsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2ThAAn2ih/nB07tqtAJEls2qlcCG//AVt7FyYOVMHTPJIqL4CPaS7\r\n1o9F/XeRDt0VaJp4K4QwsLpnnvu7qndGI+ZavvxOuzx8Y/pz5eq4shWiMy5j\r\nUkckturosd5EwuuqN8VeGDZf4QL3YmwVn1S5x8vbqh6pNeWr5/e6I+k7yg4e\r\nU/4eXW/SW3qpdoXEMor6KyHvyjhn1xyi/bdRmEMSGdRfdJp2V4r4HzXsUnNM\r\nNgBHUJMqLZsERi/7KgU75cSBavB9gQ82pOBxUmIAORc0RiSeVdXACJgR2EIe\r\nzpTxgMi4CLI2OuLMut68zN/BPVyEY8hVasENxID13nlJzGiGqRHoDo7q3Egp\r\nd1hPbpeyBrUu9E4Oa2e2sd4ucbYsEkGKSsEINbENdOCxfjw7o+9uJCotoPmn\r\nvbfnvsmkjABZh4no8irfHsRkwZCnuq8QgfjjKfQoRs38VAjFGFsOGE76xtGy\r\nOik3mM85hblIMr0a6idIqtUrdNVgutg+in5USQD6nWwcIOAsF/rY2HicvqkE\r\nakJtTpWEgPsRLiG2NINRODFTGYswhO6ucmEWXeBMMHW02MPLoTIoZpYBmZob\r\nCEiiNzmb3fUrKbTe/O6mEA9kb7MdqQAu3ajOCJIfFlnwpDRrHK/egevo7K9/\r\nmMU5iHZfPnVDUx8e+azAtseVnH1HhGejBCw=\r\n=Ktnd\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10"}}, "1.20.3": {"name": "body-parser", "version": "1.20.3", "dependencies": {"qs": "6.13.0", "depd": "2.0.0", "bytes": "3.1.2", "debug": "2.6.9", "unpipe": "1.0.0", "destroy": "1.2.0", "type-is": "~1.6.18", "raw-body": "2.5.2", "iconv-lite": "0.4.24", "http-errors": "2.0.0", "on-finished": "2.4.1", "content-type": "~1.0.5"}, "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.34.0", "methods": "1.1.2", "supertest": "6.3.3", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "1953431221c6fb5cd63c4b36d53fab0928e548c6", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.3.tgz", "fileCount": 11, "integrity": "sha512-7rAxByjUMqQ3/bHJy7D6OGXvx/MMc4IqBn/X0fcM1QUcAItpZrBEYhWGem+tzXH90c+G01ypMcYJBO9Y30203g==", "signatures": [{"sig": "MEUCIB0vgxyEUJdgVza3GVKw7TdT/emSwJAP5yMHGliV5V3rAiEA8gku++m3YMFhAQlz5DO6MA+ATWJVJZ0qxXmdFxrr+fA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62624}, "engines": {"npm": "1.2.8000 || >= 1.4.16", "node": ">= 0.8"}}, "2.0.0": {"name": "body-parser", "version": "2.0.0", "dependencies": {"qs": "6.13.0", "bytes": "3.1.2", "debug": "3.1.0", "unpipe": "1.0.0", "destroy": "1.2.0", "type-is": "~1.6.18", "raw-body": "^3.0.0", "iconv-lite": "0.5.2", "http-errors": "2.0.0", "on-finished": "2.4.1", "content-type": "~1.0.5"}, "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.34.0", "methods": "1.1.2", "supertest": "6.3.3", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "c402c354d64e2a387b683f89d574b03cbef004d8", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-2.0.0.tgz", "fileCount": 11, "integrity": "sha512-Dp+ktxd2u4GkMLJoVvSKLiEDsRRKvbTorW0k66C1Z2aI9X1xKoOIueI4Au7zmMS2yi3ECwdXRHclk/VjvrczDA==", "signatures": [{"sig": "MEQCIDDS8q4T9Xnmdymk4gbIk6FPhnwtAXYBuqV1f6gA8A6/AiAjuVUWSK5S/iSuPCGMCrxOgqrYiTdVT4tzDwaf82Q8gQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 63035}, "engines": {"node": ">= 0.10"}}, "2.0.1": {"name": "body-parser", "version": "2.0.1", "dependencies": {"qs": "6.13.0", "bytes": "3.1.2", "debug": "3.1.0", "unpipe": "1.0.0", "destroy": "1.2.0", "type-is": "~1.6.18", "raw-body": "^3.0.0", "iconv-lite": "0.5.2", "http-errors": "2.0.0", "on-finished": "2.4.1", "content-type": "~1.0.5"}, "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.34.0", "methods": "1.1.2", "supertest": "6.3.3", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "979de4a43468c5624403457fd6d45f797faffbaf", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-2.0.1.tgz", "fileCount": 11, "integrity": "sha512-PagxbjvuPH6tv0f/kdVbFGcb79D236SLcDTs6DrQ7GizJ88S1UWP4nMXFEo/I4fdhGRGabvFfFjVGm3M7U8JwA==", "signatures": [{"sig": "MEYCIQDfc0emvKR2IPQCLyWFuhOBpYrd/B8jQ53xopnL+um7GAIhALcgXeWsW4hnZHox9+jffh702VZVcFbG0lNdauYNuK/D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62995}, "engines": {"node": ">= 0.10"}}, "2.0.2": {"name": "body-parser", "version": "2.0.2", "dependencies": {"qs": "6.13.0", "bytes": "3.1.2", "debug": "3.1.0", "destroy": "1.2.0", "type-is": "~1.6.18", "raw-body": "^3.0.0", "iconv-lite": "0.5.2", "http-errors": "2.0.0", "on-finished": "2.4.1", "content-type": "~1.0.5"}, "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.34.0", "supertest": "6.3.3", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "52a90ca70bfafae03210b5b998e4ffcc3ecaecae", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-2.0.2.tgz", "fileCount": 11, "integrity": "sha512-SNMk0OONlQ01uk8EPeiBvTW7W4ovpL5b1O3t1sjpPgfxOQ6BqQJ6XjxinDPR79Z6HdcD5zBBwr5ssiTlgdNztQ==", "signatures": [{"sig": "MEUCICX2XmnQRes7X1LDTAHHurqVVgXB/LlVDVbd2/TIjuHnAiEA92TEJvMF7VeuUTkIDLFi1hNQaCOxBleOiK0vyKpJ8Cw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 62851}, "engines": {"node": ">=18"}}, "2.1.0": {"name": "body-parser", "version": "2.1.0", "dependencies": {"qs": "^6.14.0", "bytes": "^3.1.2", "debug": "^4.4.0", "type-is": "^2.0.0", "raw-body": "^3.0.0", "iconv-lite": "^0.5.2", "http-errors": "^2.0.0", "on-finished": "^2.4.1", "content-type": "^1.0.5"}, "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.34.0", "supertest": "6.3.3", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-promise": "6.1.1", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "2fd84396259e00fa75648835e2d95703bce8e890", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-2.1.0.tgz", "fileCount": 11, "integrity": "sha512-/hPxh61E+ll0Ujp24Ilm64cykicul1ypfwjVttduAiEdtnJFvLePSrIPk+HMImtNv5270wOGCb1Tns2rybMkoQ==", "signatures": [{"sig": "MEUCIQDY+pmjRJNArKCMKG9ErFaoYYjWFPnk/3beIN53ff+A+gIgSwOmQ0A6rY7Adkbi9FOYj134WYem/7H5t/+LvcCQBLg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 62256}, "engines": {"node": ">=18"}}, "2.2.0": {"name": "body-parser", "version": "2.2.0", "dependencies": {"bytes": "^3.1.2", "content-type": "^1.0.5", "debug": "^4.4.0", "http-errors": "^2.0.0", "iconv-lite": "^0.6.3", "on-finished": "^2.4.1", "qs": "^6.14.0", "raw-body": "^3.0.0", "type-is": "^2.0.0"}, "devDependencies": {"eslint": "8.34.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.27.5", "eslint-plugin-markdown": "3.0.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "6.1.1", "eslint-plugin-standard": "4.1.0", "mocha": "^11.1.0", "nyc": "^17.1.0", "supertest": "^7.0.0"}, "dist": {"integrity": "sha512-02qvAaxv8tp7fBa/mw1ga98OGm+eCbqzJOKoRt70sLmfEEi+jyBYVTDGfCL/k06/4EMk/z01gCe7HoCH/f2LTg==", "shasum": "f7a9656de305249a715b549b7b8fd1ab9dfddcfa", "tarball": "https://registry.npmjs.org/body-parser/-/body-parser-2.2.0.tgz", "fileCount": 11, "unpackedSize": 59258, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDrp7iY/NXFz1uz4Xx0/tyVLizCBMG5t9vrCaSGPwkeIwIhAP+fZcWBCYKWnE8KpDFWUbkiGyQ1Im+jK9eFxGuqLFND"}]}, "engines": {"node": ">=18"}}}, "modified": "2025-03-27T01:23:21.695Z"}