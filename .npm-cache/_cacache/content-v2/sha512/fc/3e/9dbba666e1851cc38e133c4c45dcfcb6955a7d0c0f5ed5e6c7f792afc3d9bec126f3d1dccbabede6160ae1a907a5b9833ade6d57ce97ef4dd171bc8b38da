{"source": 1102458, "name": "jsonwebtoken", "dependency": "jsonwebtoken", "title": "jsonwebtoken vulnerable to signature validation bypass due to insecure default algorithm in jwt.verify()", "url": "https://github.com/advisories/GHSA-qwph-4952-7xr6", "severity": "moderate", "versions": ["0.1.0", "0.2.0", "0.3.0", "0.4.0", "0.4.1", "1.0.0", "1.0.2", "1.1.0", "1.1.1", "1.1.2", "1.2.0", "1.3.0", "2.0.0", "3.0.0", "3.1.0", "3.1.1", "3.2.0", "3.2.1", "3.2.2", "4.0.0", "4.1.0", "4.2.0", "4.2.1", "4.2.2", "5.0.0", "5.0.1", "5.0.2", "5.0.3", "5.0.4", "5.0.5", "5.1.0", "5.2.0", "5.3.1", "5.4.0", "5.4.1", "5.5.0", "5.5.1", "5.5.2", "5.5.3", "5.5.4", "5.6.0", "5.6.2", "5.7.0", "6.0.0", "6.0.1", "6.1.0", "6.1.1", "6.1.2", "6.2.0", "7.0.0", "7.0.1", "7.1.0", "7.1.1", "7.1.3", "7.1.5", "7.1.6", "7.1.7", "7.1.8", "7.1.9", "7.1.10", "7.2.0", "7.2.1", "7.3.0", "7.4.0", "7.4.1", "7.4.2", "7.4.3", "8.0.0", "8.0.1", "8.1.0", "8.1.1", "8.2.0", "8.2.1", "8.2.2", "8.3.0", "8.4.0", "8.5.0", "8.5.1", "9.0.0", "9.0.1", "9.0.2"], "vulnerableVersions": ["0.1.0", "0.2.0", "0.3.0", "0.4.0", "0.4.1", "1.0.0", "1.0.2", "1.1.0", "1.1.1", "1.1.2", "1.2.0", "1.3.0", "2.0.0", "3.0.0", "3.1.0", "3.1.1", "3.2.0", "3.2.1", "3.2.2", "4.0.0", "4.1.0", "4.2.0", "4.2.1", "4.2.2", "5.0.0", "5.0.1", "5.0.2", "5.0.3", "5.0.4", "5.0.5", "5.1.0", "5.2.0", "5.3.1", "5.4.0", "5.4.1", "5.5.0", "5.5.1", "5.5.2", "5.5.3", "5.5.4", "5.6.0", "5.6.2", "5.7.0", "6.0.0", "6.0.1", "6.1.0", "6.1.1", "6.1.2", "6.2.0", "7.0.0", "7.0.1", "7.1.0", "7.1.1", "7.1.3", "7.1.5", "7.1.6", "7.1.7", "7.1.8", "7.1.9", "7.1.10", "7.2.0", "7.2.1", "7.3.0", "7.4.0", "7.4.1", "7.4.2", "7.4.3", "8.0.0", "8.0.1", "8.1.0", "8.1.1", "8.2.0", "8.2.1", "8.2.2", "8.3.0", "8.4.0", "8.5.0", "8.5.1"], "cwe": ["CWE-287", "CWE-327", "CWE-347"], "cvss": {"score": 6.4, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:H/A:L"}, "range": "<9.0.0", "id": "5eTSGixRZ+YYJAxWeF37Xzy1DLDPtlIPOBQVTmt9wlIjzoM2z17Kanf2ep5+m6cTgNWIIdD3CXQgL5A8evYnCw=="}