{"name": "cookie", "dist-tags": {"latest": "1.0.2"}, "versions": {"0.0.0": {"name": "cookie", "version": "0.0.0", "devDependencies": {"mocha": "1.x.x"}, "dist": {"shasum": "a134b9c981df85c8a67b1620be5a36c0db1bdc63", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.0.0.tgz", "integrity": "sha512-dfZ/RKwTO5TsQSEAyfdwM0eaxt4/PAzuHUY4rnNrK53nuixwuozjlBjLxFPteUfBed9wjPHWlFy54vlcwt083w==", "signatures": [{"sig": "MEUCIQC/P176YciuQyqxJA4djoogRLFL37XRrb7q7lN6OPrRsAIgMJ4wD/+DcA9RUKc80cp1IaFzwTOnhNJTAAqJx1jhw78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.0.1": {"name": "cookie", "version": "0.0.1", "devDependencies": {"mocha": "1.x.x"}, "dist": {"shasum": "3162dd34ea833740e2e0d6e7129f2dcd55dcf7ed", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.0.1.tgz", "integrity": "sha512-3Hx6vLDTn3UoJgYbSa27zIrNGbVN+W7LRZSBh7LCP9TeqM7EnmHQogrBxeD+Ge+aDWX3z9T8Mp2+fq470jPJMg==", "signatures": [{"sig": "MEUCIQDqsLWMjs5DewVN5DZXa+7ghh1GXxQvL+EioHSh7h9NAwIgJWwfcQ3pN0mNPQOmsMe/4mbmnr5lBpugP+a/m94F0Hg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.0.2": {"name": "cookie", "version": "0.0.2", "devDependencies": {"mocha": "1.x.x"}, "dist": {"shasum": "17aedf62bc6af53745fecb55c45c3f097c2e858b", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.0.2.tgz", "integrity": "sha512-Tpfsfohu2GI3qFEqXH3bufaylHZM3CEuhZKane1Vtli9V8dxYk9L7n4sjxQKsewk7o+cjzVPQJAdTFEW2u9guQ==", "signatures": [{"sig": "MEUCIDeJSTIEWrTeSJN0QKzu0Z2cYid/YFuaM+UM46UhNukUAiEA+i4YY2i6d16uie0SPiXvngLbvTS6dm1RsTi+u6/r8uw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.0.3": {"name": "cookie", "version": "0.0.3", "devDependencies": {"mocha": "1.x.x"}, "dist": {"shasum": "732b0e64cb77186954f5e36b0b6bcfd062a12e91", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.0.3.tgz", "integrity": "sha512-nuvBgiOhOcmfKwv5t+4TMKs/4NU8jSLg3wnjpwAkZcjFa3kritGQpMlfdom7ZDnxbYjUAl5UbIbRbJdmRKRcmA==", "signatures": [{"sig": "MEYCIQC8+mlRFbm3izUzszS+mBAJu2zHKc66SQYSBPSohIVU9gIhAIL59oZSoFdgPZtEgtTMzdu3WgJ0FQ66t6c0Ew848x6x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.0.4": {"name": "cookie", "version": "0.0.4", "devDependencies": {"mocha": "1.x.x"}, "dist": {"shasum": "5456bd47aee2666eac976ea80a6105940483fe98", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.0.4.tgz", "integrity": "sha512-K4/8ihPVK55g3atBFCLcDWzHnrqZBawwjQnRGZ9A4Erg/uOmZY8b9n/tssKt4odxq3eK0HTQT6NVgtKvLSoKEg==", "signatures": [{"sig": "MEYCIQD/pedDJ0nsQaEryt0yVFVZPz4kd8R3hPdeCBLhkcfhOAIhAKmD48IZjM9eqfP5sngrbSkYpJ5GkHxAtyweENmZUpIt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.0.5": {"name": "cookie", "version": "0.0.5", "devDependencies": {"mocha": "1.x.x"}, "dist": {"shasum": "f9acf9db57eb7568c9fcc596256b7bb22e307c81", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.0.5.tgz", "integrity": "sha512-STLsAHdtBDF5GJiPHc4sdfX5qzri6bcSxdSlW/o4IYJAA5yZxh3ZZsvctsKRNbhpP328sN+A2EjOF9vcW/LhdQ==", "signatures": [{"sig": "MEQCIGGLY4eyvIOKvApddSI2FnAA8poJEq1VJeYUO9wNq7vzAiBo4DF6Sxslnc3uw4+lZ0CLGuTSlboLlnoA2DCFBhd1Uw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.0.6": {"name": "cookie", "version": "0.0.6", "devDependencies": {"mocha": "1.x.x"}, "dist": {"shasum": "7bc6bb50205dcb98cf13ad09d6c60bc523f6fcb7", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.0.6.tgz", "integrity": "sha512-f8pdZ++OJo3HtU2lBP0f56fVN7Lgq/ICviGy2XrGDgC0v3wd/2mke5fGr0LM3PEOz2AyHlQ1CAb2i/PxDRlIsQ==", "signatures": [{"sig": "MEQCIB+mbAZzZasJThKnyjca4UNBeK6IOkQuZP8Ll+85QoHvAiAvyDjf274LVoGNPam0JRQzpqqJncYuUw+iOlTS2I/xIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.1.0": {"name": "cookie", "version": "0.1.0", "devDependencies": {"mocha": "1.x.x"}, "dist": {"shasum": "90eb469ddce905c866de687efc43131d8801f9d0", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.1.0.tgz", "integrity": "sha512-YSNOBX085/nzHvrTLEHYHoNdkvpLU1MPjU3r1IGawudZJjfuqnRNIFrcOJJ7bfwC+HWbHL1Y4yMkC0O+HWjV7w==", "signatures": [{"sig": "MEUCIEyl4WzqplYueG6EyhLjb/ZxuegmPKtsFccSOGjMgknuAiEA81GAYF9uBUQc9QNQdkwvQ4FUXaaKG+yQkTEfQsHvS2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.1.1": {"name": "cookie", "version": "0.1.1", "devDependencies": {"mocha": "1.x.x"}, "dist": {"shasum": "cbd4b537aa65f800b6c66ead2520ba8d6afbdf54", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.1.1.tgz", "integrity": "sha512-dHcrl81dfXXv+oyegOhauihVzLzrJdQx7XKE9o3nQ1UXNwMRm+phmODJy7S/KAhJj6rNvMR+58nDzoME4ZzCTg==", "signatures": [{"sig": "MEUCIQCR1Xcz2aLm3ho6h8XEVs//a4E8zW+7zK8FCzZoPISEvgIgNwzYklVe/n4iBw04luk6gIKjSs8ngvgQMHKn/fIY+38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.1.2": {"name": "cookie", "version": "0.1.2", "devDependencies": {"mocha": "1.x.x"}, "dist": {"shasum": "72fec3d24e48a3432073d90c12642005061004b1", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.1.2.tgz", "integrity": "sha512-+mHmWbhevLwkiBf7QcbZXHr0v4ZQQ/OgHk3fsQHrsMMiGzuvAmU/YMUR+ZfrO/BLAGIWFfx2Z7Oyso0tZR/wiA==", "signatures": [{"sig": "MEQCICTQusQhWIsNZacw+Ui1Kof9LKcZ6j60vk+dBPkkFm3iAiBnjenKjgi2tzUvNKMSR1ntefgQg8SEkmKoGu5kZYhfiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.1.3": {"name": "cookie", "version": "0.1.3", "devDependencies": {"mocha": "1.x.x", "istanbul": "0.3.9"}, "dist": {"shasum": "e734a5c1417fce472d5aef82c381cabb64d1a435", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.1.3.tgz", "integrity": "sha512-mWkFhcL+HVG1KjeCjEBVJJ7s4sAGMLiBDFSDs4bzzvgLZt7rW8BhP6XV/8b1+pNvx/skd3yYxPuaF3Z6LlQzyw==", "signatures": [{"sig": "MEUCIQCCNZnnlhQ7wLLXXybUVfwXeWzmUf6oO6mLK/tVPQXAGQIgKk958UOvVPNCy0+nNSS0yjrVa4fhjbH+cBI4FhEX02I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": "*"}}, "0.2.0": {"name": "cookie", "version": "0.2.0", "devDependencies": {"mocha": "1.21.5", "istanbul": "0.3.17"}, "dist": {"shasum": "9708beeaa361857de7d16516fea779572625caad", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.2.0.tgz", "integrity": "sha512-K+vrjxCYfXeM5wYEuBLkDsBdJz2yw1yDM3uAZ/I56sipmNr3ssNVrKfmko1z1N2c7Bu5ho9UFUR6nYCskH47xA==", "signatures": [{"sig": "MEYCIQDhdas3gc7457nYJM/VRudZSCFTlmtqdUncR5NiSwuIAAIhAOXYymPjHZXgALso0I9+CifcgiQP728MTbJboB+RC6X3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "0.1.4": {"name": "cookie", "version": "0.1.4", "devDependencies": {"mocha": "1.21.5", "istanbul": "0.3.20"}, "dist": {"shasum": "4955c0bd32fffa83b7433586185875876ea04e4b", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.1.4.tgz", "integrity": "sha512-w2MOEWrqy1q7Z3iyP0eMGaSmoL6qyqH2+h7hv1eRB3e70hBdqHj39jp1nT5FxyePotIoStLu7FS9Mkp4JQoZGg==", "signatures": [{"sig": "MEQCIAmKwzauuefhhUhGVEoyzKu6nY592kU6dwLO1jEI/PnbAiAyQQ1rcO7ofo5CBTpLdiyNYApPO949Hv4iHTz1iYmL/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "0.2.1": {"name": "cookie", "version": "0.2.1", "devDependencies": {"mocha": "1.21.5", "istanbul": "0.3.20"}, "dist": {"shasum": "e1bc7c07d1985c17ad7347502bac1a0eb072ac9a", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.2.1.tgz", "integrity": "sha512-a0nMJ599C1Gv3FJGasCIVCTJYaYep0znzxC14H+p6oTHQVx2ltZNu+G7PLbUu+QoYbCsY3K1d6juDhq34IBN9Q==", "signatures": [{"sig": "MEUCIQCQmLb09492r6nsbQkU+QqMtlq/3rBB4dyalbZqncGbmAIgHD8+06KIDR84jw4gOaEeWZXSEsOgRg4RN4ddvG0g5L8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "0.1.5": {"name": "cookie", "version": "0.1.5", "devDependencies": {"mocha": "1.21.5", "istanbul": "0.3.20"}, "dist": {"shasum": "6ab9948a4b1ae21952cd2588530a4722d4044d7c", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.1.5.tgz", "integrity": "sha512-/lhu+NGBI5pOLXILS07DrPXYX0QDD/ejVhbwoCUcLPBqMEK9b++f9rUhAlhLkcTz9mV6QSeD+w3cHJ96rMZaFQ==", "signatures": [{"sig": "MEUCIHzsfyFjLmIAYopsnPyC0myGoJ75IfBBhJI3IxCIo67pAiEAyiY2GdKtVwqEkjpKZlp9sh03omlXPD7AglgzKMgMCLo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "0.2.2": {"name": "cookie", "version": "0.2.2", "devDependencies": {"mocha": "1.21.5", "istanbul": "0.3.20"}, "dist": {"shasum": "579ef8bc9b2d6f7e975a16bf4164d572e752e540", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.2.2.tgz", "integrity": "sha512-QT1/SH6oF6jrC9K4rlWpa/5FgqUZuh/Ohl4NvGAgSm67DsieBdTz/XsiVQwBKEJMnw7Tui5uBuC7k1yUAmPO2g==", "signatures": [{"sig": "MEYCIQD7f1pH5KyaghFHDU3Xdccvdt8DnoW4pZe8i7I3Tm8aIgIhALMkrHe5DuP/buJj/U6/ET0J1Xr4j02caX1JO+9wAg+E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "0.2.3": {"name": "cookie", "version": "0.2.3", "devDependencies": {"mocha": "1.21.5", "istanbul": "0.3.22"}, "dist": {"shasum": "1a59536af68537a21178a01346f87cb059d2ae5c", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.2.3.tgz", "integrity": "sha512-mnzsjgoobV+vxz57A5ezsr9gFB4y90Yqsu2Go95nNuO/WBLLPH43gNCHzqcXG++JcP339z6IAGVo0g4qBfo6dg==", "signatures": [{"sig": "MEYCIQCErcpABARWT9TD47UBtyb5CdBXrt9oM8beMG/IFU/gYAIhAJ3w8l62NOwuXgDjCnVfgAIeqoMKJuoRRQrIB+0uQAbN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "0.2.4": {"name": "cookie", "version": "0.2.4", "devDependencies": {"mocha": "1.21.5", "istanbul": "0.4.3"}, "dist": {"shasum": "a8c155aa7b9b2cf2c4d32ebc7b9a0aa288ccc6bd", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.2.4.tgz", "integrity": "sha512-wQLxYCPiulwnfcvEZHF8YVj6cxvkpOBFgN1nL3Ukgh+D1+4A1SUKHdxR7h+T9kcuC54mFWoeZdnLT7ZeIC9Emw==", "signatures": [{"sig": "MEQCIFd7QlvY9HrpU80uZlKOAV8rqWe1dyA5vgdnVabYJFLsAiATJ0Kni9GYp4N4e5GDUGfCceYIgjw0isSNVRGdahVtTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "0.3.0": {"name": "cookie", "version": "0.3.0", "devDependencies": {"mocha": "1.21.5", "istanbul": "0.4.3"}, "dist": {"shasum": "a4bdd609d86748a5ce6c64d7ede6f4840ba434d8", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.3.0.tgz", "integrity": "sha512-hPRNPncC/li8RRZh6m5gPYf6f4CrDZK9+JFLAhHAKAxABwRPCcOy98DQ0W1mvSQCL1LlyNI5MsR981n6EqHCZw==", "signatures": [{"sig": "MEUCIGhD+ZSRMtt5B/eMW/csD6/WWX98flradQ453TY3nKrVAiEAxcBP+nUeQSceuZ0F8p2yUwbS8eO+HBiSUpvEPjNSa+4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "0.3.1": {"name": "cookie", "version": "0.3.1", "devDependencies": {"mocha": "1.21.5", "istanbul": "0.4.3"}, "dist": {"shasum": "e7e0a1f9ef43b4c8ba925c5c5a96e806d16873bb", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.3.1.tgz", "integrity": "sha512-+IJOX0OqlHCszo2mBUq+SrEbCj6w7Kpffqx60zYbPTFaO4+yYgRjHwcZNpWvaTylDHaV7PPmBHzSecZiMhtPgw==", "signatures": [{"sig": "MEYCIQCgvOyvh7LyYbqRRfP+P1EWsK+Lihw9fWoempNpPWDXWAIhAPeJXOxCFkwj6kP3fd3Z6QuGtfKjMTN7OnaKjWkPjz1m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.6"}}, "0.4.0": {"name": "cookie", "version": "0.4.0", "devDependencies": {"mocha": "6.1.4", "eslint": "5.16.0", "istanbul": "0.4.5", "benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint-plugin-markdown": "1.0.0"}, "dist": {"shasum": "beb437e7022b3b6d49019d088665303ebe9c14ba", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.4.0.tgz", "fileCount": 5, "integrity": "sha512-+Hp8fLp57wnUSt0tY0tHEXh4voZRDnoIrZPqlo3DPiI4y9lwg/jqx+1Om94/W6ZaPDOUbnjOt/99w66zk+l1Xg==", "signatures": [{"sig": "MEUCICpL17EzZZiF4ZfyjmKXAdwEk2ngytW6WMM4zpa+8DakAiEAp5rStJSo+4RI2hj/KdFjVAbE3wQbuD8fCwOdLgJBAtQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc3NhPCRA9TVsSAnZWagAADA4P/0Y8HkoR7zm45iuEtjAd\nEfcXA4oFAC9rTJtAWNcmDGwTW1HxfIX3M9ci8WSjlwbEqWsvE6XJYq4yjhZq\ncwj7IRftR1aGaGusZPrBQTXnwqMvRKTFG4ZMwH/IeeoEtmBq9hJJZX8yXjNL\nZbaAq84JTiGriMb9kjS+sizB6dHMUrCN4SDTP6EUEAVuF6lIVrikG3G6i7am\nqXeFIVXJQFn31/MRV/258l6eOefuPTlgWqpiBBLSScxSOSsyLhW2+FTpZ1Ga\n8wqAhQVf0JATKrElCH5x2u2slNxOI8FltAwxfMFHzN/5Q2XlOFznqFqoO1l6\nEYF0NgJQZXBQuKWIAVIyqwBZ5fQ9+lhjKtVqCGbcw9I2U+TRK0eHMNDgI+pR\nebdSwAhVWGTp0o2ahqhQBW/CB+tPgIjgy0lPRg5ioPs9noBb6AZZ5H9I0Ffm\niWO1FsneQYbFL+2IM/P3rIQefwFTParMXRuq70XxuVcU+cTXNi4X8lzH8KiJ\ncnq3Pit6czof7+OvNBWQAIq++d6z1tMq+ELOqp7L0QogZl7OGI/nFtZQrTbn\n2VtTg4su5j/zkE350pwTXirnRkS/9AiignuLco2H9PspMxEadPnmmakwErZJ\n+KXAFLAHZhgxfa7meSbFFoYDarbpuizUzDDULbUM0murTVR2dAYZUnCByEoi\nvmIe\r\n=Oa0u\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}}, "0.4.1": {"name": "cookie", "version": "0.4.1", "devDependencies": {"nyc": "15.0.1", "mocha": "7.1.1", "eslint": "6.8.0", "benchmark": "2.1.4", "beautify-benchmark": "0.2.4", "eslint-plugin-markdown": "1.0.2"}, "dist": {"shasum": "afd713fe26ebd21ba95ceb61f9a8116e50a537d1", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.4.1.tgz", "fileCount": 5, "integrity": "sha512-ZwrFkGJxUR3EIoXtO+yVE69Eb7KlixbaeAWfBQB9vVsNn/o+Yw69gBWSSDK825hQNdN+wF8zELf3dFNl/kxkUA==", "signatures": [{"sig": "MEQCIFraquf5MROagEGJWcsDul6SQ0y3192R88zCxcqMwnDJAiA9NKcmoUVWFR2xGonLOLbEWPMNdss4Kbu5UN57NN7MEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 18123, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJen7jNCRA9TVsSAnZWagAAX/wP/j8Mcw3MQyifqWMFSZTL\nwpTBVC0/hRHh581NsIKtfF1fzWvRHKu+VPlGhMg1vqXoZweebf1H5Mwhlz7z\nDEiUiPQgCi6hXgdsUMxc+iDOFy2fQoPOKtY4LnfDA/vZMCok8QFLVWArl3CC\nLmA5Amh/Zfud0WkGzDGrjRQKWHRRAUVrQBZ6ziTM5KplDWy1PBSE59M3WYMY\nUFOdCEqk4xGEvW8YjkAkRSiY5Z8r8wcES4GkxaLsVFfpLJbI0cbRCen6b1uK\n4ZTGjwpnQKHPaywefIX1UTV8Em6D0QGweyc4CgTicMz+lPD9uZT8jmZr+Apy\nkcfen3enN1sKrn0e4p6AXOu1Ku38KeK0nLyU3ueo7/KzbAf8XVkbbsWdZ1Yr\nteOjmHDqv5k7B7616PSjyQJRmXhfB2FgR84MmyUbeEtOSStBaNPI7BxzA0Mj\nKvSfVDGxxO0dTb3Uu6QRF4tl8T2AFKarNzpik5VNvJpYsueISnGVG4J480YI\nz8L/Ayn3+BmW7GLtVU5nfXaMZBjGNryRRQFhju7tS0JOzbRgmUCRLK77NGUX\n+warQVVAXDJJgAVexSen90OLR+SSNq84HIBT9/UP70OLFxwndxB91XuDTuMO\nNot9PtZFUNZnv9ArYN/HGCRKDkJm98Gn0Pu5eJ4ITNA3iCE/urbSnVLzigSA\n11mU\r\n=IPIC\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}}, "0.4.2": {"name": "cookie", "version": "0.4.2", "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.0", "eslint": "7.32.0", "benchmark": "2.1.4", "top-sites": "1.1.85", "beautify-benchmark": "0.2.4", "eslint-plugin-markdown": "2.2.1"}, "dist": {"shasum": "0e41f24de5ecf317947c82fc789e06a884824432", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.4.2.tgz", "fileCount": 5, "integrity": "sha512-aSWTXFzaKWkvHO1Ny/s+ePFpvKsPnjc551iI41v3ny/ow6tBG5Vd+FuqGNhh1LxOmVzOlGUriIlOaokOvhaStA==", "signatures": [{"sig": "MEUCIQCc/iA6Ebuxw296YF1n3NkFRXNlpdCsRtXiSoWAkUtTywIgQkXiuyTi+kj2rS87pqz9kE90NYgAGaJO6h95ZnWVcHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+xPaCRA9TVsSAnZWagAAKtwQAIuzkn+ot2Nzsnc6ZRYJ\nr/e1MNEBVeNVpmIae1RuGJQhXV58BWbbzSIMpN5kULGPHy4rJY0Fe+WQn17g\nilJMiQKd6CzDbylJTZWOALx7zamDL3HVravvrRgpsjMcix4VM7/v0trzDppy\nCRXx1JO+f1PcMkcnYQhSTODYIzf3cAcP2TXIXG5ZhRT6h15MkbBRSB1XrcHN\nNlobt7M8aYeoSDIMd5aC5wTWeyfp98unQwVvLzC2k2KbUaC9+dvSrKLRoAEl\n5dGUJNhV52FO2fQOWLinu4WmGLnXw7gG3SA1s56esZblSLvYtQhFmRapgE2Q\nqMP/zhAOR8qUM+XEp8e++ht7Nn4Cv1Il8DG1hqIhX0KrtgNS5kY+NBvB5PQG\nDXYcAg8HEntQwkCclubr/cuF3JN7igbuue+4lF9bx/h2nMay7J9mJn55Xc3G\nI9CqI/4SVZfViBiYLU9XodfRzBNkIjZINoaZNIhFpLmbAuvr2GWSbu4BXyL8\nuCd5eCLjtqUaSApazorqpChMefB4Q8nAkXA5WYIPKNC5wNwmEy62V3zrYU+4\nuMB+7s/1kNH6CaU80TQkFi+YP0TwIAxvX9V7++8aIMyGAoFMv0bxIbUJQ5VQ\nqTe+ILLzvcIqqwomwz1m7FZg4t/8NZI7MLxQs9zsrjnpJthcQ4B7d7bQWOBX\nSEcc\r\n=yR/c\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}}, "0.5.0": {"name": "cookie", "version": "0.5.0", "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.2", "eslint": "7.32.0", "benchmark": "2.1.4", "top-sites": "1.1.97", "safe-buffer": "5.2.1", "beautify-benchmark": "0.2.4", "eslint-plugin-markdown": "2.2.1"}, "dist": {"shasum": "d1f5d71adec6558c58f389987c366aa47e994f8b", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.5.0.tgz", "fileCount": 6, "integrity": "sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==", "signatures": [{"sig": "MEQCIHTyGhjx8nRt+f1Wkf5RFsF80x/VnzKUBa6tr07jrRCHAiA/qDBmYZDmUd+wGBg6BX6wbiLq+PG5JhA7t8k2aVfPIw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVLptACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfQBAAmCi0G6edCjzcJvhorqbfM5Yfq2fTUyHQto5S49qphrYaZhAw\r\nVhmxClv2Uy0LFkdY/WMIsE9pihciNNiHfonfz4Eis+HxoUKaGYQHAayPGB/w\r\nGHK54F7ETV8dQrOao/YHrSjTiNvmXlBoMDym7M7GVBKCWw1XOSUDt393CGZJ\r\nbpppE/dsaTXwYdF1UillWZCyE1R0mYcMw837P19rrdnSA/tjjaq3fpfmZCEk\r\nEtXgq7PwDgKFtsap4NT67LcqnLQRBpY1VsK7O71qFWl7KN34haJTiGaBZIH6\r\ndzQIC9eU28IF+TqoR/zErnUy8d5NqEl4J1kSItKOuViuJz+T7EJFdLJ4aluB\r\n1FMnpGk4vGMz7fkJPJKGrZv4qraihUz/KOkGxbawVqcN+1AgwdbE3tX/dS97\r\nAlTRyZqCiBSkpQdMNeOnLhZSPHoEXsuKgmkSh8LFxCVzaRrt8VhnDizQrwPA\r\nv53ZChQnDNYh3wYSWHayt+SvFLG+waCByshO3B4LvkEHjmLrGhf9pvmikO5Z\r\naoCas8zVWrV5M0oCE/0cEcvKQOATMzvXJHWAceUvdCSjgC8bbVhr5sMoL60L\r\n83AmM3cCtL5RUomN2k4mcpvpOmQLXZkvXjv9jFt6uwaXsmbQu2jP72vXKfV8\r\n3oDJ66twILRP9sTA5NiBirpnWOgohtjCLfo=\r\n=xQmr\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.6"}}, "0.6.0": {"name": "cookie", "version": "0.6.0", "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.53.0", "benchmark": "2.1.4", "top-sites": "1.1.194", "safe-buffer": "5.2.1", "beautify-benchmark": "0.2.4", "eslint-plugin-markdown": "3.0.1"}, "dist": {"shasum": "2798b04b071b0ecbff0dbb62a505a8efa4e19051", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.6.0.tgz", "fileCount": 6, "integrity": "sha512-U71cyTamuh1CRNCfpGY6to28lxvNwPG4Guz/EVjgf3Jmzv0vlDp1atT9eS5dDjMYHucpHbWns6Lwf3BKz6svdw==", "signatures": [{"sig": "MEQCIEL2HouINUZMTIik3Y3nbvRXXwWmfFN4K7ptI3x8KlV4AiAJDsHTaKH4r35aCzLz9tnjrAATCN7WAW351QYb2GvkNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23736}, "engines": {"node": ">= 0.6"}}, "0.7.0": {"name": "cookie", "version": "0.7.0", "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.53.0", "benchmark": "2.1.4", "top-sites": "1.1.194", "safe-buffer": "5.2.1", "beautify-benchmark": "0.2.4", "eslint-plugin-markdown": "3.0.1"}, "dist": {"shasum": "2148f68a77245d5c2c0005d264bc3e08cfa0655d", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.7.0.tgz", "fileCount": 5, "integrity": "sha512-qCf+V4dtlNhSRXGAZatc1TasyFO6GjohcOul807YOb5ik3+kQSnb4d7iajeCL8QHaJ4uZEjCgiCJerKXwdRVlQ==", "signatures": [{"sig": "MEQCIG3V+4VnRNUAXH/4ujIS7bVCP4zbU7rhne7YZvr+1j7HAiA690soiDn7+r0NEwOnJ7ssi1ZBxVMpcYXQfhy1INprsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23253}, "engines": {"node": ">= 0.6"}}, "0.7.1": {"name": "cookie", "version": "0.7.1", "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.53.0", "benchmark": "2.1.4", "top-sites": "1.1.194", "safe-buffer": "5.2.1", "beautify-benchmark": "0.2.4", "eslint-plugin-markdown": "3.0.1"}, "dist": {"shasum": "2f73c42142d5d5cf71310a74fc4ae61670e5dbc9", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.7.1.tgz", "fileCount": 5, "integrity": "sha512-6DnInpx7SJ2AK3+CTUE/ZM0vWTUboZCegxhC2xiIydHR9jNuTAASBrfEpHhiGOZw/nX51bHt6YQl8jsGo4y/0w==", "signatures": [{"sig": "MEUCIDKL4lc0hvH9qowdRFxv5qw1rB0u+IxHTqUPE76emvk8AiEA+9e03z9xhRG3US5bb1Sbp8J4OJ2Scaa5p45AV4EXPxQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23319}, "engines": {"node": ">= 0.6"}}, "0.7.2": {"name": "cookie", "version": "0.7.2", "devDependencies": {"nyc": "15.1.0", "mocha": "10.2.0", "eslint": "8.53.0", "benchmark": "2.1.4", "top-sites": "1.1.194", "safe-buffer": "5.2.1", "beautify-benchmark": "0.2.4", "eslint-plugin-markdown": "3.0.1"}, "dist": {"shasum": "556369c472a2ba910f2979891b526b3436237ed7", "tarball": "https://registry.npmjs.org/cookie/-/cookie-0.7.2.tgz", "fileCount": 5, "integrity": "sha512-yki5XnKuf750l50uGTllt6kKILY4nQ1eNIQatoXEByZ5dWgnKqbnqmTrBE5B4N7lrMJKQ2ytWMiTO2o0v6Ew/w==", "signatures": [{"sig": "MEQCIDm2q0PZlFEgY8XPuCXZEtK9ipN/2FVDL23gDjKWfQrUAiAyqfSsVQIrmonFoOR0wyDolkwCbU/PyQaDeLT6zBKrRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23382}, "engines": {"node": ">= 0.6"}}, "1.0.0": {"name": "cookie", "version": "1.0.0", "devDependencies": {"vitest": "^2.1.2", "top-sites": "1.1.194", "typescript": "^5.6.2", "@vitest/coverage-v8": "^2.1.2", "@borderless/ts-scripts": "^0.15.0"}, "dist": {"shasum": "1eee6e952e3e06f79434699fe8370c48f3a5480b", "tarball": "https://registry.npmjs.org/cookie/-/cookie-1.0.0.tgz", "fileCount": 6, "integrity": "sha512-bsSztFoaR8bw9MlFCrTHzc1wOKCUKOBsbgFdoDilZDkETAOOjKSqV7L+EQLbTaylwvZasd9vM4MGKotJaUfSpA==", "signatures": [{"sig": "MEUCIQCSWO5E6xquddNmVD/5YhvZeEgZaPa0FtqfGib/x1xcegIgckiRbstZVH/vB/bvdAJL1fklCf5z4X8LW33NqTBx1QY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45181}, "engines": {"node": ">=18"}}, "1.0.1": {"name": "cookie", "version": "1.0.1", "devDependencies": {"vitest": "^2.1.2", "top-sites": "1.1.194", "typescript": "^5.6.2", "@vitest/coverage-v8": "^2.1.2", "@borderless/ts-scripts": "^0.15.0"}, "dist": {"shasum": "e1a00d20420e0266aff817815640289eef142751", "tarball": "https://registry.npmjs.org/cookie/-/cookie-1.0.1.tgz", "fileCount": 6, "integrity": "sha512-Xd8lFX4LM9QEEwxQpF9J9NTUh8pmdJO0cyRJhFiDoLTk2eH8FXlRv2IFGYVadZpqI3j8fhNrSdKCeYPxiAhLXw==", "signatures": [{"sig": "MEYCIQDVTuh4DamUmCfnuSBittKylpinhjLm9a9QxLsf4feZOwIhAIXyoaPvc1c8JvB5sD9bxYHhtqA/3xhEfNQAqat9k9cp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45951}, "engines": {"node": ">=18"}}, "1.0.2": {"name": "cookie", "version": "1.0.2", "devDependencies": {"vitest": "^2.1.2", "top-sites": "1.1.194", "typescript": "^5.6.2", "@vitest/coverage-v8": "^2.1.2", "@borderless/ts-scripts": "^0.15.0"}, "dist": {"shasum": "27360701532116bd3f1f9416929d176afe1e4610", "tarball": "https://registry.npmjs.org/cookie/-/cookie-1.0.2.tgz", "fileCount": 6, "integrity": "sha512-9Kr/j4O16ISv8zBBhJoi4bXOYNTkFLOqSL3UDB0njXxCXNezjeyVrJyGOWtgfs/q2km1gwBcfH8q1yEGoMYunA==", "signatures": [{"sig": "MEUCIFlKqps2ND6I4CX3YDlEtWCW8vPlgDkWJ/ziBqPKA8vsAiEAu5TRmr/L4SkTfRNslDFoy38yNtn1Tn3MIhIEQ7uTnp0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46521}, "engines": {"node": ">=18"}}}, "modified": "2025-05-14T14:55:58.052Z"}