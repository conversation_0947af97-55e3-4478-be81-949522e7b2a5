{"name": "serve-static", "dist-tags": {"next": "2.1.0", "latest": "2.2.0"}, "versions": {"1.0.0": {"name": "serve-static", "version": "1.0.0", "dependencies": {"send": "0.1.4"}, "devDependencies": {"mocha": "^1.17.0", "should": "^3.0.0", "connect": "^2.13.0", "supertest": "~0.9.0"}, "dist": {"shasum": "98efa31e6ae767b233bc44c77bd29140b2d31c6f", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.0.0.tgz", "integrity": "sha512-IZh31Mm/Y2i03KU0+nJYyjIXwhsmkM3vWH/5eIINdhm/5F55qJUrqtFPF8qTTNxZeHAISWYpWeVk16MIgQvztw==", "signatures": [{"sig": "MEYCIQDtWPJQoqcWygQ0DkKNB0syEWcxWi0cQkiPnRuqsCpQIwIhAKZFtLVzynrvFySGEAjQencftehvF6cOlsfud/mbZTXY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.0.1": {"name": "serve-static", "version": "1.0.1", "dependencies": {"send": "0.1.4"}, "devDependencies": {"mocha": "^1.17.0", "should": "^3.0.0", "connect": "^2.13.0", "supertest": "~0.9.0"}, "dist": {"shasum": "10dcbfd44b3e0291a131fc9ab4ab25a9f5a78a42", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.0.1.tgz", "integrity": "sha512-bo0TWkZYykHO97QfRgoaXQQBBmyheAb3MeYFzufTXDHUdaTwJXFa8NejuKbt7UdovoUzB8lJn4gHGQSEC+R4Nw==", "signatures": [{"sig": "MEQCIBSWSDdjtDAEIn94ZWC2IcrFEMKwAdybrYaLUA9Cjz7eAiAygYe1lcyGyDtdd0uvsacfsFyCo1PuuuuIbaOtgRdPWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.0.2": {"name": "serve-static", "version": "1.0.2", "dependencies": {"send": "0.2.0"}, "devDependencies": {"mocha": "^1.17.0", "should": "^3.0.0", "connect": "^2.13.0", "supertest": "~0.9.0"}, "dist": {"shasum": "4129f6727b09fb031134fa6d185683e30bfbef54", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.0.2.tgz", "integrity": "sha512-Q6kwHQK6vejIQnc9PCLGMzXpdjmzC77d/Z2zgHDcYH8sX2fgh6wsQQL5IkAOwvqQ1+JSgpB4EX4T0L7oRtMZyQ==", "signatures": [{"sig": "MEYCIQCXrQ2vDX40fYbmP+SX24GF+Lo6DKoBwr5u7BxO66f9yQIhAP74gWyn2od6LYq08kZ3+/XLZj+uLOnGu0F5ztK8qG9b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.0.3": {"name": "serve-static", "version": "1.0.3", "dependencies": {"send": "0.2.0"}, "devDependencies": {"mocha": "~1.17.1", "should": "~3.1.3", "connect": "~2.14.1", "supertest": "~0.9.0"}, "dist": {"shasum": "3443a4002fb50d7fa0a777bb53103301e4d0c38a", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.0.3.tgz", "integrity": "sha512-8zIPP17hI1R1Clti8ILPeknj8W4Uj09Iiamt7zDDCAckogp9hqGezDdJMIzjuw34w23tc7icCFiEGSNCRJBtnQ==", "signatures": [{"sig": "MEYCIQDjZT1EhgH80e/8s+dcvkOPo6JK1Py8BwgMNXcRhnxJnwIhANUL9se3AdTX92vpocEXyF+Nyf3uLDaUjQssROQ0/CyZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.0.4": {"name": "serve-static", "version": "1.0.4", "dependencies": {"send": "0.2.0", "parseurl": "1.0.1"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.0", "connect": "~2.14.1", "supertest": "~0.10.0"}, "dist": {"shasum": "426fedebe77bad21f373f1efcae09746639fba06", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.0.4.tgz", "integrity": "sha512-HRe4CmpyO+OdhZDNhmCAU8AVTF4Ed13cRicmREPgYcWN3Yy/tr4brktDzW1M0omtv2ga+tgnb5hivkBV4MIEWw==", "signatures": [{"sig": "MEUCIBjLnQre205qBoAkMruAaL4i0+zh53nPpwdlTQBqVl/+AiEA62P1hM4v7U3LXAMMdzo9EdNEff91Km9w3vHgrXxLYYk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.1.0": {"name": "serve-static", "version": "1.1.0", "dependencies": {"send": "0.3.0", "parseurl": "1.0.1"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.0", "connect": "~2.14.1", "supertest": "~0.11.0"}, "dist": {"shasum": "454dfa05bb3ddd4e701a8915b83a278aa91c5643", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.1.0.tgz", "integrity": "sha512-vzgWiHz5xrM19pqugiYI6sWP9B0+K6vz4Ep5G1my9lVhuYkRXGYs5xtnXZ06fpLPRumROSZ1CLqiRxdngPkojQ==", "signatures": [{"sig": "MEUCIQC4/eXwKvFn7mikgl4k90AtbDmXzYfbjAh2AntTcfInpQIgfFtn6+LFnHbxtJbThmmbnaP0BJKkYlooDS7RtOPu90g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.2.0": {"name": "serve-static", "version": "1.2.0", "dependencies": {"send": "0.4.0", "parseurl": "1.0.1"}, "devDependencies": {"mocha": "~1.18.2", "should": "~3.3.0", "connect": "~2.14.1", "supertest": "~0.11.0"}, "dist": {"shasum": "b711bde722cad70686c1add385c6020bcdb7d295", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.2.0.tgz", "integrity": "sha512-C7sge0nUxEukR6en8ADwydHwDYt/yqesodzlE2rz/+9srd/icjT1yeLQZI4A59ygbrWNB1dhLgwfMbUbpB0zEQ==", "signatures": [{"sig": "MEUCIQDlggJSHjaLZTAqT2hnz7WF8mBUCU7wUAJud+MWyyn8MAIgJd8XxiBsQ0XJ8yhM9EnOG0D/BrHk+rgxiwuVvIXIrsY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.2.1": {"name": "serve-static", "version": "1.2.1", "dependencies": {"send": "0.4.1", "parseurl": "1.0.1", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0"}, "dist": {"shasum": "a800a9de23dbd1ffb1258edb986128ee4a4ea03d", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.2.1.tgz", "integrity": "sha512-R<PERSON>r<PERSON>emuggK5mxL31rrJLTVDRFWdRREVLdHsR3nPHA/zxH6Mm/4I595LOBO84PE1YjbC/hTKi3ZqfyXPhBdYjpw==", "signatures": [{"sig": "MEUCIQCjizFJYVbv8Lyx+gMlq6lmXffkUmqA1RZFitxZhhO5QwIgeQz2smWxfVi0xNosu3A2iACyn3lwyLGkMr1FrxhbOCs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.2.2": {"name": "serve-static", "version": "1.2.2", "dependencies": {"send": "0.4.2", "parseurl": "1.0.1", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0"}, "dist": {"shasum": "6ffc6c23fad03bcd0710eceda844123bd71bc951", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.2.2.tgz", "integrity": "sha512-QVxS4GoyrLCl2w5rp4Cs3CqT/QnREwKPAsy+aZCG5P9qwLa3GIXoxxzNvsgGfvngtl/begF+UiRgnpDzyiqWkA==", "signatures": [{"sig": "MEQCIC4Prc2pHkTAhjVw9Uqs3tKVCzbGVt3eVuXXrkL52/XWAiA/abr9V2Pzrp3bpM3RR3qHwMvmU+rK2LsSgC0tYS8rkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.2.3": {"name": "serve-static", "version": "1.2.3", "dependencies": {"send": "0.4.3", "parseurl": "1.0.1", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0"}, "dist": {"shasum": "93cecbc340f079ecb8589281d1dc31c26c0cd158", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.2.3.tgz", "integrity": "sha512-xaOEJYYnhmT2iVnDHcPullns+dFGC18BHseW1ZzkddtPWe4Ot/ZdifPFYk14r+tdWpVNWtXClRRENQ9ODd1Eeg==", "signatures": [{"sig": "MEUCIBm/DS7wOedseXrvBxUwL0yLQkUSXTTkZeawRS2spXEAAiEAz6HEw8crzv8+PjAgz+ftdo5Jr0cst67V6F/lTf/X/4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.3.0": {"name": "serve-static", "version": "1.3.0", "dependencies": {"send": "0.5.0", "parseurl": "1.0.1", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.2.13", "supertest": "~0.13.0"}, "dist": {"shasum": "0aba0b27c1b8264eee1a3f9c615886738d9727cb", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.3.0.tgz", "integrity": "sha512-MzfQemPCtA1VU/fj2c1Qja1V6cPaqc4nfYRDhhdgJiZOXCu1pr1zaYiXA7jBXbDk0I2aEA+AzzSiMdat2SMPyw==", "signatures": [{"sig": "MEYCIQCs7QJU56qegkJI41xZPxJKBUw8bjyaQVqgwluvhecH2QIhAPIe9P9IleWb3w+kYYyhWdZ99uQ9QvWdrgDiXFolLniP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.3.1": {"name": "serve-static", "version": "1.3.1", "dependencies": {"send": "0.5.0", "parseurl": "~1.1.3", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "95489d1bcf491d54350d5aeeb2cca53cd3b12d4f", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.3.1.tgz", "integrity": "sha512-E8AezYuv/NZau8nb5PxjMa8Wch19/ikIKJuguze18uSgL28ZUZtmguer3ACrbpwDkD03ZULrBoz2i/3nt/q3Xw==", "signatures": [{"sig": "MEUCIDr+NYNcUdsY5t+U1fEDsvZpVeJd65KpdzNS6x1pHZLaAiEA1VxMJ3GzphBO0ChaLGs5PIIqEmBJNKxSLQI1tr8ETrw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.3.2": {"name": "serve-static", "version": "1.3.2", "dependencies": {"send": "0.6.0", "parseurl": "~1.1.3", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "d904a6cbf55f511c78138f6f45ee6e69d9d105ca", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.3.2.tgz", "integrity": "sha512-KwjCeYUx7IM1neg8/P0+O1DZsl76XcOSuV0ZxrI0r60vwGlcjMjKOYCK/OFLJy/a2CFuIyAa/x0PuQ0yuG+IgQ==", "signatures": [{"sig": "MEUCIQDQpGzkAev8vCx7+/v3ioHLoE49CMhQ3R1JGyLM+y+tBgIgGZjgxA1J6YQphuuIz/cNXmCOT0FG5ZhYMPzN5PLpKV0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.4.0": {"name": "serve-static", "version": "1.4.0", "dependencies": {"send": "0.7.0", "parseurl": "~1.2.0", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "03c6608035158e3bb999129d9793cddc7e0db772", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.4.0.tgz", "integrity": "sha512-6hk7O29Y7fPquVCY541E4I1cALtAzc1Rx2/K0tHduYMERnFeIGkpi8BZ3tvQK270cxdq6sXrIk5nno3rEilSdg==", "signatures": [{"sig": "MEQCIHLZ+7RKtam2U+CUhuky+kxbon+c/5oqd2eHTKzrQORMAiB+7SLcNjsLKtBmkTR1y4PlMfdtGHcJUSn3yZeJaSlQZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.4.1": {"name": "serve-static", "version": "1.4.1", "dependencies": {"send": "0.7.1", "parseurl": "~1.2.0", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "6814dc11c575db0394883af5ec2202ff989491b6", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.4.1.tgz", "integrity": "sha512-7/19DczZAYS/IIGo/32S51h5U2+qhiqHrs68Chw56PS2mBtsE2n/AYl0niDdxmrnrBibrf4qJpgtSKiPpB3uug==", "signatures": [{"sig": "MEUCIQD2wtwY7aRXJCCQ6YSwvp6M9e7PaUw3koDipxE/KSDoNwIgOCVyWhpj8Jo7n07yYp9DVPgwYzPo/rIZLCnGBC3KcHQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.4.2": {"name": "serve-static", "version": "1.4.2", "dependencies": {"send": "0.7.2", "parseurl": "~1.2.0", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "0153b12368318402827aad902d0f124e79145092", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.4.2.tgz", "integrity": "sha512-oAN8Kjy/kwdqs7XeRUt7iU0b1eLiBSDMRh9EVbrg+WSZNLLNZfNXunlQ6wJF8uRWCIF4afV8ITcL6hzeu+c6vA==", "signatures": [{"sig": "MEUCIGkjG5fUJ5Tw5tNNaufSEvSDB0E8lJxRSZDJcMDSsmj6AiEA2vuJTT0BmLLZD7B3a/soOHuPlW19i283KHhrYg8zoG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.4.3": {"name": "serve-static", "version": "1.4.3", "dependencies": {"send": "0.7.3", "parseurl": "~1.2.0", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "9f08c7dea1b15e2eb1382ae0e12b8a0de295de52", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.4.3.tgz", "integrity": "sha512-CPLHZOJB7QVLWUaKVhp8Z59m1pPGpps1ixRnxjcPMVpJbAkiivpdeW/kIxZwWhJ8AZC07iax/Ga/+s9XyOqn+A==", "signatures": [{"sig": "MEUCIQD7pz3Rk0ip6sscnw/SHZUmvoKRNkVgSLnHJFisPFwRtgIgDdaqehDYfpX1MOsIG0+2/S6rPHNrBQcfGTxuElr3xhM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.4.4": {"name": "serve-static", "version": "1.4.4", "dependencies": {"send": "0.7.4", "parseurl": "~1.2.0", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "9dc99f37a2c5e28cda2fe6045114620a62032f29", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.4.4.tgz", "integrity": "sha512-pxuoykDJ4REf20MFjVk/RHPvogr1dSiNSd0YJU5GjBM4/tkaQV1sjm5h/tfRvrQwbn6comfRKu81uUeiQahumg==", "signatures": [{"sig": "MEQCIEQk/PKfP0EeDQ2tRcgxRw4K4ET3ZwKemQ158o/tbFumAiBDLyrABL/iO5mtzSp9vD1lt34NxPS/vIPsXpEKv3JDpg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.5.0": {"name": "serve-static", "version": "1.5.0", "dependencies": {"send": "0.8.1", "parseurl": "~1.2.0", "escape-html": "1.0.1"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "c0f19e3cb9bef0203258db282a3ddda9cb8e675c", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.5.0.tgz", "integrity": "sha512-2xTDUmQCotD/MSVSYK7D5zgah4skqwGI/Awq63MI1tsOCwVbZ7zrpL9qrGRLPpWkfhN6jxh6OVPCgbwA3I+PBA==", "signatures": [{"sig": "MEYCIQD62MbEOP60UZyPrDqSZC2fGcybsPV4nfJZgNd2SrsLOAIhAOTJoOOKGQJ/duvN+8D8B8djycEUZtET9UrGxeFez7Vm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.5.1": {"name": "serve-static", "version": "1.5.1", "dependencies": {"send": "0.8.1", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "86185e202015641a1f962447f5695605cd8aa9c2", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.5.1.tgz", "integrity": "sha512-FifcURE12EyFDTjyuNhig8P7miiZwGUpr6H7Wn0FEn9jI7CpB0ITMFplDxH/HEIhkeMgOR63xfdhE7wGOzcucQ==", "signatures": [{"sig": "MEQCIDlAdlIpoK7ZyknyzzNCZ2SzVuLNv1N0oWj8FDrI4lRBAiAxJlGpRPTZqqcOq2Dpg9CZ/7X2hSl4onDmKUjGHMckug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.5.2": {"name": "serve-static", "version": "1.5.2", "dependencies": {"send": "0.8.2", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "565d369193a075edac7fa973550d88df154f7b66", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.5.2.tgz", "integrity": "sha512-tVKWRScaHUvXsCYhIpf5Qe6ieGhogvh6f8wc+D+P11bly6Gqlh0VM1eMzoZvZfjHu6w8ZQyLecDsoZwWRrmZRQ==", "signatures": [{"sig": "MEQCIBozQnR27YZtrRlwxrpSUok3jfAQLXYiEHEk3vwJtvkKAiBWA6X6PkXknzG1dB9yt3Mwno8/B/jBmQCuWuXGQDQ0yg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.5.3": {"name": "serve-static", "version": "1.5.3", "dependencies": {"send": "0.8.3", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "2e28efa5899686fd3ccdb97a80aa464002244581", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.5.3.tgz", "integrity": "sha512-4K9GvOWb3zKUT6W98vplGGI0Ea5xh1JnD4T8Pyd+61OwQ/HLszGxNgj7qoMY0fGmo1WJKbp/OEHkOBP69VlfaQ==", "signatures": [{"sig": "MEYCIQD39h1k3+1GsbArvPKYOtT6fQBte4QTK9seZHKJTKKKUAIhAKBf2VtPlYmRt2kgLEUkpJcPHVo0tQ8pZ2pzy2RUi77W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.5.4": {"name": "serve-static", "version": "1.5.4", "dependencies": {"send": "0.8.5", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0"}, "dist": {"shasum": "819fb37ae46bd02dd520b77fcf7fd8f5112f9782", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.5.4.tgz", "integrity": "sha512-6dxbEeZO39/rL80vlKRi6OatwVN+f2cAou95AVKOQbMKuZ9ln2rDMjjRQN6tu7u9daaaDsU1+hL4NW7zrSMzZA==", "signatures": [{"sig": "MEQCIBK1j5OIPSYLNSHiQ7Eo2l1QzdP/zVc2M7cJSW7NuuuvAiBVSGKP+54FwBorSnbADgzGvBcdvkG7GMG/eykzDohaZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.6.0": {"name": "serve-static", "version": "1.6.0", "dependencies": {"send": "0.9.0", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0"}, "dist": {"shasum": "283f43b9051293691ab4979bf2e09b4482517677", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.6.0.tgz", "integrity": "sha512-gEzzllPQwCxyS4IqlQElF0JLrjaAQuyW7MHgSp7UIWaCznTLyVdLZvq3EYN9sMwXnMDZb+fv+IUfNMqlUAm9iw==", "signatures": [{"sig": "MEUCIQD7MiL7uP4YXjlv6bxKUeeLG4RJ6flCGpdOsHWSvW8GdgIgEY+FeuqVztLR7JmivREi2IuEgvOzwLjcxhn6d6wKdbk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.6.1": {"name": "serve-static", "version": "1.6.1", "dependencies": {"send": "0.9.1", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0"}, "dist": {"shasum": "2f257563afbe931d28cee4aa3dfeddc975a87193", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.6.1.tgz", "integrity": "sha512-egcgtzipGNDCWaUpxNlNw2gzSFac4s+5K1q42xfnrMeCP2IVyWR/LECrSwJ77TSvX9TYqLOPN24xSvE6Wt/wqg==", "signatures": [{"sig": "MEUCIQCjwhYPFYohwKyntP4OhBcBGQsLloX7o8G9BY4y0alouQIgQ29uBnwamwOeTpwwp9bpedv7IZlNdfDZUdHwCxFMT6M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.6.2": {"name": "serve-static", "version": "1.6.2", "dependencies": {"send": "0.9.2", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0"}, "dist": {"shasum": "c1390ff43941867250296b091391d25be7c87571", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.6.2.tgz", "integrity": "sha512-ve7so5UBrn1xrWYsANfaRxj2FSvJqs4nd1zyi9fmYCLHdhR8wKiUZWPRlUZBuxAh4/rXAd3wJMLrTqePie/l2Q==", "signatures": [{"sig": "MEUCIDXXbgLPQtthMjUA+GObbuRuyavlzZzoNb5b2M78yJjYAiEAoMaU1XTJcvux/Z7NFDQ8GnfnAW8z4KLmOtbuOUzwlOA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.6.3": {"name": "serve-static", "version": "1.6.3", "dependencies": {"send": "0.9.3", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0"}, "dist": {"shasum": "b214235d4d4516db050ea9f7b429b46212e79132", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.6.3.tgz", "integrity": "sha512-W1gpJoF2Dq98KXDEmwujmsIBN2v9fwPTeU8Inf364RYHVgvHlHHPpzd7OMMO0LkqVE0OtivjQKKJjx+Q5qmvaw==", "signatures": [{"sig": "MEUCIEIf7IK87Chw+iCL3b/Pi36E2q5+mb7X5fze4ImNfZTSAiEAzjVMdT2/DQsAaSB+/ZWOBM6SOphwJCIGUZzMhJtmLag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.6.4": {"name": "serve-static", "version": "1.6.4", "dependencies": {"send": "0.9.3", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.14.0"}, "dist": {"shasum": "c512e4188d7a9366672db24e40d294f0c6212367", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.6.4.tgz", "integrity": "sha512-etjjC5zMPVQSsUHwO8iFPgmpPDJzZOJAgYzGzuYfkgcJe3qTWGVOqAK5OQ2R6oNfLdwIlhlu4AhZs/7AGcZjJQ==", "signatures": [{"sig": "MEQCIAb67fhMkGjqqRtVr5zRohgXpsOdsksTCpJSkWZthe/SAiBfwyuJzCSS06tCVcPMLnyUod4yHUWHv4snsed3tob6mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.7.0": {"name": "serve-static", "version": "1.7.0", "dependencies": {"send": "0.10.0", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.5", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.14.0"}, "dist": {"shasum": "af2ad4e619fa2d46dcd19dd59e3b034c92510e4d", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.7.0.tgz", "integrity": "sha512-Hm9zbsZX98qckeJDfebqL651kfXr6qU5wOZnBcLR9tvj+EA04hvOX5EiDAq5XGtprK/xNBz1e+RdZczihvnV7A==", "signatures": [{"sig": "MEUCIE9Ra+MwxByYfzjK6ezetWjMG46sukgAVtY+LAm0sH1jAiEA55hijZ+06hZBzCb9rLv547QAJHFSBIZm8l6K2bYMpdc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.7.1": {"name": "serve-static", "version": "1.7.1", "dependencies": {"send": "0.10.1", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~2.0.0", "should": "~4.1.0", "istanbul": "0.3.2", "supertest": "~0.14.0"}, "dist": {"shasum": "6ea54d5ba7ef563f00e5fad25d0e4f5307e9809b", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.7.1.tgz", "integrity": "sha512-LOjpb+p1TgwkUFZ/bChRfxB5ZcmuITZGJQ+D3e5u8hjg4vuiIld78E9tJioDgfRoYQc3YKg1Ue88Y0hyflzpGg==", "signatures": [{"sig": "MEUCICxxvIyDC6tnqZQf7K9H5o3lZ6cuAimJPncTWkTNHHgkAiEAhHRwXu0+TRuoRUpNV4skQYRAA8UKSzjT27MYl+HUlGQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.7.2": {"name": "serve-static", "version": "1.7.2", "dependencies": {"send": "0.10.1", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~2.1.0", "istanbul": "0.3.5", "supertest": "~0.15.0"}, "dist": {"shasum": "3164ce06d4e6c3459bdcc9d6018fb4fb35e84b39", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.7.2.tgz", "integrity": "sha512-/PaWqZFkjFYXqGLOs6nc0ameuFkzzU0MrdKt6oxdv7EZ7Dx2/X2LKMu7VZs8/oHt2//+bBI2z+Stq0EE0F9z1g==", "signatures": [{"sig": "MEUCIQDQj2/slHHIc7lQCR36xOVmpjUIgUHRXC4fqwn/tiWkVwIgFxm4Cpn2FKBC8IVfKyoGbIEqE9KkFmQIjmpXUk+JULI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.8.0": {"name": "serve-static", "version": "1.8.0", "dependencies": {"send": "0.11.0", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~2.1.0", "istanbul": "0.3.5", "supertest": "~0.15.0"}, "dist": {"shasum": "239e57bbfce030a8933d274e3fe7b55492ea267c", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.8.0.tgz", "integrity": "sha512-Omtl+YSv9qXPn6FZbWPBEFSFmEGEZ1ljyoOHypj6WilV9GfXUxcPJbXy5H6ZHQtBKJyFE0fO9SiPECIRbJ7S3w==", "signatures": [{"sig": "MEQCIDFy7nE2TvbDoyMy8mglyGfiGH9bDtuX5Yf343JWoh48AiAxklWgu/m+qgmIOIUsWyxetoE1Jspw8Q9mzefwFOVduw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.8.1": {"name": "serve-static", "version": "1.8.1", "dependencies": {"send": "0.11.1", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~2.1.0", "istanbul": "0.3.5", "supertest": "~0.15.0"}, "dist": {"shasum": "08fabd39999f050fc311443f46d5888a77ecfc7c", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.8.1.tgz", "integrity": "sha512-6VxXIbQ64SVDQ+tcPHuibd5NUI23jRY0BQhafagbv1wahH5LDslYIpJtYl/KQrRSu1U7ax54+gWFm9yrxqLBiw==", "signatures": [{"sig": "MEQCIDZp7njAs52F494hMNNeGb/cYHfCe2oYk+aLGdmIX4r4AiBvrPtFtCzw+GgN/M0XkCgSiZh9ok++DOPu436Fywaz2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.6.5": {"name": "serve-static", "version": "1.6.5", "dependencies": {"send": "0.9.3", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.14.0"}, "dist": {"shasum": "aca17e0deac4a87729f6078781b7d27f63aa3d9c", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.6.5.tgz", "integrity": "sha512-3G68ufF2viLyU6obD2dBivUFwkQdQdYVA1Luj/1UTAZOUcaMZzWsmg5y3jgVErY+/QzodH2h7B2+d6SFGMX4Ig==", "signatures": [{"sig": "MEYCIQDz3Plz0zbbaPVBCDpe7WQRxOz4ETc1/yk78CRe4MWJWQIhAIs9bGV3s7VbF1ykG6ZSEy4izI4LJ1vmkeMAUjaXNWeI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.9.0": {"name": "serve-static", "version": "1.9.0", "dependencies": {"send": "0.12.0", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~2.1.0", "istanbul": "0.3.5", "supertest": "~0.15.0"}, "dist": {"shasum": "d304085813ee0a9b3e1c068c9062a56ad8424b44", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.9.0.tgz", "integrity": "sha512-KweArFzuxsvl0yh9yF9aU1Ob9bMw6/wO1trMyZIN9N9jl+SCjhthdsoT6ani90YncFO8l5m6m4sEzlEKF26LKg==", "signatures": [{"sig": "MEYCIQDw+a00fZGlZDkePKqpCPKGZx9B78OlxBM2L9zh1wF9fAIhAI5M36stKGIVMMX5UasFQ2SoyHEvc/uvg6B1hYZz5RgU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.9.1": {"name": "serve-static", "version": "1.9.1", "dependencies": {"send": "0.12.1", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~2.1.0", "istanbul": "0.3.5", "supertest": "~0.15.0"}, "dist": {"shasum": "a611b2b8a2cfb5f89685f293cb365f3f5eb61451", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.9.1.tgz", "integrity": "sha512-VHoWApCVs9yhKtgyigJzhOsnUuC6hdKCZcLZMGlf30E0PV/W7V/Ee0l3OmIZ+XBkYS+ZuuFdPpluhz0cEb4K1g==", "signatures": [{"sig": "MEYCIQDJID0wg6Qnjz9j/VmNLx/aARCTqbBhJrNF3SE7ZwJK9AIhAJb8oAqB6sPG0xN59boy1ZwlSUh+C1ef2R/hnogzTIxV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.9.2": {"name": "serve-static", "version": "1.9.2", "dependencies": {"send": "0.12.2", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "~2.2.1", "istanbul": "0.3.7", "supertest": "~0.15.0"}, "dist": {"shasum": "069fa32453557b218ec2e39140c82d8905d5672c", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.9.2.tgz", "integrity": "sha512-TZZCQzKNgIt+c3KGhWOVUYewLFFg8fNLKjDTBb9LE7TP/gELWj5RLSakxxNGcw7FrP1FFz+oJlE0WsHRXJHkRQ==", "signatures": [{"sig": "MEUCIQD1PrEi4Xx74BBRy4obwaRmA6FjQf3J7cnEYi0lHuB7VgIgfUDIRbeUw6aEuyopPpImrO6gB5qon9gMLcizHyjZvVI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.9.3": {"name": "serve-static", "version": "1.9.3", "dependencies": {"send": "0.12.3", "parseurl": "~1.3.0", "escape-html": "1.0.1", "utils-merge": "1.0.0"}, "devDependencies": {"mocha": "2.2.5", "istanbul": "0.3.9", "supertest": "1.0.1"}, "dist": {"shasum": "5f8da07323ad385ff3dc541f1a7917b2e436eb57", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.9.3.tgz", "integrity": "sha512-RzgLgiNjRMhvdnLWKYJtr/QZ3q8jkDv10loWizQMh53Zlmd2jId5PtarLJO9Z6RtQJ/VcZYkvMOIDTOy86h5qw==", "signatures": [{"sig": "MEUCIHMEDTWCSbFKAUlfSMLYnavVY9dE16koF45ikBINkOA6AiEAsLVVmUD/DygnSGXrM3c78NupFen/U1aUkapQf09IFUo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.10.0": {"name": "serve-static", "version": "1.10.0", "dependencies": {"send": "0.13.0", "parseurl": "~1.3.0", "escape-html": "1.0.2"}, "devDependencies": {"mocha": "2.2.5", "istanbul": "0.3.9", "supertest": "1.0.1"}, "dist": {"shasum": "be632faa685820e4a43ed3df1379135cc4f370d7", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.10.0.tgz", "integrity": "sha512-5z+SPxV9MbA/HsSiCmDgiyVdvtFHMJr22cvVVBBrXR9tJpUEuPNc+T1AiUyX/MOsCMZkar4lIvjNKRnzS9dB/w==", "signatures": [{"sig": "MEQCIQC6RiQZspYItWsPG7RGaQR10Vz5o8WX748cIX7chveD6wIfBULi90u0Dp936RzhDgVoDWtys2yk7i4iIio+qu4AXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.10.1": {"name": "serve-static", "version": "1.10.1", "dependencies": {"send": "0.13.1", "parseurl": "~1.3.0", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.3.4", "istanbul": "0.4.2", "supertest": "1.1.0"}, "dist": {"shasum": "7f80024368d7fcd7975d0c38844ec5d9b2c43ac4", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.10.1.tgz", "integrity": "sha512-YBqObpazWsQrMrqIMh4eup8Ht7SqN9eEpk2bJ74pGoGT+3ARM0Pi6HyKCuoPclhtd19vm3Bo6zKprX+jLkXcZg==", "signatures": [{"sig": "MEYCIQDSrGLH23UgbGBsIpMhkSB/vv4Pm48gK84Wr4dVvPdqYgIhAOIw76QYYXxECNhkUiu7MG8QHlEGruyNe8xYmLx41gFg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.10.2": {"name": "serve-static", "version": "1.10.2", "dependencies": {"send": "0.13.1", "parseurl": "~1.3.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.3.4", "istanbul": "0.4.2", "supertest": "1.1.0"}, "dist": {"shasum": "feb800d0e722124dd0b00333160c16e9caa8bcb3", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.10.2.tgz", "integrity": "sha512-s6sZsL6UwRJqhLRxeHQr/YClnm5p+5/AVW21LCnl1oiWI1j/kvX4jfK1Cf3g8XbGE81Qq16DW8ZUr3zCqwVd1Q==", "signatures": [{"sig": "MEYCIQC/g78P2nxecV7VXSoVk6rGrYVAwX/aq4rlVsIqJKLrKgIhAJCrt/Pbh/ie+22hPyXGOUHV/eWDNECNpoMUjwy2tP7+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.10.3": {"name": "serve-static", "version": "1.10.3", "dependencies": {"send": "0.13.2", "parseurl": "~1.3.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "2.11.1", "istanbul": "0.4.3", "supertest": "1.1.0", "eslint-plugin-promise": "1.3.1", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "dist": {"shasum": "ce5a6ecd3101fed5ec09827dac22a9c29bfb0535", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.10.3.tgz", "integrity": "sha512-ScsFovjz3Db+vGgpofR/U8p8UULEcGV9akqyo8TQ1mMnjcxemE7Y5Muo+dvy3tJLY/doY2v1H61eCBMYGmwfrA==", "signatures": [{"sig": "MEQCID3sMIzzWZXIENVKgLASisr5mtoF2pMVdZ0R1JhCyHL6AiABY5F0iSNykl9eCMhhTH3wNvlKFQB69is1+zu2A9VXvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.11.0": {"name": "serve-static", "version": "1.11.0", "dependencies": {"send": "0.14.0", "parseurl": "~1.3.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "2.11.1", "istanbul": "0.4.3", "supertest": "1.1.0", "eslint-plugin-promise": "1.3.2", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "dist": {"shasum": "dbe5fb4e4b63d4d11a824b5be3f368907e675bba", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.11.0.tgz", "integrity": "sha512-6QNWmFLt0GBpn4f5FQ3a3AsNmOM7D3IrXsiLDoYK/SJVthOP7MSd8VhQJzKROFIwWIl8iRj/JPL90Hy0f3RZzw==", "signatures": [{"sig": "MEUCIGcg2tVXPYzltZGWpxnIdqWy6yLFwPqw589UU68baj1HAiEAqZ/jE5Wc/gObIuk+GjL0EZhwWAaRBBDBz0dmLLcrrMc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.11.1": {"name": "serve-static", "version": "1.11.1", "dependencies": {"send": "0.14.1", "parseurl": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "2.11.1", "istanbul": "0.4.3", "supertest": "1.1.0", "eslint-plugin-promise": "1.3.2", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "dist": {"shasum": "d6cce7693505f733c759de57befc1af76c0f0805", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.11.1.tgz", "integrity": "sha512-SGBgnvUc8p5pZdzo4DMTFZQlzsCGGV7rqb7owrM2FAW7gNJ2Jf2T45Ej/I9sSZNGdx1V2+MXZ0bUm7JMmTYU/w==", "signatures": [{"sig": "MEQCIE1Z6vVcgIxtoSnKNpklKdb/iQv/4pPq25WYXnOuS3lHAiB6Isj2yLmFobpjQaDffV16K+D7vlRJNsUTGtAL5mxq1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.11.2": {"name": "serve-static", "version": "1.11.2", "dependencies": {"send": "0.14.2", "parseurl": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.14.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.4.0", "eslint-config-standard": "6.2.1", "eslint-plugin-standard": "2.0.1"}, "dist": {"shasum": "2cf9889bd4435a320cc36895c9aa57bd662e6ac7", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.11.2.tgz", "integrity": "sha512-nBt9IVflCqc4pEtjttEgnwUJXBdy8xk0yZm16OomALNUKVa0S4X6pupZm/92j7M1AbPrC1WYkjr6HjtLeHnsAg==", "signatures": [{"sig": "MEQCICawbmzeH8RBYnO9P4sAwOpU5dI1H8Ei2aVsSdf97yVcAiAA42ODH1PaR4DnikkI7eHISZpc4QZAxG/bUyy7Tj0IPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.12.0": {"name": "serve-static", "version": "1.12.0", "dependencies": {"send": "0.15.0", "parseurl": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.16.1", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.4.0", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "dist": {"shasum": "150eb8aa262c2dd1924e960373145446c069dad6", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.12.0.tgz", "integrity": "sha512-wUl7rlZeKrwCxSnEJ0q18HHg9DpV3VCDPPxrtUPVBbGhc/dyMBq5qVdXiY35VqoSlml/TVrGQ3p4QARnODCQGg==", "signatures": [{"sig": "MEUCIQCeSzrxJSqAY5vEV6OEcNSz7dQjsEdzddNM0pqqblOg8AIgP23W36jZo8NA9TCDKlElboambaT3XfxDbefCAZw1aGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.12.1": {"name": "serve-static", "version": "1.12.1", "dependencies": {"send": "0.15.1", "parseurl": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.17.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "7.0.0", "eslint-plugin-markdown": "1.0.0-beta.4", "eslint-plugin-standard": "2.1.1"}, "dist": {"shasum": "7443a965e3ced647aceb5639fa06bf4d1bbe0039", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.12.1.tgz", "integrity": "sha512-/bjOTafwjEin0RIKpFxB6n82TVPV/z0fH2InaCTkUrxmDQZuPZe/aSqHpOSfzguS1k5oHbf6gWKw0fSUOlKa0A==", "signatures": [{"sig": "MEYCIQDK2A0+rDcbnDY/dxMhzRbiG++eDF8+o3ZgqjMZc3jqpgIhAIaE0wIo/6QqLZr8lYgOWW3rrwTdNyscHRB26a6JwBHp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.12.2": {"name": "serve-static", "version": "1.12.2", "dependencies": {"send": "0.15.2", "parseurl": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "4.2.2", "eslint-plugin-import": "2.2.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.4", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "e546e2726081b81b4bcec8e90808ebcdd323afba", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.12.2.tgz", "integrity": "sha512-Fy8ZkU7VUqhQSkI8/rdVK2jg6pS91oWcjjw5T5qJhEOZA85P3oNBIIAAPyIoOVIfwkBE2jNGNZhVtslarpv+ug==", "signatures": [{"sig": "MEYCIQDvQs1naHfk7YT+fA3+AGygF2HRS1PPypEizgNKyee59wIhAPTRNLAmc98VWRpBUmN2/C7irI8ARbQhdjriHx0GcHHJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.12.3": {"name": "serve-static", "version": "1.12.3", "dependencies": {"send": "0.15.3", "parseurl": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "4.2.2", "eslint-plugin-import": "2.2.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "9f4ba19e2f3030c547f8af99107838ec38d5b1e2", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.12.3.tgz", "integrity": "sha512-M83Sy74z4RJdcXeZUvNjuv/AtjE/38BNYR8h8mgCC8w32QA6hcQx9Efr/YIlmqIDnxNkS1/fcxNJJtK+g9O+QQ==", "signatures": [{"sig": "MEYCIQCsmaLR/nd0jsw+HtLGzU8gEJsejtyo6erC/SKi+D53EQIhAJWhCpouhJrOsuqMQv8TcbkFck2bXoHAvS0+2ZLBCk43", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.12.4": {"name": "serve-static", "version": "1.12.4", "dependencies": {"send": "0.15.4", "parseurl": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "5.1.0", "eslint-plugin-import": "2.6.1", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "9b6aa98eeb7253c4eedc4c1f6fdbca609901a961", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.12.4.tgz", "integrity": "sha512-S0jS3FQXrBdeOwbysweEhiB5ogGfVZND6H2c2Vh2RbriSPe8Bogtbb4J5ZUeViXS0v0YZYe7llGYrEXjK8fnqw==", "signatures": [{"sig": "MEQCIDa9fQUUjspxHshM7kOT/bcN6GkHAY5EXkoHA1lxnCt4AiBHHazJWWr9c/HipMpciWqoNDMTkmEcqHr3uc9thdocQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.12.5": {"name": "serve-static", "version": "1.12.5", "dependencies": {"send": "0.15.5", "parseurl": "~1.3.2", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "693a54118216f0310105c7180e5fdd6a50f654a5", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.12.5.tgz", "integrity": "sha512-i7WU1T31eTcKqcmYs1JYr57EpP0MvwS4/QYD4wFK9JBVpN6/WD40lesYatoHGfdKox7Q35QTXtjvan9eO9+4aA==", "signatures": [{"sig": "MEQCIAGupLUD8bMNG9gM0ULO4PduEFty4wHVHF+1DxHEyYjZAiBr2sc32mR4LMWNK9QdOx2tokWcuxeSvWSN8USpdXKBLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.12.6": {"name": "serve-static", "version": "1.12.6", "dependencies": {"send": "0.15.6", "parseurl": "~1.3.2", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "b973773f63449934da54e5beba5e31d9f4211577", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.12.6.tgz", "integrity": "sha512-M2CMkmVnR22x7taVSeYGdfIhnh/mhanPZqqBCSRAvVZNkrhaOpOLlwimKpJR+NhFpD/8Dr9G0YpAtkDkCcAVJQ==", "signatures": [{"sig": "MEQCIDRl26470kT68nKUTw0riPNrDvPsGX2w7UivE9PXLgjfAiB9kVVkzm8B9AoT9wB13U2xr55QBbYDPJOAHH70bU7QlA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.13.0": {"name": "serve-static", "version": "1.13.0", "dependencies": {"send": "0.16.0", "parseurl": "~1.3.2", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "810c91db800e94ba287eae6b4e06caab9fdc16f1", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.13.0.tgz", "integrity": "sha512-SEsucoUvAoXLTLznbyNUCw5Vg/7qaFyENZQITDwJWvGLYuRhw+hIDVXEFGaayXZimatgQxDRgcJve9Kp6rZqqw==", "signatures": [{"sig": "MEYCIQDm4guVbbQf8Sil2+PUgGizC+8kKHMqRpOmfXADC6ZwdQIhAKE8DGJz0/9lZBh3zdON9OhwGGrnun0lpdRRvswvgFjW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.13.1": {"name": "serve-static", "version": "1.13.1", "dependencies": {"send": "0.16.1", "parseurl": "~1.3.2", "encodeurl": "~1.0.1", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "5.2.0", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "4c57d53404a761d8f2e7c1e8a18a47dbf278a719", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.13.1.tgz", "integrity": "sha512-hSMUZrsPa/I09VYFJwa627JJkNs0NrfL1Uzuup+GqHfToR2KcsXFymXSV90hoyw3M+msjFuQly+YzIH/q0MGlQ==", "signatures": [{"sig": "MEUCIHsTIv4YxnGJwg6qjTeqjs1lmyMaNWdEd7qfeB9x51V1AiEAzRHmgCJ42uAPur+ItT6nGyOmdZRT/wdzSU6g7FiC/+k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "1.13.2": {"name": "serve-static", "version": "1.13.2", "dependencies": {"send": "0.16.2", "parseurl": "~1.3.2", "encodeurl": "~1.0.2", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "5.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-promise": "3.6.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "095e8472fd5b46237db50ce486a43f4b86c6cec1", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.13.2.tgz", "fileCount": 5, "integrity": "sha512-p/tdJrO4U387R9oMjb1oj7qSMaMfmOyd4j9hOFoxZe2baQszgHcSWjuya/CiT5kgZZKRudHNOA0pYXOl8rQ5nw==", "signatures": [{"sig": "MEUCIDmGhoCwFrrNbMPh+unDDM0Wb+THUbq6nyDRYxynB83hAiEA21s/R6oAhmQh34IU/1oXCOyqMFr+CZ7vNHwDG2TwImM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24364}, "engines": {"node": ">= 0.8.0"}}, "1.14.0": {"name": "serve-static", "version": "1.14.0", "dependencies": {"send": "0.17.0", "parseurl": "~1.3.3", "encodeurl": "~1.0.2", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "6.1.4", "eslint": "5.16.0", "istanbul": "0.4.5", "supertest": "4.0.2", "safe-buffer": "5.1.2", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "dist": {"shasum": "fad67e9f36d8c670b93fffd0586afe634f6c88a5", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.14.0.tgz", "fileCount": 5, "integrity": "sha512-Kg15ayeXWLhAE5T9adD3xGcEHchIZsDUExIIfSTOzg6kgmIa86NP8NpuOAYKLbPEYU1OQ+KCQrtKh8AG3h7KAQ==", "signatures": [{"sig": "MEUCIQCkqhI3tnyMgb5mGc0M0YEdHkSBFwykgW4droL4dzMGjAIgRddiB69uc5DWg3IbLHasgiCT+OgG29IUWKqCrvEu76Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24749, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc0k4NCRA9TVsSAnZWagAA7mkQAI7fJzKb8h9eZnXTjYY8\nZTg81cXUpO2bJnIx7ZQkiyL4KQaO62FVuVF/T0l2fAuaqE9MqUWVFGuqyYN7\nu0ZO2vHCf3SZqqNxdBw+xMFD8mIHHyCsAy1se1TyI1yXpo9nbuh5a5aCL4Tm\n0Ly+NazfLOtu/YX7JUj2qwI0BscPJuWB6sUiOZC1pQshiGmOomAbrD/6s7nr\nDkFMFSs5B6/GpiYkh5gHh+JI2NXFxzxciUpHcq2Ef0G5sDbPt3I1p+tuL/EV\noI9lugHSRh6P5hyB9M60a+SeQAS4sRea77VigXNjotHUw7krTUgVA4+pJUdA\no4fEAJg0+rxUK6SEcI4vADXDqRU1CG7FQBgYle+wJdp3IwbEWd47sfZE1ZIW\nVXgK5HWU9+Di8TCTSasM8JSiwqsIJZTL3C3Q2R0Jilsw8JE8EIsgrx/gSlyM\nXnWa/JWRVpj/w64IGf+gH2nl36B9Y938bjaTKMEYxiHpWnu9Yl0yWixsD3kw\nOSyoLv1ZvGLml4u6g+EzCsRup8pZOtT2+TJFI0IHsVYbskdTAE7q5H6R0Ra0\nlwqgdqMdS83Bf80wc/B6XRH5ZJINxWglV6kUfiOMp3Y2dBt83aiePOqsaOKI\nQKHrB1EtbjNjez0YVrqGH0rVsCenXD6pu5YHkO1Hg3YsoTUEeMzyET0AQ6tJ\nHaMq\r\n=44Cy\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8.0"}}, "1.14.1": {"name": "serve-static", "version": "1.14.1", "dependencies": {"send": "0.17.1", "parseurl": "~1.3.3", "encodeurl": "~1.0.2", "escape-html": "~1.0.3"}, "devDependencies": {"mocha": "6.1.4", "eslint": "5.16.0", "istanbul": "0.4.5", "supertest": "4.0.2", "safe-buffer": "5.1.2", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "dist": {"shasum": "666e636dc4f010f7ef29970a88a674320898b2f9", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.14.1.tgz", "fileCount": 5, "integrity": "sha512-JMrvUwE54emCYWlTI+hGrGv5I8dEwmco/00EvkzIIsR7MqrHonbD9pO2MOfFnpFntl7ecpZs+3mW+XbQZu9QCg==", "signatures": [{"sig": "MEYCIQDeqLkJX2oLjurB6jU0L1CV8O+OrAiqjH75JKe550nVlAIhAPgjWqGiUmMCiJsqxPI/ZIoZaBQDiNBkP/d/rCNBqorx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24894, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc1kQ5CRA9TVsSAnZWagAATzwP/j3OHYbfRHaSzvx+7R9w\nS65ncOxHfuv8DsFQRCJsWmkq1+px8WBIRCZiPePbZC4U/bH9ZnOSycKOWEn9\nc+YAWpOvR+JGFJjMI2KFn3kQgb//WFnD6Hg2d1wY9CeIGp5wfhyDpf7sl1oZ\n9MByAaTfHBxc46eoZ5w2drm7XlOseA5rk8r12NeN7q5JVVRJuPUS2k3Xu+sM\n4vv95+kKz9K4kNLxKfBxK28DNKk1zbtvfade6fMi24YfWVSJO+eiQZ2pCXXf\nx5I31i7gE6RMM2ijr/mwCsZn4zGMzWhnRgejTxEIQeEGm5skMP8MfeobUNon\nRb/XABMEAhWwYBssPwOccjlBPy+iK5KYxSihx28uIj++yreQIWqjdHaqeq7j\nZPdUitvLTfZ3PNCKwtjYqfbKQXZhGlMoT0fOIHYm7KXT2RRwi8XyZVR607xT\nBZVksFpf3K7uuoRWowRohNTpNRJZI90sUm08IBV3iL6XtJg4Rb/iGZCOUHnJ\nEcPKmQZxPKE/Af//RTqBQAOSfYSCoHrWzI7M07JEuGHsQSXB1eeXtZkVirqd\n9i9kSN/u1j7UMj6ml2OJTcH4mchvkPYTS+I+ailnzEPqyaXVZvYCOGTZ4OAl\ng5lKaWEDeYxdg2FwjWKRQCU39kaV7Ia47fTY0sDGfqXREPyJ3ZyhuMrBKHac\nMTiw\r\n=GE2J\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8.0"}}, "1.14.2": {"name": "serve-static", "version": "1.14.2", "dependencies": {"send": "0.17.2", "parseurl": "~1.3.3", "encodeurl": "~1.0.2", "escape-html": "~1.0.3"}, "devDependencies": {"nyc": "15.1.0", "mocha": "9.1.3", "eslint": "7.32.0", "supertest": "6.1.6", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "722d6294b1d62626d41b43a013ece4598d292bfa", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.14.2.tgz", "fileCount": 5, "integrity": "sha512-+TMNA9AFxUEGuC0z2mevogSnn9MXKb4fa7ngeRMJaaGv8vTwnIEkKi+QGvPt33HSnf8pRS+WGM0EbMtCJLKMBQ==", "signatures": [{"sig": "MEUCIQDzFHtq8zkQ5kei4iDvfkmW28lzh2j5WQrYN2oUXcJaYgIgedm71RYEFOS5wvmHTMU5itSIRt9NgBsXK6W3QggfceM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhusaPCRA9TVsSAnZWagAA7L0P/2udwkon40470t4kMRKs\n+eY2sbKU++l4ogJg1wI1FuafFcz+LgXfOp3qk38dQVzHIqFKjUibAb5xcsIX\ngbYsth9vFlueuFsGgMIBqzfTEjb9U6ZSloGoLC2acoGXA0jFjQCHKo2yZG3O\nMH0nbFm9LVgm5kozBrLjXdX8xnvS5+mwqnYhmEPa0bb0K048YiLlM1fuwX8P\n5dC0ll0Kc16MlKI2XTPA39shd3l6Tz0wlKrKKcLdyjDxHCKcRtn+2FFf+hNz\ndz+lHX/fYI0Gv5CIhs8YVG6QUmd83AR4hiHZtzpHDXR48llBuCgjxRJIUqgK\njl/GK9Am/sKi/or9LXL9K7N6pE/xb941W8N+MTzlM+T5DNOfr2oU0AnnZObQ\nc1ycNd76FAhH4aV9QuSMew3Br/2TI0wOjQN+eusva4TfPWMQ+ncfwPzVKvj7\nNukNaUUsERbgSDi17L0LILsbNyGkf70SIDJ+eGCPDHe2PJtHagSsEv9e/cyP\nM01Osf/F6nj2HN8ftNYZxeEX5MZfGlvCwaaqd6lYEBpfB7vvYE6iL/QGCKvb\nc/x0G4fFCnx+gOMrrnbDOPAHMjHWW2xCR9tXA6idFA6iw3hCoQzVY3C5/YX+\nElxCRy9jQd7+U6WNvudux72fQNzCfswB1jXdZKkmcn2+iKwSoi/Uz8WngMcd\nLX2y\r\n=6WhO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8.0"}}, "2.0.0-beta.1": {"name": "serve-static", "version": "2.0.0-beta.1", "dependencies": {"send": "1.0.0-beta.1", "parseurl": "~1.3.3", "encodeurl": "~1.0.2", "escape-html": "~1.0.3"}, "devDependencies": {"nyc": "15.1.0", "mocha": "9.1.3", "eslint": "7.32.0", "supertest": "6.1.6", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "003bc35db8b37905e1f6d380a4ccc0a34f30ec1d", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-2.0.0-beta.1.tgz", "fileCount": 5, "integrity": "sha512-DEJ9on/tQeFO2Omj7ovT02lCp1YgP4Kb8W2lv2o/4keTFAbgc8HtH3yPd47++2wv9lvQeqiA7FHFDe5+8c4XpA==", "signatures": [{"sig": "MEYCIQCUkxuo9cny6bmaGyNT8KSturrfqMUM3Ur+AuMeaoeG9gIhANOnsKQY2RLD9ui2vQqlzfmR99NdbPaaDTdpFZaez630", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25095, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/sbTCRA9TVsSAnZWagAAC5MP/0ibFVwiYpeOrc8HptUW\naKLLGhHnEIHWNaKb83bDHMj8h4cSlcfx52/UoQCcFP7guJnD7zy1k9rByotN\nz083bHq6ediCYVyzfuJ7ltukgu4WYsnjcbAG37rY8Bbx4Gu9KRoG+Bl9Cgrc\n3IiL9+ynVV0PeNd+TcFSIYSfrI0K4p4IFrWoWXcYwsqUv5LtVQ8RulLXMemv\nHBngq0nZnmVUKMfpf54yU4YC+8LUg+CrYxyQp5DEauloFhOXP0C3VLvKLu79\nQHqboOMCipu6efvNz5LwGqO3+rSmd5zo6s2uPtUwNhleSn68PuGBvfnLnz1V\nwsdXyeQetyatQJDZ7g/tEOpnx+wK/zmbOVQkICt+/dffI9Q14uLpS77PKB9r\nqJwUkWzspSWIijdkrzOT3ZpwcLYGtGI6g8BozRC3kZVaU/9aVyk/4My5jFIO\nsFF7dyC5yUl5KcbLwuTf+8MPRBPWhughkuivaS30hjr509pvldhkZViY2I9/\nBj8Sv/QtjdxA5et8eMFZuHFo36vWo/E7TAO20S5tODYGzBZBfrggZNymNf4F\npNvupx+EmM6n/CSkwL4cmNZfAYsLqRV0Qssk5aY7lAu8CNkVr/L9ehF4HS7o\nxnnZ6pF/u7TaZyMZTiyMSvZvUEJf4n56gsZk0jJSzT52aK12ZsHMAlHgBO0f\nYSm3\r\n=SWPh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10"}}, "1.15.0": {"name": "serve-static", "version": "1.15.0", "dependencies": {"send": "0.18.0", "parseurl": "~1.3.3", "encodeurl": "~1.0.2", "escape-html": "~1.0.3"}, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.2", "eslint": "7.32.0", "supertest": "6.2.2", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "faaef08cffe0a1a62f60cad0c4e513cff0ac9540", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.15.0.tgz", "fileCount": 5, "integrity": "sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==", "signatures": [{"sig": "MEUCIEZf9eBW61lcWw8CYuHpws6QKWi8wAKW2JV2majfWNhXAiEA/+h00dxADf6BCFZZjbO6jvuA8HlZyn47cap7jQ/KQYc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiPSZOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqilA//WFELsx4rTp3leSdsnVrr4KdsrFPc0zhfHBl2E+yOOUXO6nSW\r\nuUZyV+ZFgJnqqwQCEJrn9t59CDzk73+cgkdxF1cqcdrPXz1uAoFROHqQUoFV\r\nV2rgjOv9iHdH7Jq1E69Qwdd8uxwSuVRmcop1SQ0T5FV5Kc4PUIGvnyrwkQW7\r\ntMu9sJ9uznLlvDec54vMr2zWk2p2M0DFd3PNs6rmftO2dclBl0kqCMPfZaFY\r\nSXKBK99AQTqyuSm3JpQ1Ja7xQnoYXjXet2IwhnQFFPrRuRwAuMTTC/03C6QW\r\n5jaExj7fxEHHY4inQ2+yc9YWQs/k1xj565glazhHfmE0vNRp8xMgIoEXCRWV\r\nncKWHUzRnciSdA3D8D5EBJOxFRPDpv2ty3pwtCkUz4IypYv9pjLdrcZ8+V9d\r\ndRZZL/sX6b0t7ek3izXzb2C3cAwPjshJe2fRk58VyAKxA0DT0VSJnNZjpimb\r\n0vXPyDDkZgo00wv3fdAVkx6M+fkJDc+8uNRpJnNab4fiQ0ONF+9tECa0daF1\r\n2VfgdZgyp07w5p0RcnKnQ+Yz5VWJO/fJzr8ER9suhwDF2mNzTMkvUSdLVPjE\r\nqhaj9b1zpwMzx4KNlil/kuBU0/lH/PXfJLBIQpB7Fh/Xb1b3lDy0/JT8ZJK8\r\nOzBWDGmdEA0lav1XGQ+adL4q3wPx5Ee8qoo=\r\n=eUEv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8.0"}}, "2.0.0-beta.2": {"name": "serve-static", "version": "2.0.0-beta.2", "dependencies": {"send": "^1.0.0-beta.2", "parseurl": "~1.3.3", "encodeurl": "~1.0.2", "escape-html": "~1.0.3"}, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.2", "eslint": "7.32.0", "supertest": "6.2.2", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "9c0ca60e21a6332d18ade72db7cf7a96b27db474", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-2.0.0-beta.2.tgz", "fileCount": 5, "integrity": "sha512-Ge718g4UJjzYoXFEGLY/VLSuTHp0kQcUV65QA98J8d3XREsVIHu53GBh9NWjDy4u2xwsSwRzu9nu7Q+b4o6Xyw==", "signatures": [{"sig": "MEUCIQCL/i9i7R2h8FsiXC2nRERCGMPbySc6YFuK8c3cBB9x9wIgSZplomAhdviN275+/+1WxHnYokEHTcQoAkAhDClAZdQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25477}, "engines": {"node": ">= 0.10"}}, "2.0.0": {"name": "serve-static", "version": "2.0.0", "dependencies": {"send": "^1.0.0", "parseurl": "^1.3.3", "encodeurl": "^2.0.0", "escape-html": "^1.0.3"}, "devDependencies": {"nyc": "^17.0.0", "mocha": "^10.7.0", "eslint": "7.32.0", "supertest": "^6.3.4", "safe-buffer": "^5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "849bd6decd1dd4ae9233e4ed95581eb1ae031549", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-2.0.0.tgz", "fileCount": 5, "integrity": "sha512-HixicRldw1CCqCTgNQOYAUVNiP4B7bwcgNJT0HEY87yrBU4EVcNZ/GkQE0Ufbcuyqaw6DHeoUkhjGq4/vjb+gA==", "signatures": [{"sig": "MEYCIQDtBTebAhRCBZLHrXicyq0LRxfvvdWyclUDFesT9F/imQIhAMPcFGydxYapeM74dbSKIJGH/KPH82sbkBh5wfSfrApV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25676}, "engines": {"node": ">= 18"}}, "1.16.0": {"name": "serve-static", "version": "1.16.0", "dependencies": {"send": "0.18.0", "parseurl": "~1.3.3", "encodeurl": "~1.0.2", "escape-html": "~1.0.3"}, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.2", "eslint": "7.32.0", "supertest": "6.2.2", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "2bf4ed49f8af311b519c46f272bf6ac3baf38a92", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.16.0.tgz", "fileCount": 5, "integrity": "sha512-pDLK8zwl2eKaYrs8mrPZBJua4hMplRWJ1tIFksVC3FtBEBnl8dxgeHtsaMS8DhS9i4fLObaon6ABoc4/hQGdPA==", "signatures": [{"sig": "MEYCIQC9mKYRUvv8fVtpD//OvL22c0x3m9voXPR6Gu1lRvkX4QIhALQBgAjmGKJCiTIOC7Y7CKxEXqKhFj+5e4wxRpHaxZ01", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25297}, "engines": {"node": ">= 0.8.0"}}, "2.1.0": {"name": "serve-static", "version": "2.1.0", "dependencies": {"send": "^1.0.0", "parseurl": "^1.3.3", "encodeurl": "^2.0.0", "escape-html": "^1.0.3"}, "devDependencies": {"nyc": "^17.0.0", "mocha": "^10.7.0", "eslint": "7.32.0", "supertest": "^6.3.4", "safe-buffer": "^5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "1b4eacbe93006b79054faa4d6d0a501d7f0e84e2", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-2.1.0.tgz", "fileCount": 5, "integrity": "sha512-A3We5UfEjG8Z7VkDv6uItWw6HY2bBSBJT1KtVESn6EOoOr2jAxNhxWCLY3jDE2WcuHXByWju74ck3ZgLwL8xmA==", "signatures": [{"sig": "MEUCIFY3iOM5HQWL0KyQ8aQ1nNs/9z6wWZ9X8SOzF7zMvW8lAiEAhMRwT4I3UdCOZhum3nJbknFrJUIFY7GH9r9v9j5bPvM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25786}, "engines": {"node": ">= 18"}}, "1.16.1": {"name": "serve-static", "version": "1.16.1", "dependencies": {"send": "0.19.0", "parseurl": "~1.3.3", "encodeurl": "~1.0.2", "escape-html": "~1.0.3"}, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.2", "eslint": "7.32.0", "supertest": "6.2.2", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "ef5b5e8c045446a12e0efe1cb2bc2246dbcf4b66", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.16.1.tgz", "fileCount": 5, "integrity": "sha512-lQEnvznm+LlIN7a5B/LdKGFt1b/a+L/DNojweniJHyoBxqQlAVnv0QC5UuRbQC4udn3n4Tj2pcvbJa3Wiqwxtw==", "signatures": [{"sig": "MEUCIQCc/ZG/RXfjbl+4NZJwQ56HFKwaFzVA5FlV60HNu0iPpAIgRDASPz01/TN795I4aW2VRGSftj1RnAn0HXO84/nSqQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25359}, "engines": {"node": ">= 0.8.0"}}, "1.16.2": {"name": "serve-static", "version": "1.16.2", "dependencies": {"send": "0.19.0", "parseurl": "~1.3.3", "encodeurl": "~2.0.0", "escape-html": "~1.0.3"}, "devDependencies": {"nyc": "15.1.0", "mocha": "9.2.2", "eslint": "7.32.0", "supertest": "6.2.2", "safe-buffer": "5.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "b6a5343da47f6bdd2673848bf45754941e803296", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-1.16.2.tgz", "fileCount": 5, "integrity": "sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw==", "signatures": [{"sig": "MEUCIQCsndmgSGAp9vXsjAYSPMOa5Dq2KV76nUiqOP2ECVTN1QIgbcHHRmG5XV21lvK1SHe194hnKkoVWfS8wj+B7U7rcuQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25426}, "engines": {"node": ">= 0.8.0"}}, "2.2.0": {"name": "serve-static", "version": "2.2.0", "dependencies": {"encodeurl": "^2.0.0", "escape-html": "^1.0.3", "parseurl": "^1.3.3", "send": "^1.2.0"}, "devDependencies": {"eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "^10.7.0", "nyc": "^17.0.0", "supertest": "^6.3.4"}, "dist": {"integrity": "sha512-61g9pCh0Vnh7IutZjtLGGpTA355+OPn2TyDv/6ivP2h/AdAVX9azsoxmg2/M6nZeQZNYBEwIcsne1mJd9oQItQ==", "shasum": "9c02564ee259bdd2251b82d659a2e7e1938d66f9", "tarball": "https://registry.npmjs.org/serve-static/-/serve-static-2.2.0.tgz", "fileCount": 5, "unpackedSize": 25672, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCEbgwSj5X8NfSS9x9DXRK/J/SadyzJwGkvWp76oiGUNwIhAJVpObU+UH53Rpfo0FgMLL83Pmxwk1togbV9jWxgmSRp"}]}, "engines": {"node": ">= 18"}}}, "modified": "2025-03-28T00:38:59.979Z"}