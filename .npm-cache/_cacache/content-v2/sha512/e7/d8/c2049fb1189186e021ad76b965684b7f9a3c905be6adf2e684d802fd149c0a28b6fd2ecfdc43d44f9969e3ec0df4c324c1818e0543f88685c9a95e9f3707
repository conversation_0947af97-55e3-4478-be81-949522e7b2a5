{"source": 1101844, "name": "path-to-regexp", "dependency": "path-to-regexp", "title": "Unpatched `path-to-regexp` ReDoS in 0.1.x", "url": "https://github.com/advisories/GHSA-rhx6-c78j-4q9w", "severity": "high", "versions": ["0.0.1", "0.0.2", "0.1.0", "0.1.1", "0.1.2", "0.1.3", "0.1.4", "0.1.5", "0.1.6", "0.1.7", "0.1.8", "0.1.9", "0.1.10", "0.1.11", "0.1.12", "0.2.0", "0.2.1", "0.2.2", "0.2.3", "0.2.4", "0.2.5", "1.0.0", "1.0.1", "1.0.2", "1.0.3", "1.1.0", "1.1.1", "1.2.0", "1.2.1", "1.3.0", "1.4.0", "1.5.0", "1.5.1", "1.5.2", "1.5.3", "1.6.0", "1.7.0", "1.8.0", "1.9.0", "2.0.0", "2.1.0", "2.2.0", "2.2.1", "2.3.0", "2.4.0", "3.0.0", "3.1.0", "3.2.0", "3.3.0", "4.0.0", "4.0.1", "4.0.2", "4.0.3", "4.0.4", "4.0.5", "5.0.0", "6.0.0", "6.1.0", "6.2.0", "6.2.1", "6.2.2", "6.3.0", "7.0.0", "7.1.0", "7.2.0", "8.0.0", "8.1.0", "8.2.0"], "vulnerableVersions": ["0.0.1", "0.0.2", "0.1.0", "0.1.1", "0.1.2", "0.1.3", "0.1.4", "0.1.5", "0.1.6", "0.1.7", "0.1.8", "0.1.9", "0.1.10", "0.1.11"], "cwe": ["CWE-1333"], "cvss": {"score": 0, "vectorString": null}, "range": "<0.1.12", "id": "a8yr62Ee8m0qU/KLrejYtFegYsBDCx0VyL+D7MchlPgXVigeGCEJ8XCIPHSeGDvvSzgpJDr3ArcqUFmPzMHK9g=="}