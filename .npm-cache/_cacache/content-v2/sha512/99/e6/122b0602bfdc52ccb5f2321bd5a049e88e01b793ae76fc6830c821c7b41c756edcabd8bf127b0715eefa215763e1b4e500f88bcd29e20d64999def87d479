{"name": "send", "dist-tags": {"latest": "1.2.0", "next": "1.1.0"}, "versions": {"0.0.1": {"name": "send", "version": "0.0.1", "dependencies": {"mime": "1.2.6", "debug": "*", "fresh": "0.1.0", "range-parser": "0.0.4"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "2.x", "supertest": "0.0.1"}, "dist": {"shasum": "0d04102e8ac681fb635dc7030e9c9b41de683e00", "tarball": "https://registry.npmjs.org/send/-/send-0.0.1.tgz", "integrity": "sha512-CUeiu6vtcyI8HMj8D8sjQnT9K5P0yXH5/tuEeaDr401zeaW9S09mLqyjhiBUjMsDtt1MKohdix9Pw7cz1suTtw==", "signatures": [{"sig": "MEUCIQCu3ZIiC0LxqxK3K/f6eBC21qFvzvP358Khp9zihSchNQIgImjjGC4gnT9dwN/qleDGwA11tWHJhhdXMTpkic5sDFU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.2": {"name": "send", "version": "0.0.2", "dependencies": {"mime": "1.2.6", "debug": "*", "fresh": "0.1.0", "range-parser": "0.0.4"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "2.x", "supertest": "0.0.1"}, "dist": {"shasum": "8792a53497bb91b62973b588179eb4c5ed0ff7fd", "tarball": "https://registry.npmjs.org/send/-/send-0.0.2.tgz", "integrity": "sha512-NyXB5ehv6wUmL8IRJZDtO2MkQ60M96diF4Iee8u7TtLX+/N9gFES6c6TdOXfAhxdikw1uaXQmrdU1ohJ1appQw==", "signatures": [{"sig": "MEUCIQCWqzmBZVB1kN4vPwG2mS8w1/Ai8v4FeV3uqM81NtgckgIgMDydGgPgaxffNUzXgSN+jf7w4tC97wTcQk85lAORJMY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.3": {"name": "send", "version": "0.0.3", "dependencies": {"mime": "1.2.6", "debug": "*", "fresh": "0.1.0", "range-parser": "0.0.4"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "2.x", "supertest": "0.0.1"}, "dist": {"shasum": "4d5f843edf9d65dac31c8a5d2672c179ecb67184", "tarball": "https://registry.npmjs.org/send/-/send-0.0.3.tgz", "integrity": "sha512-3DZtRDSPm+ikrsRnURa3LHd6R4Dmg5OI5UhiczbO1q6aBkDmJOUz/sFjn4xNl95PVeuccD7lqiM2Cy/0by2Uow==", "signatures": [{"sig": "MEUCIAzRBmy0wbjXduObeH+rnqNktWGhZUtgMZpiEvyHBrXCAiEAwpsKy5eHEfp3gz9z+IAX5kF/xfO5aXQiYIxOkTt4cl0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.0.4": {"name": "send", "version": "0.0.4", "dependencies": {"mime": "1.2.6", "debug": "*", "fresh": "0.1.0", "range-parser": "0.0.4"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "2.x", "supertest": "0.0.1"}, "dist": {"shasum": "2d4cf79b189fcd09610e1302510ac9b0e4dde800", "tarball": "https://registry.npmjs.org/send/-/send-0.0.4.tgz", "integrity": "sha512-weKMWbrKdW7kqeHbk1IWf+u25CqLx1xrqhDrRUV02yW5BNzUp783GRxgxziFWH3QGrQPMvR5/DTUN9RuO2u9ew==", "signatures": [{"sig": "MEUCIQDMLMdXs6iDV4hVIWOjBlnKSZs0O2WT5ebCNiAEOYWAxQIgaaM7oip0P0x/sdF0tHRNbrCAD4TAf1DWYe+AdQ3Pucs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.0": {"name": "send", "version": "0.1.0", "dependencies": {"mime": "1.2.6", "debug": "*", "fresh": "0.1.0", "range-parser": "0.0.4"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "2.x", "supertest": "0.0.1"}, "dist": {"shasum": "cfb08ebd3cec9b7fc1a37d9ff9e875a971cf4640", "tarball": "https://registry.npmjs.org/send/-/send-0.1.0.tgz", "integrity": "sha512-D/GaJQQYx7ICNq9Te5V4wHetfDQdFk3HJ4oBfDUBNW7XQmLbJ8sQDm/wFvVUUpKN8tluOnO1dFdM8KODn6D79w==", "signatures": [{"sig": "MEYCIQCFKwsonIfo1Ff+OC1ZOoz+YNveIf/6+vrp1K4TeoVQPgIhALGU9sqMvaCD2cnRTK3h0JN3ykonkpFM9zqVP0vsYYwn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.1": {"name": "send", "version": "0.1.1", "dependencies": {"mime": "~1.2.9", "debug": "*", "fresh": "0.1.0", "range-parser": "0.0.4"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "2.x", "supertest": "0.0.1"}, "dist": {"shasum": "0bcfcbd03def6e2d8612e1abf8f4895b450c60c8", "tarball": "https://registry.npmjs.org/send/-/send-0.1.1.tgz", "integrity": "sha512-u4xNGU4XrE/d+e65Py/Qek4DVjYOICk8kAXSjEMZE89VO69FofFmo1PXHk/I/4pf58xteafBAa/Fi1+zZVZkGA==", "signatures": [{"sig": "MEYCIQC211wY2VK9YSaZCAWq6I9e+v7KGf329kw6ce2BqEtVtAIhALM1yq8pS7dEOYdKwn4A3IBTuPZH9grc1SRf2J8/Xrc+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.2": {"name": "send", "version": "0.1.2", "dependencies": {"mime": "~1.2.9", "debug": "*", "fresh": "0.1.0", "range-parser": "0.0.4"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "2.x", "supertest": "0.0.1"}, "dist": {"shasum": "c2744e98111bf1bb62eb4996dfda8a9980752984", "tarball": "https://registry.npmjs.org/send/-/send-0.1.2.tgz", "integrity": "sha512-bnL7/kaSbL5L1xQjTDtSMi7HNydwyBZbGaK7nylypGUEWsbbSpL8wpvoK5b5K6yWNaTR3mnPO0MLh4+/Mw3/XA==", "signatures": [{"sig": "MEYCIQCWa51sGn/GZSArQkS6Qnw1gA6p/v/qzkjl7Sqa/TpQAAIhAKl6XG3raoePJYD2D3jKKDffUxpT0o3FDXBCucr5bdVT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.3": {"name": "send", "version": "0.1.3", "dependencies": {"mime": "~1.2.9", "debug": "*", "fresh": "0.1.0", "range-parser": "0.0.4"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "2.x", "supertest": "0.0.1"}, "dist": {"shasum": "a7875daa6802d31e2ce32fdad98d3664c51ecea3", "tarball": "https://registry.npmjs.org/send/-/send-0.1.3.tgz", "integrity": "sha512-FqBloej/3p6+mr88cqdh0bC7AYOpOrXBP3lqbOGnQDyatiHxgmPfpBOkN2sXUuFhpJpwiab5FPh+rCOxsBYgvg==", "signatures": [{"sig": "MEQCIGllB1/c23eWN9A4uBP2zk+95M54+ZwV6zSTJQYNT8SKAiBZtu6coKavrJgY6AzB8OcCZ7kCXqrgKLv4YD6dVXeWjw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.4": {"name": "send", "version": "0.1.4", "dependencies": {"mime": "~1.2.9", "debug": "*", "fresh": "0.2.0", "range-parser": "0.0.4"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "2.x", "supertest": "0.0.1"}, "dist": {"shasum": "be70d8d1be01de61821af13780b50345a4f71abd", "tarball": "https://registry.npmjs.org/send/-/send-0.1.4.tgz", "integrity": "sha512-NJnIaB29/EcNqkNneUAm16oEVnzM2LeNBc/hmgKuExv2k9pCZQEw8SHJeCdjqesHJTyWAr7x5HjeOmRFS4BoFw==", "signatures": [{"sig": "MEQCIEIf5n7OPa8JievtwDvfY95iZPLyo5sEz3l+Gmb50JOHAiB9dxjoAl9YzYwR1GfsnV4ET/Y/56HlUejqxzaVJt2eqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.0": {"name": "send", "version": "0.2.0", "dependencies": {"mime": "~1.2.9", "debug": "*", "fresh": "~0.2.1", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "2.x", "supertest": "0.0.1"}, "dist": {"shasum": "067abf45cff8bffb29cbdb7439725b32388a2c58", "tarball": "https://registry.npmjs.org/send/-/send-0.2.0.tgz", "integrity": "sha512-CR/kej5a8BChsMJwpmAtqOgdGI3nemoRaPcoXj/choHibvaOfkYcohcAbd9aEG8MhL9CfRH3KlUb+oHZsdNmTg==", "signatures": [{"sig": "MEUCICyRHeAaGqfRHdn0xTnFqeLyOXtl2m7vp8m9U1gdvXJjAiEAxQkEusylEMwpF1zjP7QzVTcKM/mSnS8vLDm6KjAVKCk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.0": {"name": "send", "version": "0.3.0", "dependencies": {"mime": "1.2.11", "debug": "0.8.0", "fresh": "~0.2.1", "buffer-crc32": "0.2.1", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "*", "should": "*", "connect": "2.x", "supertest": "0.10.0"}, "dist": {"shasum": "9718324634806fc75bc4f8f5e51f57d9d66606e7", "tarball": "https://registry.npmjs.org/send/-/send-0.3.0.tgz", "integrity": "sha512-FPyeqtit9Z3zbusjv0KQyR8vQ9CL57qPNOz4GgcuIPSk+nx9WTUIMQoR6+0a7YOZpQVTtk04qH0IVQG3rohZ0Q==", "signatures": [{"sig": "MEUCIQDdkGi7xnI8ASt/rGXYdrE12OHdwi3Fx6jsaEYrGfxzmQIgbUpM3ZbZm1pSrmFB/2Asrq+JblN+zsupENM+6X+MgqY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.0": {"name": "send", "version": "0.4.0", "dependencies": {"mime": "1.2.11", "debug": "0.8.1", "fresh": "~0.2.1", "finished": "1.1.4", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "~1.19.0", "should": "~3.3.2", "istanbul": "0.2.10", "supertest": "~0.13.0"}, "dist": {"shasum": "e7ec677072e5651f18712dd493732fcf422cec39", "tarball": "https://registry.npmjs.org/send/-/send-0.4.0.tgz", "integrity": "sha512-RHuhIpGQPbgA2BMLhx4U1wbcrp+UyR36q2OTo3/VxcmeKp7Hsf+1F1Z1ddGtuqXlGWFdYKJpgiiWGd5TryfBsw==", "signatures": [{"sig": "MEUCIDWoNuh1082qCgrk/l8ZhhgDwLvRYyl3eey7AZtwd8DTAiEAp+nq5dGfE/ZppB9fTn1o4moQ6+caMDm/4sz3lMcjOhY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.4.1": {"name": "send", "version": "0.4.1", "dependencies": {"mime": "1.2.11", "debug": "0.8.1", "fresh": "~0.2.1", "finished": "1.1.4", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0"}, "dist": {"shasum": "6e9a5d41cb9c0fb3514226446fa319aed46d433d", "tarball": "https://registry.npmjs.org/send/-/send-0.4.1.tgz", "integrity": "sha512-+xISm28vKB0WFrKNe2WF3c+/tv3dqRHYMuH6g6jq3ZfcxG9X37n4Dzbb6/bJuEjFSly95VWEwnF6SQgmGzO0hA==", "signatures": [{"sig": "MEUCIQDs5DKBRiWcnfpMkyCzhS7euqrhY2YSoawLahuPYprBeAIgDFfEmsLu24XWQQGipzxM+CW3jCemRgaR89o7b2V+htE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.4.2": {"name": "send", "version": "0.4.2", "dependencies": {"mime": "1.2.11", "debug": "1.0.1", "fresh": "~0.2.1", "finished": "1.2.1", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0"}, "dist": {"shasum": "7641b23126fc54975d2be37674b36d6bb617b26c", "tarball": "https://registry.npmjs.org/send/-/send-0.4.2.tgz", "integrity": "sha512-7SYh4BepTqJg3e7kZgn4t5pH+7305g67QathoCbT6Rqarlm4Lmppen9iifKr2R1ingaD1AOtvDAQkwNav6g0Cg==", "signatures": [{"sig": "MEYCIQDHQpVEU1dJ8FyNdAPaQvScmqH0kXkt+DQliB7P5x+JywIhALoejJIXPdeNw5Bo0SCLiYTZQfovuZO6gpfLHhalsnA1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.4.3": {"name": "send", "version": "0.4.3", "dependencies": {"mime": "1.2.11", "debug": "1.0.2", "fresh": "0.2.2", "finished": "1.2.2", "escape-html": "1.0.1", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0"}, "dist": {"shasum": "9627b23b7707fbf6373831cac5793330b594b640", "tarball": "https://registry.npmjs.org/send/-/send-0.4.3.tgz", "integrity": "sha512-Tl3/iKtlp1WM0hDyackntOVwx5kc8GET/zgEj9AOYRX5ideM/33FeRYk4L19IqioGxCkxHSyq1PThVs6PVvk+w==", "signatures": [{"sig": "MEUCIBOHQObFJ3vu4Vq5qMVXQWgRcDcdG7KEduidyCGlMOqtAiEAx/OSNqc7lpQrxj0VCHE1WBCnjIcsZkdYma85YCYMRlU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.5.0": {"name": "send", "version": "0.5.0", "dependencies": {"ms": "0.6.2", "mime": "1.2.11", "debug": "1.0.2", "fresh": "0.2.2", "finished": "1.2.2", "escape-html": "1.0.1", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.2.10", "supertest": "~0.13.0"}, "dist": {"shasum": "fc0f7e2f92e29aebfd8a1b2deb4a394e7a531a68", "tarball": "https://registry.npmjs.org/send/-/send-0.5.0.tgz", "integrity": "sha512-uuPWJQK6p0btveYhOxRqvcB9Cfcs9ugNrMd0SoM/tqzOC9A86bX4rDEQN84X3Cc5Gpo5Az2cB4Olo5c3Aso2Sg==", "signatures": [{"sig": "MEUCIQDTVmObDBKOBGwX5g4BsWShDXkzhgfZ/mIoq6uN4Xd9DAIgXl7LPagylEsfhmLet5kFdASuAJx9g7lFkCNwY4w4LjA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.6.0": {"name": "send", "version": "0.6.0", "dependencies": {"ms": "0.6.2", "depd": "0.3.0", "mime": "1.2.11", "debug": "1.0.3", "fresh": "0.2.2", "finished": "1.2.2", "escape-html": "1.0.1", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "a59da9265db7c35141e1079cf1f368ee0d59b3ab", "tarball": "https://registry.npmjs.org/send/-/send-0.6.0.tgz", "integrity": "sha512-A3EwHmDwcPcmLxIRNjr2YbXiYWq6M9JyUq4303pLKVFs4m5oeME0a9Cpcu9N22fED5XVepldjPYGo9eJifb7Yg==", "signatures": [{"sig": "MEQCIDeYlRCbVTGgLVXIgoun29kyPmHYtLnMrXDisBghRUMxAiAUSzn85gaYTMDwVCXsik5POMqQHutwYsvCYSWcADNriA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.7.0": {"name": "send", "version": "0.7.0", "dependencies": {"ms": "0.6.2", "depd": "0.4.2", "mime": "1.2.11", "debug": "1.0.4", "fresh": "0.2.2", "finished": "1.2.2", "escape-html": "1.0.1", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "~1.20.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "f479a05c57d36bf564311dd1e3825b84b26ae336", "tarball": "https://registry.npmjs.org/send/-/send-0.7.0.tgz", "integrity": "sha512-rErCu1TWwg6FgzF4YAJUeg8XZHgLDXE4vKhv8QvzHqdFHyZYypolTUostkRbBYwnFfTKQwVFa21/+3tjsVs65A==", "signatures": [{"sig": "MEUCIQCWeBVYBSwIdYw8hy2ITaLKYGQ5qdpdbaOGVuZAngUMbwIgfRaaT0ENXa4WXtZ8wZZS0N0TfjMXLVKao0qqZf5pbHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.7.1": {"name": "send", "version": "0.7.1", "dependencies": {"ms": "0.6.2", "depd": "0.4.3", "mime": "1.2.11", "debug": "1.0.4", "fresh": "0.2.2", "finished": "1.2.2", "escape-html": "1.0.1", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "fe02421cd5fb3bcc10287f72c18e94818e3f80fd", "tarball": "https://registry.npmjs.org/send/-/send-0.7.1.tgz", "integrity": "sha512-zxf2FeD9mAHmRxVsTyPUfEd9bzVn6OOB6bTWDBhEftAPS5U2UHAq1S6oshjRo8SjKAhu8DPW8ui7o4qK26hXWw==", "signatures": [{"sig": "MEUCIH+P3IOhj7f+7SvIq9ZoY31OqiSM9fIJXNJEEpblycoQAiEAgFZj5nEoF1GhQy9ah0aBS3iAQnVtNhwG26xbKV+Mll4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.7.2": {"name": "send", "version": "0.7.2", "dependencies": {"ms": "0.6.2", "depd": "0.4.4", "mime": "1.2.11", "debug": "1.0.4", "fresh": "0.2.2", "finished": "1.2.2", "escape-html": "1.0.1", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "3b5f696f701d56fe115b860cc6b3f0cdbfbf7804", "tarball": "https://registry.npmjs.org/send/-/send-0.7.2.tgz", "integrity": "sha512-z6PstXKza7kKyOHn1PCGBY6OYUoQM1TiUAh1Yky68M/IDNBh79fSSQdKpGiu+ah8RVBrxAudCD6qcQ1aE1Er4g==", "signatures": [{"sig": "MEUCIAWPzcZxuMCWxM9KBF4o05EUKRAbe+ZRtConK+hKruDQAiEAuHynXl1v8J/2x0IlTpVhp5D1P76lxqRkS0hjuELVzPc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.7.3": {"name": "send", "version": "0.7.3", "dependencies": {"ms": "0.6.2", "depd": "0.4.4", "mime": "1.2.11", "debug": "1.0.4", "fresh": "0.2.2", "finished": "1.2.2", "escape-html": "1.0.1", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "2caa2e2627d2f9c2d109d3f5c2942935480aa993", "tarball": "https://registry.npmjs.org/send/-/send-0.7.3.tgz", "integrity": "sha512-Oib7tv5vsA3LGnE01nUvARgHAVmWCSyG3H9b/7pv1esTX3aSwH89k0Tg5YiOp7EHiCI5Ru/1ILq+YB/FfN8SAg==", "signatures": [{"sig": "MEUCIBglXKOJlQMwk8AS4cPWxOZrW3cUtSOVzC83c2GTpS4zAiEAp79E7IbCWrPhx+xVdn89kds7oZzPJGPaZvnsUQzb4A0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.7.4": {"name": "send", "version": "0.7.4", "dependencies": {"ms": "0.6.2", "depd": "0.4.4", "mime": "1.2.11", "debug": "1.0.4", "fresh": "0.2.2", "finished": "1.2.2", "escape-html": "1.0.1", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "c80a084cb8eb940345f3ab4ce9e4ee25cb6647cb", "tarball": "https://registry.npmjs.org/send/-/send-0.7.4.tgz", "integrity": "sha512-THlZnUAUb8f6iJk/XpadDFFT5xGIlPTwX6THZJNsTmsqM+CCfcNJpdO7Vw8E1ZvBnvMZskuSjAEZ7aPDqiabKw==", "signatures": [{"sig": "MEQCIG+zNSnSjkaQ+z+i82fXWsDmk039qMMnGf8Bl5PmWU7RAiAO9+zcFJLLpj/1ANxeA6tWo/mt5qvhECODuPTrF9JZ9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.8.0": {"name": "send", "version": "0.8.0", "dependencies": {"ms": "0.6.2", "depd": "0.4.4", "mime": "1.2.11", "debug": "1.0.4", "fresh": "0.2.2", "finished": "1.2.2", "escape-html": "1.0.1", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "cbe98d58c1bdaa666bb95acb68ed1df92e1ae6e1", "tarball": "https://registry.npmjs.org/send/-/send-0.8.0.tgz", "integrity": "sha512-4aiN/zM0v9GZ62i3xS0F9Um0OOFAIw/bPp2pcI3mANi/EPGx/A0m0Z0QNVPC0s3VGJ+JX0CPCQmL9gqo78IP5A==", "signatures": [{"sig": "MEQCICiWgmAkhEDmhlHTghyW8XxKs59u5xy63kBSeB/y/KWGAiASTFSuwAfyeFiNs6/16mzBxZmSnxE6N0I0DGbs5JYwyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.8.1": {"name": "send", "version": "0.8.1", "dependencies": {"ms": "0.6.2", "depd": "0.4.4", "mime": "1.2.11", "debug": "1.0.4", "fresh": "0.2.2", "finished": "1.2.2", "escape-html": "1.0.1", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "86bbdcc3fb0ce6ebc2d15af977d94c0b300d02eb", "tarball": "https://registry.npmjs.org/send/-/send-0.8.1.tgz", "integrity": "sha512-TY8jG+K2f6G4XXMpEbC3sFR2LP2SuLXXydEQj1kl8KPXvTs5sRCy8pnwa7GuanwZC9YaI6wBo4FcHLEcSj8UwQ==", "signatures": [{"sig": "MEYCIQDs2zJq2/pDcsId8vQjx4oCBx8UuSM2qE50Zt0ZzeBgfgIhAJHvw2fTM8ubHk2ppfF07xODty91JXC+MhVt/+p5rnb3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.8.2": {"name": "send", "version": "0.8.2", "dependencies": {"ms": "0.6.2", "depd": "0.4.4", "mime": "1.2.11", "debug": "1.0.4", "fresh": "0.2.2", "dethroy": "1.0.2", "finished": "1.2.2", "escape-html": "1.0.1", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "f67efb2e3c89bf5bcd90ccda8683b17f1cbfd0ac", "tarball": "https://registry.npmjs.org/send/-/send-0.8.2.tgz", "integrity": "sha512-fbdYTCu6dpN/Dcz9kd3HFPHzn0HUa/WtEPyDW3s95fK9bCK19tPxrpYjTK7akV63MtjspjQap6yNODKLUlCIgQ==", "signatures": [{"sig": "MEUCIQCwDJ/XitC5VO2/vv7MuvHgClW+rfXF6YjDH8EOpvQVqQIgUAh4s8ZGU1JFqtUtLHx468odNGmRkk8lnEfCFXkMBZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.8.3": {"name": "send", "version": "0.8.3", "dependencies": {"ms": "0.6.2", "depd": "0.4.4", "mime": "1.2.11", "debug": "1.0.4", "fresh": "0.2.2", "destroy": "1.0.3", "escape-html": "1.0.1", "on-finished": "2.1.0", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "593886004fcb968a1b5727814a32b388b3b99083", "tarball": "https://registry.npmjs.org/send/-/send-0.8.3.tgz", "integrity": "sha512-pB//2WPgYGXQPNUq1XJ2wLdZU4P4fchsM09vfDdeEYOxs+4OnQKN+DqJJQ0kAxP5mpdLJf3nWcLftKn2Pb+mvg==", "signatures": [{"sig": "MEUCIBKmGPnr+xJvoRPdN25QtOG9a8jFYuU2TLi6768hATN5AiEA9gQ1RU2who5jDIUi71+vjT8yJ+ydojBS2ehN0NL4bwk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.8.4": {"name": "send", "version": "0.8.4", "dependencies": {"ms": "0.6.2", "depd": "0.4.4", "mime": "1.2.11", "debug": "1.0.4", "fresh": "0.2.2", "destroy": "1.0.3", "escape-html": "1.0.1", "on-finished": "2.1.0", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.0", "supertest": "~0.13.0"}, "dist": {"shasum": "259cd04e507df26a70eaa5b66cb20a26d8f18d65", "tarball": "https://registry.npmjs.org/send/-/send-0.8.4.tgz", "integrity": "sha512-kxWrH6nCg5Ac6CNWAqyCERLj9FsYwmT/EhsJA6nrZUdEXIgooXg8uht9EowoScRxIgBtItoszphMzkh6SZXY5w==", "signatures": [{"sig": "MEYCIQCCMaXZILghjT+gVrqUQtcy6jhip2xBTSV+XoVoqV0nvAIhAMk/vy46JM2/JU8phuRv9G1gqqC437R5Yo+lIkQJ3L9k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.8.5": {"name": "send", "version": "0.8.5", "dependencies": {"ms": "0.6.2", "depd": "0.4.4", "mime": "1.2.11", "debug": "1.0.4", "fresh": "0.2.2", "destroy": "1.0.3", "escape-html": "1.0.1", "on-finished": "2.1.0", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0"}, "dist": {"shasum": "37f708216e6f50c175e74c69fec53484e2fd82c7", "tarball": "https://registry.npmjs.org/send/-/send-0.8.5.tgz", "integrity": "sha512-U6FiwCZ1ZCtLqLj2vgEX+3CljIDAzrkCSsbwdS+VKWxYR9/5Rk7GR+Rg79np/CsvElh8u7q3YmJu6PLGdDJqgw==", "signatures": [{"sig": "MEYCIQDyrygBDp6+HQBD+mAn4mNn+pogigfokj2Bq/H7uGQjUQIhAP3qDS1NTMF02aoXnsoXs2007xjT+ViS/pEKuUH1WeqT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.9.0": {"name": "send", "version": "0.9.0", "dependencies": {"ms": "0.6.2", "depd": "0.4.4", "etag": "~1.3.0", "mime": "1.2.11", "debug": "~2.0.0", "fresh": "0.2.2", "destroy": "1.0.3", "escape-html": "1.0.1", "on-finished": "2.1.0", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0"}, "dist": {"shasum": "778341d52134c895a4ecaf44a4a30d762f8ee3eb", "tarball": "https://registry.npmjs.org/send/-/send-0.9.0.tgz", "integrity": "sha512-AYziNjFd75TTu6thdc/bG0GGs8UE+e2DJH4Dtt0k8tbFyg2mRjWOMvoVMwOFl/8EzPq1KnjzQqujM4ZHt5UVzA==", "signatures": [{"sig": "MEUCIQCGR6cTrjIZLL3P2swSdwTP4OjfDxHBL5GIGl9flrJD1wIgOA/mWXhzF7hwJaOF4FYq+2OBdDpt0lipnxRGiPZ7mBA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.9.1": {"name": "send", "version": "0.9.1", "dependencies": {"ms": "0.6.2", "depd": "0.4.4", "etag": "~1.3.0", "mime": "1.2.11", "debug": "~2.0.0", "fresh": "0.2.4", "destroy": "1.0.3", "escape-html": "1.0.1", "on-finished": "2.1.0", "range-parser": "~1.0.0"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0"}, "dist": {"shasum": "d93689f7c9ce36bd32f8ee572bb60bda032edc23", "tarball": "https://registry.npmjs.org/send/-/send-0.9.1.tgz", "integrity": "sha512-MjQa+SIsbWpY5rKYov3fY0lkRyCZGp06H3tZNXTbMVt5OQtmF9eGUwWiY12iDqD6Sz2JdeA1XsdjWosaeI0FnQ==", "signatures": [{"sig": "MEYCIQDGHP4eNbyWbnsWKzyYNO3Ql79FHDWNxRc0AxJxzb5WywIhAIunKaYo+I2e1+tks5Rcg//MLlMPEVJuKF5FPp9in9eL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.9.2": {"name": "send", "version": "0.9.2", "dependencies": {"ms": "0.6.2", "depd": "0.4.5", "etag": "~1.3.1", "mime": "1.2.11", "debug": "~2.0.0", "fresh": "0.2.4", "destroy": "1.0.3", "escape-html": "1.0.1", "on-finished": "2.1.0", "range-parser": "~1.0.2"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0"}, "dist": {"shasum": "77d22a0f462604451917075c6f52e69c2b3b6e25", "tarball": "https://registry.npmjs.org/send/-/send-0.9.2.tgz", "integrity": "sha512-o0LGsJJ2ath+6Zjnhtm0vXXJYB6p9t25WkT1nYuLrEx3LPvMHLGdjll/OOyRIl+0Zb3WJa3On2ji0OGJZm7Bgg==", "signatures": [{"sig": "MEUCIQDWb5ESjzdcNstymRwMBsUKo4Kk8sXyPw8BtANibSAeOAIgfr9a2FfywkKtcPZ+dWwzABwZhbL8cdSn0b13OmYLCHM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.9.3": {"name": "send", "version": "0.9.3", "dependencies": {"ms": "0.6.2", "depd": "0.4.5", "etag": "~1.4.0", "mime": "1.2.11", "debug": "~2.0.0", "fresh": "0.2.4", "destroy": "1.0.3", "escape-html": "1.0.1", "on-finished": "2.1.0", "range-parser": "~1.0.2"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.13.0"}, "dist": {"shasum": "b43a7414cd089b7fbec9b755246f7c37b7b85cc0", "tarball": "https://registry.npmjs.org/send/-/send-0.9.3.tgz", "integrity": "sha512-XT6wlKiDYip1xFylnX4y3cDej4RmDk/DuOCEhzXd4Lprb1DVMTU9o0N739te05nVwJ51IughQGWMsISrAm6GSg==", "signatures": [{"sig": "MEYCIQDiP6s9LkAXUQlNhWHPIVilTKhdjcZizFWQjtQ67HVkpQIhAL6ko5V/dbqHKSkWZ3/j9KTN9YgUPSnVvcEJF4JpKsuj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.10.0": {"name": "send", "version": "0.10.0", "dependencies": {"ms": "0.6.2", "depd": "~1.0.0", "etag": "~1.5.0", "mime": "1.2.11", "debug": "~2.1.0", "fresh": "0.2.4", "destroy": "1.0.3", "escape-html": "1.0.1", "on-finished": "2.1.0", "range-parser": "~1.0.2"}, "devDependencies": {"mocha": "~1.21.0", "should": "~4.0.0", "istanbul": "0.3.2", "supertest": "~0.14.0"}, "dist": {"shasum": "2f984b703934c628b72b72d70557b75ca906ea6c", "tarball": "https://registry.npmjs.org/send/-/send-0.10.0.tgz", "integrity": "sha512-Z6jqWIQeUD7H0b/BD+6D0GcB83bM+AXRT5cVq6E/BrkGp7h59o9zb3JFLW5uMkzECdZdK4eLoSNNnw/NLKXTfg==", "signatures": [{"sig": "MEQCIDviQg0KluKffNItijpmJR4mhuJ0A4Fu7kONzHOskRKfAiBTMSYfzLc9hMcKmg4gg2JUZGjJ0bh9Ts6H2FsadXAHzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.10.1": {"name": "send", "version": "0.10.1", "dependencies": {"ms": "0.6.2", "depd": "~1.0.0", "etag": "~1.5.0", "mime": "1.2.11", "debug": "~2.1.0", "fresh": "0.2.4", "destroy": "1.0.3", "escape-html": "1.0.1", "on-finished": "~2.1.1", "range-parser": "~1.0.2"}, "devDependencies": {"mocha": "~2.0.0", "should": "~4.1.0", "istanbul": "0.3.2", "supertest": "~0.14.0"}, "dist": {"shasum": "7745c50ec72f115115980e8fb179aec01900e08a", "tarball": "https://registry.npmjs.org/send/-/send-0.10.1.tgz", "integrity": "sha512-dSL7VfFGv0Du8qj0YntGl552UjWgZxTfFrBvngjc1wDPncyZnukfbGKWLW/Eo7qNlEbm6cUbLeCJBH9LJ/cDPQ==", "signatures": [{"sig": "MEYCIQCrU0Cssfbq87LPCceizyCqxPoxeyO8F/w2TpFn+TwUIAIhANC9Jd11dk2VWaAMgycNmONc6scag2YKoNAfVzpENBGo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.11.0": {"name": "send", "version": "0.11.0", "dependencies": {"ms": "0.7.0", "depd": "~1.0.0", "etag": "~1.5.1", "mime": "1.2.11", "debug": "~2.1.1", "fresh": "0.2.4", "destroy": "1.0.3", "escape-html": "1.0.1", "on-finished": "~2.2.0", "range-parser": "~1.0.2"}, "devDependencies": {"after": "0.8.1", "mocha": "~2.1.0", "istanbul": "0.3.5", "supertest": "~0.15.0"}, "dist": {"shasum": "d66b83b44576061ebd49551943b3c5c1f61cb308", "tarball": "https://registry.npmjs.org/send/-/send-0.11.0.tgz", "integrity": "sha512-zn4NlpyHfJboM1xJjoUd0N/sQOiO2C9rJanG5zUI5iEEuWrPKP6y01ArCFqLTTGRx2rPLEC5qiKBlqEI+w4SqA==", "signatures": [{"sig": "MEQCIE0lgSeoJRCKPY21FKQaJetZdd+wiNmPz1pVLFOazB/MAiBGeuTS/0jtPBP3dYDEZ/XOGolyVDyP+guUkUx6ofHsng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.11.1": {"name": "send", "version": "0.11.1", "dependencies": {"ms": "0.7.0", "depd": "~1.0.0", "etag": "~1.5.1", "mime": "1.2.11", "debug": "~2.1.1", "fresh": "0.2.4", "destroy": "1.0.3", "escape-html": "1.0.1", "on-finished": "~2.2.0", "range-parser": "~1.0.2"}, "devDependencies": {"after": "0.8.1", "mocha": "~2.1.0", "istanbul": "0.3.5", "supertest": "~0.15.0"}, "dist": {"shasum": "1beabfd42f9e2709f99028af3078ac12b47092d5", "tarball": "https://registry.npmjs.org/send/-/send-0.11.1.tgz", "integrity": "sha512-OeD0vwGXsQpyrdGganLdZRYOZFOM5Acb99nloxNcx7ECJfqB4XUGUfFNE12/TU1VwpJnCUt1XFIT02prFFHdzg==", "signatures": [{"sig": "MEYCIQCJU2gNVir1efNLdtZyFQxBJ/LLbM+xcb9rnrgO2T75+QIhANi/w0AH/OqU4MqrhrFSG9eapuEhkJ1Phsiceoo61PpJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.12.0": {"name": "send", "version": "0.12.0", "dependencies": {"ms": "0.7.0", "depd": "~1.0.0", "etag": "~1.5.1", "mime": "1.3.4", "debug": "~2.1.1", "fresh": "0.2.4", "destroy": "1.0.3", "escape-html": "1.0.1", "on-finished": "~2.2.0", "range-parser": "~1.0.2"}, "devDependencies": {"after": "0.8.1", "mocha": "~2.1.0", "istanbul": "0.3.5", "supertest": "~0.15.0"}, "dist": {"shasum": "d8c124a27797c47206d8fd52d37cd27ef15a506e", "tarball": "https://registry.npmjs.org/send/-/send-0.12.0.tgz", "integrity": "sha512-q0W10Rvj9T85UdCTovBd55Yz7xJcVzTAX1WYWobHSIHdIDtkKnW7v8yW7Yi33BRVGVungzCSX7ArKyhXYUqlfQ==", "signatures": [{"sig": "MEUCIQCuDdso8x6INL6FLF0GvGdMoIDVJOTZBJeG7qenHNK5kgIgJwDetwHad+3w6h4FivAyqdoVQ48RhxzqLuJDc+sXvhk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.12.1": {"name": "send", "version": "0.12.1", "dependencies": {"ms": "0.7.0", "depd": "~1.0.0", "etag": "~1.5.1", "mime": "1.3.4", "debug": "~2.1.1", "fresh": "0.2.4", "destroy": "1.0.3", "escape-html": "1.0.1", "on-finished": "~2.2.0", "range-parser": "~1.0.2"}, "devDependencies": {"after": "0.8.1", "mocha": "~2.1.0", "istanbul": "0.3.5", "supertest": "~0.15.0"}, "dist": {"shasum": "65e2e4330eae6b4d1082a921bfc8e9c9f1776b31", "tarball": "https://registry.npmjs.org/send/-/send-0.12.1.tgz", "integrity": "sha512-8ADnrRIOgJ6pqGmKwNvo48U6lF3LE0OgFr0dKxTOSiXaGQNgTJooiQOMVQ8eqfhcO6ZXx4UCkKFCHgvkULcPrg==", "signatures": [{"sig": "MEYCIQDsfWBNxIKX1Y27DuJWBR/PYjob+fOrCkkr05rQfSYKlgIhAI22e5xPwk//Z8RDRVP8AZEuD9glZv2RxfLqx4WCmsQZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.12.2": {"name": "send", "version": "0.12.2", "dependencies": {"ms": "0.7.0", "depd": "~1.0.0", "etag": "~1.5.1", "mime": "1.3.4", "debug": "~2.1.3", "fresh": "0.2.4", "destroy": "1.0.3", "escape-html": "1.0.1", "on-finished": "~2.2.0", "range-parser": "~1.0.2"}, "devDependencies": {"after": "0.8.1", "mocha": "~2.2.1", "istanbul": "0.3.7", "supertest": "~0.15.0"}, "dist": {"shasum": "ba6785e47ab41aa0358b9da401ab22ff0f58eab6", "tarball": "https://registry.npmjs.org/send/-/send-0.12.2.tgz", "integrity": "sha512-EANBz0IZpoywzaJxlojGQx78mRBf9cRAv58rEYS8G9mS3PDm7NOMyCO1z/UgiW3giSRldQlv0RtvOWSmJ6lsgQ==", "signatures": [{"sig": "MEQCIEOO/Cwh6YhQkf7ChKliwZv+iKkRcz9IGhe4kvE3LpvrAiBWSbThNyvhtXqXPNjC7h5UMSbU1rykUzvi2WqTlahbHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.12.3": {"name": "send", "version": "0.12.3", "dependencies": {"ms": "0.7.1", "depd": "~1.0.1", "etag": "~1.6.0", "mime": "1.3.4", "debug": "~2.2.0", "fresh": "0.2.4", "destroy": "1.0.3", "escape-html": "1.0.1", "on-finished": "~2.2.1", "range-parser": "~1.0.2"}, "devDependencies": {"after": "0.8.1", "mocha": "2.2.4", "istanbul": "0.3.9", "supertest": "~0.15.0"}, "dist": {"shasum": "cd12dc58fde21e4f91902b39b2fda05a7a6d9bdc", "tarball": "https://registry.npmjs.org/send/-/send-0.12.3.tgz", "integrity": "sha512-T/5qhRIkka7r2hnJRWcgpylTpreWNYk7G5EpYrmLNBhz3eP3c8TeasftFr9q++7rKVwRmnfuksMxujot1a74HA==", "signatures": [{"sig": "MEYCIQCPSvtOrW5sCpvGaT8w4jfuC/+DfkAm+7SEzsc5PIl3dQIhAKZiJ54SDt0gODgnC69Bq53vEUlktZwtVLmshW2j61lR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.13.0": {"name": "send", "version": "0.13.0", "dependencies": {"ms": "0.7.1", "depd": "~1.0.1", "etag": "~1.7.0", "mime": "1.3.4", "debug": "~2.2.0", "fresh": "0.3.0", "destroy": "1.0.3", "statuses": "~1.2.1", "escape-html": "1.0.2", "http-errors": "~1.3.1", "on-finished": "~2.3.0", "range-parser": "~1.0.2"}, "devDependencies": {"after": "0.8.1", "mocha": "2.2.5", "istanbul": "0.3.9", "supertest": "1.0.1"}, "dist": {"shasum": "518f921aeb0560aec7dcab2990b14cf6f3cce5de", "tarball": "https://registry.npmjs.org/send/-/send-0.13.0.tgz", "integrity": "sha512-zck2y84i0SbUUiwq2l5gGPNVpCplL48og5xIhFjNjQa09003YCTy6Vb3rKfVuG8W8PWNUtUOntjQEBdwkJ9oBw==", "signatures": [{"sig": "MEQCIACUqEiAZiFv+BBqafeoLBcMw9OonWavdvf4VtJfiRFwAiAo6uV2tcmwMDORwi0xYOeaB71Nu/vPs58OGoh8PuVLDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.13.1": {"name": "send", "version": "0.13.1", "dependencies": {"ms": "0.7.1", "depd": "~1.1.0", "etag": "~1.7.0", "mime": "1.3.4", "debug": "~2.2.0", "fresh": "0.3.0", "destroy": "~1.0.4", "statuses": "~1.2.1", "escape-html": "~1.0.3", "http-errors": "~1.3.1", "on-finished": "~2.3.0", "range-parser": "~1.0.3"}, "devDependencies": {"after": "0.8.1", "mocha": "2.3.4", "istanbul": "0.4.2", "supertest": "1.1.0"}, "dist": {"shasum": "a30d5f4c82c8a9bae9ad00a1d9b1bdbe6f199ed7", "tarball": "https://registry.npmjs.org/send/-/send-0.13.1.tgz", "integrity": "sha512-tajY7yMvJena2iggWhCzaysOVj/CH4AzqV2lJHUHboVNWQkIFEBJdKtzryKg3fLa83lxq9n/WQV53w9JZCe72w==", "signatures": [{"sig": "MEUCIQDbT0E2yCWAUmkcLm1rfDpx2l8nTc0F47TFsaSkRjRjHAIgMmBDZgh+rNVrkiz5zhsJCZEpS6BJ2E67pHdj0vdH+nI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.13.2": {"name": "send", "version": "0.13.2", "dependencies": {"ms": "0.7.1", "depd": "~1.1.0", "etag": "~1.7.0", "mime": "1.3.4", "debug": "~2.2.0", "fresh": "0.3.0", "destroy": "~1.0.4", "statuses": "~1.2.1", "escape-html": "~1.0.3", "http-errors": "~1.3.1", "on-finished": "~2.3.0", "range-parser": "~1.0.3"}, "devDependencies": {"after": "0.8.1", "mocha": "2.4.5", "istanbul": "0.4.2", "supertest": "1.1.0"}, "dist": {"shasum": "765e7607c8055452bba6f0b052595350986036de", "tarball": "https://registry.npmjs.org/send/-/send-0.13.2.tgz", "integrity": "sha512-cQ0rmXHrdO2Iof08igV2bG/yXWD106ANwBg6DkGQNT2Vsznbgq6T0oAIQboy1GoFsIuy51jCim26aA9tj3Z3Zg==", "signatures": [{"sig": "MEUCIBjkL/6qoRJN751LQagntOKCoTY7p5Zoqxt5XBoCa+lbAiEAxhtVF2Sri0L5C9HNtJLHI7pEO9zKJp2forRUUpwvHxA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.14.0": {"name": "send", "version": "0.14.0", "dependencies": {"ms": "0.7.1", "depd": "~1.1.0", "etag": "~1.7.0", "mime": "1.3.4", "debug": "~2.2.0", "fresh": "0.3.0", "destroy": "~1.0.4", "statuses": "~1.3.0", "escape-html": "~1.0.3", "http-errors": "~1.5.0", "on-finished": "~2.3.0", "range-parser": "~1.2.0"}, "devDependencies": {"after": "0.8.1", "mocha": "2.5.3", "eslint": "2.11.1", "istanbul": "0.4.3", "supertest": "1.1.0", "eslint-plugin-promise": "1.3.1", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "dist": {"shasum": "6b192d05c0b87c48263738bba9d50d04b2328b77", "tarball": "https://registry.npmjs.org/send/-/send-0.14.0.tgz", "integrity": "sha512-f/lAV4N30Bt4niddCWU1aZotFKF2yepR/bHjMcvaVk/dm1UhZZe55kTX2Xtc7GIvnr5Wqv1X5xE2QkG9kWPhbQ==", "signatures": [{"sig": "MEQCIGN3eQgTr0dh3ZQIKl5RpQyvaOMCVQ0cDIazQUs5iVqKAiB7Y0E19VG0Icykeh2vMnEGcWed2/iyuHES8Yptx6+02g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.14.1": {"name": "send", "version": "0.14.1", "dependencies": {"ms": "0.7.1", "depd": "~1.1.0", "etag": "~1.7.0", "mime": "1.3.4", "debug": "~2.2.0", "fresh": "0.3.0", "destroy": "~1.0.4", "statuses": "~1.3.0", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "http-errors": "~1.5.0", "on-finished": "~2.3.0", "range-parser": "~1.2.0"}, "devDependencies": {"after": "0.8.1", "mocha": "2.5.3", "eslint": "2.11.1", "istanbul": "0.4.3", "supertest": "1.1.0", "eslint-plugin-promise": "1.3.1", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "dist": {"shasum": "a954984325392f51532a7760760e459598c89f7a", "tarball": "https://registry.npmjs.org/send/-/send-0.14.1.tgz", "integrity": "sha512-1Ru269QpUVUgD32Y9jdyBXiX+pHYuYnTzR17w+DhyOWvGMPjJILrnLhl9c4LQjtIy2BSAa6Ykq0ZdGcAjaXlwQ==", "signatures": [{"sig": "MEYCIQCBOHw4ZokPkXJx4rQgSwBeDQp8KeNmbAK7fJ9TT4GUFAIhALFylNF9F509sBlocKHetUgc5LxOykRuJoCKc54siZkP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.14.2": {"name": "send", "version": "0.14.2", "dependencies": {"ms": "0.7.2", "depd": "~1.1.0", "etag": "~1.7.0", "mime": "1.3.4", "debug": "~2.2.0", "fresh": "0.3.0", "destroy": "~1.0.4", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "http-errors": "~1.5.1", "on-finished": "~2.3.0", "range-parser": "~1.2.0"}, "devDependencies": {"after": "0.8.2", "mocha": "2.5.3", "eslint": "2.11.1", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "1.3.1", "eslint-config-standard": "5.3.1", "eslint-plugin-standard": "1.3.2"}, "dist": {"shasum": "39b0438b3f510be5dc6f667a11f71689368cdeef", "tarball": "https://registry.npmjs.org/send/-/send-0.14.2.tgz", "integrity": "sha512-36O39SV4A6lj4TBALc0tAMmiTwClC2Npp6wiRvzxqyrH3yTiYwAmWVyB2a0a/D3ISCQVHY/l+VO/9JVo6ZubfA==", "signatures": [{"sig": "MEQCICYSRMA5oy+/YDf/qSBpe5eZAYPtBSaf7f0CZVO03cXoAiALwsksDKNKLm45t6/jMpqOtU/2xbmbVRNG3zIO50UIqQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.15.0": {"name": "send", "version": "0.15.0", "dependencies": {"ms": "0.7.2", "depd": "~1.1.0", "etag": "~1.8.0", "mime": "1.3.4", "debug": "2.6.1", "fresh": "0.5.0", "destroy": "~1.0.4", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "http-errors": "~1.6.1", "on-finished": "~2.3.0", "range-parser": "~1.2.0"}, "devDependencies": {"after": "0.8.2", "mocha": "2.5.3", "eslint": "3.16.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.4.2", "eslint-config-standard": "6.2.1", "eslint-plugin-markdown": "1.0.0-beta.3", "eslint-plugin-standard": "2.0.1"}, "dist": {"shasum": "f0185d6466fa76424b866f3d533e2d19dd0aaa39", "tarball": "https://registry.npmjs.org/send/-/send-0.15.0.tgz", "integrity": "sha512-ZhqaTSQTbQmRvPLArT534A3SJVbnZknM9XcPOJoMulg9A4hXStwKaTaR7dve8eFOA9DmzmZdJpc1nVEkeKyxCw==", "signatures": [{"sig": "MEUCIQDKvTxmuX/1YCwerY3LF0PPVJ9R6C9RUXWbnw+pQdD3ZAIgc2A/9g49RJ9yZudtXElUiIWcsCymT3CbLVq0ngjJ3v8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.15.1": {"name": "send", "version": "0.15.1", "dependencies": {"ms": "0.7.2", "depd": "~1.1.0", "etag": "~1.8.0", "mime": "1.3.4", "debug": "2.6.1", "fresh": "0.5.0", "destroy": "~1.0.4", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "http-errors": "~1.6.1", "on-finished": "~2.3.0", "range-parser": "~1.2.0"}, "devDependencies": {"after": "0.8.2", "mocha": "2.5.3", "eslint": "3.17.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "7.0.0", "eslint-plugin-markdown": "1.0.0-beta.4", "eslint-plugin-standard": "2.1.1"}, "dist": {"shasum": "8a02354c26e6f5cca700065f5f0cdeba90ec7b5f", "tarball": "https://registry.npmjs.org/send/-/send-0.15.1.tgz", "integrity": "sha512-mDWpAnBCtb9eQJCpZwH8lHf2bFfKl3H575n/xcBU3xfH+gYoHTxfTZ1EhQF+W5k2SpsaRtQTLjhAAjDFekdEgw==", "signatures": [{"sig": "MEUCIDOrjeT6X3NJ29+tUMnCD9xYRqViGjXiFQbnvK8XVq4CAiEArLUKmNQgfvEJbZOZZQDHZHzLRlsYFeQlfsRRNSLvBaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.15.2": {"name": "send", "version": "0.15.2", "dependencies": {"ms": "1.0.0", "depd": "~1.1.0", "etag": "~1.8.0", "mime": "1.3.4", "debug": "2.6.4", "fresh": "0.5.0", "destroy": "~1.0.4", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "http-errors": "~1.6.1", "on-finished": "~2.3.0", "range-parser": "~1.2.0"}, "devDependencies": {"after": "0.8.2", "mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "7.1.0", "eslint-plugin-markdown": "1.0.0-beta.4", "eslint-plugin-standard": "2.3.1"}, "dist": {"shasum": "f91fab4403bcf87e716f70ceb5db2f578bdc17d6", "tarball": "https://registry.npmjs.org/send/-/send-0.15.2.tgz", "integrity": "sha512-hvoAUZRaU7JoQb8Bk+6DxFKkAxQLI1MhMkGHlc2p0HNSnNVCSNgVCt2dTOE47dOm4z/L6kmAGThzCR2l5S/DUg==", "signatures": [{"sig": "MEUCIQCjo90JnN4l+93hLhYufI2WFcJ2szD7pKdYd0egfK5viwIgZaLNikyWsaSQOPDrB4y5jUE/gDVmMmFiuE9yLvSMxiI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.15.3": {"name": "send", "version": "0.15.3", "dependencies": {"ms": "2.0.0", "depd": "~1.1.0", "etag": "~1.8.0", "mime": "1.3.4", "debug": "2.6.7", "fresh": "0.5.0", "destroy": "~1.0.4", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "http-errors": "~1.6.1", "on-finished": "~2.3.0", "range-parser": "~1.2.0"}, "devDependencies": {"after": "0.8.2", "mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "7.1.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "2.3.1"}, "dist": {"shasum": "5013f9f99023df50d1bd9892c19e3defd1d53309", "tarball": "https://registry.npmjs.org/send/-/send-0.15.3.tgz", "integrity": "sha512-w21BXI1HgOfW8PQmKAU7yQJJmTPaXN3zwsAnRXkawmSC+W+pUeAE0rgt3dO5bM+ia3Gy8CDekD1FE/ZZMtlewQ==", "signatures": [{"sig": "MEQCIBN9Plthbp+5NqzkUImCTbFNiflu4WOwszxXHmCqhDqRAiAIJ7ItVJ+POcXFaAL+lKIUPqDRgva71hLQ62Z5A7iNyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.15.4": {"name": "send", "version": "0.15.4", "dependencies": {"ms": "2.0.0", "depd": "~1.1.1", "etag": "~1.8.0", "mime": "1.3.4", "debug": "2.6.8", "fresh": "0.5.0", "destroy": "~1.0.4", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "on-finished": "~2.3.0", "range-parser": "~1.2.0"}, "devDependencies": {"after": "0.8.2", "mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "7.1.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "2.3.1"}, "dist": {"shasum": "985faa3e284b0273c793364a35c6737bd93905b9", "tarball": "https://registry.npmjs.org/send/-/send-0.15.4.tgz", "integrity": "sha512-EwaLqFmlXRHiqBkYXIuQ8WtslF3EwRslJwhdrhRv4ypTNpR1cGQlHLeH1SqMWwPdqf0CntKCV22+EWpFmW+QcQ==", "signatures": [{"sig": "MEYCIQCP0rwqSG7gnOiWi9JtDSN7EbEkjDaqgRlmI05B3mgh8wIhAKfLKxJ3hoKacbsu1tPi64Nl4To+7IuUt727a8zhAp/N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.15.5": {"name": "send", "version": "0.15.5", "dependencies": {"ms": "2.0.0", "depd": "~1.1.1", "etag": "~1.8.1", "mime": "1.3.4", "debug": "2.6.8", "fresh": "0.5.2", "destroy": "~1.0.4", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "on-finished": "~2.3.0", "range-parser": "~1.2.0"}, "devDependencies": {"after": "0.8.2", "mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "7.1.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "2.3.1"}, "dist": {"shasum": "32ef6c8d820c9756597c3174b8c9dd51e3319be2", "tarball": "https://registry.npmjs.org/send/-/send-0.15.5.tgz", "integrity": "sha512-SQKz2Vgiirpd/0q9fy8zEGyx+ZlqCGvyR+2uj8r8FYTXkJAsEFEpqh/nsFmUxn6eFtuCENorJGWgC74rHx92NQ==", "signatures": [{"sig": "MEUCIQC2e+YV6K4UlUISkop1W9TeUV4Rr3Cxb3dT10Tl9aZJXAIgcfa90o/ARb4YBc3cLtoqiH1MwwHWHgHMPvI0rDgPI94=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.15.6": {"name": "send", "version": "0.15.6", "dependencies": {"ms": "2.0.0", "depd": "~1.1.1", "etag": "~1.8.1", "mime": "1.3.4", "debug": "2.6.9", "fresh": "0.5.2", "destroy": "~1.0.4", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "on-finished": "~2.3.0", "range-parser": "~1.2.0"}, "devDependencies": {"after": "0.8.2", "mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "7.1.0", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "2.3.1"}, "dist": {"shasum": "20f23a9c925b762ab82705fe2f9db252ace47e34", "tarball": "https://registry.npmjs.org/send/-/send-0.15.6.tgz", "integrity": "sha512-e1/758VJ+GsPg8vE+Z/xE7R36IWogUl8rrrs53CsfHrT2IyZyPggfvbHT8HTV3yBNKrUHYUTsBQ9pXQYkcB4YQ==", "signatures": [{"sig": "MEQCIBcdGaVWePSVKesfgcfni+qEN1smLULDFJZCEHAFbXisAiA42YTjl1UKoFzHj1amLSH4heAUO7W+uXI4dsY1fsahkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.16.0": {"name": "send", "version": "0.16.0", "dependencies": {"ms": "2.0.0", "depd": "~1.1.1", "etag": "~1.8.1", "mime": "1.4.1", "debug": "2.6.9", "fresh": "0.5.2", "destroy": "~1.0.4", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "on-finished": "~2.3.0", "range-parser": "~1.2.0"}, "devDependencies": {"after": "0.8.2", "mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "5.1.1", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "16338dbb9a2ede4ad57b48420ec3b82d8e80a57b", "tarball": "https://registry.npmjs.org/send/-/send-0.16.0.tgz", "integrity": "sha512-W81c6BxGKWLkZVosEEUy30CKXVR4CjCaviWzU4vE6lxhz1/vokb7b6OGbnlj+hPECUPBzv1iXTyRjt6opRRsdg==", "signatures": [{"sig": "MEUCIQC2o6MYz/x1VOp8YMBgJnqGXed7aXF8ndwMuQW0wUEcmgIgWXsPTPDeAm+eNgqs53qZj0mZYAMWngNb+sxEtmqwfww=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.16.1": {"name": "send", "version": "0.16.1", "dependencies": {"ms": "2.0.0", "depd": "~1.1.1", "etag": "~1.8.1", "mime": "1.4.1", "debug": "2.6.9", "fresh": "0.5.2", "destroy": "~1.0.4", "statuses": "~1.3.1", "encodeurl": "~1.0.1", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "on-finished": "~2.3.0", "range-parser": "~1.2.0"}, "devDependencies": {"after": "0.8.2", "mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "5.2.0", "eslint-plugin-import": "2.7.0", "eslint-plugin-promise": "3.5.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "a70e1ca21d1382c11d0d9f6231deb281080d7ab3", "tarball": "https://registry.npmjs.org/send/-/send-0.16.1.tgz", "integrity": "sha512-ElCLJdJIKPk6ux/Hocwhk7NFHpI3pVm/IZOYWqUmoxcgeyM+MpxHHKhb8QmlJDX1pU6WrgaHBkVNm73Sv7uc2A==", "signatures": [{"sig": "MEYCIQDK9zSphnDZxdUi8+BUMzK3Ls078llHXZka+uTO1dxlggIhALMvyyY0sDIrN2+nxdMaf8WvrQNK2tLDhWvSp0VW+X77", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">= 0.8.0"}}, "0.16.2": {"name": "send", "version": "0.16.2", "dependencies": {"ms": "2.0.0", "depd": "~1.1.2", "etag": "~1.8.1", "mime": "1.4.1", "debug": "2.6.9", "fresh": "0.5.2", "destroy": "~1.0.4", "statuses": "~1.4.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "on-finished": "~2.3.0", "range-parser": "~1.2.0"}, "devDependencies": {"after": "0.8.2", "mocha": "2.5.3", "eslint": "3.19.0", "istanbul": "0.4.5", "supertest": "1.1.0", "eslint-plugin-node": "5.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-promise": "3.6.0", "eslint-config-standard": "10.2.1", "eslint-plugin-markdown": "1.0.0-beta.6", "eslint-plugin-standard": "3.0.1"}, "dist": {"shasum": "6ecca1e0f8c156d141597559848df64730a6bbc1", "tarball": "https://registry.npmjs.org/send/-/send-0.16.2.tgz", "fileCount": 5, "integrity": "sha512-E64YFPUssFHEFBvpbbjr44NCLtI1AohxQ8ZSiJjQLskAdKuriYEP6VyGEsRDH8ScozGpkaX1BGvhanqCwkcEZw==", "signatures": [{"sig": "MEUCICMHLRAl2W5+FP9U8XKoSFlv8aJz0lSXEzhA436go5PBAiEAqSRQ7eFvPGvXTX/jJZiNCbcF757sCnRpZWvE2Z3yGVo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46571}, "engines": {"node": ">= 0.8.0"}}, "0.17.0": {"name": "send", "version": "0.17.0", "dependencies": {"ms": "2.1.1", "depd": "~1.1.2", "etag": "~1.8.1", "mime": "1.6.0", "debug": "2.6.9", "fresh": "0.5.2", "destroy": "~1.0.4", "statuses": "~1.5.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "http-errors": "~1.7.2", "on-finished": "~2.3.0", "range-parser": "~1.2.0"}, "devDependencies": {"after": "0.8.2", "mocha": "6.1.4", "eslint": "5.16.0", "istanbul": "0.4.5", "supertest": "4.0.2", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "dist": {"shasum": "6d190beaaf08c5cf7e325ded024f1a7cd934ed9a", "tarball": "https://registry.npmjs.org/send/-/send-0.17.0.tgz", "fileCount": 5, "integrity": "sha512-NYR0jCuwnBaGA2X5bO3+QDZmmJ+PUCvFCRTED5nx9l/BK3Pr8mD8Ryvk9bw08JJUdXxt2u+tVIGoqJPrHWGqSA==", "signatures": [{"sig": "MEMCH1MArrssZ3V+XQ3LISC+4/+lFxvhHoUwCh/HKonUc6YCIEyRJg09OyLqSxLScAWp50z9T6FtqTcJlxSe88Rymrgr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczLPjCRA9TVsSAnZWagAAzrIP/30L3J0n3A7tIHr+BqYX\nvEBFSet7QNTYYmdiunNkEVZaiJtrJTRspaipTRtBrJw7Bmjhe9Fi5pvan3gi\nd1lLBQMWSfVJoSLAwpDtBzmq73e2QNs8e3tH9cmYNBX79oFKNa2CNoftiZtD\n1QMphhjmUD7Ead/YCS/lroNY2TvJjkOscsk7DiVFbu4YKGldqVDz/qLeQPV9\nMxpQcWkS0yKla9N8y4mrLBmEjKwKNsDiyWP5BJIhDWNLWcI3yICNpY3Z3pcz\n9X7VPZNMnpuFSpqZ37C/a5cfTjki0roBd4vGgNC4B72N+4MNF11HQzeMjNL1\neolCWtS6WDijOtUBuiV8+BHM4iElyDQ2PkelCM/23AXLC4wLxYH+JiLVsHNP\nm/gfHK7Y/Brr44r5zyKk4BP+HOgaRefzEPv0jJ+lkdH9N/R22Cw1yMn1FVZ0\nbRDCFlQyKRCbPgKIzEREGcU/3Z+KMbc8JDKs6RfCxQx5WXEjFKgx1aLM0inR\nIeBv/v6T+8Xjm6XbXZFNK5VOuP2ujKEjjK9JRp6lD3aJOe51XUzq/jwARnXr\n5h9adHpY4i55gxmBGKln/muQ3ADZG+bZp372JVT5nAJK83/Y4P0InuUrgoyj\nuVBSj+Fl5Xh1JP31D2aTEm3OAFUAKh2r8rn7qiAjevnByBC0X89EJTYzFiS6\nrNoQ\r\n=F+qh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8.0"}}, "0.17.1": {"name": "send", "version": "0.17.1", "dependencies": {"ms": "2.1.1", "depd": "~1.1.2", "etag": "~1.8.1", "mime": "1.6.0", "debug": "2.6.9", "fresh": "0.5.2", "destroy": "~1.0.4", "statuses": "~1.5.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "http-errors": "~1.7.2", "on-finished": "~2.3.0", "range-parser": "~1.2.1"}, "devDependencies": {"after": "0.8.2", "mocha": "6.1.4", "eslint": "5.16.0", "istanbul": "0.4.5", "supertest": "4.0.2", "eslint-plugin-node": "8.0.1", "eslint-plugin-import": "2.17.2", "eslint-plugin-promise": "4.1.1", "eslint-config-standard": "12.0.0", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-standard": "4.0.0"}, "dist": {"shasum": "c1d8b059f7900f7466dd4938bdc44e11ddb376c8", "tarball": "https://registry.npmjs.org/send/-/send-0.17.1.tgz", "fileCount": 5, "integrity": "sha512-BsVKsiGcQMFwT8UxypobUKyv7irCNRHk1T0G680vk88yf6LBByGcZJOTJCrTP2xVN6yI+XjPJcNuE3V4fT9sAg==", "signatures": [{"sig": "MEUCIQCw4cMNj+Z+r45WWiPOQuLBBHB4QdTELnioza3Cwzi4ugIgbnpdTwR4uc7n8usKOKeKhH2L1FmUJC3clv/h/TxJLJM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc1igoCRA9TVsSAnZWagAA4xIP/RwpQJfYKVcRmNymna4k\n8R7ZXHoC9MyoxLCBvNuzZ5uy/HCYYVgPR7ilzmDfDQInJZJOZUBiMXFBs413\ntyXBv5Y0kXVARuXPBcy/pH6cVCn5nFlOneEj+ntNo5mrFCJxysxCR9xfoG7o\nuKfVrPmKRVm7dLJBuJzPzjQZEL1b6GqV3+aMypBNdGwK8E53MgJodwdZQbvg\nixSOMJ1D0e9qY4afOZII9Ejpoxk3+bu5+UadK++vYtWFCh2REhd+dzpD8FTu\nAah/Ub1jt7WHb2rJNclhxh+DsiIwWukCIpJ1dsPSBTQ+MQjoAXNrJdVxnMcj\n7uwXW/7wRho0o5q59JfUH98zv0GeIYjfjLfhS8uHm43niDtvnTLKe3ZTJqVY\nMPQz71+VfaTE5rHKpyrHxEhj1MGKFwAGQbNtrhx2HVvtLMz+qNLAeCa6rUPR\n48U9yJ2HfEhlm+y08i43lEtdY+Sk5oNtG+Wk1PUUlPdbXW/Hma13ALXK57pP\n09Q8IZwbTGufeJnK2maVByHc+08GZ4FclVAd8h3pUeFxyK2MR9hbtlQx27sg\n4KXbizzUuPx4tO7qp9aa+oS8wS1qLn/BkrFJPKuzExvnnsrTttEc1S1LHbhg\nlVFT6U1oGpXQlLiBwbzotePJeFXcLZsxovN+NfCZT0csI83ivh5bREiUImkH\nIWSh\r\n=fsxk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8.0"}}, "0.17.2": {"name": "send", "version": "0.17.2", "dependencies": {"ms": "2.1.3", "depd": "~1.1.2", "etag": "~1.8.1", "mime": "1.6.0", "debug": "2.6.9", "fresh": "0.5.2", "destroy": "~1.0.4", "statuses": "~1.5.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "http-errors": "1.8.1", "on-finished": "~2.3.0", "range-parser": "~1.2.1"}, "devDependencies": {"nyc": "15.1.0", "after": "0.8.2", "mocha": "9.1.3", "eslint": "7.32.0", "supertest": "6.1.6", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "926622f76601c41808012c8bf1688fe3906f7820", "tarball": "https://registry.npmjs.org/send/-/send-0.17.2.tgz", "fileCount": 5, "integrity": "sha512-UJYB6wFSJE3G00nEivR5rgWp8c2xXvJ3OPWPhmuteU0IKj8nKbG3DrjiOmLwpnHGYWAVwA69zmTm++YG0Hmwww==", "signatures": [{"sig": "MEYCIQC7P4n76KYapsycPyRE4sWAcMsppZhu6EizzWSeDtLqkAIhAMPbp8YdBmdhs9+5J8G3stlvfO8/v9JxT9iHbhDUuirN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhtU3hCRA9TVsSAnZWagAA/10P/Rv5PGfaGUFE5uCg0x0T\nAjEBZT2PD0k6eLINMk8LDLJeq3mBG7o+4A+aIu6eHSYyiSFYVKrsMGPQDgil\nUHAnKKMpQfb+JoIknTZ3MO91cxPb3sHyMVthlFFDgVONNfGz6jLVRJM+JkI1\nLGmRYIqy1dMVEGpiiquCi8akl1HlpvwMRgMycued7RQR9jFHIVSvAH9JJDHv\nnh5v4STONZkIOlXtRlsWt1NKlYANMyb0Nn1aaWZCM0M5/vicF059yCbcl23N\niJaiptTGaalVcp1vPNlPBPpSi6g8rZM/k+HCZ7s2F0BvODUbQV3PZ50/gqFX\nSvvKr1g9R/slukgf3lkf62BOyCfCGM+2aMxEXIflVkS5si/77R1hubgfTPy8\nomrXsWlgowDO9wQEHEqabX4mnrJsvPSaw1R48f7he5IQ1/UTmUWq+eDkJ/zK\ngN+CJBClSEhqv57TWpL4ompHUdFOzLctxT2fvHkhnWTu2y/A6tkyiCA/gBzE\naxhwf8+xI87mPA0mQghKIsWeJzoHBPMbqIH2/SuntROl7/4nP0ssutUiR0e/\npv5qJxc9IjaEjrvPqst2edrg8rJK52hSOo7w7B9qx0XNTBSGJljvWfwYanNO\nd7KRYSh9jSdCmxvdewQsG2hhHKyrsup6pdl/KKawbwLsBJIYFsCwx5buyp/l\nzoS0\r\n=hCoN\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8.0"}}, "1.0.0-beta.1": {"name": "send", "version": "1.0.0-beta.1", "dependencies": {"ms": "2.1.3", "etag": "~1.8.1", "debug": "3.1.0", "fresh": "0.5.2", "destroy": "~1.0.4", "statuses": "~1.5.0", "encodeurl": "~1.0.2", "mime-types": "~2.1.34", "escape-html": "~1.0.3", "http-errors": "1.8.1", "on-finished": "~2.3.0", "range-parser": "~1.2.1"}, "devDependencies": {"nyc": "15.1.0", "after": "0.8.2", "mocha": "9.1.3", "eslint": "7.32.0", "supertest": "6.1.6", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "9db741443b0b25771cfd38adc833376ae2d606fe", "tarball": "https://registry.npmjs.org/send/-/send-1.0.0-beta.1.tgz", "fileCount": 5, "integrity": "sha512-OKTRokcl/oo34O8+6aUpj8Jf2Bjw2D0tZzmX0/RvyfVC9ZOZW+HPAWAlhS817IsRaCnzYX1z++h2kHFr2/KNRg==", "signatures": [{"sig": "MEUCIQDC2bOrz17sxiJzI2Qg6+B3HKbCYRlcmNl8yYg1h1A5MgIgTI17tEzY105NeshlVNlPDS2Y7sMjl923M58qavzXbPE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/gPECRA9TVsSAnZWagAAaCEP/Rpn3JJ5qqXEZ67C9tuj\nkHINGkRNOnNhWZ3FNKaTLLdNcAiesq3vHqw6Y3bBbKre2UXIw8iqTzeeulIW\noLJsSx6M6sVA43EMLL/waYt9H4NpeY3WPbk5rfw44cQxq7Bb3Nih65UuXKAO\nYoIMRFvrKSKwy2gfri+yBvWn3MntLCTAylSGYpbyItSXP8gbVuB+3kU1ATzO\nld6YCdRn8F6ocwSAZFsvRXj3VbVMYtE9Q4we/1Jjh64UcxofRC1f1JRfxvye\nvTlC/+Y5RAeUGFzSYHafvAVucPhAqR2vvnQLsFhHEvP/8I9CJDh1Z3Km/8Yz\nZWhqj57xt6o4uGQ5EfNBjgmbzIvM9BgIvaHN95UkiMspldmac6pllr5Whwt+\n46yxLCkHgk9W934K6MDfZiwLQaaJrShgHqE0irdC0YAZRKs43THqOiY+muRI\nagi6BMWxwPnP2nw4KQLmtZ110q07Puywg/C4Elzo47AFg8zcSgXSyOIde2Os\n/LDfNThC2r765i3O9NggSN5i6D27xvPsN3TSzjQ0J06B2vyTlgdHpx2VuaYo\n7pjvCYe4XV5zL1JElhr3P+9zqLFA8llZUBuS/32MZ7ChJGd3rAFq2JBkWX4U\nNfjuh5FODHY9l4eKveMKqFGfQB02lgjpzzf9lRpGlYFuRyW3m8spCCcrwVm7\nrNGZ\r\n=TmVk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.10"}}, "0.18.0": {"name": "send", "version": "0.18.0", "dependencies": {"ms": "2.1.3", "depd": "2.0.0", "etag": "~1.8.1", "mime": "1.6.0", "debug": "2.6.9", "fresh": "0.5.2", "destroy": "1.2.0", "statuses": "2.0.1", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "http-errors": "2.0.0", "on-finished": "2.4.1", "range-parser": "~1.2.1"}, "devDependencies": {"nyc": "15.1.0", "after": "0.8.2", "mocha": "9.2.2", "eslint": "7.32.0", "supertest": "6.2.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "670167cc654b05f5aa4a767f9113bb371bc706be", "tarball": "https://registry.npmjs.org/send/-/send-0.18.0.tgz", "fileCount": 6, "integrity": "sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==", "signatures": [{"sig": "MEUCIFj934rWRXZmJKTfRQSls/wU0u1YwNmgwfBdpVp9Q3JIAiEAwprO9Qcf306cRvl2/xAtfGvogS4aUaC39HVopQ64kWM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiO98HACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrkahAAmygn2pHNUEvccqTmsxb9ZkLdGeZ1RUAohkdwvl9hBC3vmHaD\r\nk8s2pTz5k6QL7z6gf6WjHJEKRqSCTX/iCkLron8LZwQs3Z7hRM4DZ3lChqgF\r\nDXrjiRGU5ifYddZGoLevdGRSUqywWrDmuJu8cfHMfS84DeMfavTBE8+g+S2b\r\nLrSPKyKs9HIG3eeo8C8pVBUgCRvt4OKauCsPMisZM6f3AQMww3lirM6O3Det\r\nHl2uyI58dsfD66616Yd2tR2UGlD+vpjNQAm4sW8ZLaTU9EYa5V1dU+UH5qMQ\r\nGuEriYOwGfAW24xv98EDI72+1oXB5NVOKMYD2oBRz5iaqESJ9EFsnIT+ZVXR\r\nObyFa9fkUyAV88znI5azD5e/qPwKhd5tEZOghITMmQ/j5JB3swhEs0P6Gvg8\r\n4F21VC0O0OzYSSQUJLvQZERsKlUg65O8XO3n87MKapXhcEhuytyf+xLLKUQp\r\nk4K4GVnM0oGPL8YlSL6utS8aHoI1R8RT0WkzDfkWtwJ7EF2XaXkVhapk8YV9\r\nYZ8zJeKWy7Z7+BbxLVOJcU18Ji9y4MkazkJbyy7qy0UHKDP3GutyiFCVilWM\r\natdztLfetvqprGHmgjPZOWwK1XuVLtF3r1IXDzQ8xHVn0ixpHdLDheueZjOv\r\n6tWs10Jb77KXQR731pW9B0uHxIHXEL1jakE=\r\n=ze+8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 0.8.0"}}, "1.0.0-beta.2": {"name": "send", "version": "1.0.0-beta.2", "dependencies": {"ms": "2.1.3", "etag": "~1.8.1", "debug": "3.1.0", "fresh": "0.5.2", "destroy": "1.2.0", "statuses": "2.0.1", "encodeurl": "~1.0.2", "mime-types": "~2.1.34", "escape-html": "~1.0.3", "http-errors": "2.0.0", "on-finished": "2.4.1", "range-parser": "~1.2.1"}, "devDependencies": {"nyc": "15.1.0", "after": "0.8.2", "mocha": "9.2.2", "eslint": "7.32.0", "supertest": "6.2.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "1b6d727648acd1564d4095cd7f618b7b9474ce3e", "tarball": "https://registry.npmjs.org/send/-/send-1.0.0-beta.2.tgz", "fileCount": 6, "integrity": "sha512-k1yHu/FNK745PULKdsGpQ+bVSXYNwSk+bWnYzbxGZbt5obZc0JKDVANsCRuJD1X/EG15JtP9eZpwxkhUxIYEcg==", "signatures": [{"sig": "MEUCIQCQEOCsv6sTRw7qi8etXFffAOwa5jN52v06l7cPBKt7SQIgT0GLbtvYY1r4cTyJECTskLmLiUyfvYXRDe2c9wXoPbs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47956}, "engines": {"node": ">= 0.10"}}, "1.0.0": {"name": "send", "version": "1.0.0", "dependencies": {"ms": "^2.1.3", "etag": "^1.8.1", "debug": "^4.3.5", "fresh": "^0.5.2", "destroy": "^1.2.0", "statuses": "^2.0.1", "encodeurl": "^2.0.0", "mime-types": "^2.1.35", "escape-html": "^1.0.3", "http-errors": "^2.0.0", "on-finished": "^2.4.1", "range-parser": "^1.2.1"}, "devDependencies": {"nyc": "^17.0.0", "after": "^0.8.2", "mocha": "^10.7.0", "eslint": "7.32.0", "supertest": "6.2.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "5730c3f3a78186a67b1c1095611763e04e709c05", "tarball": "https://registry.npmjs.org/send/-/send-1.0.0.tgz", "fileCount": 6, "integrity": "sha512-iNjhr+MLlsha/HBB39iZQhbyaZQRdcA041yMn/wn18VxghMsfkIdELRVCtGk9G4Adq0Fd7oQpus6LHbkNnHG4w==", "signatures": [{"sig": "MEYCIQCDEWSV25T+ZCWcOverw1MzeNQOTqbyc1sjeru5JQ5ksQIhALZr0FcZOfYV+WTp+y+BZ/SXXRvlr7oQRawSWgtYZwYR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48296}, "engines": {"node": ">= 0.10"}}, "0.19.0": {"name": "send", "version": "0.19.0", "dependencies": {"ms": "2.1.3", "depd": "2.0.0", "etag": "~1.8.1", "mime": "1.6.0", "debug": "2.6.9", "fresh": "0.5.2", "destroy": "1.2.0", "statuses": "2.0.1", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "http-errors": "2.0.0", "on-finished": "2.4.1", "range-parser": "~1.2.1"}, "devDependencies": {"nyc": "15.1.0", "after": "0.8.2", "mocha": "9.2.2", "eslint": "7.32.0", "supertest": "6.2.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "bbc5a388c8ea6c048967049dbeac0e4a3f09d7f8", "tarball": "https://registry.npmjs.org/send/-/send-0.19.0.tgz", "fileCount": 6, "integrity": "sha512-dW41u5VfLXu8SJh5bwRmyYUbAoSB3c9uQh6L8h/KtsFREPWpbX1lrljJo186Jc4nmci/sGUZ9a0a0J2zgfq2hw==", "signatures": [{"sig": "MEUCIEKEfTSfCitKxxkKjIc1pTkA2OkENElLuDC8lBMR/TP0AiEA2FwTEwW48gwBubdhVRnkize5X73WZJ7FdR7S+hR0/FM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50197}, "engines": {"node": ">= 0.8.0"}}, "1.1.0": {"name": "send", "version": "1.1.0", "dependencies": {"ms": "^2.1.3", "etag": "^1.8.1", "debug": "^4.3.5", "fresh": "^0.5.2", "destroy": "^1.2.0", "statuses": "^2.0.1", "encodeurl": "^2.0.0", "mime-types": "^2.1.35", "escape-html": "^1.0.3", "http-errors": "^2.0.0", "on-finished": "^2.4.1", "range-parser": "^1.2.1"}, "devDependencies": {"nyc": "^17.0.0", "after": "^0.8.2", "mocha": "^10.7.0", "eslint": "7.32.0", "supertest": "6.2.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "4efe6ff3bb2139b0e5b2648d8b18d4dec48fc9c5", "tarball": "https://registry.npmjs.org/send/-/send-1.1.0.tgz", "fileCount": 6, "integrity": "sha512-v67WcEouB5GxbTWL/4NeToqcZiAWEq90N888fczVArY8A79J0L4FD7vj5hm3eUMua5EpoQ59wa/oovY6TLvRUA==", "signatures": [{"sig": "MEQCIEdhHCnJKRjejCb3bTELCDLA6Du5cRWsm8nQDoGQX5mXAiA+dGgEl+H4byCV1afLws7JlJ1l2meFtpkJoFzdyRFPZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48496}, "engines": {"node": ">= 18"}}, "0.19.1": {"name": "send", "version": "0.19.1", "dependencies": {"ms": "2.1.3", "depd": "2.0.0", "etag": "~1.8.1", "mime": "1.6.0", "debug": "2.6.9", "fresh": "0.5.2", "destroy": "1.2.0", "statuses": "2.0.1", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "http-errors": "2.0.0", "on-finished": "2.4.1", "range-parser": "~1.2.1"}, "devDependencies": {"nyc": "15.1.0", "after": "0.8.2", "mocha": "9.2.2", "eslint": "7.32.0", "supertest": "6.2.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-import": "2.25.4", "eslint-plugin-promise": "5.2.0", "eslint-config-standard": "14.1.1", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-standard": "4.1.0"}, "dist": {"shasum": "1c2563b2ee4fe510b806b21ec46f355005a369f9", "tarball": "https://registry.npmjs.org/send/-/send-0.19.1.tgz", "fileCount": 6, "integrity": "sha512-p4rRk4f23ynFEfcD9LA0xRYngj+IyGiEYyqqOak8kaN0TvNmuxC2dcVeBn62GpCeR2CpWqyHCNScTP91QbAVFg==", "signatures": [{"sig": "MEQCIGeBlqrp37u3j/b08oWZ+B0mQgW+EjZ6/h+lKeDz8gMoAiBMRbQndy2BY/bisQtDUlQ1HFh7n0meRaVJ8x3zc9XoYw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50257}, "engines": {"node": ">= 0.8.0"}}, "1.2.0": {"name": "send", "version": "1.2.0", "dependencies": {"debug": "^4.3.5", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "fresh": "^2.0.0", "http-errors": "^2.0.0", "mime-types": "^3.0.1", "ms": "^2.1.3", "on-finished": "^2.4.1", "range-parser": "^1.2.1", "statuses": "^2.0.1"}, "devDependencies": {"after": "^0.8.2", "eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "^10.7.0", "nyc": "^17.0.0", "supertest": "6.2.2"}, "dist": {"integrity": "sha512-uaW0WwXKpL9blXE2o0bRhoL2EGXIrZxQ2ZQ4mgcfoBxdFmQold+qWsD2jLrfZ0trjKL6vOw0j//eAwcALFjKSw==", "shasum": "32a7554fb777b831dfa828370f773a3808d37212", "tarball": "https://registry.npmjs.org/send/-/send-1.2.0.tgz", "fileCount": 5, "unpackedSize": 46921, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIAN7LO7yUWu2/B8cjR4QH9Oyezh4ggLbrm6cm/1aJrvRAiARvWpnYLGSfRrKkfHGcaiMKHCt8dilHxuOi39OpxL0aQ=="}]}, "engines": {"node": ">= 18"}}}, "modified": "2025-03-27T01:39:13.310Z"}