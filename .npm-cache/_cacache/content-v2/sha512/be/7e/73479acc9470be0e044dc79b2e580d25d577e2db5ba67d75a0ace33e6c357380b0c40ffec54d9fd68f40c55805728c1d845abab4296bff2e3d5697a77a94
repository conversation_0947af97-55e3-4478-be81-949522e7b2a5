{"source": 1100205, "name": "secp256k1", "dependency": "secp256k1", "title": "secp256k1-node allows private key extraction over ECDH", "url": "https://github.com/advisories/GHSA-584q-6j8j-r5pm", "severity": "high", "versions": ["0.0.1", "0.0.2", "0.0.3", "0.0.4", "0.0.5", "0.0.6", "0.0.7", "0.0.8", "0.0.9", "0.0.10", "0.0.11", "0.0.12", "0.0.13", "0.0.14", "0.0.15", "0.0.16", "0.0.17", "1.0.0", "1.0.1", "1.1.0", "1.1.1", "1.1.2", "1.1.3", "1.1.4", "1.1.5", "1.1.6", "2.0.0", "2.0.1", "2.0.2", "2.0.3", "2.0.4", "2.0.5", "2.0.6", "2.0.7", "2.0.8", "2.0.9", "2.0.10", "3.0.0", "3.0.1", "3.1.0", "3.2.0", "3.2.2", "3.2.5", "3.3.0", "3.3.1", "3.4.0", "3.5.0", "3.5.2", "3.6.0", "3.6.1", "3.6.2", "3.7.0", "3.7.1", "3.8.0", "3.8.1", "4.0.0", "4.0.1", "4.0.2", "4.0.3", "4.0.4", "5.0.0", "5.0.1"], "vulnerableVersions": ["4.0.0", "4.0.1", "4.0.2", "4.0.3"], "cwe": ["CWE-200", "CWE-354"], "cvss": {"score": 0, "vectorString": null}, "range": ">=4.0.0 <4.0.4", "id": "mPyuqM2YOlD275CmRtrmCQ98WRPtFkN8b74XN5XKA41vJBX4RYoeES8UllH1wUSX+pV52BPpPbbfeIDm7f+AeQ=="}