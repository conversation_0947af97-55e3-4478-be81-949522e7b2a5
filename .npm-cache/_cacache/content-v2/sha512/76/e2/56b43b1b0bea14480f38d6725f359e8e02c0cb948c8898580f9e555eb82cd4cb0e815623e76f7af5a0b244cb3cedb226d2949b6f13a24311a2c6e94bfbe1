{"source": 1103907, "name": "cookie", "dependency": "cookie", "title": "cookie accepts cookie name, path, and domain with out of bounds characters", "url": "https://github.com/advisories/GHSA-pxg6-pf52-xh8x", "severity": "low", "versions": ["0.0.0", "0.0.1", "0.0.2", "0.0.3", "0.0.4", "0.0.5", "0.0.6", "0.1.0", "0.1.1", "0.1.2", "0.1.3", "0.1.4", "0.1.5", "0.2.0", "0.2.1", "0.2.2", "0.2.3", "0.2.4", "0.3.0", "0.3.1", "0.4.0", "0.4.1", "0.4.2", "0.5.0", "0.6.0", "0.7.0", "0.7.1", "0.7.2", "1.0.0", "1.0.1", "1.0.2"], "vulnerableVersions": ["0.0.0", "0.0.1", "0.0.2", "0.0.3", "0.0.4", "0.0.5", "0.0.6", "0.1.0", "0.1.1", "0.1.2", "0.1.3", "0.1.4", "0.1.5", "0.2.0", "0.2.1", "0.2.2", "0.2.3", "0.2.4", "0.3.0", "0.3.1", "0.4.0", "0.4.1", "0.4.2", "0.5.0", "0.6.0"], "cwe": ["CWE-74"], "cvss": {"score": 0, "vectorString": null}, "range": "<0.7.0", "id": "EFt7h/FZLeopARa06+F2CBY5ItUm007mP/cHgZjTJZX1MeIwg3JD7toSioRoDeE8wm5VdkimqlS+CnuPhZqbIg=="}