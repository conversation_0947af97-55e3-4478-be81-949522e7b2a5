0 verbose cli /Users/<USER>/.nvm/versions/node/v20.19.2/bin/node /Users/<USER>/.nvm/versions/node/v20.19.2/bin/npm
1 info using npm@10.8.2
2 info using node@v20.19.2
3 silly config load:file:/Users/<USER>/.nvm/versions/node/v20.19.2/lib/node_modules/npm/npmrc
4 silly config load:file:/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/.npmrc
5 silly config load:file:/Users/<USER>/.npmrc
6 silly config load:file:/Users/<USER>/.nvm/versions/node/v20.19.2/etc/npmrc
7 verbose title npm install
8 verbose argv "install"
9 verbose logfile logs-max:10 dir:/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/.npm-cache/_logs/2025-06-02T02_09_45_211Z-
10 verbose logfile /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/.npm-cache/_logs/2025-06-02T02_09_45_211Z-debug-0.log
11 silly packumentCache heap:4345298944 maxSize:1086324736 maxEntrySize:*********
12 silly logfile done cleaning log files
13 silly idealTree buildDeps
14 verbose shrinkwrap failed to load node_modules/.package-lock.json out of date, updated: .wasp/out/sdk/wasp
15 silly reify moves {}
16 silly reify mark deleted [
16 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@babel/plugin-syntax-async-generators'
16 silly reify ]
17 silly reify mark deleted [
17 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@babel/plugin-syntax-bigint'
17 silly reify ]
18 silly reify mark deleted [
18 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@babel/plugin-syntax-class-properties'
18 silly reify ]
19 silly reify mark deleted [
19 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@babel/plugin-syntax-class-static-block'
19 silly reify ]
20 silly reify mark deleted [
20 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@babel/plugin-syntax-import-attributes'
20 silly reify ]
21 silly reify mark deleted [
21 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@babel/plugin-syntax-import-meta'
21 silly reify ]
22 silly reify mark deleted [
22 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@babel/plugin-syntax-json-strings'
22 silly reify ]
23 silly reify mark deleted [
23 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@babel/plugin-syntax-jsx'
23 silly reify ]
24 silly reify mark deleted [
24 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@babel/plugin-syntax-logical-assignment-operators'
24 silly reify ]
25 silly reify mark deleted [
25 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@babel/plugin-syntax-nullish-coalescing-operator'
25 silly reify ]
26 silly reify mark deleted [
26 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@babel/plugin-syntax-numeric-separator'
26 silly reify ]
27 silly reify mark deleted [
27 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@babel/plugin-syntax-object-rest-spread'
27 silly reify ]
28 silly reify mark deleted [
28 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@babel/plugin-syntax-optional-catch-binding'
28 silly reify ]
29 silly reify mark deleted [
29 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@babel/plugin-syntax-optional-chaining'
29 silly reify ]
30 silly reify mark deleted [
30 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@babel/plugin-syntax-private-property-in-object'
30 silly reify ]
31 silly reify mark deleted [
31 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@babel/plugin-syntax-top-level-await'
31 silly reify ]
32 silly reify mark deleted [
32 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@babel/plugin-syntax-typescript'
32 silly reify ]
33 silly reify mark deleted [
33 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@bcoe/v8-coverage'
33 silly reify ]
34 silly reify mark deleted [
34 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@cspotcode/source-map-support'
34 silly reify ]
35 silly reify mark deleted [
35 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@cspotcode/source-map-support/node_modules/@jridgewell/trace-mapping'
35 silly reify ]
36 silly reify mark deleted [
36 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@edge-runtime/format'
36 silly reify ]
37 silly reify mark deleted [
37 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@edge-runtime/node-utils'
37 silly reify ]
38 silly reify mark deleted [
38 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@edge-runtime/ponyfill'
38 silly reify ]
39 silly reify mark deleted [
39 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@fastify/busboy'
39 silly reify ]
40 silly reify mark deleted [
40 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@flydotio/dockerfile',
40 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/dockerfile'
40 silly reify ]
41 silly reify mark deleted [
41 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@flydotio/dockerfile/node_modules/chalk'
41 silly reify ]
42 silly reify mark deleted [
42 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@flydotio/dockerfile/node_modules/diff'
42 silly reify ]
43 silly reify mark deleted [
43 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@flydotio/dockerfile/node_modules/inquirer'
43 silly reify ]
44 silly reify mark deleted [
44 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@flydotio/dockerfile/node_modules/mute-stream'
44 silly reify ]
45 silly reify mark deleted [
45 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@flydotio/dockerfile/node_modules/run-async'
45 silly reify ]
46 silly reify mark deleted [
46 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@inquirer/checkbox'
46 silly reify ]
47 silly reify mark deleted [
47 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@inquirer/confirm'
47 silly reify ]
48 silly reify mark deleted [
48 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@inquirer/core'
48 silly reify ]
49 silly reify mark deleted [
49 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@inquirer/core/node_modules/cli-width'
49 silly reify ]
50 silly reify mark deleted [
50 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@inquirer/core/node_modules/mute-stream'
50 silly reify ]
51 silly reify mark deleted [
51 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@inquirer/core/node_modules/signal-exit'
51 silly reify ]
52 silly reify mark deleted [
52 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@inquirer/core/node_modules/wrap-ansi'
52 silly reify ]
53 silly reify mark deleted [
53 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@inquirer/editor'
53 silly reify ]
54 silly reify mark deleted [
54 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@inquirer/expand'
54 silly reify ]
55 silly reify mark deleted [
55 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@inquirer/figures'
55 silly reify ]
56 silly reify mark deleted [
56 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@inquirer/input'
56 silly reify ]
57 silly reify mark deleted [
57 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@inquirer/number'
57 silly reify ]
58 silly reify mark deleted [
58 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@inquirer/password'
58 silly reify ]
59 silly reify mark deleted [
59 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@inquirer/prompts'
59 silly reify ]
60 silly reify mark deleted [
60 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@inquirer/rawlist'
60 silly reify ]
61 silly reify mark deleted [
61 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@inquirer/search'
61 silly reify ]
62 silly reify mark deleted [
62 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@inquirer/select'
62 silly reify ]
63 silly reify mark deleted [
63 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@inquirer/type'
63 silly reify ]
64 silly reify mark deleted [
64 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@isaacs/cliui'
64 silly reify ]
65 silly reify mark deleted [
65 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@isaacs/cliui/node_modules/ansi-regex'
65 silly reify ]
66 silly reify mark deleted [
66 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@isaacs/cliui/node_modules/ansi-styles'
66 silly reify ]
67 silly reify mark deleted [
67 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@isaacs/cliui/node_modules/emoji-regex'
67 silly reify ]
68 silly reify mark deleted [
68 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@isaacs/cliui/node_modules/string-width'
68 silly reify ]
69 silly reify mark deleted [
69 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@isaacs/cliui/node_modules/strip-ansi'
69 silly reify ]
70 silly reify mark deleted [
70 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@isaacs/cliui/node_modules/wrap-ansi'
70 silly reify ]
71 silly reify mark deleted [
71 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@isaacs/fs-minipass'
71 silly reify ]
72 silly reify mark deleted [
72 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@isaacs/fs-minipass/node_modules/minipass'
72 silly reify ]
73 silly reify mark deleted [
73 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@istanbuljs/load-nyc-config'
73 silly reify ]
74 silly reify mark deleted [
74 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@istanbuljs/schema'
74 silly reify ]
75 silly reify mark deleted [
75 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/console'
75 silly reify ]
76 silly reify mark deleted [
76 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/console/node_modules/chalk'
76 silly reify ]
77 silly reify mark deleted [
77 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/core'
77 silly reify ]
78 silly reify mark deleted [
78 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/core/node_modules/chalk'
78 silly reify ]
79 silly reify mark deleted [
79 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/core/node_modules/pretty-format'
79 silly reify ]
80 silly reify mark deleted [
80 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/core/node_modules/pretty-format/node_modules/ansi-styles'
80 silly reify ]
81 silly reify mark deleted [
81 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/core/node_modules/react-is'
81 silly reify ]
82 silly reify mark deleted [
82 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/environment'
82 silly reify ]
83 silly reify mark deleted [
83 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/expect'
83 silly reify ]
84 silly reify mark deleted [
84 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/expect-utils'
84 silly reify ]
85 silly reify mark deleted [
85 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/fake-timers'
85 silly reify ]
86 silly reify mark deleted [
86 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/globals'
86 silly reify ]
87 silly reify mark deleted [
87 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/reporters'
87 silly reify ]
88 silly reify mark deleted [
88 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/reporters/node_modules/chalk'
88 silly reify ]
89 silly reify mark deleted [
89 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/source-map'
89 silly reify ]
90 silly reify mark deleted [
90 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/test-result'
90 silly reify ]
91 silly reify mark deleted [
91 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/test-sequencer'
91 silly reify ]
92 silly reify mark deleted [
92 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/transform'
92 silly reify ]
93 silly reify mark deleted [
93 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/transform/node_modules/chalk'
93 silly reify ]
94 silly reify mark deleted [
94 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/transform/node_modules/convert-source-map'
94 silly reify ]
95 silly reify mark deleted [
95 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/types'
95 silly reify ]
96 silly reify mark deleted [
96 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@jest/types/node_modules/chalk'
96 silly reify ]
97 silly reify mark deleted [
97 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@pkgjs/parseargs'
97 silly reify ]
98 silly reify mark deleted [
98 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/pluginutils'
98 silly reify ]
99 silly reify mark deleted [
99 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@sinclair/typebox'
99 silly reify ]
100 silly reify mark deleted [
100 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@sinonjs/commons'
100 silly reify ]
101 silly reify mark deleted [
101 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@sinonjs/fake-timers'
101 silly reify ]
102 silly reify mark deleted [
102 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@testing-library/dom'
102 silly reify ]
103 silly reify mark deleted [
103 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@testing-library/dom/node_modules/aria-query'
103 silly reify ]
104 silly reify mark deleted [
104 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@testing-library/dom/node_modules/chalk'
104 silly reify ]
105 silly reify mark deleted [
105 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@testing-library/jest-dom'
105 silly reify ]
106 silly reify mark deleted [
106 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@testing-library/react'
106 silly reify ]
107 silly reify mark deleted [
107 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@trysound/sax'
107 silly reify ]
108 silly reify mark deleted [
108 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@ts-morph/common'
108 silly reify ]
109 silly reify mark deleted [
109 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@tsconfig/node10'
109 silly reify ]
110 silly reify mark deleted [
110 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@tsconfig/node12'
110 silly reify ]
111 silly reify mark deleted [
111 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@tsconfig/node14'
111 silly reify ]
112 silly reify mark deleted [
112 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@tsconfig/node16'
112 silly reify ]
113 silly reify mark deleted [
113 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@tsconfig/node18'
113 silly reify ]
114 silly reify mark deleted [
114 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/body-parser'
114 silly reify ]
115 silly reify mark deleted [
115 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/connect'
115 silly reify ]
116 silly reify mark deleted [
116 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/express'
116 silly reify ]
117 silly reify mark deleted [
117 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/express-serve-static-core'
117 silly reify ]
118 silly reify mark deleted [
118 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/graceful-fs'
118 silly reify ]
119 silly reify mark deleted [
119 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/http-errors'
119 silly reify ]
120 silly reify mark deleted [
120 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/istanbul-lib-coverage'
120 silly reify ]
121 silly reify mark deleted [
121 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/istanbul-lib-report'
121 silly reify ]
122 silly reify mark deleted [
122 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/istanbul-reports'
122 silly reify ]
123 silly reify mark deleted [
123 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/jest'
123 silly reify ]
124 silly reify mark deleted [
124 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/jest/node_modules/ansi-styles'
124 silly reify ]
125 silly reify mark deleted [
125 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/jest/node_modules/pretty-format'
125 silly reify ]
126 silly reify mark deleted [
126 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/jest/node_modules/react-is'
126 silly reify ]
127 silly reify mark deleted [
127 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/json-schema'
127 silly reify ]
128 silly reify mark deleted [
128 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/jsonwebtoken'
128 silly reify ]
129 silly reify mark deleted [
129 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/mime'
129 silly reify ]
130 silly reify mark deleted [
130 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/qs'
130 silly reify ]
131 silly reify mark deleted [
131 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/range-parser'
131 silly reify ]
132 silly reify mark deleted [
132 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/send'
132 silly reify ]
133 silly reify mark deleted [
133 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/serve-static'
133 silly reify ]
134 silly reify mark deleted [
134 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/stack-utils'
134 silly reify ]
135 silly reify mark deleted [
135 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/testing-library__jest-dom'
135 silly reify ]
136 silly reify mark deleted [
136 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/yargs'
136 silly reify ]
137 silly reify mark deleted [
137 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@types/yargs-parser'
137 silly reify ]
138 silly reify mark deleted [
138 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/build-utils'
138 silly reify ]
139 silly reify mark deleted [
139 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/error-utils'
139 silly reify ]
140 silly reify mark deleted [
140 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/fun'
140 silly reify ]
141 silly reify mark deleted [
141 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/fun/node_modules/async-listen'
141 silly reify ]
142 silly reify mark deleted [
142 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/fun/node_modules/debug'
142 silly reify ]
143 silly reify mark deleted [
143 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/fun/node_modules/debug/node_modules/ms'
143 silly reify ]
144 silly reify mark deleted [
144 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/fun/node_modules/lru-cache'
144 silly reify ]
145 silly reify mark deleted [
145 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/fun/node_modules/ms'
145 silly reify ]
146 silly reify mark deleted [
146 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/fun/node_modules/node-fetch'
146 silly reify ]
147 silly reify mark deleted [
147 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/fun/node_modules/semver',
147 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/fun/node_modules/.bin/semver'
147 silly reify ]
148 silly reify mark deleted [
148 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/fun/node_modules/yallist'
148 silly reify ]
149 silly reify mark deleted [
149 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/gatsby-plugin-vercel-analytics'
149 silly reify ]
150 silly reify mark deleted [
150 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/gatsby-plugin-vercel-analytics/node_modules/web-vitals'
150 silly reify ]
151 silly reify mark deleted [
151 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/gatsby-plugin-vercel-builder'
151 silly reify ]
152 silly reify mark deleted [
152 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/gatsby-plugin-vercel-builder/node_modules/@vercel/build-utils'
152 silly reify ]
153 silly reify mark deleted [
153 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/go'
153 silly reify ]
154 silly reify mark deleted [
154 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/hydrogen'
154 silly reify ]
155 silly reify mark deleted [
155 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/hydrogen/node_modules/@vercel/static-config'
155 silly reify ]
156 silly reify mark deleted [
156 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next'
156 silly reify ]
157 silly reify mark deleted [
157 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/@mapbox/node-pre-gyp',
157 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/.bin/node-pre-gyp'
157 silly reify ]
158 silly reify mark deleted [
158 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/@rollup/pluginutils'
158 silly reify ]
159 silly reify mark deleted [
159 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/@vercel/nft',
159 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/.bin/nft'
159 silly reify ]
160 silly reify mark deleted [
160 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/abbrev'
160 silly reify ]
161 silly reify mark deleted [
161 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/agent-base'
161 silly reify ]
162 silly reify mark deleted [
162 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/brace-expansion'
162 silly reify ]
163 silly reify mark deleted [
163 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/chownr'
163 silly reify ]
164 silly reify mark deleted [
164 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/glob',
164 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/.bin/glob'
164 silly reify ]
165 silly reify mark deleted [
165 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/https-proxy-agent'
165 silly reify ]
166 silly reify mark deleted [
166 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/minimatch'
166 silly reify ]
167 silly reify mark deleted [
167 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/minipass'
167 silly reify ]
168 silly reify mark deleted [
168 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/minizlib'
168 silly reify ]
169 silly reify mark deleted [
169 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/mkdirp',
169 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/.bin/mkdirp'
169 silly reify ]
170 silly reify mark deleted [
170 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/node-fetch'
170 silly reify ]
171 silly reify mark deleted [
171 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/nopt',
171 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/.bin/nopt'
171 silly reify ]
172 silly reify mark deleted [
172 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/picomatch'
172 silly reify ]
173 silly reify mark deleted [
173 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/tar'
173 silly reify ]
174 silly reify mark deleted [
174 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/next/node_modules/yallist'
174 silly reify ]
175 silly reify mark deleted [
175 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/nft',
175 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/nft'
175 silly reify ]
176 silly reify mark deleted [
176 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/node'
176 silly reify ]
177 silly reify mark deleted [
177 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/node/node_modules/@types/node'
177 silly reify ]
178 silly reify mark deleted [
178 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/node/node_modules/node-fetch'
178 silly reify ]
179 silly reify mark deleted [
179 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/python'
179 silly reify ]
180 silly reify mark deleted [
180 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood'
180 silly reify ]
181 silly reify mark deleted [
181 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/@mapbox/node-pre-gyp',
181 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/.bin/node-pre-gyp'
181 silly reify ]
182 silly reify mark deleted [
182 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/@mapbox/node-pre-gyp/node_modules/semver',
182 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/@mapbox/node-pre-gyp/node_modules/.bin/semver'
182 silly reify ]
183 silly reify mark deleted [
183 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/@rollup/pluginutils'
183 silly reify ]
184 silly reify mark deleted [
184 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/@vercel/nft',
184 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/.bin/nft'
184 silly reify ]
185 silly reify mark deleted [
185 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/@vercel/static-config'
185 silly reify ]
186 silly reify mark deleted [
186 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/abbrev'
186 silly reify ]
187 silly reify mark deleted [
187 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/agent-base'
187 silly reify ]
188 silly reify mark deleted [
188 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/brace-expansion'
188 silly reify ]
189 silly reify mark deleted [
189 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/chownr'
189 silly reify ]
190 silly reify mark deleted [
190 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/glob',
190 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/.bin/glob'
190 silly reify ]
191 silly reify mark deleted [
191 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/https-proxy-agent'
191 silly reify ]
192 silly reify mark deleted [
192 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/minimatch'
192 silly reify ]
193 silly reify mark deleted [
193 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/minipass'
193 silly reify ]
194 silly reify mark deleted [
194 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/minizlib'
194 silly reify ]
195 silly reify mark deleted [
195 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/mkdirp',
195 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/.bin/mkdirp'
195 silly reify ]
196 silly reify mark deleted [
196 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/node-fetch'
196 silly reify ]
197 silly reify mark deleted [
197 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/nopt',
197 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/.bin/nopt'
197 silly reify ]
198 silly reify mark deleted [
198 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/picomatch'
198 silly reify ]
199 silly reify mark deleted [
199 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/semver',
199 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/.bin/semver'
199 silly reify ]
200 silly reify mark deleted [
200 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/tar'
200 silly reify ]
201 silly reify mark deleted [
201 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/redwood/node_modules/yallist'
201 silly reify ]
202 silly reify mark deleted [
202 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder'
202 silly reify ]
203 silly reify mark deleted [
203 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/@mapbox/node-pre-gyp',
203 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/.bin/node-pre-gyp'
203 silly reify ]
204 silly reify mark deleted [
204 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/@rollup/pluginutils'
204 silly reify ]
205 silly reify mark deleted [
205 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/@vercel/nft',
205 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/.bin/nft'
205 silly reify ]
206 silly reify mark deleted [
206 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/@vercel/static-config'
206 silly reify ]
207 silly reify mark deleted [
207 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/abbrev'
207 silly reify ]
208 silly reify mark deleted [
208 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/agent-base'
208 silly reify ]
209 silly reify mark deleted [
209 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/brace-expansion'
209 silly reify ]
210 silly reify mark deleted [
210 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/chownr'
210 silly reify ]
211 silly reify mark deleted [
211 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/glob',
211 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/.bin/glob'
211 silly reify ]
212 silly reify mark deleted [
212 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/https-proxy-agent'
212 silly reify ]
213 silly reify mark deleted [
213 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/minimatch'
213 silly reify ]
214 silly reify mark deleted [
214 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/minipass'
214 silly reify ]
215 silly reify mark deleted [
215 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/minizlib'
215 silly reify ]
216 silly reify mark deleted [
216 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/mkdirp',
216 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/.bin/mkdirp'
216 silly reify ]
217 silly reify mark deleted [
217 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/node-fetch'
217 silly reify ]
218 silly reify mark deleted [
218 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/nopt',
218 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/.bin/nopt'
218 silly reify ]
219 silly reify mark deleted [
219 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/path-to-regexp'
219 silly reify ]
220 silly reify mark deleted [
220 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/picomatch'
220 silly reify ]
221 silly reify mark deleted [
221 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/tar'
221 silly reify ]
222 silly reify mark deleted [
222 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/remix-builder/node_modules/yallist'
222 silly reify ]
223 silly reify mark deleted [
223 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/ruby'
223 silly reify ]
224 silly reify mark deleted [
224 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/static-build'
224 silly reify ]
225 silly reify mark deleted [
225 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/static-build/node_modules/@vercel/static-config'
225 silly reify ]
226 silly reify mark deleted [
226 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@vercel/static-config'
226 silly reify ]
227 silly reify mark deleted [
227 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/acorn-import-attributes'
227 silly reify ]
228 silly reify mark deleted [
228 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/ajv'
228 silly reify ]
229 silly reify mark deleted [
229 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/any-promise'
229 silly reify ]
230 silly reify mark deleted [
230 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arg'
230 silly reify ]
231 silly reify mark deleted [
231 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/argparse'
231 silly reify ]
232 silly reify mark deleted [
232 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/aria-query'
232 silly reify ]
233 silly reify mark deleted [
233 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/async-listen'
233 silly reify ]
234 silly reify mark deleted [
234 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/async-sema'
234 silly reify ]
235 silly reify mark deleted [
235 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/babel-jest'
235 silly reify ]
236 silly reify mark deleted [
236 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/babel-jest/node_modules/chalk'
236 silly reify ]
237 silly reify mark deleted [
237 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/babel-plugin-istanbul'
237 silly reify ]
238 silly reify mark deleted [
238 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/babel-plugin-istanbul/node_modules/istanbul-lib-instrument'
238 silly reify ]
239 silly reify mark deleted [
239 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/babel-plugin-istanbul/node_modules/semver',
239 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/babel-plugin-istanbul/node_modules/.bin/semver'
239 silly reify ]
240 silly reify mark deleted [
240 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/babel-plugin-jest-hoist'
240 silly reify ]
241 silly reify mark deleted [
241 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/babel-preset-current-node-syntax'
241 silly reify ]
242 silly reify mark deleted [
242 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/babel-preset-jest'
242 silly reify ]
243 silly reify mark deleted [
243 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/bindings'
243 silly reify ]
244 silly reify mark deleted [
244 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/boolbase'
244 silly reify ]
245 silly reify mark deleted [
245 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/bser'
245 silly reify ]
246 silly reify mark deleted [
246 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/buffer-crc32'
246 silly reify ]
247 silly reify mark deleted [
247 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/buffer-from'
247 silly reify ]
248 silly reify mark deleted [
248 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/camelcase'
248 silly reify ]
249 silly reify mark deleted [
249 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/caniuse-api'
249 silly reify ]
250 silly reify mark deleted [
250 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/char-regex'
250 silly reify ]
251 silly reify mark deleted [
251 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/chokidar'
251 silly reify ]
252 silly reify mark deleted [
252 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/ci-info'
252 silly reify ]
253 silly reify mark deleted [
253 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/cjs-module-lexer'
253 silly reify ]
254 silly reify mark deleted [
254 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/co'
254 silly reify ]
255 silly reify mark deleted [
255 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/code-block-writer'
255 silly reify ]
256 silly reify mark deleted [
256 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/collect-v8-coverage'
256 silly reify ]
257 silly reify mark deleted [
257 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/colord'
257 silly reify ]
258 silly reify mark deleted [
258 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/consola'
258 silly reify ]
259 silly reify mark deleted [
259 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/convert-hrtime'
259 silly reify ]
260 silly reify mark deleted [
260 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/create-jest',
260 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/create-jest'
260 silly reify ]
261 silly reify mark deleted [
261 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/create-jest/node_modules/chalk'
261 silly reify ]
262 silly reify mark deleted [
262 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/create-require'
262 silly reify ]
263 silly reify mark deleted [
263 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/css-declaration-sorter'
263 silly reify ]
264 silly reify mark deleted [
264 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/css-select'
264 silly reify ]
265 silly reify mark deleted [
265 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/css-tree'
265 silly reify ]
266 silly reify mark deleted [
266 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/css-what'
266 silly reify ]
267 silly reify mark deleted [
267 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/cssesc',
267 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/cssesc'
267 silly reify ]
268 silly reify mark deleted [
268 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/cssnano'
268 silly reify ]
269 silly reify mark deleted [
269 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/cssnano-preset-default'
269 silly reify ]
270 silly reify mark deleted [
270 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/cssnano-utils'
270 silly reify ]
271 silly reify mark deleted [
271 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/csso'
271 silly reify ]
272 silly reify mark deleted [
272 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/csso/node_modules/css-tree'
272 silly reify ]
273 silly reify mark deleted [
273 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/csso/node_modules/mdn-data'
273 silly reify ]
274 silly reify mark deleted [
274 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/dedent'
274 silly reify ]
275 silly reify mark deleted [
275 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/detect-newline'
275 silly reify ]
276 silly reify mark deleted [
276 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/diff'
276 silly reify ]
277 silly reify mark deleted [
277 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/dom-accessibility-api'
277 silly reify ]
278 silly reify mark deleted [
278 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/eastasianwidth'
278 silly reify ]
279 silly reify mark deleted [
279 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/edge-runtime',
279 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/edge-runtime'
279 silly reify ]
280 silly reify mark deleted [
280 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/edge-runtime/node_modules/async-listen'
280 silly reify ]
281 silly reify mark deleted [
281 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/edge-runtime/node_modules/picocolors'
281 silly reify ]
282 silly reify mark deleted [
282 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/ejs',
282 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/ejs'
282 silly reify ]
283 silly reify mark deleted [
283 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/emittery'
283 silly reify ]
284 silly reify mark deleted [
284 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/end-of-stream'
284 silly reify ]
285 silly reify mark deleted [
285 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/end-of-stream/node_modules/once'
285 silly reify ]
286 silly reify mark deleted [
286 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/es-module-lexer'
286 silly reify ]
287 silly reify mark deleted [
287 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild',
287 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/esbuild'
287 silly reify ]
288 silly reify mark deleted [
288 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-android-64'
288 silly reify ]
289 silly reify mark deleted [
289 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-android-arm64'
289 silly reify ]
290 silly reify mark deleted [
290 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-darwin-64'
290 silly reify ]
291 silly reify mark deleted [
291 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-darwin-arm64'
291 silly reify ]
292 silly reify mark deleted [
292 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-freebsd-64'
292 silly reify ]
293 silly reify mark deleted [
293 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-freebsd-arm64'
293 silly reify ]
294 silly reify mark deleted [
294 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-linux-32'
294 silly reify ]
295 silly reify mark deleted [
295 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-linux-64'
295 silly reify ]
296 silly reify mark deleted [
296 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-linux-arm'
296 silly reify ]
297 silly reify mark deleted [
297 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-linux-arm64'
297 silly reify ]
298 silly reify mark deleted [
298 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-linux-mips64le'
298 silly reify ]
299 silly reify mark deleted [
299 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-linux-ppc64le'
299 silly reify ]
300 silly reify mark deleted [
300 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-linux-riscv64'
300 silly reify ]
301 silly reify mark deleted [
301 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-linux-s390x'
301 silly reify ]
302 silly reify mark deleted [
302 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-netbsd-64'
302 silly reify ]
303 silly reify mark deleted [
303 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-openbsd-64'
303 silly reify ]
304 silly reify mark deleted [
304 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-sunos-64'
304 silly reify ]
305 silly reify mark deleted [
305 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-windows-32'
305 silly reify ]
306 silly reify mark deleted [
306 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-windows-64'
306 silly reify ]
307 silly reify mark deleted [
307 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/esbuild-windows-arm64'
307 silly reify ]
308 silly reify mark deleted [
308 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/estree-walker'
308 silly reify ]
309 silly reify mark deleted [
309 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/events-intercept'
309 silly reify ]
310 silly reify mark deleted [
310 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/execa'
310 silly reify ]
311 silly reify mark deleted [
311 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/execa/node_modules/signal-exit'
311 silly reify ]
312 silly reify mark deleted [
312 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/exit'
312 silly reify ]
313 silly reify mark deleted [
313 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/expect'
313 silly reify ]
314 silly reify mark deleted [
314 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/fast-deep-equal'
314 silly reify ]
315 silly reify mark deleted [
315 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/fast-json-stable-stringify'
315 silly reify ]
316 silly reify mark deleted [
316 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/fb-watchman'
316 silly reify ]
317 silly reify mark deleted [
317 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/fd-slicer'
317 silly reify ]
318 silly reify mark deleted [
318 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/file-uri-to-path'
318 silly reify ]
319 silly reify mark deleted [
319 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/filelist'
319 silly reify ]
320 silly reify mark deleted [
320 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/filelist/node_modules/brace-expansion'
320 silly reify ]
321 silly reify mark deleted [
321 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/filelist/node_modules/minimatch'
321 silly reify ]
322 silly reify mark deleted [
322 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/find-up'
322 silly reify ]
323 silly reify mark deleted [
323 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/foreground-child'
323 silly reify ]
324 silly reify mark deleted [
324 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/fs-extra'
324 silly reify ]
325 silly reify mark deleted [
325 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/generic-pool'
325 silly reify ]
326 silly reify mark deleted [
326 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/get-package-type'
326 silly reify ]
327 silly reify mark deleted [
327 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/get-stream'
327 silly reify ]
328 silly reify mark deleted [
328 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/html-escaper'
328 silly reify ]
329 silly reify mark deleted [
329 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/human-signals'
329 silly reify ]
330 silly reify mark deleted [
330 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/import-local',
330 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/import-local-fixture'
330 silly reify ]
331 silly reify mark deleted [
331 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/imurmurhash'
331 silly reify ]
332 silly reify mark deleted [
332 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/is-generator-fn'
332 silly reify ]
333 silly reify mark deleted [
333 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/is-stream'
333 silly reify ]
334 silly reify mark deleted [
334 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/istanbul-lib-coverage'
334 silly reify ]
335 silly reify mark deleted [
335 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/istanbul-lib-instrument'
335 silly reify ]
336 silly reify mark deleted [
336 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/istanbul-lib-report'
336 silly reify ]
337 silly reify mark deleted [
337 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/istanbul-lib-report/node_modules/make-dir'
337 silly reify ]
338 silly reify mark deleted [
338 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/istanbul-lib-source-maps'
338 silly reify ]
339 silly reify mark deleted [
339 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/istanbul-lib-source-maps/node_modules/source-map'
339 silly reify ]
340 silly reify mark deleted [
340 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/istanbul-reports'
340 silly reify ]
341 silly reify mark deleted [
341 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jackspeak'
341 silly reify ]
342 silly reify mark deleted [
342 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jake',
342 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/jake'
342 silly reify ]
343 silly reify mark deleted [
343 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jake/node_modules/chalk'
343 silly reify ]
344 silly reify mark deleted [
344 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest',
344 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/jest'
344 silly reify ]
345 silly reify mark deleted [
345 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-changed-files'
345 silly reify ]
346 silly reify mark deleted [
346 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-circus'
346 silly reify ]
347 silly reify mark deleted [
347 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-circus/node_modules/chalk'
347 silly reify ]
348 silly reify mark deleted [
348 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-circus/node_modules/pretty-format'
348 silly reify ]
349 silly reify mark deleted [
349 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-circus/node_modules/pretty-format/node_modules/ansi-styles'
349 silly reify ]
350 silly reify mark deleted [
350 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-circus/node_modules/react-is'
350 silly reify ]
351 silly reify mark deleted [
351 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-cli',
351 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/jest'
351 silly reify ]
352 silly reify mark deleted [
352 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-cli/node_modules/chalk'
352 silly reify ]
353 silly reify mark deleted [
353 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-config'
353 silly reify ]
354 silly reify mark deleted [
354 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-config/node_modules/chalk'
354 silly reify ]
355 silly reify mark deleted [
355 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-config/node_modules/pretty-format'
355 silly reify ]
356 silly reify mark deleted [
356 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-config/node_modules/pretty-format/node_modules/ansi-styles'
356 silly reify ]
357 silly reify mark deleted [
357 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-config/node_modules/react-is'
357 silly reify ]
358 silly reify mark deleted [
358 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-diff'
358 silly reify ]
359 silly reify mark deleted [
359 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-diff/node_modules/chalk'
359 silly reify ]
360 silly reify mark deleted [
360 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-diff/node_modules/pretty-format'
360 silly reify ]
361 silly reify mark deleted [
361 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-diff/node_modules/pretty-format/node_modules/ansi-styles'
361 silly reify ]
362 silly reify mark deleted [
362 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-diff/node_modules/react-is'
362 silly reify ]
363 silly reify mark deleted [
363 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-docblock'
363 silly reify ]
364 silly reify mark deleted [
364 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-each'
364 silly reify ]
365 silly reify mark deleted [
365 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-each/node_modules/chalk'
365 silly reify ]
366 silly reify mark deleted [
366 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-each/node_modules/pretty-format'
366 silly reify ]
367 silly reify mark deleted [
367 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-each/node_modules/pretty-format/node_modules/ansi-styles'
367 silly reify ]
368 silly reify mark deleted [
368 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-each/node_modules/react-is'
368 silly reify ]
369 silly reify mark deleted [
369 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-environment-node'
369 silly reify ]
370 silly reify mark deleted [
370 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-get-type'
370 silly reify ]
371 silly reify mark deleted [
371 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-haste-map'
371 silly reify ]
372 silly reify mark deleted [
372 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-leak-detector'
372 silly reify ]
373 silly reify mark deleted [
373 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-leak-detector/node_modules/ansi-styles'
373 silly reify ]
374 silly reify mark deleted [
374 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-leak-detector/node_modules/pretty-format'
374 silly reify ]
375 silly reify mark deleted [
375 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-leak-detector/node_modules/react-is'
375 silly reify ]
376 silly reify mark deleted [
376 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-matcher-utils'
376 silly reify ]
377 silly reify mark deleted [
377 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-matcher-utils/node_modules/chalk'
377 silly reify ]
378 silly reify mark deleted [
378 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-matcher-utils/node_modules/pretty-format'
378 silly reify ]
379 silly reify mark deleted [
379 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-matcher-utils/node_modules/pretty-format/node_modules/ansi-styles'
379 silly reify ]
380 silly reify mark deleted [
380 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-matcher-utils/node_modules/react-is'
380 silly reify ]
381 silly reify mark deleted [
381 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-message-util'
381 silly reify ]
382 silly reify mark deleted [
382 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-message-util/node_modules/chalk'
382 silly reify ]
383 silly reify mark deleted [
383 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-message-util/node_modules/pretty-format'
383 silly reify ]
384 silly reify mark deleted [
384 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-message-util/node_modules/pretty-format/node_modules/ansi-styles'
384 silly reify ]
385 silly reify mark deleted [
385 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-message-util/node_modules/react-is'
385 silly reify ]
386 silly reify mark deleted [
386 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-mock'
386 silly reify ]
387 silly reify mark deleted [
387 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-pnp-resolver'
387 silly reify ]
388 silly reify mark deleted [
388 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-regex-util'
388 silly reify ]
389 silly reify mark deleted [
389 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-resolve'
389 silly reify ]
390 silly reify mark deleted [
390 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-resolve-dependencies'
390 silly reify ]
391 silly reify mark deleted [
391 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-resolve/node_modules/chalk'
391 silly reify ]
392 silly reify mark deleted [
392 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-runner'
392 silly reify ]
393 silly reify mark deleted [
393 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-runner/node_modules/chalk'
393 silly reify ]
394 silly reify mark deleted [
394 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-runtime'
394 silly reify ]
395 silly reify mark deleted [
395 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-runtime/node_modules/chalk'
395 silly reify ]
396 silly reify mark deleted [
396 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-snapshot'
396 silly reify ]
397 silly reify mark deleted [
397 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-snapshot/node_modules/chalk'
397 silly reify ]
398 silly reify mark deleted [
398 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-snapshot/node_modules/pretty-format'
398 silly reify ]
399 silly reify mark deleted [
399 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-snapshot/node_modules/pretty-format/node_modules/ansi-styles'
399 silly reify ]
400 silly reify mark deleted [
400 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-snapshot/node_modules/react-is'
400 silly reify ]
401 silly reify mark deleted [
401 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-util'
401 silly reify ]
402 silly reify mark deleted [
402 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-util/node_modules/chalk'
402 silly reify ]
403 silly reify mark deleted [
403 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-validate'
403 silly reify ]
404 silly reify mark deleted [
404 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-validate/node_modules/camelcase'
404 silly reify ]
405 silly reify mark deleted [
405 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-validate/node_modules/chalk'
405 silly reify ]
406 silly reify mark deleted [
406 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-validate/node_modules/pretty-format'
406 silly reify ]
407 silly reify mark deleted [
407 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-validate/node_modules/pretty-format/node_modules/ansi-styles'
407 silly reify ]
408 silly reify mark deleted [
408 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-validate/node_modules/react-is'
408 silly reify ]
409 silly reify mark deleted [
409 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-watcher'
409 silly reify ]
410 silly reify mark deleted [
410 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-watcher/node_modules/chalk'
410 silly reify ]
411 silly reify mark deleted [
411 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-worker'
411 silly reify ]
412 silly reify mark deleted [
412 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jest-worker/node_modules/supports-color'
412 silly reify ]
413 silly reify mark deleted [
413 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jose'
413 silly reify ]
414 silly reify mark deleted [
414 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/js-yaml',
414 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/js-yaml'
414 silly reify ]
415 silly reify mark deleted [
415 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/json-schema-to-ts'
415 silly reify ]
416 silly reify mark deleted [
416 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/json-schema-traverse'
416 silly reify ]
417 silly reify mark deleted [
417 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/jsonfile'
417 silly reify ]
418 silly reify mark deleted [
418 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/kleur'
418 silly reify ]
419 silly reify mark deleted [
419 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/leven'
419 silly reify ]
420 silly reify mark deleted [
420 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/lilconfig'
420 silly reify ]
421 silly reify mark deleted [
421 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/locate-path'
421 silly reify ]
422 silly reify mark deleted [
422 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/lodash.memoize'
422 silly reify ]
423 silly reify mark deleted [
423 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/lodash.uniq'
423 silly reify ]
424 silly reify mark deleted [
424 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/make-error'
424 silly reify ]
425 silly reify mark deleted [
425 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/makeerror'
425 silly reify ]
426 silly reify mark deleted [
426 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/mdn-data'
426 silly reify ]
427 silly reify mark deleted [
427 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/micro',
427 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/micro'
427 silly reify ]
428 silly reify mark deleted [
428 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/micro/node_modules/arg'
428 silly reify ]
429 silly reify mark deleted [
429 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/micro/node_modules/bytes'
429 silly reify ]
430 silly reify mark deleted [
430 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/micro/node_modules/content-type'
430 silly reify ]
431 silly reify mark deleted [
431 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/micro/node_modules/depd'
431 silly reify ]
432 silly reify mark deleted [
432 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/micro/node_modules/http-errors'
432 silly reify ]
433 silly reify mark deleted [
433 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/micro/node_modules/raw-body'
433 silly reify ]
434 silly reify mark deleted [
434 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/micro/node_modules/setprototypeof'
434 silly reify ]
435 silly reify mark deleted [
435 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/micro/node_modules/statuses'
435 silly reify ]
436 silly reify mark deleted [
436 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/micro/node_modules/toidentifier'
436 silly reify ]
437 silly reify mark deleted [
437 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/mri'
437 silly reify ]
438 silly reify mark deleted [
438 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/natural-compare'
438 silly reify ]
439 silly reify mark deleted [
439 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/netlify-plugin-cache'
439 silly reify ]
440 silly reify mark deleted [
440 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/node-int64'
440 silly reify ]
441 silly reify mark deleted [
441 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/npm-run-path'
441 silly reify ]
442 silly reify mark deleted [
442 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/nth-check'
442 silly reify ]
443 silly reify mark deleted [
443 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/os-paths'
443 silly reify ]
444 silly reify mark deleted [
444 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/p-limit'
444 silly reify ]
445 silly reify mark deleted [
445 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/p-locate'
445 silly reify ]
446 silly reify mark deleted [
446 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/p-locate/node_modules/p-limit'
446 silly reify ]
447 silly reify mark deleted [
447 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/p-try'
447 silly reify ]
448 silly reify mark deleted [
448 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/package-json-from-dist'
448 silly reify ]
449 silly reify mark deleted [
449 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/parse-ms'
449 silly reify ]
450 silly reify mark deleted [
450 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/path-browserify'
450 silly reify ]
451 silly reify mark deleted [
451 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/path-exists'
451 silly reify ]
452 silly reify mark deleted [
452 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/path-match'
452 silly reify ]
453 silly reify mark deleted [
453 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/path-match/node_modules/http-errors'
453 silly reify ]
454 silly reify mark deleted [
454 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/path-match/node_modules/inherits'
454 silly reify ]
455 silly reify mark deleted [
455 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/path-match/node_modules/isarray'
455 silly reify ]
456 silly reify mark deleted [
456 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/path-match/node_modules/path-to-regexp'
456 silly reify ]
457 silly reify mark deleted [
457 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/path-match/node_modules/statuses'
457 silly reify ]
458 silly reify mark deleted [
458 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/path-scurry'
458 silly reify ]
459 silly reify mark deleted [
459 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/path-scurry/node_modules/lru-cache'
459 silly reify ]
460 silly reify mark deleted [
460 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/path-to-regexp'
460 silly reify ]
461 silly reify mark deleted [
461 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/path-to-regexp-updated'
461 silly reify ]
462 silly reify mark deleted [
462 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/pend'
462 silly reify ]
463 silly reify mark deleted [
463 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/pirates'
463 silly reify ]
464 silly reify mark deleted [
464 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/pkg-dir'
464 silly reify ]
465 silly reify mark deleted [
465 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-calc'
465 silly reify ]
466 silly reify mark deleted [
466 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-colormin'
466 silly reify ]
467 silly reify mark deleted [
467 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-convert-values'
467 silly reify ]
468 silly reify mark deleted [
468 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-discard-comments'
468 silly reify ]
469 silly reify mark deleted [
469 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-discard-duplicates'
469 silly reify ]
470 silly reify mark deleted [
470 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-discard-empty'
470 silly reify ]
471 silly reify mark deleted [
471 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-discard-overridden'
471 silly reify ]
472 silly reify mark deleted [
472 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-merge-longhand'
472 silly reify ]
473 silly reify mark deleted [
473 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-merge-rules'
473 silly reify ]
474 silly reify mark deleted [
474 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-minify-font-values'
474 silly reify ]
475 silly reify mark deleted [
475 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-minify-gradients'
475 silly reify ]
476 silly reify mark deleted [
476 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-minify-params'
476 silly reify ]
477 silly reify mark deleted [
477 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-minify-selectors'
477 silly reify ]
478 silly reify mark deleted [
478 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-normalize-charset'
478 silly reify ]
479 silly reify mark deleted [
479 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-normalize-display-values'
479 silly reify ]
480 silly reify mark deleted [
480 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-normalize-positions'
480 silly reify ]
481 silly reify mark deleted [
481 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-normalize-repeat-style'
481 silly reify ]
482 silly reify mark deleted [
482 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-normalize-string'
482 silly reify ]
483 silly reify mark deleted [
483 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-normalize-timing-functions'
483 silly reify ]
484 silly reify mark deleted [
484 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-normalize-unicode'
484 silly reify ]
485 silly reify mark deleted [
485 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-normalize-url'
485 silly reify ]
486 silly reify mark deleted [
486 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-normalize-whitespace'
486 silly reify ]
487 silly reify mark deleted [
487 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-ordered-values'
487 silly reify ]
488 silly reify mark deleted [
488 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-reduce-initial'
488 silly reify ]
489 silly reify mark deleted [
489 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-reduce-transforms'
489 silly reify ]
490 silly reify mark deleted [
490 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-selector-parser'
490 silly reify ]
491 silly reify mark deleted [
491 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-svgo'
491 silly reify ]
492 silly reify mark deleted [
492 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-unique-selectors'
492 silly reify ]
493 silly reify mark deleted [
493 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/postcss-value-parser'
493 silly reify ]
494 silly reify mark deleted [
494 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/pretty-ms'
494 silly reify ]
495 silly reify mark deleted [
495 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/promisepipe'
495 silly reify ]
496 silly reify mark deleted [
496 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/prompts'
496 silly reify ]
497 silly reify mark deleted [
497 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/pure-rand'
497 silly reify ]
498 silly reify mark deleted [
498 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/readdirp'
498 silly reify ]
499 silly reify mark deleted [
499 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/require-from-string'
499 silly reify ]
500 silly reify mark deleted [
500 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/resolve-cwd'
500 silly reify ]
501 silly reify mark deleted [
501 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/resolve-from'
501 silly reify ]
502 silly reify mark deleted [
502 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/resolve.exports'
502 silly reify ]
503 silly reify mark deleted [
503 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/shell-quote'
503 silly reify ]
504 silly reify mark deleted [
504 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/signal-exit'
504 silly reify ]
505 silly reify mark deleted [
505 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/sisteransi'
505 silly reify ]
506 silly reify mark deleted [
506 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/slash'
506 silly reify ]
507 silly reify mark deleted [
507 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/source-map-support'
507 silly reify ]
508 silly reify mark deleted [
508 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/source-map-support/node_modules/source-map'
508 silly reify ]
509 silly reify mark deleted [
509 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/sprintf-js'
509 silly reify ]
510 silly reify mark deleted [
510 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/stack-utils'
510 silly reify ]
511 silly reify mark deleted [
511 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/stack-utils/node_modules/escape-string-regexp'
511 silly reify ]
512 silly reify mark deleted [
512 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/stat-mode'
512 silly reify ]
513 silly reify mark deleted [
513 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/stream-to-array'
513 silly reify ]
514 silly reify mark deleted [
514 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/stream-to-promise'
514 silly reify ]
515 silly reify mark deleted [
515 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/string-length'
515 silly reify ]
516 silly reify mark deleted [
516 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/string-width-cjs'
516 silly reify ]
517 silly reify mark deleted [
517 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/strip-ansi-cjs'
517 silly reify ]
518 silly reify mark deleted [
518 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/strip-bom'
518 silly reify ]
519 silly reify mark deleted [
519 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/strip-final-newline'
519 silly reify ]
520 silly reify mark deleted [
520 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/strip-json-comments'
520 silly reify ]
521 silly reify mark deleted [
521 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/stylehacks'
521 silly reify ]
522 silly reify mark deleted [
522 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/svgo',
522 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/svgo'
522 silly reify ]
523 silly reify mark deleted [
523 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/svgo/node_modules/commander'
523 silly reify ]
524 silly reify mark deleted [
524 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/test-exclude'
524 silly reify ]
525 silly reify mark deleted [
525 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/time-span'
525 silly reify ]
526 silly reify mark deleted [
526 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/tinyexec'
526 silly reify ]
527 silly reify mark deleted [
527 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/tmpl'
527 silly reify ]
528 silly reify mark deleted [
528 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/tree-kill',
528 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/tree-kill'
528 silly reify ]
529 silly reify mark deleted [
529 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/ts-morph'
529 silly reify ]
530 silly reify mark deleted [
530 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/ts-node',
530 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/ts-node',
530 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/ts-node-cwd',
530 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/ts-node-esm',
530 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/ts-node-script',
530 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/ts-node-transpile-only',
530 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/ts-script'
530 silly reify ]
531 silly reify mark deleted [
531 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/ts-toolbelt'
531 silly reify ]
532 silly reify mark deleted [
532 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/uid-promise'
532 silly reify ]
533 silly reify mark deleted [
533 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/undici'
533 silly reify ]
534 silly reify mark deleted [
534 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/universalify'
534 silly reify ]
535 silly reify mark deleted [
535 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/uri-js'
535 silly reify ]
536 silly reify mark deleted [
536 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/v8-compile-cache-lib'
536 silly reify ]
537 silly reify mark deleted [
537 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/v8-to-istanbul'
537 silly reify ]
538 silly reify mark deleted [
538 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/v8-to-istanbul/node_modules/convert-source-map'
538 silly reify ]
539 silly reify mark deleted [
539 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel',
539 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/vc',
539 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/.bin/vercel'
539 silly reify ]
540 silly reify mark deleted [
540 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/@mapbox/node-pre-gyp',
540 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/.bin/node-pre-gyp'
540 silly reify ]
541 silly reify mark deleted [
541 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/@rollup/pluginutils'
541 silly reify ]
542 silly reify mark deleted [
542 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/@types/node'
542 silly reify ]
543 silly reify mark deleted [
543 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/@vercel/build-utils'
543 silly reify ]
544 silly reify mark deleted [
544 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/@vercel/nft',
544 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/.bin/nft'
544 silly reify ]
545 silly reify mark deleted [
545 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/@vercel/node'
545 silly reify ]
546 silly reify mark deleted [
546 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/@vercel/static-config'
546 silly reify ]
547 silly reify mark deleted [
547 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/abbrev'
547 silly reify ]
548 silly reify mark deleted [
548 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/agent-base'
548 silly reify ]
549 silly reify mark deleted [
549 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/brace-expansion'
549 silly reify ]
550 silly reify mark deleted [
550 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/chownr'
550 silly reify ]
551 silly reify mark deleted [
551 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/glob',
551 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/.bin/glob'
551 silly reify ]
552 silly reify mark deleted [
552 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/https-proxy-agent'
552 silly reify ]
553 silly reify mark deleted [
553 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/minimatch'
553 silly reify ]
554 silly reify mark deleted [
554 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/minipass'
554 silly reify ]
555 silly reify mark deleted [
555 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/minizlib'
555 silly reify ]
556 silly reify mark deleted [
556 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/mkdirp',
556 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/.bin/mkdirp'
556 silly reify ]
557 silly reify mark deleted [
557 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/node-fetch'
557 silly reify ]
558 silly reify mark deleted [
558 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/nopt',
558 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/.bin/nopt'
558 silly reify ]
559 silly reify mark deleted [
559 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/path-to-regexp'
559 silly reify ]
560 silly reify mark deleted [
560 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/picomatch'
560 silly reify ]
561 silly reify mark deleted [
561 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/tar'
561 silly reify ]
562 silly reify mark deleted [
562 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vercel/node_modules/yallist'
562 silly reify ]
563 silly reify mark deleted [
563 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/walker'
563 silly reify ]
564 silly reify mark deleted [
564 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/wrap-ansi-cjs'
564 silly reify ]
565 silly reify mark deleted [
565 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/write-file-atomic'
565 silly reify ]
566 silly reify mark deleted [
566 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/write-file-atomic/node_modules/signal-exit'
566 silly reify ]
567 silly reify mark deleted [
567 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/xdg-app-paths'
567 silly reify ]
568 silly reify mark deleted [
568 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/xdg-portable'
568 silly reify ]
569 silly reify mark deleted [
569 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/yauzl'
569 silly reify ]
570 silly reify mark deleted [
570 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/yauzl-clone'
570 silly reify ]
571 silly reify mark deleted [
571 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/yauzl-promise'
571 silly reify ]
572 silly reify mark deleted [
572 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/yn'
572 silly reify ]
573 silly reify mark deleted [
573 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/yocto-queue'
573 silly reify ]
574 silly reify mark deleted [
574 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/yoctocolors-cjs'
574 silly reify ]
575 silly audit bulk request {
575 silly audit   wasp: [ '1.0.0' ],
575 silly audit   '@tanstack/react-query': [ '4.39.1' ],
575 silly audit   '@testing-library/dom': [ '9.3.4' ],
575 silly audit   chalk: [ '4.1.2', '3.0.0' ],
575 silly audit   'dom-accessibility-api': [ '0.5.16', '0.6.3' ],
575 silly audit   '@testing-library/jest-dom': [ '6.6.3' ],
575 silly audit   '@testing-library/react': [ '14.3.1' ],
575 silly audit   'aria-query': [ '5.1.3' ],
575 silly audit   '@adobe/css-tools': [ '4.4.3' ],
575 silly audit   '@ampproject/remapping': [ '2.3.0' ],
575 silly audit   '@babel/code-frame': [ '7.27.1' ],
575 silly audit   '@babel/compat-data': [ '7.27.3' ],
575 silly audit   '@babel/core': [ '7.27.4' ],
575 silly audit   'convert-source-map': [ '2.0.0', '1.9.0' ],
575 silly audit   semver: [ '6.3.1', '5.7.2', '7.7.2' ],
575 silly audit   '@babel/generator': [ '7.27.3' ],
575 silly audit   '@babel/helper-compilation-targets': [ '7.27.2' ],
575 silly audit   '@babel/helper-module-imports': [ '7.27.1' ],
575 silly audit   '@babel/helper-module-transforms': [ '7.27.3' ],
575 silly audit   '@babel/helper-plugin-utils': [ '7.27.1' ],
575 silly audit   '@babel/helper-string-parser': [ '7.27.1' ],
575 silly audit   '@babel/helper-validator-identifier': [ '7.27.1' ],
575 silly audit   '@babel/helper-validator-option': [ '7.27.1' ],
575 silly audit   '@babel/helpers': [ '7.27.4' ],
575 silly audit   '@babel/parser': [ '7.27.4' ],
575 silly audit   '@babel/plugin-transform-react-jsx-self': [ '7.27.1' ],
575 silly audit   '@babel/plugin-transform-react-jsx-source': [ '7.27.1' ],
575 silly audit   '@babel/runtime': [ '7.27.4' ],
575 silly audit   '@babel/template': [ '7.27.2' ],
575 silly audit   '@babel/traverse': [ '7.27.4' ],
575 silly audit   '@babel/types': [ '7.27.3' ],
575 silly audit   '@bleskomat/form': [ '1.2.6' ],
575 silly audit   '@chakra-ui/accordion': [ '2.3.0' ],
575 silly audit   '@chakra-ui/alert': [ '2.2.0' ],
575 silly audit   '@chakra-ui/anatomy': [ '2.2.0' ],
575 silly audit   '@chakra-ui/avatar': [ '2.3.0' ],
575 silly audit   '@chakra-ui/breadcrumb': [ '2.2.0' ],
575 silly audit   '@chakra-ui/breakpoint-utils': [ '2.0.8' ],
575 silly audit   '@chakra-ui/button': [ '2.1.0' ],
575 silly audit   '@chakra-ui/card': [ '2.2.0' ],
575 silly audit   '@chakra-ui/checkbox': [ '2.3.0' ],
575 silly audit   '@chakra-ui/clickable': [ '2.1.0' ],
575 silly audit   '@chakra-ui/close-button': [ '2.1.0' ],
575 silly audit   '@chakra-ui/color-mode': [ '2.2.0' ],
575 silly audit   '@chakra-ui/control-box': [ '2.1.0' ],
575 silly audit   '@chakra-ui/counter': [ '2.1.0' ],
575 silly audit   '@chakra-ui/css-reset': [ '2.2.0' ],
575 silly audit   '@chakra-ui/descendant': [ '3.1.0' ],
575 silly audit   '@chakra-ui/dom-utils': [ '2.1.0' ],
575 silly audit   '@chakra-ui/editable': [ '3.1.0' ],
575 silly audit   '@chakra-ui/event-utils': [ '2.0.8' ],
575 silly audit   '@chakra-ui/focus-lock': [ '2.1.0' ],
575 silly audit   '@chakra-ui/form-control': [ '2.1.0' ],
575 silly audit   '@chakra-ui/hooks': [ '2.2.0' ],
575 silly audit   '@chakra-ui/icon': [ '3.1.0' ],
575 silly audit   '@chakra-ui/icons': [ '2.2.4' ],
575 silly audit   '@chakra-ui/image': [ '2.1.0' ],
575 silly audit   '@chakra-ui/input': [ '2.1.0' ],
575 silly audit   '@chakra-ui/layout': [ '2.3.0' ],
575 silly audit   '@chakra-ui/lazy-utils': [ '2.0.5' ],
575 silly audit   '@chakra-ui/live-region': [ '2.1.0' ],
575 silly audit   '@chakra-ui/media-query': [ '3.3.0' ],
575 silly audit   '@chakra-ui/menu': [ '2.2.0' ],
575 silly audit   '@chakra-ui/modal': [ '2.3.0' ],
575 silly audit   '@chakra-ui/number-input': [ '2.1.0' ],
575 silly audit   '@chakra-ui/number-utils': [ '2.0.7' ],
575 silly audit   '@chakra-ui/object-utils': [ '2.1.0' ],
575 silly audit   '@chakra-ui/pin-input': [ '2.1.0' ],
575 silly audit   '@chakra-ui/popover': [ '2.2.0' ],
575 silly audit   '@chakra-ui/popper': [ '3.1.0' ],
575 silly audit   '@chakra-ui/portal': [ '2.1.0' ],
575 silly audit   '@chakra-ui/progress': [ '2.2.0' ],
575 silly audit   '@chakra-ui/provider': [ '2.4.0' ],
575 silly audit   '@chakra-ui/radio': [ '2.1.0' ],
575 silly audit   '@chakra-ui/react': [ '2.8.0' ],
575 silly audit   '@chakra-ui/react-children-utils': [ '2.0.6' ],
575 silly audit   '@chakra-ui/react-context': [ '2.1.0' ],
575 silly audit   '@chakra-ui/react-env': [ '3.1.0' ],
575 silly audit   '@chakra-ui/react-types': [ '2.0.7' ],
575 silly audit   '@chakra-ui/react-use-animation-state': [ '2.1.0' ],
575 silly audit   '@chakra-ui/react-use-callback-ref': [ '2.1.0' ],
575 silly audit   '@chakra-ui/react-use-controllable-state': [ '2.1.0' ],
575 silly audit   '@chakra-ui/react-use-disclosure': [ '2.1.0' ],
575 silly audit   '@chakra-ui/react-use-event-listener': [ '2.1.0' ],
575 silly audit   '@chakra-ui/react-use-focus-effect': [ '2.1.0' ],
575 silly audit   '@chakra-ui/react-use-focus-on-pointer-down': [ '2.1.0' ],
575 silly audit   '@chakra-ui/react-use-interval': [ '2.1.0' ],
575 silly audit   '@chakra-ui/react-use-latest-ref': [ '2.1.0' ],
575 silly audit   '@chakra-ui/react-use-merge-refs': [ '2.1.0' ],
575 silly audit   '@chakra-ui/react-use-outside-click': [ '2.2.0' ],
575 silly audit   '@chakra-ui/react-use-pan-event': [ '2.1.0' ],
575 silly audit   '@chakra-ui/react-use-previous': [ '2.1.0' ],
575 silly audit   '@chakra-ui/react-use-safe-layout-effect': [ '2.1.0' ],
575 silly audit   '@chakra-ui/react-use-size': [ '2.1.0' ],
575 silly audit   '@chakra-ui/react-use-timeout': [ '2.1.0' ],
575 silly audit   '@chakra-ui/react-use-update-effect': [ '2.1.0' ],
575 silly audit   '@chakra-ui/react-utils': [ '2.0.12' ],
575 silly audit   '@chakra-ui/select': [ '2.1.0' ],
575 silly audit   '@chakra-ui/shared-utils': [ '2.0.5' ],
575 silly audit   '@chakra-ui/skeleton': [ '2.1.0' ],
575 silly audit   '@chakra-ui/skip-nav': [ '2.1.0' ],
575 silly audit   '@chakra-ui/slider': [ '2.1.0' ],
575 silly audit   '@chakra-ui/spinner': [ '2.1.0' ],
575 silly audit   '@chakra-ui/stat': [ '2.1.0' ],
575 silly audit   '@chakra-ui/stepper': [ '2.3.0' ],
575 silly audit   '@chakra-ui/styled-system': [ '2.9.1' ],
575 silly audit   '@chakra-ui/switch': [ '2.1.0' ],
575 silly audit   '@chakra-ui/system': [ '2.6.0' ],
575 silly audit   '@chakra-ui/table': [ '2.1.0' ],
575 silly audit   '@chakra-ui/tabs': [ '2.2.0' ],
575 silly audit   '@chakra-ui/tag': [ '3.1.0' ],
575 silly audit   '@chakra-ui/textarea': [ '2.1.0' ],
575 silly audit   '@chakra-ui/theme': [ '3.2.0' ],
575 silly audit   '@chakra-ui/theme-tools': [ '2.1.0' ],
575 silly audit   '@chakra-ui/theme-utils': [ '2.0.19' ],
575 silly audit   '@chakra-ui/toast': [ '7.0.0' ],
575 silly audit   '@chakra-ui/tooltip': [ '2.3.0' ],
575 silly audit   '@chakra-ui/transition': [ '2.1.0' ],
575 silly audit   '@chakra-ui/utils': [ '2.0.15' ],
575 silly audit   '@chakra-ui/visually-hidden': [ '2.1.0' ],
575 silly audit   '@jridgewell/trace-mapping': [ '0.3.25' ],
575 silly audit   '@edge-runtime/primitives': [ '4.1.0' ],
575 silly audit   '@edge-runtime/vm': [ '3.2.0' ],
575 silly audit   '@emnapi/core': [ '1.4.3', '0.45.0' ],
575 silly audit   '@emnapi/runtime': [ '1.4.3', '0.45.0' ],
575 silly audit   '@emnapi/wasi-threads': [ '1.0.2' ],
575 silly audit   '@emotion/babel-plugin': [ '11.13.5' ],
575 silly audit   '@emotion/cache': [ '11.14.0' ],
575 silly audit   '@emotion/weak-memoize': [ '0.4.0', '0.3.1' ],
575 silly audit   '@emotion/hash': [ '0.9.2' ],
575 silly audit   '@emotion/is-prop-valid': [ '1.3.1', '0.8.8' ],
575 silly audit   '@emotion/memoize': [ '0.9.0', '0.7.4' ],
575 silly audit   '@emotion/react': [ '11.10.6' ],
575 silly audit   '@emotion/serialize': [ '1.3.3' ],
575 silly audit   '@emotion/sheet': [ '1.4.0' ],
575 silly audit   '@emotion/styled': [ '11.10.6' ],
575 silly audit   '@emotion/unitless': [ '0.10.0' ],
575 silly audit   '@emotion/use-insertion-effect-with-fallbacks': [ '1.2.0' ],
575 silly audit   '@emotion/utils': [ '1.4.2' ],
575 silly audit   '@esbuild/aix-ppc64': [ '0.21.5' ],
575 silly audit   '@esbuild/android-arm': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/android-arm64': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/android-x64': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/darwin-arm64': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/darwin-x64': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/freebsd-arm64': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/freebsd-x64': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/linux-arm': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/linux-arm64': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/linux-ia32': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/linux-loong64': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/linux-mips64el': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/linux-ppc64': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/linux-riscv64': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/linux-s390x': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/linux-x64': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/netbsd-x64': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/openbsd-x64': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/sunos-x64': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/win32-arm64': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/win32-ia32': [ '0.18.20', '0.21.5' ],
575 silly audit   '@esbuild/win32-x64': [ '0.18.20', '0.21.5' ],
575 silly audit   inquirer: [ '8.2.6' ],
575 silly audit   'mute-stream': [ '0.0.8' ],
575 silly audit   'run-async': [ '2.4.1' ],
575 silly audit   'cli-width': [ '3.0.0' ],
575 silly audit   'signal-exit': [ '3.0.7', '4.1.0' ],
575 silly audit   'wrap-ansi': [ '6.2.0', '7.0.0' ],
575 silly audit   'ansi-regex': [ '5.0.1' ],
575 silly audit   'ansi-styles': [ '5.2.0', '4.3.0' ],
575 silly audit   'emoji-regex': [ '8.0.0' ],
575 silly audit   'string-width': [ '4.2.3' ],
575 silly audit   'strip-ansi': [ '6.0.1' ],
575 silly audit   minipass: [ '3.3.6', '5.0.0' ],
575 silly audit   'pretty-format': [ '29.7.0', '27.5.1' ],
575 silly audit   'react-is': [ '18.3.1', '17.0.2', '16.13.1' ],
575 silly audit   '@jest/schemas': [ '29.6.3' ],
575 silly audit   '@sinclair/typebox': [ '0.27.8' ],
575 silly audit   '@jridgewell/gen-mapping': [ '0.3.8' ],
575 silly audit   '@jridgewell/resolve-uri': [ '3.1.2' ],
575 silly audit   '@jridgewell/set-array': [ '1.2.1' ],
575 silly audit   '@jridgewell/sourcemap-codec': [ '1.5.0' ],
575 silly audit   '@kurkle/color': [ '0.3.4' ],
575 silly audit   '@lucia-auth/adapter-prisma': [ '4.0.1' ],
575 silly audit   '@mapbox/node-pre-gyp': [ '1.0.11' ],
575 silly audit   'node-fetch': [ '2.7.0', '3.3.0' ],
575 silly audit   '@mswjs/cookies': [ '0.2.2' ],
575 silly audit   '@mswjs/interceptors': [ '0.17.10' ],
575 silly audit   'strict-event-emitter': [ '0.2.8', '0.4.6' ],
575 silly audit   '@napi-rs/wasm-runtime': [ '0.2.10' ],
575 silly audit   '@noble/hashes': [ '1.8.0' ],
575 silly audit   '@node-rs/argon2': [ '1.8.3', '1.7.0' ],
575 silly audit   '@node-rs/argon2-android-arm-eabi': [ '1.8.3', '1.7.0' ],
575 silly audit   '@node-rs/argon2-android-arm64': [ '1.8.3', '1.7.0' ],
575 silly audit   '@node-rs/argon2-darwin-arm64': [ '1.8.3', '1.7.0' ],
575 silly audit   '@node-rs/argon2-darwin-x64': [ '1.8.3', '1.7.0' ],
575 silly audit   '@node-rs/argon2-freebsd-x64': [ '1.8.3', '1.7.0' ],
575 silly audit   '@node-rs/argon2-linux-arm-gnueabihf': [ '1.8.3', '1.7.0' ],
575 silly audit   '@node-rs/argon2-linux-arm64-gnu': [ '1.8.3', '1.7.0' ],
575 silly audit   '@node-rs/argon2-linux-arm64-musl': [ '1.8.3', '1.7.0' ],
575 silly audit   '@node-rs/argon2-linux-x64-gnu': [ '1.8.3', '1.7.0' ],
575 silly audit   '@node-rs/argon2-linux-x64-musl': [ '1.8.3', '1.7.0' ],
575 silly audit   '@node-rs/argon2-wasm32-wasi': [ '1.8.3', '1.7.0' ],
575 silly audit   '@node-rs/argon2-win32-arm64-msvc': [ '1.8.3', '1.7.0' ],
575 silly audit   '@node-rs/argon2-win32-ia32-msvc': [ '1.8.3', '1.7.0' ],
575 silly audit   '@node-rs/argon2-win32-x64-msvc': [ '1.8.3', '1.7.0' ],
575 silly audit   '@node-rs/bcrypt': [ '1.9.0' ],
575 silly audit   '@node-rs/bcrypt-android-arm-eabi': [ '1.9.0' ],
575 silly audit   '@node-rs/bcrypt-android-arm64': [ '1.9.0' ],
575 silly audit   '@node-rs/bcrypt-darwin-arm64': [ '1.9.0' ],
575 silly audit   '@node-rs/bcrypt-darwin-x64': [ '1.9.0' ],
575 silly audit   '@node-rs/bcrypt-freebsd-x64': [ '1.9.0' ],
575 silly audit   '@node-rs/bcrypt-linux-arm-gnueabihf': [ '1.9.0' ],
575 silly audit   '@node-rs/bcrypt-linux-arm64-gnu': [ '1.9.0' ],
575 silly audit   '@node-rs/bcrypt-linux-arm64-musl': [ '1.9.0' ],
575 silly audit   '@node-rs/bcrypt-linux-x64-gnu': [ '1.9.0' ],
575 silly audit   '@node-rs/bcrypt-linux-x64-musl': [ '1.9.0' ],
575 silly audit   '@node-rs/bcrypt-wasm32-wasi': [ '1.9.0' ],
575 silly audit   '@tybys/wasm-util': [ '0.8.3', '0.9.0' ],
575 silly audit   '@node-rs/bcrypt-win32-arm64-msvc': [ '1.9.0' ],
575 silly audit   '@node-rs/bcrypt-win32-ia32-msvc': [ '1.9.0' ],
575 silly audit   '@node-rs/bcrypt-win32-x64-msvc': [ '1.9.0' ],
575 silly audit   '@nodelib/fs.scandir': [ '2.1.5' ],
575 silly audit   '@nodelib/fs.stat': [ '2.0.5' ],
575 silly audit   '@nodelib/fs.walk': [ '1.2.8' ],
575 silly audit   '@open-draft/until': [ '1.0.3' ],
575 silly audit   '@oslojs/asn1': [ '1.0.0' ],
575 silly audit   '@oslojs/binary': [ '1.0.0' ],
575 silly audit   '@oslojs/crypto': [ '1.0.1' ],
575 silly audit   '@oslojs/encoding': [ '1.1.0' ],
575 silly audit   '@polka/url': [ '1.0.0-next.29' ],
575 silly audit   '@popperjs/core': [ '2.11.8' ],
575 silly audit   '@prisma/client': [ '5.19.1' ],
575 silly audit   '@prisma/debug': [ '5.19.1' ],
575 silly audit   '@prisma/engines': [ '5.19.1' ],
575 silly audit   '@prisma/engines-version': [ '5.19.1-2.69d742ee20b815d88e17e54db4a2a7a3b30324e3' ],
575 silly audit   '@prisma/fetch-engine': [ '5.19.1' ],
575 silly audit   '@prisma/get-platform': [ '5.19.1' ],
575 silly audit   '@react-email/render': [ '1.0.6' ],
575 silly audit   '@remix-run/router': [ '1.23.0' ],
575 silly audit   '@rolldown/pluginutils': [ '1.0.0-beta.9' ],
575 silly audit   '@rollup/rollup-android-arm-eabi': [ '4.41.1' ],
575 silly audit   '@rollup/rollup-android-arm64': [ '4.41.1' ],
575 silly audit   '@rollup/rollup-darwin-arm64': [ '4.41.1' ],
575 silly audit   '@rollup/rollup-darwin-x64': [ '4.41.1' ],
575 silly audit   '@rollup/rollup-freebsd-arm64': [ '4.41.1' ],
575 silly audit   '@rollup/rollup-freebsd-x64': [ '4.41.1' ],
575 silly audit   '@rollup/rollup-linux-arm-gnueabihf': [ '4.41.1' ],
575 silly audit   '@rollup/rollup-linux-arm-musleabihf': [ '4.41.1' ],
575 silly audit   '@rollup/rollup-linux-arm64-gnu': [ '4.41.1' ],
575 silly audit   '@rollup/rollup-linux-arm64-musl': [ '4.41.1' ],
575 silly audit   '@rollup/rollup-linux-loongarch64-gnu': [ '4.41.1' ],
575 silly audit   '@rollup/rollup-linux-powerpc64le-gnu': [ '4.41.1' ],
575 silly audit   '@rollup/rollup-linux-riscv64-gnu': [ '4.41.1' ],
575 silly audit   '@rollup/rollup-linux-riscv64-musl': [ '4.41.1' ],
575 silly audit   '@rollup/rollup-linux-s390x-gnu': [ '4.41.1' ],
575 silly audit   '@rollup/rollup-linux-x64-gnu': [ '4.41.1' ],
575 silly audit   '@rollup/rollup-linux-x64-musl': [ '4.41.1' ],
575 silly audit   '@rollup/rollup-win32-arm64-msvc': [ '4.41.1' ],
575 silly audit   '@rollup/rollup-win32-ia32-msvc': [ '4.41.1' ],
575 silly audit   '@rollup/rollup-win32-x64-msvc': [ '4.41.1' ],
575 silly audit   '@selderee/plugin-htmlparser2': [ '0.11.0' ],
575 silly audit   '@stitches/react': [ '1.2.8' ],
575 silly audit   '@tanstack/query-core': [ '4.39.1' ],
575 silly audit   '@tootallnate/once': [ '2.0.0' ],
575 silly audit   '@types/aria-query': [ '5.0.4' ],
575 silly audit   '@types/babel__core': [ '7.20.5' ],
575 silly audit   '@types/babel__generator': [ '7.27.0' ],
575 silly audit   '@types/babel__template': [ '7.4.4' ],
575 silly audit   '@types/babel__traverse': [ '7.20.7' ],
575 silly audit   '@types/bn.js': [ '4.11.6' ],
575 silly audit   '@types/cookie': [ '0.4.1' ],
575 silly audit   '@types/cors': [ '2.8.18' ],
575 silly audit   '@types/debug': [ '4.1.12' ],
575 silly audit   '@types/estree': [ '1.0.7' ],
575 silly audit   '@types/js-levenshtein': [ '1.1.3' ],
575 silly audit   '@types/lodash': [ '4.17.17' ],
575 silly audit   '@types/lodash.mergewith': [ '4.6.7' ],
575 silly audit   '@types/ms': [ '2.1.0' ],
575 silly audit   '@types/node': [ '18.19.110' ],
575 silly audit   '@types/node-fetch': [ '2.6.12' ],
575 silly audit   '@types/parse-json': [ '4.0.2' ],
575 silly audit   '@types/prop-types': [ '15.7.14' ],
575 silly audit   '@types/react': [ '18.3.23' ],
575 silly audit   '@types/react-dom': [ '18.3.7' ],
575 silly audit   '@types/set-cookie-parser': [ '2.4.10' ],
575 silly audit   '@types/uuid': [ '10.0.0' ],
575 silly audit   debug: [ '2.6.9', '4.4.1', '4.3.4' ],
575 silly audit   ms: [ '2.0.0', '2.1.2', '2.1.3' ],
575 silly audit   'lru-cache': [ '5.1.1' ],
575 silly audit   yallist: [ '4.0.0', '3.1.1' ],
575 silly audit   'web-vitals': [ '5.0.2' ],
575 silly audit   abbrev: [ '1.1.1' ],
575 silly audit   'agent-base': [ '6.0.2' ],
575 silly audit   'brace-expansion': [ '1.1.11' ],
575 silly audit   chownr: [ '2.0.0' ],
575 silly audit   glob: [ '7.2.3' ],
575 silly audit   'https-proxy-agent': [ '5.0.1' ],
575 silly audit   minimatch: [ '3.1.2' ],
575 silly audit   minizlib: [ '2.1.2' ],
575 silly audit   mkdirp: [ '1.0.4' ],
575 silly audit   nopt: [ '5.0.0' ],
575 silly audit   picomatch: [ '2.3.1' ],
575 silly audit   tar: [ '6.2.1' ],
575 silly audit   'path-to-regexp': [ '0.1.12', '0.1.7', '6.3.0' ],
575 silly audit   '@vitejs/plugin-react': [ '4.5.0' ],
575 silly audit   '@vitest/expect': [ '1.6.1' ],
575 silly audit   '@vitest/runner': [ '1.6.1' ],
575 silly audit   'p-limit': [ '5.0.0' ],
575 silly audit   'yocto-queue': [ '1.2.1' ],
575 silly audit   '@vitest/snapshot': [ '1.6.1' ],
575 silly audit   '@vitest/spy': [ '1.6.1' ],
575 silly audit   '@vitest/ui': [ '1.6.1' ],
575 silly audit   '@vitest/utils': [ '1.6.1' ],
575 silly audit   'estree-walker': [ '3.0.3' ],
575 silly audit   '@xmldom/xmldom': [ '0.8.10' ],
575 silly audit   '@zag-js/dom-query': [ '0.10.5' ],
575 silly audit   '@zag-js/element-size': [ '0.10.5' ],
575 silly audit   '@zag-js/focus-visible': [ '0.10.5' ],
575 silly audit   '@zxing/text-encoding': [ '0.9.0' ],
575 silly audit   abab: [ '2.0.6' ],
575 silly audit   'abort-controller': [ '3.0.0' ],
575 silly audit   accepts: [ '1.3.8' ],
575 silly audit   acorn: [ '8.14.1' ],
575 silly audit   'acorn-globals': [ '7.0.1' ],
575 silly audit   'acorn-walk': [ '8.3.4' ],
575 silly audit   agentkeepalive: [ '4.6.0' ],
575 silly audit   'aggregate-error': [ '3.1.0' ],
575 silly audit   'ansi-escapes': [ '4.3.2' ],
575 silly audit   anymatch: [ '3.1.3' ],
575 silly audit   aproba: [ '2.0.0' ],
575 silly audit   arctic: [ '1.9.2' ],
575 silly audit   oslo: [ '1.2.0', '1.2.1' ],
575 silly audit   'are-we-there-yet': [ '2.0.0' ],
575 silly audit   'aria-hidden': [ '1.2.6' ],
575 silly audit   'array-buffer-byte-length': [ '1.0.2' ],
575 silly audit   'array-flatten': [ '1.1.1' ],
575 silly audit   'assertion-error': [ '1.1.0' ],
575 silly audit   async: [ '3.2.4', '3.2.3' ],
575 silly audit   asynckit: [ '0.4.0' ],
575 silly audit   'available-typed-arrays': [ '1.0.7' ],
575 silly audit   axios: [ '1.9.0' ],
575 silly audit   'babel-plugin-macros': [ '3.1.0' ],
575 silly audit   'balanced-match': [ '1.0.2' ],
575 silly audit   'base-x': [ '4.0.1' ],
575 silly audit   'base64-js': [ '1.5.1' ],
575 silly audit   bech32: [ '1.1.4', '2.0.0' ],
575 silly audit   'bignumber.js': [ '9.0.2' ],
575 silly audit   'binary-extensions': [ '2.3.0' ],
575 silly audit   bip174: [ '2.1.1' ],
575 silly audit   'bitcoinjs-lib': [ '6.1.7' ],
575 silly audit   bl: [ '4.1.0' ],
575 silly audit   'bn.js': [ '4.12.2' ],
575 silly audit   'body-parser': [ '1.20.3', '1.20.0' ],
575 silly audit   bolt11: [ '1.4.1', '1.3.4' ],
575 silly audit   braces: [ '3.0.3' ],
575 silly audit   brorand: [ '1.1.0' ],
575 silly audit   browserslist: [ '4.25.0' ],
575 silly audit   bs58: [ '5.0.0' ],
575 silly audit   bs58check: [ '3.0.1' ],
575 silly audit   buffer: [ '5.7.1' ],
575 silly audit   'buffer-equal-constant-time': [ '1.0.1' ],
575 silly audit   bytes: [ '3.1.2' ],
575 silly audit   cac: [ '6.7.14' ],
575 silly audit   'call-bind': [ '1.0.8' ],
575 silly audit   'call-bind-apply-helpers': [ '1.0.2' ],
575 silly audit   'call-bound': [ '1.0.4' ],
575 silly audit   callsites: [ '3.1.0' ],
575 silly audit   'caniuse-lite': [ '1.0.30001720' ],
575 silly audit   canvas: [ '2.11.2' ],
575 silly audit   chai: [ '4.5.0' ],
575 silly audit   'type-detect': [ '4.1.0', '4.0.8' ],
575 silly audit   chardet: [ '0.7.0' ],
575 silly audit   'chart.js': [ '4.4.9' ],
575 silly audit   'check-error': [ '1.0.3' ],
575 silly audit   chokidar: [ '3.6.0' ],
575 silly audit   'cipher-base': [ '1.0.6' ],
575 silly audit   'clean-stack': [ '2.2.0' ],
575 silly audit   'cli-cursor': [ '3.1.0' ],
575 silly audit   'cli-spinners': [ '2.9.2' ],
575 silly audit   cliui: [ '8.0.1' ],
575 silly audit   clone: [ '1.0.4' ],
575 silly audit   'color-convert': [ '2.0.1' ],
575 silly audit   'color-name': [ '1.1.4' ],
575 silly audit   'color-support': [ '1.1.3' ],
575 silly audit   color2k: [ '2.0.3' ],
575 silly audit   'combined-stream': [ '1.0.8' ],
575 silly audit   commander: [ '9.3.0' ],
575 silly audit   'compute-scroll-into-view': [ '1.0.20' ],
575 silly audit   'concat-map': [ '0.0.1' ],
575 silly audit   confbox: [ '0.1.8' ],
575 silly audit   'console-control-strings': [ '1.1.0' ],
575 silly audit   'content-disposition': [ '0.5.4' ],
575 silly audit   'content-type': [ '1.0.5' ],
575 silly audit   cookie: [ '0.7.1', '0.5.0', '0.4.2' ],
575 silly audit   'cookie-signature': [ '1.0.6' ],
575 silly audit   'copy-anything': [ '3.0.5' ],
575 silly audit   'copy-to-clipboard': [ '3.3.3' ],
575 silly audit   cors: [ '2.8.5' ],
575 silly audit   cosmiconfig: [ '7.1.0' ],
575 silly audit   'create-hash': [ '1.2.0' ],
575 silly audit   'cron-parser': [ '4.9.0' ],
575 silly audit   'cross-spawn': [ '7.0.6' ],
575 silly audit   'css-box-model': [ '1.2.1' ],
575 silly audit   'css.escape': [ '1.5.1' ],
575 silly audit   cssstyle: [ '3.0.0' ],
575 silly audit   csstype: [ '3.1.3' ],
575 silly audit   'data-uri-to-buffer': [ '4.0.1' ],
575 silly audit   'data-urls': [ '4.0.0' ],
575 silly audit   tr46: [ '4.1.1', '0.0.3' ],
575 silly audit   'webidl-conversions': [ '7.0.0', '3.0.1' ],
575 silly audit   'whatwg-url': [ '12.0.1', '5.0.0' ],
575 silly audit   'decimal.js': [ '10.5.0' ],
575 silly audit   'decompress-response': [ '4.2.1' ],
575 silly audit   'deep-eql': [ '4.1.4' ],
575 silly audit   'deep-equal': [ '2.2.3' ],
575 silly audit   deepmerge: [ '4.3.1' ],
575 silly audit   defaults: [ '1.0.4' ],
575 silly audit   'define-data-property': [ '1.1.4' ],
575 silly audit   'define-properties': [ '1.2.1' ],
575 silly audit   delay: [ '5.0.0' ],
575 silly audit   'delayed-stream': [ '1.0.0' ],
575 silly audit   delegates: [ '1.0.0' ],
575 silly audit   depd: [ '2.0.0' ],
575 silly audit   destroy: [ '1.2.0' ],
575 silly audit   'detect-libc': [ '2.0.4' ],
575 silly audit   'detect-node-es': [ '1.1.0' ],
575 silly audit   'diff-sequences': [ '29.6.3' ],
575 silly audit   'dom-serializer': [ '2.0.0' ],
575 silly audit   domelementtype: [ '2.3.0' ],
575 silly audit   domexception: [ '4.0.0' ],
575 silly audit   domhandler: [ '5.0.3' ],
575 silly audit   domutils: [ '3.2.2' ],
575 silly audit   'dunder-proto': [ '1.0.1' ],
575 silly audit   'ecdsa-sig-formatter': [ '1.0.11' ],
575 silly audit   picocolors: [ '1.1.1' ],
575 silly audit   'ee-first': [ '1.1.1' ],
575 silly audit   'electron-to-chromium': [ '1.5.161' ],
575 silly audit   elliptic: [ '6.6.1' ],
575 silly audit   encodeurl: [ '2.0.0', '1.0.2' ],
575 silly audit   once: [ '1.4.0' ],
575 silly audit   entities: [ '4.5.0', '6.0.0' ],
575 silly audit   'error-ex': [ '1.3.2' ],
575 silly audit   'es-define-property': [ '1.0.1' ],
575 silly audit   'es-errors': [ '1.3.0' ],
575 silly audit   'es-get-iterator': [ '1.1.3' ],
575 silly audit   'es-object-atoms': [ '1.1.1' ],
575 silly audit   'es-set-tostringtag': [ '2.1.0' ],
575 silly audit   esbuild: [ '0.21.5', '0.18.20' ],
575 silly audit   escalade: [ '3.2.0' ],
575 silly audit   'escape-html': [ '1.0.3' ],
575 silly audit   'escape-string-regexp': [ '4.0.0', '1.0.5' ],
575 silly audit   escodegen: [ '2.1.0' ],
575 silly audit   'source-map': [ '0.6.1', '0.5.7' ],
575 silly audit   esprima: [ '4.0.1' ],
575 silly audit   estraverse: [ '5.3.0' ],
575 silly audit   esutils: [ '2.0.3' ],
575 silly audit   etag: [ '1.8.1' ],
575 silly audit   'event-target-shim': [ '5.0.1' ],
575 silly audit   events: [ '3.3.0' ],
575 silly audit   execa: [ '8.0.1' ],
575 silly audit   express: [ '4.21.2', '4.18.1' ],
575 silly audit   'express-handlebars': [ '6.0.3' ],
575 silly audit   'express-rate-limit': [ '7.5.0' ],
575 silly audit   'external-editor': [ '3.1.0' ],
575 silly audit   'fast-deep-equal': [ '2.0.1' ],
575 silly audit   'fast-glob': [ '3.3.3' ],
575 silly audit   fastq: [ '1.19.1' ],
575 silly audit   'fetch-blob': [ '3.2.0' ],
575 silly audit   fflate: [ '0.8.2' ],
575 silly audit   figures: [ '3.2.0' ],
575 silly audit   'fill-range': [ '7.1.1' ],
575 silly audit   finalhandler: [ '1.3.1', '1.2.0' ],
575 silly audit   'find-root': [ '1.1.0' ],
575 silly audit   flatted: [ '3.3.3' ],
575 silly audit   'focus-lock': [ '1.3.6' ],
575 silly audit   'follow-redirects': [ '1.15.9' ],
575 silly audit   'for-each': [ '0.3.5' ],
575 silly audit   'form-data': [ '4.0.2' ],
575 silly audit   'form-data-encoder': [ '1.7.2' ],
575 silly audit   'formdata-node': [ '4.4.1' ],
575 silly audit   'web-streams-polyfill': [ '4.0.0-beta.3', '3.3.3' ],
575 silly audit   'formdata-polyfill': [ '4.0.10' ],
575 silly audit   forwarded: [ '0.2.0' ],
575 silly audit   'framer-motion': [ '10.18.0' ],
575 silly audit   framesync: [ '6.1.2' ],
575 silly audit   tslib: [ '2.4.0', '2.8.1' ],
575 silly audit   fresh: [ '0.5.2' ],
575 silly audit   'fs-minipass': [ '2.1.0' ],
575 silly audit   'fs-monkey': [ '1.0.6' ],
575 silly audit   'fs.realpath': [ '1.0.0' ],
575 silly audit   fsevents: [ '2.3.3' ],
575 silly audit   'function-bind': [ '1.1.2' ],
575 silly audit   'functions-have-names': [ '1.2.3' ],
575 silly audit   gauge: [ '3.0.2' ],
575 silly audit   gensync: [ '1.0.0-beta.2' ],
575 silly audit   'get-caller-file': [ '2.0.5' ],
575 silly audit   'get-func-name': [ '2.0.2' ],
575 silly audit   'get-intrinsic': [ '1.3.0' ],
575 silly audit   'get-nonce': [ '1.0.1' ],
575 silly audit   'get-proto': [ '1.0.1' ],
575 silly audit   'get-stream': [ '8.0.1' ],
575 silly audit   'glob-parent': [ '5.1.2' ],
575 silly audit   globals: [ '11.12.0' ],
575 silly audit   gopd: [ '1.2.0' ],
575 silly audit   'graceful-fs': [ '4.2.11' ],
575 silly audit   graphql: [ '16.11.0' ],
575 silly audit   handlebars: [ '4.7.8' ],
575 silly audit   'has-bigints': [ '1.1.0' ],
575 silly audit   'has-flag': [ '4.0.0' ],
575 silly audit   'has-property-descriptors': [ '1.0.2' ],
575 silly audit   'has-symbols': [ '1.1.0' ],
575 silly audit   'has-tostringtag': [ '1.0.2' ],
575 silly audit   'has-unicode': [ '2.0.1' ],
575 silly audit   'hash-base': [ '3.1.0' ],
575 silly audit   'hash.js': [ '1.1.7' ],
575 silly audit   hasown: [ '2.0.2' ],
575 silly audit   'headers-polyfill': [ '3.2.5' ],
575 silly audit   helmet: [ '6.2.0' ],
575 silly audit   'hmac-drbg': [ '1.0.1' ],
575 silly audit   'hoist-non-react-statics': [ '3.3.2' ],
575 silly audit   'html-encoding-sniffer': [ '3.0.0' ],
575 silly audit   'html-to-text': [ '9.0.5' ],
575 silly audit   htmlparser2: [ '8.0.2' ],
575 silly audit   'http-errors': [ '2.0.0' ],
575 silly audit   'http-proxy-agent': [ '5.0.0' ],
575 silly audit   'human-signals': [ '5.0.0' ],
575 silly audit   'humanize-ms': [ '1.2.1' ],
575 silly audit   'iconv-lite': [ '0.4.24', '0.6.3' ],
575 silly audit   ieee754: [ '1.2.1' ],
575 silly audit   'import-fresh': [ '3.3.1' ],
575 silly audit   'resolve-from': [ '4.0.0' ],
575 silly audit   'indent-string': [ '4.0.0' ],
575 silly audit   inflight: [ '1.0.6' ],
575 silly audit   inherits: [ '2.0.4' ],
575 silly audit   'internal-slot': [ '1.1.0' ],
575 silly audit   'ip-address': [ '9.0.5' ],
575 silly audit   'sprintf-js': [ '1.1.3' ],
575 silly audit   'ipaddr.js': [ '1.9.1' ],
575 silly audit   'is-arguments': [ '1.2.0' ],
575 silly audit   'is-array-buffer': [ '3.0.5' ],
575 silly audit   'is-arrayish': [ '0.2.1' ],
575 silly audit   'is-bigint': [ '1.1.0' ],
575 silly audit   'is-binary-path': [ '2.1.0' ],
575 silly audit   'is-boolean-object': [ '1.2.2' ],
575 silly audit   'is-callable': [ '1.2.7' ],
575 silly audit   'is-core-module': [ '2.16.1' ],
575 silly audit   'is-date-object': [ '1.1.0' ],
575 silly audit   'is-extglob': [ '2.1.1' ],
575 silly audit   'is-fullwidth-code-point': [ '3.0.0' ],
575 silly audit   'is-generator-function': [ '1.1.0' ],
575 silly audit   'is-glob': [ '4.0.3' ],
575 silly audit   'is-interactive': [ '1.0.0' ],
575 silly audit   'is-map': [ '2.0.3' ],
575 silly audit   'is-node-process': [ '1.2.0' ],
575 silly audit   'is-number': [ '7.0.0' ],
575 silly audit   'is-number-object': [ '1.1.1' ],
575 silly audit   'is-potential-custom-element-name': [ '1.0.1' ],
575 silly audit   'is-regex': [ '1.2.1' ],
575 silly audit   'is-set': [ '2.0.3' ],
575 silly audit   'is-shared-array-buffer': [ '1.0.4' ],
575 silly audit   'is-stream': [ '3.0.0' ],
575 silly audit   'is-string': [ '1.1.1' ],
575 silly audit   'is-symbol': [ '1.1.1' ],
575 silly audit   'is-typed-array': [ '1.1.15' ],
575 silly audit   'is-unicode-supported': [ '0.1.0' ],
575 silly audit   'is-weakmap': [ '2.0.2' ],
575 silly audit   'is-weakset': [ '2.0.4' ],
575 silly audit   'is-what': [ '4.1.16' ],
575 silly audit   isarray: [ '2.0.5' ],
575 silly audit   isexe: [ '2.0.0' ],
575 silly audit   'make-dir': [ '3.1.0' ],
575 silly audit   'supports-color': [ '7.2.0' ],
575 silly audit   'js-levenshtein': [ '1.1.6' ],
575 silly audit   'js-tokens': [ '4.0.0', '9.0.1' ],
575 silly audit   jsbn: [ '1.1.0' ],
575 silly audit   jsdom: [ '21.1.2' ],
575 silly audit   jsesc: [ '3.1.0' ],
575 silly audit   'json-parse-even-better-errors': [ '2.3.1' ],
575 silly audit   json5: [ '2.2.3' ],
575 silly audit   jsonwebtoken: [ '8.5.1' ],
575 silly audit   jwa: [ '1.4.2' ],
575 silly audit   jws: [ '3.2.2' ],
575 silly audit   leac: [ '0.6.0' ],
575 silly audit   'lightning-backends': [ '1.5.1' ],
575 silly audit   'node-addon-api': [ '2.0.2', '5.1.0' ],
575 silly audit   secp256k1: [ '4.0.3', '4.0.4' ],
575 silly audit   'lines-and-columns': [ '1.2.4' ],
575 silly audit   lnurl: [ '0.24.2' ],
575 silly audit   'lnurl-offline': [ '1.1.1' ],
575 silly audit   'merge-descriptors': [ '1.0.1', '1.0.3' ],
575 silly audit   qs: [ '6.10.3', '6.13.0' ],
575 silly audit   'raw-body': [ '2.5.1', '2.5.2' ],
575 silly audit   send: [ '0.18.0', '0.19.0' ],
575 silly audit   'serve-static': [ '1.15.0', '1.16.2' ],
575 silly audit   'local-pkg': [ '0.5.1' ],
575 silly audit   lodash: [ '4.17.21' ],
575 silly audit   'lodash.debounce': [ '4.0.8' ],
575 silly audit   'lodash.includes': [ '4.3.0' ],
575 silly audit   'lodash.isboolean': [ '3.0.3' ],
575 silly audit   'lodash.isinteger': [ '4.0.4' ],
575 silly audit   'lodash.isnumber': [ '3.0.3' ],
575 silly audit   'lodash.isplainobject': [ '4.0.6' ],
575 silly audit   'lodash.isstring': [ '4.0.1' ],
575 silly audit   'lodash.merge': [ '4.6.2' ],
575 silly audit   'lodash.mergewith': [ '4.6.2' ],
575 silly audit   'lodash.once': [ '4.1.1' ],
575 silly audit   'log-symbols': [ '4.1.0' ],
575 silly audit   'loose-envify': [ '1.4.0' ],
575 silly audit   loupe: [ '2.3.7' ],
575 silly audit   lucia: [ '3.2.2' ],
575 silly audit   luxon: [ '3.6.1' ],
575 silly audit   'lz-string': [ '1.5.0' ],
575 silly audit   'magic-string': [ '0.30.17' ],
575 silly audit   'math-intrinsics': [ '1.1.0' ],
575 silly audit   'md5.js': [ '1.3.5' ],
575 silly audit   'media-typer': [ '0.3.0' ],
575 silly audit   memfs: [ '3.5.3' ],
575 silly audit   'memfs-browser': [ '3.5.10302' ],
575 silly audit   'merge-stream': [ '2.0.0' ],
575 silly audit   merge2: [ '1.4.1' ],
575 silly audit   methods: [ '1.1.2' ],
575 silly audit   setprototypeof: [ '1.2.0' ],
575 silly audit   statuses: [ '2.0.1' ],
575 silly audit   toidentifier: [ '1.0.1' ],
575 silly audit   micromatch: [ '4.0.8' ],
575 silly audit   mime: [ '1.6.0' ],
575 silly audit   'mime-db': [ '1.52.0' ],
575 silly audit   'mime-types': [ '2.1.35' ],
575 silly audit   'mimic-fn': [ '2.1.0', '4.0.0' ],
575 silly audit   'mimic-response': [ '2.1.0' ],
575 silly audit   'min-indent': [ '1.0.1' ],
575 silly audit   'minimalistic-assert': [ '1.0.1' ],
575 silly audit   'minimalistic-crypto-utils': [ '1.0.1' ],
575 silly audit   minimist: [ '1.2.8' ],
575 silly audit   mitt: [ '3.0.0' ],
575 silly audit   mlly: [ '1.7.4' ],
575 silly audit   pathe: [ '2.0.3', '1.1.2' ],
575 silly audit   mrmime: [ '2.0.1' ],
575 silly audit   msw: [ '1.3.5' ],
575 silly audit   readdirp: [ '3.6.0' ],
575 silly audit   'type-fest': [ '2.19.0', '0.20.2', '0.21.3' ],
575 silly audit   nan: [ '2.22.2' ],
575 silly audit   nanoid: [ '3.3.11' ],
575 silly audit   negotiator: [ '0.6.3' ],
575 silly audit   'neo-async': [ '2.6.2' ],
575 silly audit   'node-domexception': [ '1.0.0' ],
575 silly audit   'node-gyp-build': [ '4.8.4' ],
575 silly audit   'node-releases': [ '2.0.19' ],
575 silly audit   nodemailer: [ '6.10.1' ],
575 silly audit   'normalize-path': [ '3.0.0' ],
575 silly audit   'npm-run-path': [ '5.3.0' ],
575 silly audit   npmlog: [ '5.0.1' ],
575 silly audit   nwsapi: [ '2.2.20' ],
575 silly audit   'object-assign': [ '4.1.1' ],
575 silly audit   'object-inspect': [ '1.13.4' ],
575 silly audit   'object-is': [ '1.1.6' ],
575 silly audit   'object-keys': [ '1.1.1' ],
575 silly audit   'object.assign': [ '4.1.7' ],
575 silly audit   obuf: [ '1.1.2' ],
575 silly audit   'on-finished': [ '2.4.1' ],
575 silly audit   onetime: [ '5.1.2', '6.0.0' ],
575 silly audit   openai: [ '4.104.0' ],
575 silly audit   ora: [ '5.4.1' ],
575 silly audit   'os-tmpdir': [ '1.0.2' ],
575 silly audit   outvariant: [ '1.4.3' ],
575 silly audit   'p-map': [ '4.0.0' ],
575 silly audit   'parent-module': [ '1.0.1' ],
575 silly audit   'parse-json': [ '5.2.0' ],
575 silly audit   parse5: [ '7.3.0' ],
575 silly audit   parseley: [ '0.12.1' ],
575 silly audit   parseurl: [ '1.3.3' ],
575 silly audit   'path-is-absolute': [ '1.0.1' ],
575 silly audit   'path-key': [ '3.1.1', '4.0.0' ],
575 silly audit   'path-parse': [ '1.0.7' ],
575 silly audit   'path-type': [ '4.0.0' ],
575 silly audit   'path2d-polyfill': [ '2.0.1' ],
575 silly audit   pathval: [ '1.1.1' ],
575 silly audit   'pdfjs-dist': [ '3.3.122' ],
575 silly audit   peberminta: [ '0.9.0' ],
575 silly audit   pg: [ '8.16.0' ],
575 silly audit   'pg-boss': [ '8.4.2' ],
575 silly audit   uuid: [ '9.0.1', '11.1.0' ],
575 silly audit   'pg-cloudflare': [ '1.2.5' ],
575 silly audit   'pg-connection-string': [ '2.9.0' ],
575 silly audit   'pg-int8': [ '1.0.1' ],
575 silly audit   'pg-numeric': [ '1.0.2' ],
575 silly audit   'pg-pool': [ '3.10.0' ],
575 silly audit   'pg-protocol': [ '1.10.0' ],
575 silly audit   'pg-types': [ '4.0.2', '2.2.0' ],
575 silly audit   'postgres-array': [ '2.0.0', '3.0.4' ],
575 silly audit   'postgres-bytea': [ '1.0.0', '3.0.0' ],
575 silly audit   'postgres-date': [ '1.0.7', '2.1.0' ],
575 silly audit   'postgres-interval': [ '1.2.0', '3.0.0' ],
575 silly audit   pgpass: [ '1.0.5' ],
575 silly audit   'pkg-types': [ '1.3.1' ],
575 silly audit   'possible-typed-array-names': [ '1.1.0' ],
575 silly audit   postcss: [ '8.5.4' ],
575 silly audit   'postgres-range': [ '1.1.4' ],
575 silly audit   prettier: [ '3.5.3' ],
575 silly audit   prisma: [ '5.19.1' ],
575 silly audit   'prop-types': [ '15.8.1' ],
575 silly audit   'proxy-addr': [ '2.0.7' ],
575 silly audit   'proxy-from-env': [ '1.1.0' ],
575 silly audit   psl: [ '1.15.0' ],
575 silly audit   punycode: [ '2.3.1' ],
575 silly audit   'qrcode.react': [ '3.1.0' ],
575 silly audit   querystringify: [ '2.2.0' ],
575 silly audit   'queue-microtask': [ '1.2.3' ],
575 silly audit   'range-parser': [ '1.2.1' ],
575 silly audit   react: [ '18.3.1' ],
575 silly audit   'react-chartjs-2': [ '5.3.0' ],
575 silly audit   'react-clientside-effect': [ '1.2.8' ],
575 silly audit   'react-dom': [ '18.3.1' ],
575 silly audit   'react-fast-compare': [ '3.2.1' ],
575 silly audit   'react-focus-lock': [ '2.13.6' ],
575 silly audit   'react-hook-form': [ '7.56.4' ],
575 silly audit   'react-icons': [ '4.8.0' ],
575 silly audit   'react-promise-suspense': [ '0.3.4' ],
575 silly audit   'react-refresh': [ '0.17.0' ],
575 silly audit   'react-remove-scroll': [ '2.7.0' ],
575 silly audit   'react-remove-scroll-bar': [ '2.3.8' ],
575 silly audit   'react-router': [ '6.30.1' ],
575 silly audit   'react-router-dom': [ '6.30.1' ],
575 silly audit   'react-style-singleton': [ '2.2.3' ],
575 silly audit   'readable-stream': [ '3.6.2' ],
575 silly audit   redent: [ '3.0.0' ],
575 silly audit   'regexp.prototype.flags': [ '1.5.4' ],
575 silly audit   'require-directory': [ '2.1.1' ],
575 silly audit   'requires-port': [ '1.0.0' ],
575 silly audit   resend: [ '4.5.1' ],
575 silly audit   resolve: [ '1.22.10' ],
575 silly audit   'restore-cursor': [ '3.1.0' ],
575 silly audit   reusify: [ '1.1.0' ],
575 silly audit   rimraf: [ '3.0.2' ],
575 silly audit   ripemd160: [ '2.0.2' ],
575 silly audit   rollup: [ '4.41.1', '3.29.5' ],
575 silly audit   'rrweb-cssom': [ '0.6.0' ],
575 silly audit   'run-parallel': [ '1.2.0' ],
575 silly audit   rxjs: [ '7.8.2' ],
575 silly audit   'safe-buffer': [ '5.2.1' ],
575 silly audit   'safe-regex-test': [ '1.1.0' ],
575 silly audit   'safer-buffer': [ '2.1.2' ],
575 silly audit   saxes: [ '6.0.0' ],
575 silly audit   scheduler: [ '0.23.2' ],
575 silly audit   selderee: [ '0.11.0' ],
575 silly audit   'serialize-error': [ '8.1.0' ],
575 silly audit   'set-blocking': [ '2.0.0' ],
575 silly audit   'set-cookie-parser': [ '2.7.1' ],
575 silly audit   'set-function-length': [ '1.2.2' ],
575 silly audit   'set-function-name': [ '2.0.2' ],
575 silly audit   'sha.js': [ '2.4.11' ],
575 silly audit   'shebang-command': [ '2.0.0' ],
575 silly audit   'shebang-regex': [ '3.0.0' ],
575 silly audit   'side-channel': [ '1.1.0' ],
575 silly audit   'side-channel-list': [ '1.0.0' ],
575 silly audit   'side-channel-map': [ '1.0.1' ],
575 silly audit   'side-channel-weakmap': [ '1.0.2' ],
575 silly audit   siginfo: [ '2.0.0' ],
575 silly audit   'simple-concat': [ '1.0.1' ],
575 silly audit   'simple-get': [ '3.1.1' ],
575 silly audit   sirv: [ '2.0.4' ],
575 silly audit   'smart-buffer': [ '4.2.0' ],
575 silly audit   socks: [ '2.8.4' ],
575 silly audit   'socks-proxy-agent': [ '6.2.0' ],
575 silly audit   'source-map-js': [ '1.2.1' ],
575 silly audit   split2: [ '4.2.0' ],
575 silly audit   stackback: [ '0.0.2' ],
575 silly audit   'std-env': [ '3.9.0' ],
575 silly audit   'stop-iteration-iterator': [ '1.1.0' ],
575 silly audit   string_decoder: [ '1.3.0' ],
575 silly audit   'strip-final-newline': [ '3.0.0' ],
575 silly audit   'strip-indent': [ '3.0.0' ],
575 silly audit   'strip-literal': [ '2.1.1' ],
575 silly audit   stripe: [ '13.3.0' ],
575 silly audit   stylis: [ '4.2.0' ],
575 silly audit   superjson: [ '2.2.2' ],
575 silly audit   'supports-preserve-symlinks-flag': [ '1.0.0' ],
575 silly audit   'symbol-tree': [ '3.2.4' ],
575 silly audit   through: [ '2.3.8' ],
575 silly audit   'tiny-invariant': [ '1.3.3' ],
575 silly audit   tinybench: [ '2.9.0' ],
575 silly audit   tinypool: [ '0.8.4' ],
575 silly audit   tinyspy: [ '2.2.1' ],
575 silly audit   tmp: [ '0.0.33' ],
575 silly audit   'to-regex-range': [ '5.0.1' ],
575 silly audit   'toggle-selection': [ '1.0.6' ],
575 silly audit   totalist: [ '3.0.1' ],
575 silly audit   'tough-cookie': [ '4.1.4' ],
575 silly audit   universalify: [ '0.2.0' ],
575 silly audit   'type-is': [ '1.6.18' ],
575 silly audit   typeforce: [ '1.18.0' ],
575 silly audit   typescript: [ '5.8.3' ],
575 silly audit   ufo: [ '1.6.1' ],
575 silly audit   'uglify-js': [ '3.19.3' ],
575 silly audit   underscore: [ '1.13.2' ],
575 silly audit   'undici-types': [ '5.26.5' ],
575 silly audit   unpipe: [ '1.0.0' ],
575 silly audit   'update-browserslist-db': [ '1.1.3' ],
575 silly audit   'url-parse': [ '1.5.10' ],
575 silly audit   'use-callback-ref': [ '1.3.3' ],
575 silly audit   'use-sidecar': [ '1.1.3' ],
575 silly audit   'use-sync-external-store': [ '1.5.0' ],
575 silly audit   util: [ '0.12.5' ],
575 silly audit   'util-deprecate': [ '1.0.2' ],
575 silly audit   'utils-merge': [ '1.0.1' ],
575 silly audit   'varuint-bitcoin': [ '1.1.2' ],
575 silly audit   vary: [ '1.1.2' ],
575 silly audit   vite: [ '4.5.14', '5.4.19' ],
575 silly audit   'vite-node': [ '1.6.1' ],
575 silly audit   vitest: [ '1.6.1' ],
575 silly audit   'w3c-xmlserializer': [ '4.0.0' ],
575 silly audit   wcwidth: [ '1.0.1' ],
575 silly audit   'web-encoding': [ '1.1.5' ],
575 silly audit   'whatwg-encoding': [ '2.0.0' ],
575 silly audit   'whatwg-mimetype': [ '3.0.0' ],
575 silly audit   which: [ '2.0.2' ],
575 silly audit   'which-boxed-primitive': [ '1.1.1' ],
575 silly audit   'which-collection': [ '1.0.2' ],
575 silly audit   'which-typed-array': [ '1.1.19' ],
575 silly audit   'why-is-node-running': [ '2.3.0' ],
575 silly audit   'wide-align': [ '1.1.5' ],
575 silly audit   wordwrap: [ '1.0.0' ],
575 silly audit   wrappy: [ '1.0.2' ],
575 silly audit   ws: [ '8.18.2' ],
575 silly audit   'xml-name-validator': [ '4.0.0' ],
575 silly audit   xmlchars: [ '2.2.0' ],
575 silly audit   xtend: [ '4.0.2' ],
575 silly audit   y18n: [ '5.0.8' ],
575 silly audit   yaml: [ '1.10.2' ],
575 silly audit   yargs: [ '17.7.2' ],
575 silly audit   'yargs-parser': [ '21.1.1' ],
575 silly audit   zod: [ '3.25.43' ]
575 silly audit }
576 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-win32-x64-msvc
577 silly reify mark deleted [
577 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-win32-x64-msvc'
577 silly reify ]
578 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-win32-ia32-msvc
579 silly reify mark deleted [
579 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-win32-ia32-msvc'
579 silly reify ]
580 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-win32-arm64-msvc
581 silly reify mark deleted [
581 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-win32-arm64-msvc'
581 silly reify ]
582 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-x64-musl
583 silly reify mark deleted [
583 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-x64-musl'
583 silly reify ]
584 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-x64-gnu
585 silly reify mark deleted [
585 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-x64-gnu'
585 silly reify ]
586 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-s390x-gnu
587 silly reify mark deleted [
587 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-s390x-gnu'
587 silly reify ]
588 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-riscv64-musl
589 silly reify mark deleted [
589 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-riscv64-musl'
589 silly reify ]
590 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-riscv64-gnu
591 silly reify mark deleted [
591 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-riscv64-gnu'
591 silly reify ]
592 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-powerpc64le-gnu
593 silly reify mark deleted [
593 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-powerpc64le-gnu'
593 silly reify ]
594 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-loongarch64-gnu
595 silly reify mark deleted [
595 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-loongarch64-gnu'
595 silly reify ]
596 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-arm64-musl
597 silly reify mark deleted [
597 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-arm64-musl'
597 silly reify ]
598 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-arm64-gnu
599 silly reify mark deleted [
599 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-arm64-gnu'
599 silly reify ]
600 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-arm-musleabihf
601 silly reify mark deleted [
601 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-arm-musleabihf'
601 silly reify ]
602 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-arm-gnueabihf
603 silly reify mark deleted [
603 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-linux-arm-gnueabihf'
603 silly reify ]
604 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-freebsd-x64
605 silly reify mark deleted [
605 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-freebsd-x64'
605 silly reify ]
606 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-freebsd-arm64
607 silly reify mark deleted [
607 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-freebsd-arm64'
607 silly reify ]
608 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-darwin-arm64
609 silly reify mark deleted [
609 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-darwin-arm64'
609 silly reify ]
610 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-android-arm64
611 silly reify mark deleted [
611 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-android-arm64'
611 silly reify ]
612 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-android-arm-eabi
613 silly reify mark deleted [
613 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@rollup/rollup-android-arm-eabi'
613 silly reify ]
614 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-win32-x64-msvc
615 silly reify mark deleted [
615 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-win32-x64-msvc'
615 silly reify ]
616 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-win32-ia32-msvc
617 silly reify mark deleted [
617 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-win32-ia32-msvc'
617 silly reify ]
618 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-win32-arm64-msvc
619 silly reify mark deleted [
619 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-win32-arm64-msvc'
619 silly reify ]
620 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-wasm32-wasi
621 silly reify mark deleted [
621 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-wasm32-wasi'
621 silly reify ]
622 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-wasm32-wasi/node_modules/@emnapi/core
623 silly reify mark deleted [
623 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-wasm32-wasi/node_modules/@emnapi/core'
623 silly reify ]
624 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-wasm32-wasi/node_modules/@emnapi/runtime
625 silly reify mark deleted [
625 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-wasm32-wasi/node_modules/@emnapi/runtime'
625 silly reify ]
626 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-wasm32-wasi/node_modules/@tybys/wasm-util
627 silly reify mark deleted [
627 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-wasm32-wasi/node_modules/@tybys/wasm-util'
627 silly reify ]
628 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-linux-x64-musl
629 silly reify mark deleted [
629 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-linux-x64-musl'
629 silly reify ]
630 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-linux-x64-gnu
631 silly reify mark deleted [
631 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-linux-x64-gnu'
631 silly reify ]
632 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-linux-arm64-musl
633 silly reify mark deleted [
633 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-linux-arm64-musl'
633 silly reify ]
634 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-linux-arm64-gnu
635 silly reify mark deleted [
635 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-linux-arm64-gnu'
635 silly reify ]
636 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-linux-arm-gnueabihf
637 silly reify mark deleted [
637 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-linux-arm-gnueabihf'
637 silly reify ]
638 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-freebsd-x64
639 silly reify mark deleted [
639 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-freebsd-x64'
639 silly reify ]
640 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-darwin-arm64
641 silly reify mark deleted [
641 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-darwin-arm64'
641 silly reify ]
642 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-android-arm64
643 silly reify mark deleted [
643 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-android-arm64'
643 silly reify ]
644 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-android-arm-eabi
645 silly reify mark deleted [
645 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/bcrypt-android-arm-eabi'
645 silly reify ]
646 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-win32-x64-msvc
647 silly reify mark deleted [
647 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-win32-x64-msvc'
647 silly reify ]
648 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-win32-ia32-msvc
649 silly reify mark deleted [
649 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-win32-ia32-msvc'
649 silly reify ]
650 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-win32-arm64-msvc
651 silly reify mark deleted [
651 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-win32-arm64-msvc'
651 silly reify ]
652 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-wasm32-wasi
653 silly reify mark deleted [
653 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-wasm32-wasi'
653 silly reify ]
654 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@napi-rs/wasm-runtime
655 silly reify mark deleted [
655 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@napi-rs/wasm-runtime'
655 silly reify ]
656 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@emnapi/core
657 silly reify mark deleted [
657 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@emnapi/core'
657 silly reify ]
658 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@emnapi/runtime
659 silly reify mark deleted [
659 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@emnapi/runtime'
659 silly reify ]
660 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@tybys/wasm-util
661 silly reify mark deleted [
661 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@tybys/wasm-util'
661 silly reify ]
662 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@emnapi/wasi-threads
663 silly reify mark deleted [
663 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@emnapi/wasi-threads'
663 silly reify ]
664 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-linux-x64-musl
665 silly reify mark deleted [
665 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-linux-x64-musl'
665 silly reify ]
666 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-linux-x64-gnu
667 silly reify mark deleted [
667 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-linux-x64-gnu'
667 silly reify ]
668 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-linux-arm64-musl
669 silly reify mark deleted [
669 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-linux-arm64-musl'
669 silly reify ]
670 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-linux-arm64-gnu
671 silly reify mark deleted [
671 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-linux-arm64-gnu'
671 silly reify ]
672 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-linux-arm-gnueabihf
673 silly reify mark deleted [
673 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-linux-arm-gnueabihf'
673 silly reify ]
674 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-freebsd-x64
675 silly reify mark deleted [
675 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-freebsd-x64'
675 silly reify ]
676 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-darwin-arm64
677 silly reify mark deleted [
677 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-darwin-arm64'
677 silly reify ]
678 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-android-arm64
679 silly reify mark deleted [
679 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-android-arm64'
679 silly reify ]
680 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-android-arm-eabi
681 silly reify mark deleted [
681 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@node-rs/argon2-android-arm-eabi'
681 silly reify ]
682 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/win32-x64
683 silly reify mark deleted [
683 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/win32-x64'
683 silly reify ]
684 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/win32-ia32
685 silly reify mark deleted [
685 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/win32-ia32'
685 silly reify ]
686 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/win32-arm64
687 silly reify mark deleted [
687 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/win32-arm64'
687 silly reify ]
688 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/sunos-x64
689 silly reify mark deleted [
689 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/sunos-x64'
689 silly reify ]
690 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/openbsd-x64
691 silly reify mark deleted [
691 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/openbsd-x64'
691 silly reify ]
692 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/netbsd-x64
693 silly reify mark deleted [
693 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/netbsd-x64'
693 silly reify ]
694 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/linux-x64
695 silly reify mark deleted [
695 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/linux-x64'
695 silly reify ]
696 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/linux-s390x
697 silly reify mark deleted [
697 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/linux-s390x'
697 silly reify ]
698 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/linux-riscv64
699 silly reify mark deleted [
699 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/linux-riscv64'
699 silly reify ]
700 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/linux-ppc64
701 silly reify mark deleted [
701 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/linux-ppc64'
701 silly reify ]
702 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/linux-mips64el
703 silly reify mark deleted [
703 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/linux-mips64el'
703 silly reify ]
704 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/linux-loong64
705 silly reify mark deleted [
705 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/linux-loong64'
705 silly reify ]
706 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/linux-ia32
707 silly reify mark deleted [
707 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/linux-ia32'
707 silly reify ]
708 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/linux-arm64
709 silly reify mark deleted [
709 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/linux-arm64'
709 silly reify ]
710 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/linux-arm
711 silly reify mark deleted [
711 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/linux-arm'
711 silly reify ]
712 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/freebsd-x64
713 silly reify mark deleted [
713 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/freebsd-x64'
713 silly reify ]
714 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/freebsd-arm64
715 silly reify mark deleted [
715 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/freebsd-arm64'
715 silly reify ]
716 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/darwin-arm64
717 silly reify mark deleted [
717 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/darwin-arm64'
717 silly reify ]
718 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/android-x64
719 silly reify mark deleted [
719 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/android-x64'
719 silly reify ]
720 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/android-arm64
721 silly reify mark deleted [
721 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/android-arm64'
721 silly reify ]
722 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/android-arm
723 silly reify mark deleted [
723 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/android-arm'
723 silly reify ]
724 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/aix-ppc64
725 silly reify mark deleted [
725 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/@esbuild/aix-ppc64'
725 silly reify ]
726 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/win32-x64
727 silly reify mark deleted [
727 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/win32-x64'
727 silly reify ]
728 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/win32-ia32
729 silly reify mark deleted [
729 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/win32-ia32'
729 silly reify ]
730 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/win32-arm64
731 silly reify mark deleted [
731 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/win32-arm64'
731 silly reify ]
732 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/sunos-x64
733 silly reify mark deleted [
733 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/sunos-x64'
733 silly reify ]
734 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/openbsd-x64
735 silly reify mark deleted [
735 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/openbsd-x64'
735 silly reify ]
736 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/netbsd-x64
737 silly reify mark deleted [
737 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/netbsd-x64'
737 silly reify ]
738 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/linux-x64
739 silly reify mark deleted [
739 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/linux-x64'
739 silly reify ]
740 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/linux-s390x
741 silly reify mark deleted [
741 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/linux-s390x'
741 silly reify ]
742 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/linux-riscv64
743 silly reify mark deleted [
743 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/linux-riscv64'
743 silly reify ]
744 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/linux-ppc64
745 silly reify mark deleted [
745 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/linux-ppc64'
745 silly reify ]
746 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/linux-mips64el
747 silly reify mark deleted [
747 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/linux-mips64el'
747 silly reify ]
748 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/linux-loong64
749 silly reify mark deleted [
749 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/linux-loong64'
749 silly reify ]
750 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/linux-ia32
751 silly reify mark deleted [
751 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/linux-ia32'
751 silly reify ]
752 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/linux-arm64
753 silly reify mark deleted [
753 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/linux-arm64'
753 silly reify ]
754 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/linux-arm
755 silly reify mark deleted [
755 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/linux-arm'
755 silly reify ]
756 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/freebsd-x64
757 silly reify mark deleted [
757 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/freebsd-x64'
757 silly reify ]
758 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/freebsd-arm64
759 silly reify mark deleted [
759 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/freebsd-arm64'
759 silly reify ]
760 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/darwin-arm64
761 silly reify mark deleted [
761 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/darwin-arm64'
761 silly reify ]
762 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/android-x64
763 silly reify mark deleted [
763 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/android-x64'
763 silly reify ]
764 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/android-arm64
765 silly reify mark deleted [
765 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/android-arm64'
765 silly reify ]
766 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/android-arm
767 silly reify mark deleted [
767 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vitest/node_modules/@esbuild/android-arm'
767 silly reify ]
768 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/win32-x64
769 silly reify mark deleted [
769 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/win32-x64'
769 silly reify ]
770 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/win32-ia32
771 silly reify mark deleted [
771 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/win32-ia32'
771 silly reify ]
772 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/win32-arm64
773 silly reify mark deleted [
773 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/win32-arm64'
773 silly reify ]
774 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/sunos-x64
775 silly reify mark deleted [
775 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/sunos-x64'
775 silly reify ]
776 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/openbsd-x64
777 silly reify mark deleted [
777 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/openbsd-x64'
777 silly reify ]
778 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/netbsd-x64
779 silly reify mark deleted [
779 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/netbsd-x64'
779 silly reify ]
780 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/linux-x64
781 silly reify mark deleted [
781 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/linux-x64'
781 silly reify ]
782 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/linux-s390x
783 silly reify mark deleted [
783 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/linux-s390x'
783 silly reify ]
784 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/linux-riscv64
785 silly reify mark deleted [
785 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/linux-riscv64'
785 silly reify ]
786 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/linux-ppc64
787 silly reify mark deleted [
787 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/linux-ppc64'
787 silly reify ]
788 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/linux-mips64el
789 silly reify mark deleted [
789 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/linux-mips64el'
789 silly reify ]
790 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/linux-loong64
791 silly reify mark deleted [
791 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/linux-loong64'
791 silly reify ]
792 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/linux-ia32
793 silly reify mark deleted [
793 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/linux-ia32'
793 silly reify ]
794 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/linux-arm64
795 silly reify mark deleted [
795 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/linux-arm64'
795 silly reify ]
796 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/linux-arm
797 silly reify mark deleted [
797 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/linux-arm'
797 silly reify ]
798 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/freebsd-x64
799 silly reify mark deleted [
799 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/freebsd-x64'
799 silly reify ]
800 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/freebsd-arm64
801 silly reify mark deleted [
801 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/freebsd-arm64'
801 silly reify ]
802 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/darwin-arm64
803 silly reify mark deleted [
803 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/darwin-arm64'
803 silly reify ]
804 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/android-x64
805 silly reify mark deleted [
805 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/android-x64'
805 silly reify ]
806 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/android-arm64
807 silly reify mark deleted [
807 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/android-arm64'
807 silly reify ]
808 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/android-arm
809 silly reify mark deleted [
809 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/vite-node/node_modules/@esbuild/android-arm'
809 silly reify ]
810 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-win32-x64-msvc
811 silly reify mark deleted [
811 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-win32-x64-msvc'
811 silly reify ]
812 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-win32-ia32-msvc
813 silly reify mark deleted [
813 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-win32-ia32-msvc'
813 silly reify ]
814 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-win32-arm64-msvc
815 silly reify mark deleted [
815 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-win32-arm64-msvc'
815 silly reify ]
816 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-wasm32-wasi
817 silly reify mark deleted [
817 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-wasm32-wasi'
817 silly reify ]
818 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@emnapi/core
819 silly reify mark deleted [
819 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@emnapi/core'
819 silly reify ]
820 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@emnapi/runtime
821 silly reify mark deleted [
821 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@emnapi/runtime'
821 silly reify ]
822 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@tybys/wasm-util
823 silly reify mark deleted [
823 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@tybys/wasm-util'
823 silly reify ]
824 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-linux-x64-musl
825 silly reify mark deleted [
825 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-linux-x64-musl'
825 silly reify ]
826 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-linux-x64-gnu
827 silly reify mark deleted [
827 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-linux-x64-gnu'
827 silly reify ]
828 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-linux-arm64-musl
829 silly reify mark deleted [
829 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-linux-arm64-musl'
829 silly reify ]
830 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-linux-arm64-gnu
831 silly reify mark deleted [
831 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-linux-arm64-gnu'
831 silly reify ]
832 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-linux-arm-gnueabihf
833 silly reify mark deleted [
833 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-linux-arm-gnueabihf'
833 silly reify ]
834 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-freebsd-x64
835 silly reify mark deleted [
835 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-freebsd-x64'
835 silly reify ]
836 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-darwin-arm64
837 silly reify mark deleted [
837 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-darwin-arm64'
837 silly reify ]
838 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-android-arm64
839 silly reify mark deleted [
839 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-android-arm64'
839 silly reify ]
840 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-android-arm-eabi
841 silly reify mark deleted [
841 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/oslo/node_modules/@node-rs/argon2-android-arm-eabi'
841 silly reify ]
842 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-win32-x64-msvc
843 silly reify mark deleted [
843 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-win32-x64-msvc'
843 silly reify ]
844 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-win32-ia32-msvc
845 silly reify mark deleted [
845 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-win32-ia32-msvc'
845 silly reify ]
846 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-win32-arm64-msvc
847 silly reify mark deleted [
847 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-win32-arm64-msvc'
847 silly reify ]
848 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-wasm32-wasi
849 silly reify mark deleted [
849 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-wasm32-wasi'
849 silly reify ]
850 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@emnapi/core
851 silly reify mark deleted [
851 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@emnapi/core'
851 silly reify ]
852 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@emnapi/runtime
853 silly reify mark deleted [
853 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@emnapi/runtime'
853 silly reify ]
854 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@tybys/wasm-util
855 silly reify mark deleted [
855 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@tybys/wasm-util'
855 silly reify ]
856 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-linux-x64-musl
857 silly reify mark deleted [
857 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-linux-x64-musl'
857 silly reify ]
858 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-linux-x64-gnu
859 silly reify mark deleted [
859 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-linux-x64-gnu'
859 silly reify ]
860 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-linux-arm64-musl
861 silly reify mark deleted [
861 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-linux-arm64-musl'
861 silly reify ]
862 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-linux-arm64-gnu
863 silly reify mark deleted [
863 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-linux-arm64-gnu'
863 silly reify ]
864 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-linux-arm-gnueabihf
865 silly reify mark deleted [
865 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-linux-arm-gnueabihf'
865 silly reify ]
866 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-freebsd-x64
867 silly reify mark deleted [
867 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-freebsd-x64'
867 silly reify ]
868 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-darwin-arm64
869 silly reify mark deleted [
869 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-darwin-arm64'
869 silly reify ]
870 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-android-arm64
871 silly reify mark deleted [
871 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-android-arm64'
871 silly reify ]
872 verbose reify failed optional dependency /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-android-arm-eabi
873 silly reify mark deleted [
873 silly reify   '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/node_modules/arctic/node_modules/@node-rs/argon2-android-arm-eabi'
873 silly reify ]
874 http fetch POST 200 https://registry.npmjs.org/-/npm/v1/security/advisories/bulk 466ms
875 silly audit report {
875 silly audit report   'path-to-regexp': [
875 silly audit report     {
875 silly audit report       id: 1101844,
875 silly audit report       url: 'https://github.com/advisories/GHSA-rhx6-c78j-4q9w',
875 silly audit report       title: 'Unpatched `path-to-regexp` ReDoS in 0.1.x',
875 silly audit report       severity: 'high',
875 silly audit report       vulnerable_versions: '<0.1.12',
875 silly audit report       cwe: [Array],
875 silly audit report       cvss: [Object]
875 silly audit report     },
875 silly audit report     {
875 silly audit report       id: 1101850,
875 silly audit report       url: 'https://github.com/advisories/GHSA-9wv6-86v2-598j',
875 silly audit report       title: 'path-to-regexp outputs backtracking regular expressions',
875 silly audit report       severity: 'high',
875 silly audit report       vulnerable_versions: '<0.1.10',
875 silly audit report       cwe: [Array],
875 silly audit report       cvss: [Object]
875 silly audit report     }
875 silly audit report   ],
875 silly audit report   'body-parser': [
875 silly audit report     {
875 silly audit report       id: 1099520,
875 silly audit report       url: 'https://github.com/advisories/GHSA-qwcr-r2fm-qrc7',
875 silly audit report       title: 'body-parser vulnerable to denial of service when url encoding is enabled',
875 silly audit report       severity: 'high',
875 silly audit report       vulnerable_versions: '<1.20.3',
875 silly audit report       cwe: [Array],
875 silly audit report       cvss: [Object]
875 silly audit report     }
875 silly audit report   ],
875 silly audit report   cookie: [
875 silly audit report     {
875 silly audit report       id: 1103907,
875 silly audit report       url: 'https://github.com/advisories/GHSA-pxg6-pf52-xh8x',
875 silly audit report       title: 'cookie accepts cookie name, path, and domain with out of bounds characters',
875 silly audit report       severity: 'low',
875 silly audit report       vulnerable_versions: '<0.7.0',
875 silly audit report       cwe: [Array],
875 silly audit report       cvss: [Object]
875 silly audit report     }
875 silly audit report   ],
875 silly audit report   esbuild: [
875 silly audit report     {
875 silly audit report       id: 1102341,
875 silly audit report       url: 'https://github.com/advisories/GHSA-67mh-4wv8-2f99',
875 silly audit report       title: 'esbuild enables any website to send any requests to the development server and read the response',
875 silly audit report       severity: 'moderate',
875 silly audit report       vulnerable_versions: '<=0.24.2',
875 silly audit report       cwe: [Array],
875 silly audit report       cvss: [Object]
875 silly audit report     }
875 silly audit report   ],
875 silly audit report   express: [
875 silly audit report     {
875 silly audit report       id: 1100530,
875 silly audit report       url: 'https://github.com/advisories/GHSA-qw6h-vgh9-j6wx',
875 silly audit report       title: 'express vulnerable to XSS via response.redirect()',
875 silly audit report       severity: 'low',
875 silly audit report       vulnerable_versions: '<4.20.0',
875 silly audit report       cwe: [Array],
875 silly audit report       cvss: [Object]
875 silly audit report     },
875 silly audit report     {
875 silly audit report       id: 1096820,
875 silly audit report       url: 'https://github.com/advisories/GHSA-rv95-896h-c2vc',
875 silly audit report       title: 'Express.js Open Redirect in malformed URLs',
875 silly audit report       severity: 'moderate',
875 silly audit report       vulnerable_versions: '<4.19.2',
875 silly audit report       cwe: [Array],
875 silly audit report       cvss: [Object]
875 silly audit report     }
875 silly audit report   ],
875 silly audit report   jsonwebtoken: [
875 silly audit report     {
875 silly audit report       id: 1102458,
875 silly audit report       url: 'https://github.com/advisories/GHSA-qwph-4952-7xr6',
875 silly audit report       title: 'jsonwebtoken vulnerable to signature validation bypass due to insecure default algorithm in jwt.verify()',
875 silly audit report       severity: 'moderate',
875 silly audit report       vulnerable_versions: '<9.0.0',
875 silly audit report       cwe: [Array],
875 silly audit report       cvss: [Object]
875 silly audit report     },
875 silly audit report     {
875 silly audit report       id: 1097690,
875 silly audit report       url: 'https://github.com/advisories/GHSA-8cf7-32gw-wr33',
875 silly audit report       title: 'jsonwebtoken unrestricted key type could lead to legacy keys usage ',
875 silly audit report       severity: 'high',
875 silly audit report       vulnerable_versions: '<=8.5.1',
875 silly audit report       cwe: [Array],
875 silly audit report       cvss: [Object]
875 silly audit report     },
875 silly audit report     {
875 silly audit report       id: 1097694,
875 silly audit report       url: 'https://github.com/advisories/GHSA-hjrf-2m68-5959',
875 silly audit report       title: "jsonwebtoken's insecure implementation of key retrieval function could lead to Forgeable Public/Private Tokens from RSA to HMAC",
875 silly audit report       severity: 'moderate',
875 silly audit report       vulnerable_versions: '<=8.5.1',
875 silly audit report       cwe: [Array],
875 silly audit report       cvss: [Object]
875 silly audit report     }
875 silly audit report   ],
875 silly audit report   secp256k1: [
875 silly audit report     {
875 silly audit report       id: 1100205,
875 silly audit report       url: 'https://github.com/advisories/GHSA-584q-6j8j-r5pm',
875 silly audit report       title: 'secp256k1-node allows private key extraction over ECDH',
875 silly audit report       severity: 'high',
875 silly audit report       vulnerable_versions: '>=4.0.0 <4.0.4',
875 silly audit report       cwe: [Array],
875 silly audit report       cvss: [Object]
875 silly audit report     }
875 silly audit report   ],
875 silly audit report   send: [
875 silly audit report     {
875 silly audit report       id: 1100526,
875 silly audit report       url: 'https://github.com/advisories/GHSA-m6fv-jmcg-4jfg',
875 silly audit report       title: 'send vulnerable to template injection that can lead to XSS',
875 silly audit report       severity: 'low',
875 silly audit report       vulnerable_versions: '<0.19.0',
875 silly audit report       cwe: [Array],
875 silly audit report       cvss: [Object]
875 silly audit report     }
875 silly audit report   ],
875 silly audit report   'serve-static': [
875 silly audit report     {
875 silly audit report       id: 1100528,
875 silly audit report       url: 'https://github.com/advisories/GHSA-cm22-4g7w-348p',
875 silly audit report       title: 'serve-static vulnerable to template injection that can lead to XSS',
875 silly audit report       severity: 'low',
875 silly audit report       vulnerable_versions: '<1.16.0',
875 silly audit report       cwe: [Array],
875 silly audit report       cvss: [Object]
875 silly audit report     }
875 silly audit report   ],
875 silly audit report   'pdfjs-dist': [
875 silly audit report     {
875 silly audit report       id: 1104044,
875 silly audit report       url: 'https://github.com/advisories/GHSA-wgrm-67xf-hhpq',
875 silly audit report       title: 'PDF.js vulnerable to arbitrary JavaScript execution upon opening a malicious PDF',
875 silly audit report       severity: 'high',
875 silly audit report       vulnerable_versions: '<=4.1.392',
875 silly audit report       cwe: [Array],
875 silly audit report       cvss: [Object]
875 silly audit report     }
875 silly audit report   ]
875 silly audit report }
876 silly packumentCache corgi:https://registry.npmjs.org/path-to-regexp cache-miss
877 silly packumentCache corgi:https://registry.npmjs.org/body-parser cache-miss
878 silly packumentCache corgi:https://registry.npmjs.org/cookie cache-miss
879 silly packumentCache corgi:https://registry.npmjs.org/esbuild cache-miss
880 silly packumentCache corgi:https://registry.npmjs.org/express cache-miss
881 silly packumentCache corgi:https://registry.npmjs.org/jsonwebtoken cache-miss
882 silly packumentCache corgi:https://registry.npmjs.org/secp256k1 cache-miss
883 silly packumentCache corgi:https://registry.npmjs.org/send cache-miss
884 silly packumentCache corgi:https://registry.npmjs.org/serve-static cache-miss
885 silly packumentCache corgi:https://registry.npmjs.org/pdfjs-dist cache-miss
886 http fetch GET 200 https://registry.npmjs.org/body-parser 99ms (cache revalidated)
887 silly packumentCache corgi:https://registry.npmjs.org/body-parser set size:74404 disposed:false
888 http fetch GET 200 https://registry.npmjs.org/cookie 169ms (cache revalidated)
889 silly packumentCache corgi:https://registry.npmjs.org/cookie set size:22175 disposed:false
890 http fetch GET 200 https://registry.npmjs.org/serve-static 171ms (cache revalidated)
891 silly packumentCache corgi:https://registry.npmjs.org/serve-static set size:56254 disposed:false
892 http fetch GET 200 https://registry.npmjs.org/esbuild 176ms (cache revalidated)
893 silly packumentCache corgi:https://registry.npmjs.org/esbuild set size:861077 disposed:false
894 http fetch GET 200 https://registry.npmjs.org/jsonwebtoken 190ms (cache revalidated)
895 silly packumentCache corgi:https://registry.npmjs.org/jsonwebtoken set size:66386 disposed:false
896 http fetch GET 200 https://registry.npmjs.org/path-to-regexp 202ms (cache revalidated)
897 silly packumentCache corgi:https://registry.npmjs.org/path-to-regexp set size:62557 disposed:false
898 http fetch GET 200 https://registry.npmjs.org/send 200ms (cache revalidated)
899 silly packumentCache corgi:https://registry.npmjs.org/send set size:60926 disposed:false
900 http fetch GET 200 https://registry.npmjs.org/pdfjs-dist 202ms (cache revalidated)
901 silly packumentCache corgi:https://registry.npmjs.org/pdfjs-dist set size:829958 disposed:false
902 http fetch GET 200 https://registry.npmjs.org/express 301ms (cache revalidated)
903 silly packumentCache corgi:https://registry.npmjs.org/express set size:322362 disposed:false
904 http fetch GET 200 https://registry.npmjs.org/secp256k1 313ms (cache revalidated)
905 silly packumentCache corgi:https://registry.npmjs.org/secp256k1 set size:65099 disposed:false
906 silly packumentCache corgi:https://registry.npmjs.org/msw cache-miss
907 http fetch GET 200 https://registry.npmjs.org/msw 53ms (cache revalidated)
908 silly packumentCache corgi:https://registry.npmjs.org/msw set size:804836 disposed:false
909 silly packumentCache corgi:https://registry.npmjs.org/vite cache-miss
910 http fetch GET 200 https://registry.npmjs.org/vite 1365ms (cache revalidated)
911 silly packumentCache corgi:https://registry.npmjs.org/vite set size:2007743 disposed:false
912 silly packumentCache corgi:https://registry.npmjs.org/lnurl cache-miss
913 http fetch GET 200 https://registry.npmjs.org/lnurl 1867ms (cache revalidated)
914 silly packumentCache corgi:https://registry.npmjs.org/lnurl set size:78747 disposed:false
915 silly packumentCache corgi:https://registry.npmjs.org/lightning-backends cache-miss
916 silly packumentCache corgi:https://registry.npmjs.org/bolt11 cache-miss
917 http fetch GET 200 https://registry.npmjs.org/lightning-backends 381ms (cache revalidated)
918 silly packumentCache corgi:https://registry.npmjs.org/lightning-backends set size:22803 disposed:false
919 http fetch GET 200 https://registry.npmjs.org/bolt11 381ms (cache revalidated)
920 silly packumentCache corgi:https://registry.npmjs.org/bolt11 set size:27850 disposed:false
921 silly packumentCache corgi:https://registry.npmjs.org/@vitejs%2fplugin-react cache-miss
922 silly packumentCache corgi:https://registry.npmjs.org/vite-node cache-miss
923 silly packumentCache corgi:https://registry.npmjs.org/vitest cache-miss
924 http fetch GET 200 https://registry.npmjs.org/vitest 48ms (cache revalidated)
925 silly packumentCache corgi:https://registry.npmjs.org/vitest set size:1077813 disposed:false
926 http fetch GET 200 https://registry.npmjs.org/vite-node 73ms (cache revalidated)
927 silly packumentCache corgi:https://registry.npmjs.org/vite-node set size:420161 disposed:false
928 http fetch GET 200 https://registry.npmjs.org/@vitejs%2fplugin-react 83ms (cache revalidated)
929 silly packumentCache corgi:https://registry.npmjs.org/@vitejs%2fplugin-react set size:88828 disposed:false
930 silly packumentCache corgi:https://registry.npmjs.org/@vitest%2fui cache-miss
931 http fetch GET 200 https://registry.npmjs.org/@vitest%2fui 139ms (cache revalidated)
932 silly packumentCache corgi:https://registry.npmjs.org/@vitest%2fui set size:582691 disposed:false
933 silly ADD .wasp/out/sdk/wasp/node_modules/dom-accessibility-api
934 silly ADD .wasp/out/sdk/wasp/node_modules/aria-query
935 silly ADD .wasp/out/sdk/wasp/node_modules/@testing-library/react
936 silly ADD .wasp/out/sdk/wasp/node_modules/@testing-library/jest-dom
937 silly ADD .wasp/out/sdk/wasp/node_modules/@testing-library/dom
938 silly ADD .wasp/out/sdk/wasp/node_modules/@testing-library/dom/node_modules/dom-accessibility-api
939 silly ADD .wasp/out/sdk/wasp/node_modules/@testing-library/dom/node_modules/chalk
940 silly ADD .wasp/out/sdk/wasp/node_modules/@tanstack/react-query
941 silly ADD
942 silly ADD
943 silly ADD
944 silly ADD
945 silly ADD
946 silly ADD
947 silly ADD
948 silly ADD
949 silly ADD
950 silly ADD
951 silly ADD
952 silly ADD
953 silly ADD
954 silly ADD
955 silly ADD
956 silly ADD
957 silly ADD
958 silly ADD
959 silly ADD
960 silly ADD
961 silly ADD
962 silly ADD
963 silly ADD
964 silly ADD
965 silly ADD
966 silly ADD
967 silly ADD
968 silly ADD
969 silly ADD
970 silly ADD
971 silly ADD
972 silly ADD
973 silly ADD
974 silly ADD
975 silly ADD
976 silly ADD
977 silly ADD
978 silly ADD
979 silly ADD
980 silly ADD
981 silly ADD
982 silly ADD
983 silly ADD
984 silly ADD node_modules/@tybys/wasm-util
985 silly ADD node_modules/@emnapi/runtime
986 silly ADD node_modules/@emnapi/core
987 silly ADD
988 silly ADD
989 silly ADD
990 silly ADD
991 silly ADD
992 silly ADD
993 silly ADD
994 silly ADD
995 silly ADD
996 silly ADD
997 silly ADD
998 silly ADD
999 silly ADD
1000 silly ADD
1001 silly ADD
1002 silly ADD
1003 silly ADD
1004 silly ADD
1005 silly ADD
1006 silly ADD
1007 silly ADD
1008 silly ADD
1009 silly ADD
1010 silly ADD
1011 silly ADD
1012 silly ADD
1013 silly ADD
1014 silly ADD
1015 silly ADD
1016 silly ADD
1017 silly ADD
1018 silly ADD
1019 silly ADD
1020 silly ADD
1021 silly ADD
1022 silly ADD
1023 silly ADD
1024 silly ADD
1025 silly ADD
1026 silly ADD
1027 silly ADD
1028 silly ADD
1029 silly ADD
1030 silly ADD
1031 silly ADD
1032 silly ADD
1033 silly ADD
1034 silly ADD
1035 silly ADD
1036 silly ADD
1037 silly ADD
1038 silly ADD
1039 silly ADD
1040 silly ADD
1041 silly ADD
1042 silly ADD
1043 silly ADD
1044 silly ADD
1045 silly ADD
1046 silly ADD
1047 silly ADD
1048 silly ADD
1049 silly ADD
1050 silly ADD
1051 silly ADD
1052 silly ADD
1053 silly ADD
1054 silly ADD
1055 silly ADD
1056 silly ADD
1057 silly ADD
1058 silly ADD
1059 silly ADD
1060 silly ADD
1061 silly ADD
1062 silly ADD
1063 silly ADD
1064 silly ADD
1065 silly ADD
1066 silly ADD
1067 silly ADD
1068 silly ADD
1069 silly ADD
1070 silly ADD
1071 silly ADD
1072 silly ADD
1073 silly ADD
1074 silly ADD
1075 silly ADD
1076 silly ADD
1077 silly ADD
1078 silly ADD
1079 silly ADD
1080 silly ADD
1081 silly ADD
1082 silly ADD
1083 silly ADD
1084 silly ADD
1085 silly ADD
1086 silly ADD
1087 silly ADD
1088 silly ADD
1089 silly ADD
1090 silly ADD
1091 silly ADD
1092 silly ADD
1093 silly ADD
1094 silly ADD
1095 silly ADD
1096 silly ADD
1097 silly ADD
1098 silly ADD
1099 silly ADD
1100 silly ADD
1101 silly ADD
1102 silly ADD
1103 silly ADD
1104 silly ADD
1105 silly ADD
1106 silly ADD
1107 silly ADD
1108 silly ADD
1109 verbose cwd /Users/<USER>/CascadeProjects/Coverletter/coverlettergpt
1110 verbose os Darwin 24.3.0
1111 verbose node v20.19.2
1112 verbose npm  v10.8.2
1113 verbose exit 0
1114 info ok
