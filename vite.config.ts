import { defineConfig } from 'vite'
import { splitVendorChunkPlugin } from 'vite'

export default defineConfig({
  server: {
    port: 3000,
    open: true,
    host: true,
  },
  build: {
    target: 'es2020',
    sourcemap: false,
    minify: 'terser',
    cssMinify: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'framer-motion'],
          ui: ['@chakra-ui/react', '@emotion/react', '@emotion/styled'],
          charts: ['chart.js', 'react-chartjs-2'],
          icons: ['react-icons', '@chakra-ui/icons'],
          utils: ['uuid', 'zod', 'react-hook-form']
        },
        assetFileNames: 'assets/[name]-[hash][extname]',
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js'
      }
    },
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug', 'console.warn']
      }
    },
    chunkSizeWarningLimit: 1000
  },
  define: {
    'process.env.REACT_APP_API_URL': `"${process.env.REACT_APP_API_URL || 'https://api.careerdart.com'}"`,
    'process.env.NODE_ENV': '"production"'
  },
  plugins: [
    splitVendorChunkPlugin()
  ],
  esbuild: {
    drop: ['console', 'debugger']
  }
})
