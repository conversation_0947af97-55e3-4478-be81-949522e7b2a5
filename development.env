# Development Environment Variables
# ✅ CONFIGURED ON FLY.IO: careerdart-dev

# === DEVELOPMENT ENVIRONMENT URLS ===
WASP_WEB_CLIENT_URL=https://careerdart-dev.netlify.app
WASP_SERVER_URL=https://careerdart-dev.fly.dev

# Environment
NODE_ENV=development
WASP_ENV=development

# Database (✅ configured)
DATABASE_URL=postgresql://[configured]

# JWT Secret (✅ configured)
JWT_SECRET=[configured]

# Email Configuration (✅ configured with RESEND)
SMTP_HOST=smtp.resend.com
SMTP_PORT=587
SMTP_USERNAME=resend
SMTP_PASSWORD=[uses RESEND_API_KEY]
RESEND_API_KEY=[configured]
EMAIL_FROM=[configured]

# OpenAI API Key (✅ configured)
OPENAI_API_KEY=[configured]

# Google OAuth Configuration (⚠️ NEEDS SETUP)
# You need to create separate OAuth credentials for development
GOOGLE_CLIENT_ID=your-google-client-id-for-dev.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-your-google-client-secret-for-dev

# Stripe Configuration (if using payments - optional for dev)
STRIPE_KEY=sk_test_your-stripe-test-key-here
PRODUCT_PRICE_ID=price_your-test-product-price-id-here
PRODUCT_CREDITS_PRICE_ID=price_your-test-credits-price-id-here

# === STATUS ===
# ✅ Backend: careerdart-dev.fly.dev - RUNNING
# ✅ Environment Variables: CONFIGURED
# ⚠️ OAuth: Needs separate Google OAuth app setup
# 🔄 Frontend: Ready for deployment to careerdart-dev.netlify.app

# Development specific settings
DEBUG=true
ENABLE_LOGGING=true 