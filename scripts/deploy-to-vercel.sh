#!/bin/bash

echo "==================================================================="
echo "CAREERDART VERCEL DEPLOYMENT SCRIPT"
echo "==================================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
print_status "Checking prerequisites..."

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    print_warning "Vercel CLI not found. Installing..."
    npm install -g vercel
fi

# Check if logged into Vercel
if ! vercel whoami &> /dev/null; then
    print_warning "Not logged into Vercel. Please log in..."
    vercel login
fi

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    print_warning ".env.production not found. Creating from template..."
    cp .env.production.example .env.production
    print_error "Please edit .env.production with your actual values before continuing!"
    exit 1
fi

# Make build script executable
chmod +x scripts/build-for-vercel.sh

print_status "Starting deployment process..."

# Option to deploy
echo ""
echo "Choose deployment option:"
echo "1) Deploy to preview (development)"
echo "2) Deploy to production"
echo "3) Just build (no deployment)"
read -p "Enter your choice (1-3): " choice

case $choice in
    1)
        print_status "Deploying to preview environment..."
        vercel
        ;;
    2)
        print_status "Deploying to production..."
        vercel --prod
        ;;
    3)
        print_status "Building application..."
        npm run vercel-build
        print_success "Build completed! You can now deploy manually."
        exit 0
        ;;
    *)
        print_error "Invalid choice. Exiting."
        exit 1
        ;;
esac

if [ $? -eq 0 ]; then
    print_success "Deployment completed successfully!"
    echo ""
    print_status "Next steps:"
    echo "1. Update Google OAuth redirect URIs with your new domain"
    echo "2. Update Stripe webhook URLs"
    echo "3. Test your application"
    echo "4. Set up monitoring and alerts"
else
    print_error "Deployment failed. Check the logs above for details."
    exit 1
fi
