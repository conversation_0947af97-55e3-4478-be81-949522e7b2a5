#!/bin/bash

# Optimized Fly.io Deployment Script for CareerDart
# Deploys the server to Fly.io with proper configuration

set -e  # Exit on any error

echo "🚀 Starting Fly.io deployment for CareerDart Server..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[DEPLOY]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "main.wasp" ]; then
  print_error "main.wasp not found. Please run from project root."
  exit 1
fi

# Check if flyctl is installed
if ! command -v flyctl &> /dev/null; then
    print_status "Installing Fly.io CLI..."
    curl -L https://fly.io/install.sh | sh
    export PATH="$HOME/.fly/bin:$PATH"
    
    if command -v flyctl &> /dev/null; then
        print_success "Fly.io CLI installed successfully"
    else
        print_error "Failed to install Fly.io CLI"
        exit 1
    fi
else
    print_success "Fly.io CLI already available"
fi

# Check if user is logged in
if ! flyctl auth whoami &> /dev/null; then
    print_status "Please log in to Fly.io..."
    flyctl auth login
fi

print_success "Authenticated with Fly.io: $(flyctl auth whoami)"

# Set deployment configuration
APP_NAME="careerdart-server"
REGION="iad"  # US East
BUILD_TARGET="server"

print_status "Deployment configuration:"
echo "  App name: $APP_NAME"
echo "  Region: $REGION"
echo "  Build target: $BUILD_TARGET"

# Check if app exists
if flyctl apps list | grep -q "$APP_NAME"; then
    print_status "App '$APP_NAME' already exists"
    DEPLOY_TYPE="update"
else
    print_status "Creating new app '$APP_NAME'"
    DEPLOY_TYPE="create"
    
    # Create the app
    flyctl apps create "$APP_NAME" --region "$REGION"
    print_success "App '$APP_NAME' created successfully"
fi

# Ensure required secrets are set
print_status "Checking required environment variables..."

REQUIRED_SECRETS=(
    "DATABASE_URL"
    "OPENAI_API_KEY"
    "GOOGLE_CLIENT_ID"
    "GOOGLE_CLIENT_SECRET"
    "SENDGRID_API_KEY"
    "STRIPE_KEY"
    "PRODUCT_PRICE_ID"
    "PRODUCT_CREDITS_PRICE_ID"
)

MISSING_SECRETS=()

for secret in "${REQUIRED_SECRETS[@]}"; do
    if flyctl secrets list --app "$APP_NAME" | grep -q "$secret"; then
        print_success "✓ $secret is configured"
    else
        print_warning "⚠ $secret is missing"
        MISSING_SECRETS+=("$secret")
    fi
done

if [ ${#MISSING_SECRETS[@]} -gt 0 ]; then
    print_warning "Missing secrets detected. Please set them before deployment:"
    for secret in "${MISSING_SECRETS[@]}"; do
        echo "  flyctl secrets set $secret=your_value --app $APP_NAME"
    done
    
    read -p "Do you want to continue with deployment anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Deployment cancelled. Please set required secrets and try again."
        exit 1
    fi
fi

# Clean environment for fresh build
print_status "Cleaning environment for fresh dependency resolution..."
print_status "Removing package-lock.json and cleaning npm cache..."
rm -f package-lock.json
npm cache clean --force

print_status "Cleaning Wasp build directory..."
wasp clean || true

print_success "Environment cleaned successfully"

# Build locally first (recommended for Wasp apps)
print_status "Building application locally with fresh dependencies..."
    
    # Install Wasp if not available
    if ! command -v wasp &> /dev/null; then
        print_status "Installing Wasp CLI..."
        curl -sSL https://get.wasp.sh/installer.sh | sh -s -- -v 0.15.0
        export PATH="$HOME/.local/bin:$PATH"
    fi

# Ensure fresh dependency installation
print_status "Installing fresh dependencies..."
if npm install; then
    print_success "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi
    
    # Build the application
print_status "Building Wasp application..."
if wasp build; then
    print_success "Wasp build completed"
else
    print_error "Wasp build failed"
    exit 1
fi

# Verify build output
if [ ! -d ".wasp/build/server" ]; then
    print_error "Server build not found at .wasp/build/server"
    exit 1
fi

# Create optimized Dockerfile for server deployment
print_status "Creating optimized Dockerfile for server..."
cat > Dockerfile << 'EOF'
# Optimized Dockerfile for CareerDart Server
FROM node:18-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    dumb-init \
    curl \
    tini

# Create app user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S careerdart -u 1001

# Set working directory
WORKDIR /app

# Copy pre-built server from .wasp/build/server
COPY .wasp/build/server ./

# Clean npm cache and ensure fresh dependency resolution for production
RUN rm -f package-lock.json && \
    npm cache clean --force && \
    npm ci --only=production && \
    npm cache clean --force

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080
ENV NODE_OPTIONS="--max-old-space-size=1024"

# Change ownership to app user
RUN chown -R careerdart:nodejs /app

# Switch to app user
USER careerdart

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

# Use tini as init system
ENTRYPOINT ["/sbin/tini", "--"]

# Start the server
CMD ["node", "index.js"]
EOF

print_success "Dockerfile created"

# Create .dockerignore for faster builds
print_status "Creating .dockerignore for optimized builds..."
cat > .dockerignore << 'EOF'
# Source files (we only need the built server)
src/
migrations/
public/
scripts/
docs/
*.md
*.wasp

# Development files
.git/
.vscode/
.env*
!.wasp/build/server

# Dependencies (will be installed in container)
node_modules/
.npm/
npm-debug.log*

# Build artifacts we don't need
.wasp/build/web-app/
.wasp/out/
dist/
build/

# OS generated files
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Temporary files
.tmp/
temp/
EOF

print_success ".dockerignore created"

# Set scaling configuration based on deployment type
if [ "$DEPLOY_TYPE" = "create" ]; then
    print_status "Setting initial scaling configuration..."
    
    # Set machine configuration
    flyctl machine run \
        --app "$APP_NAME" \
        --region "$REGION" \
        --vm-memory 512 \
        --vm-cpus 1 \
        --vm-cpu-kind shared \
        --env NODE_ENV=production \
        --env PORT=8080 \
        --port 8080:80/tcp:tls \
        --port 8080:443/tcp:tls \
        . || print_warning "Machine configuration may need manual adjustment"
fi

# Deploy the application
print_status "Deploying to Fly.io..."
if flyctl deploy \
    --app "$APP_NAME" \
    --region "$REGION" \
    --build-target "$BUILD_TARGET" \
    --strategy immediate \
    --wait-timeout 300; then
    
    print_success "🎉 Deployment completed successfully!"
else
    print_error "Deployment failed"
    exit 1
fi

# Post-deployment checks
print_status "Running post-deployment checks..."

# Check if app is healthy
sleep 30  # Wait for app to start

if flyctl status --app "$APP_NAME" | grep -q "running"; then
    print_success "✓ Application is running"
else
    print_warning "⚠ Application may not be running properly"
fi

# Get app URL
APP_URL="https://${APP_NAME}.fly.dev"
print_status "Application URL: $APP_URL"

# Test health endpoint
if curl -f "$APP_URL/health" &> /dev/null; then
    print_success "✓ Health check passed"
else
    print_warning "⚠ Health check failed - app may still be starting"
fi

# Show logs for debugging
print_status "Recent logs:"
flyctl logs --app "$APP_NAME" --lines 10

# Deployment summary
print_success "🚀 Fly.io deployment completed!"
echo ""
print_status "Deployment Summary:"
echo "  📱 App name: $APP_NAME"
echo "  🌍 Region: $REGION"
echo "  🔗 URL: $APP_URL"
echo "  📊 Status: $(flyctl status --app "$APP_NAME" | grep -o "running\|stopped\|pending" | head -1 || echo "unknown")"
echo ""
print_status "Management commands:"
echo "  flyctl status --app $APP_NAME        # Check app status"
echo "  flyctl logs --app $APP_NAME          # View logs"
echo "  flyctl ssh console --app $APP_NAME   # SSH into container"
echo "  flyctl scale count 2 --app $APP_NAME # Scale to 2 instances"
echo ""

if [ ${#MISSING_SECRETS[@]} -gt 0 ]; then
    print_warning "⚠ Remember to set missing secrets:"
    for secret in "${MISSING_SECRETS[@]}"; do
        echo "  flyctl secrets set $secret=your_value --app $APP_NAME"
    done
fi

print_success "Deployment script completed successfully!" 