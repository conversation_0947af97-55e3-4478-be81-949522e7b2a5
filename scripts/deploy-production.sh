#!/bin/bash

# Quick Production Deployment Script for CareerDart
# This script builds and deploys directly to production (careerdart.com)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Netlify Site IDs
PRODUCTION_SITE_ID="3a38799a-f093-4f55-91d9-fc0fbbbdb372"  # careerdart.com

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo "🚀 Quick Production Deployment to careerdart.com"
echo ""

# Confirmation prompt
read -p "⚠️  This will deploy to PRODUCTION (careerdart.com). Continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Deployment cancelled."
    exit 0
fi

# Check if we're in the right directory
if [[ ! -f "main.wasp" ]]; then
    print_error "main.wasp not found. Please run this script from the project root."
    exit 1
fi

# Check if Netlify CLI is available
if ! command -v netlify &> /dev/null; then
    print_error "Netlify CLI not found. Install with: npm install -g netlify-cli"
    exit 1
fi

# Run the full build and deploy
print_info "Running full build and deployment process..."
./scripts/deploy-to-netlify.sh

# Deploy to production
print_info "Deploying to PRODUCTION (careerdart.com)..."
netlify deploy --dir=netlify-client-build --prod --site=$PRODUCTION_SITE_ID

print_success "🎉 Successfully deployed to PRODUCTION!"
print_info "🌐 Live at: https://careerdart.com"
