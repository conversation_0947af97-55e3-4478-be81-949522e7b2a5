#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const illustrationsDir = path.join(__dirname, '../public/images/illustrations');

// Ensure directory exists
if (!fs.existsSync(illustrationsDir)) {
  fs.mkdirSync(illustrationsDir, { recursive: true });
}

const illustrations = {
  'success.svg': `<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="300" fill="#f8fafc"/>
  <circle cx="200" cy="150" r="60" fill="#10b981" opacity="0.1"/>
  <circle cx="200" cy="150" r="40" fill="#10b981" opacity="0.2"/>
  <circle cx="200" cy="150" r="25" fill="#10b981"/>
  <path d="M 185 150 L 195 160 L 215 140" stroke="white" stroke-width="4" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
  <circle cx="100" cy="80" r="8" fill="#fbbf24" opacity="0.4"/>
  <circle cx="320" cy="90" r="6" fill="#6366f1" opacity="0.4"/>
  <circle cx="350" cy="220" r="10" fill="#ef4444" opacity="0.3"/>
</svg>`,

  'loading.svg': `<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="300" fill="#f8fafc"/>
  <circle cx="200" cy="150" r="30" stroke="#6366f1" stroke-width="4" fill="none" stroke-dasharray="47" stroke-dashoffset="47">
    <animateTransform attributeName="transform" type="rotate" values="0 200 150;360 200 150" dur="1s" repeatCount="indefinite"/>
  </circle>
  <circle cx="200" cy="150" r="15" fill="#6366f1" opacity="0.3"/>
  <text x="200" y="200" font-family="Arial, sans-serif" font-size="14" fill="#6b7280" text-anchor="middle">Loading...</text>
  <circle cx="120" cy="100" r="6" fill="#10b981" opacity="0.4"/>
  <circle cx="300" cy="80" r="8" fill="#fbbf24" opacity="0.4"/>
  <circle cx="80" cy="220" r="10" fill="#ef4444" opacity="0.3"/>
</svg>`,

  'warning.svg': `<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="300" fill="#f8fafc"/>
  <polygon points="200,80 240,200 160,200" fill="#fbbf24" opacity="0.1"/>
  <polygon points="200,90 230,190 170,190" fill="#fbbf24" opacity="0.2"/>
  <polygon points="200,100 220,180 180,180" fill="#fbbf24"/>
  <circle cx="200" cy="140" r="4" fill="white"/>
  <rect x="198" y="150" width="4" height="20" rx="2" fill="white"/>
  <text x="200" y="210" font-family="Arial, sans-serif" font-size="12" fill="#92400e" text-anchor="middle">Warning</text>
  <circle cx="100" cy="70" r="8" fill="#6366f1" opacity="0.3"/>
  <circle cx="320" cy="90" r="6" fill="#10b981" opacity="0.4"/>
  <circle cx="350" cy="230" r="10" fill="#ef4444" opacity="0.3"/>
</svg>`,

  'server-down.svg': `<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="300" fill="#f8fafc"/>
  <rect x="150" y="100" width="100" height="120" rx="8" fill="#374151"/>
  <rect x="160" y="110" width="80" height="15" rx="2" fill="#4b5563"/>
  <rect x="160" y="135" width="80" height="15" rx="2" fill="#4b5563"/>
  <rect x="160" y="160" width="80" height="15" rx="2" fill="#4b5563"/>
  <circle cx="170" cy="190" r="3" fill="#ef4444"/>
  <circle cx="185" cy="190" r="3" fill="#ef4444"/>
  <circle cx="200" cy="190" r="3" fill="#10b981"/>
  <line x1="180" y1="80" x2="220" y2="80" stroke="#ef4444" stroke-width="3" stroke-linecap="round"/>
  <line x1="185" y1="75" x2="215" y2="85" stroke="#ef4444" stroke-width="3" stroke-linecap="round"/>
  <line x1="185" y1="85" x2="215" y2="75" stroke="#ef4444" stroke-width="3" stroke-linecap="round"/>
  <text x="200" y="250" font-family="Arial, sans-serif" font-size="12" fill="#6b7280" text-anchor="middle">Server Offline</text>
  <circle cx="80" cy="120" r="8" fill="#fbbf24" opacity="0.4"/>
  <circle cx="330" cy="100" r="6" fill="#6366f1" opacity="0.4"/>
</svg>`,

  'login.svg': `<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="300" fill="#f8fafc"/>
  <rect x="120" y="80" width="160" height="140" rx="12" fill="white" stroke="#e5e7eb" stroke-width="2"/>
  <circle cx="200" cy="130" r="20" fill="#6366f1"/>
  <rect x="150" y="160" width="100" height="12" rx="6" fill="#e5e7eb"/>
  <rect x="150" y="180" width="100" height="12" rx="6" fill="#e5e7eb"/>
  <rect x="150" y="200" width="100" height="20" rx="10" fill="#6366f1"/>
  <text x="200" y="212" font-family="Arial, sans-serif" font-size="10" fill="white" text-anchor="middle">Sign In</text>
  <rect x="80" y="50" width="20" height="30" rx="10" fill="#10b981" opacity="0.6"/>
  <rect x="300" y="60" width="15" height="25" rx="7" fill="#fbbf24" opacity="0.6"/>
  <circle cx="350" cy="200" r="10" fill="#ef4444" opacity="0.3"/>
</svg>`,

  'learning.svg': `<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="300" fill="#f8fafc"/>
  <rect x="100" y="100" width="200" height="120" rx="8" fill="#1f2937"/>
  <rect x="110" y="110" width="180" height="100" rx="4" fill="#374151"/>
  <circle cx="200" cy="160" r="20" fill="#6366f1"/>
  <polygon points="190,160 200,170 210,160 200,150" fill="white"/>
  <rect x="120" y="190" width="40" height="4" rx="2" fill="#9ca3af"/>
  <rect x="170" y="190" width="60" height="4" rx="2" fill="#9ca3af"/>
  <rect x="240" y="190" width="50" height="4" rx="2" fill="#9ca3af"/>
  <rect x="150" y="80" width="100" height="8" rx="4" fill="#10b981"/>
  <text x="200" y="250" font-family="Arial, sans-serif" font-size="12" fill="#6b7280" text-anchor="middle">Online Learning</text>
  <circle cx="80" cy="80" r="8" fill="#fbbf24" opacity="0.4"/>
  <circle cx="330" cy="70" r="6" fill="#ef4444" opacity="0.4"/>
</svg>`,

  'ai.svg': `<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="300" fill="#f8fafc"/>
  <circle cx="200" cy="150" r="50" fill="#6366f1" opacity="0.1"/>
  <circle cx="200" cy="150" r="35" fill="#6366f1" opacity="0.2"/>
  <circle cx="200" cy="150" r="20" fill="#6366f1"/>
  <circle cx="190" cy="145" r="2" fill="white"/>
  <circle cx="210" cy="145" r="2" fill="white"/>
  <rect x="195" y="155" width="10" height="2" rx="1" fill="white"/>
  <circle cx="150" cy="100" r="8" fill="#6366f1" opacity="0.3"/>
  <circle cx="250" cy="120" r="6" fill="#6366f1" opacity="0.4"/>
  <circle cx="170" cy="200" r="10" fill="#6366f1" opacity="0.2"/>
  <circle cx="230" cy="180" r="12" fill="#6366f1" opacity="0.3"/>
  <line x1="180" y1="130" x2="160" y2="110" stroke="#6366f1" stroke-width="2" opacity="0.6"/>
  <line x1="220" y1="130" x2="240" y2="110" stroke="#6366f1" stroke-width="2" opacity="0.6"/>
  <line x1="180" y1="170" x2="160" y2="190" stroke="#6366f1" stroke-width="2" opacity="0.6"/>
  <line x1="220" y1="170" x2="240" y2="190" stroke="#6366f1" stroke-width="2" opacity="0.6"/>
  <text x="200" y="230" font-family="Arial, sans-serif" font-size="12" fill="#6366f1" text-anchor="middle">AI Powered</text>
</svg>`,

  'career.svg': `<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="300" fill="#f8fafc"/>
  <path d="M 50 200 Q 100 150 150 160 T 250 140 T 350 120" stroke="#6366f1" stroke-width="4" fill="none"/>
  <circle cx="50" cy="200" r="6" fill="#ef4444"/>
  <circle cx="150" cy="160" r="6" fill="#fbbf24"/>
  <circle cx="250" cy="140" r="6" fill="#10b981"/>
  <circle cx="350" cy="120" r="8" fill="#6366f1"/>
  <rect x="40" y="210" width="20" height="30" rx="2" fill="#e5e7eb"/>
  <rect x="140" y="170" width="20" height="40" rx="2" fill="#e5e7eb"/>
  <rect x="240" y="150" width="20" height="60" rx="2" fill="#e5e7eb"/>
  <rect x="340" y="130" width="20" height="80" rx="2" fill="#e5e7eb"/>
  <text x="200" y="50" font-family="Arial, sans-serif" font-size="16" fill="#1f2937" text-anchor="middle">Career Progress</text>
  <text x="50" y="255" font-family="Arial, sans-serif" font-size="10" fill="#6b7280" text-anchor="middle">Start</text>
  <text x="350" y="255" font-family="Arial, sans-serif" font-size="10" fill="#6b7280" text-anchor="middle">Goal</text>
</svg>`,

  'empty.svg': `<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="300" fill="#f8fafc"/>
  <rect x="150" y="120" width="100" height="80" rx="8" fill="none" stroke="#d1d5db" stroke-width="2" stroke-dasharray="5,5"/>
  <circle cx="200" cy="160" r="15" fill="#e5e7eb"/>
  <text x="200" y="165" font-family="Arial, sans-serif" font-size="20" fill="#9ca3af" text-anchor="middle">?</text>
  <text x="200" y="230" font-family="Arial, sans-serif" font-size="12" fill="#6b7280" text-anchor="middle">No items found</text>
  <circle cx="100" cy="80" r="6" fill="#6366f1" opacity="0.3"/>
  <circle cx="320" cy="90" r="8" fill="#10b981" opacity="0.3"/>
  <circle cx="80" cy="220" r="10" fill="#fbbf24" opacity="0.3"/>
</svg>`
};

console.log('🎨 Creating custom SVG illustrations...\n');

let created = 0;
for (const [filename, content] of Object.entries(illustrations)) {
  const filepath = path.join(illustrationsDir, filename);
  try {
    fs.writeFileSync(filepath, content);
    console.log(`✅ Created: ${filename}`);
    created++;
  } catch (error) {
    console.log(`❌ Failed: ${filename} - ${error.message}`);
  }
}

console.log(`\n🎉 Created ${created} custom illustrations!`);
console.log(`📁 Location: ${illustrationsDir}`);
