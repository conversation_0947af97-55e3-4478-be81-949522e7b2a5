#!/usr/bin/env node

/**
 * Health Check Script for CareerDart Server
 * Used by Fly.io and Docker health checks
 */

const http = require('http');
const { performance } = require('perf_hooks');

const PORT = process.env.PORT || 8080;
const HOST = process.env.HOST || 'localhost';
const TIMEOUT = 5000; // 5 seconds timeout

// Health check configuration
const checks = {
  server: {
    name: 'Server Response',
    timeout: TIMEOUT,
    critical: true
  },
  database: {
    name: 'Database Connection',
    timeout: TIMEOUT * 2,
    critical: true
  },
  memory: {
    name: 'Memory Usage',
    timeout: 1000,
    critical: false
  }
};

/**
 * Check server response time
 */
async function checkServer() {
  return new Promise((resolve, reject) => {
    const startTime = performance.now();
    
    const req = http.request({
      hostname: HOST,
      port: PORT,
      path: '/health',
      method: 'GET',
      timeout: checks.server.timeout
    }, (res) => {
      const endTime = performance.now();
      const responseTime = Math.round(endTime - startTime);
      
      if (res.statusCode === 200) {
        resolve({
          status: 'healthy',
          responseTime: `${responseTime}ms`,
          statusCode: res.statusCode
        });
      } else {
        reject(new Error(`Server returned status ${res.statusCode}`));
      }
    });

    req.on('error', (error) => {
      reject(new Error(`Server check failed: ${error.message}`));
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error(`Server check timed out after ${checks.server.timeout}ms`));
    });

    req.end();
  });
}

/**
 * Check database connectivity (simplified)
 */
async function checkDatabase() {
  return new Promise((resolve) => {
    // For now, we'll assume database is healthy if server is responding
    // In a real implementation, you'd check the actual database connection
    const dbUrl = process.env.DATABASE_URL;
    
    if (!dbUrl) {
      resolve({
        status: 'warning',
        message: 'DATABASE_URL not configured',
        connection: 'unknown'
      });
      return;
    }

    // Simplified check - in production you'd ping the actual database
    resolve({
      status: 'healthy',
      connection: 'assumed-healthy',
      url: dbUrl.includes('@') ? dbUrl.split('@')[1].split('/')[0] : 'configured'
    });
  });
}

/**
 * Check memory usage
 */
function checkMemory() {
  const usage = process.memoryUsage();
  const maxMemory = parseInt(process.env.NODE_OPTIONS?.match(/--max-old-space-size=(\d+)/)?.[1] || '1024') * 1024 * 1024;
  const usedMemory = usage.heapUsed;
  const memoryUsagePercent = Math.round((usedMemory / maxMemory) * 100);

  return {
    status: memoryUsagePercent > 90 ? 'warning' : 'healthy',
    heapUsed: `${Math.round(usage.heapUsed / 1024 / 1024)}MB`,
    heapTotal: `${Math.round(usage.heapTotal / 1024 / 1024)}MB`,
    external: `${Math.round(usage.external / 1024 / 1024)}MB`,
    arrayBuffers: `${Math.round(usage.arrayBuffers / 1024 / 1024)}MB`,
    usage: `${memoryUsagePercent}%`,
    maxMemory: `${Math.round(maxMemory / 1024 / 1024)}MB`
  };
}

/**
 * Run all health checks
 */
async function runHealthChecks() {
  const results = {
    timestamp: new Date().toISOString(),
    status: 'healthy',
    checks: {}
  };

  const checkPromises = [
    { name: 'server', fn: checkServer },
    { name: 'database', fn: checkDatabase },
    { name: 'memory', fn: () => Promise.resolve(checkMemory()) }
  ];

  for (const { name, fn } of checkPromises) {
    try {
      const result = await Promise.race([
        fn(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Timeout')), checks[name].timeout)
        )
      ]);
      
      results.checks[name] = {
        ...result,
        timestamp: new Date().toISOString()
      };

      // If any critical check fails, mark overall status as unhealthy
      if (result.status !== 'healthy' && checks[name].critical) {
        results.status = 'unhealthy';
      }
    } catch (error) {
      results.checks[name] = {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };

      if (checks[name].critical) {
        results.status = 'unhealthy';
      }
    }
  }

  return results;
}

/**
 * Main health check execution
 */
async function main() {
  try {
    const results = await runHealthChecks();
    
    // Log results for debugging
    console.log('Health Check Results:', JSON.stringify(results, null, 2));
    
    // Exit with appropriate code
    if (results.status === 'healthy') {
      console.log('✅ All health checks passed');
      process.exit(0);
    } else {
      console.error('❌ Health checks failed');
      process.exit(1);
    }
  } catch (error) {
    console.error('💥 Health check script failed:', error.message);
    process.exit(1);
  }
}

// Handle script execution
if (require.main === module) {
  main();
}

module.exports = {
  runHealthChecks,
  checkServer,
  checkDatabase,
  checkMemory
}; 