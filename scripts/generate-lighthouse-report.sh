#!/bin/bash

# Lighthouse Report Generation Script for CareerDart
# This script generates a Lighthouse performance report for the deployed application

set -e  # Exit on any error

# Configuration
DOMAIN="${1:-https://careerdart.com}"  # Default to production domain, or use provided argument
OUTPUT_PATH="./lighthouse-report.json"
TIMEOUT=60000  # 60 seconds timeout

echo "🔍 Generating Lighthouse report for: $DOMAIN"

# Check if lighthouse is installed
if ! command -v lighthouse &> /dev/null; then
    echo "⚠️  Lighthouse not found. Installing globally..."
    npm install -g lighthouse
fi

# Generate the report
echo "📊 Running Lighthouse audit..."
lighthouse "$DOMAIN" \
    --output=json \
    --output-path="$OUTPUT_PATH" \
    --chrome-flags="--headless --no-sandbox --disable-dev-shm-usage" \
    --timeout="$TIMEOUT" \
    --quiet \
    --no-enable-error-reporting

# Check if report was generated successfully
if [ -f "$OUTPUT_PATH" ]; then
    echo "✅ Lighthouse report generated successfully!"
    echo "📄 Report saved to: $OUTPUT_PATH"
    
    # Extract key metrics for quick overview
    if command -v jq &> /dev/null; then
        echo ""
        echo "🚀 Performance Summary:"
        echo "--------------------------------"
        jq -r '.categories.performance | "Performance Score: " + (.score * 100 | tostring) + "%"' "$OUTPUT_PATH"
        jq -r '.categories.accessibility | "Accessibility Score: " + (.score * 100 | tostring) + "%"' "$OUTPUT_PATH"
        jq -r '.categories."best-practices" | "Best Practices Score: " + (.score * 100 | tostring) + "%"' "$OUTPUT_PATH"
        jq -r '.categories.seo | "SEO Score: " + (.score * 100 | tostring) + "%"' "$OUTPUT_PATH"
        echo ""
        echo "📈 Key Metrics:"
        echo "--------------------------------"
        jq -r '.audits."first-contentful-paint" | "First Contentful Paint: " + .displayValue' "$OUTPUT_PATH"
        jq -r '.audits."largest-contentful-paint" | "Largest Contentful Paint: " + .displayValue' "$OUTPUT_PATH"
        jq -r '.audits."speed-index" | "Speed Index: " + .displayValue' "$OUTPUT_PATH"
        jq -r '.audits.interactive | "Time to Interactive: " + .displayValue' "$OUTPUT_PATH"
        jq -r '.audits."total-blocking-time" | "Total Blocking Time: " + .displayValue' "$OUTPUT_PATH"
        jq -r '.audits."cumulative-layout-shift" | "Cumulative Layout Shift: " + .displayValue' "$OUTPUT_PATH"
    else
        echo "💡 Install jq for detailed metrics: brew install jq"
    fi
else
    echo "❌ Failed to generate Lighthouse report!"
    exit 1
fi

echo ""
echo "🎯 Usage Tips:"
echo "• For production: ./scripts/generate-lighthouse-report.sh https://your-domain.com"
echo "• For staging: ./scripts/generate-lighthouse-report.sh https://staging.your-domain.com"
echo "• For local (with public tunnel): ./scripts/generate-lighthouse-report.sh https://abc123.ngrok.io" 