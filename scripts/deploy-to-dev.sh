#!/bin/bash

# CareerDart Development Deployment Script
# This script builds and deploys to the development environment

set -e

echo "🔥 Starting CareerDart Development deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [[ ! -f "main.wasp" ]]; then
    print_error "main.wasp not found. Please run this script from the project root."
    exit 1
fi

print_info "Building Wasp application for development..."

# Clean previous builds
print_info "Cleaning previous builds..."
rm -rf .wasp/build
rm -rf netlify-dev-build

# Build the Wasp application
wasp build

print_success "Wasp build completed"

# Navigate to the web app build directory
cd .wasp/build/web-app

print_info "Installing development dependencies..."
npm install

print_info "Building frontend with development configuration..."

# Set environment variables for development deployment
# Development: dev.careerdart.com -> careerdart-dev.netlify.app
export NODE_ENV=production
export REACT_APP_API_URL="https://api-dev.careerdart.com"
export REACT_APP_ENV="development"
export WASP_WEB_CLIENT_URL="https://dev.careerdart.com"
export REACT_APP_ENABLE_ANALYTICS="false"
export REACT_APP_ENABLE_DEBUG="true"
export GENERATE_SOURCEMAP="false"
export SKIP_PREFLIGHT_CHECK=true
export CI=true

# Build the frontend
npm run build

print_success "Frontend build completed"

# Navigate back to project root
cd ../../../

# Create development deployment directory
print_info "Preparing development deployment files..."
mkdir -p netlify-dev-build

# Copy built files to development deployment directory
cp -r .wasp/build/web-app/build/* netlify-dev-build/

# Copy Lighthouse reports for admin dashboard if they exist
if ls lighthouse*.json 1> /dev/null 2>&1; then
    print_info "Copying Lighthouse reports..."
    cp lighthouse*.json netlify-dev-build/ 2>/dev/null || true
fi

# Add redirects file for development environment
print_info "Creating development _redirects file..."
cat > netlify-dev-build/_redirects << 'EOF'
# API redirects to development backend with custom domain
/api/* https://api-dev.careerdart.com/api/:splat 200
/auth/* https://api-dev.careerdart.com/auth/:splat 200
/operations/* https://api-dev.careerdart.com/operations/:splat 200

# SPA routing
/* /index.html 200
EOF

print_success "Development deployment files prepared in netlify-dev-build/"

# Display build information
print_info "Development Build Summary:"
echo "📁 Build directory: netlify-dev-build/"
echo "🌐 API URL: https://api-dev.careerdart.com"
echo "🚀 Environment: Development"
echo "📊 Source maps: Enabled"
echo "🔍 Debug mode: Enabled"

print_warning "Important: Make sure your Netlify development site is configured to:"
echo "  • Publish directory: netlify-dev-build"
echo "  • Build command: echo 'Using prebuilt files'"
echo "  • Custom domain: dev.careerdart.com"

print_success "🎉 Development build completed! Ready for Netlify deployment"

# Optional: Deploy directly with Netlify CLI if available
if command -v netlify &> /dev/null; then
    echo ""
    read -p "Deploy to Netlify development site now? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Deploying to Netlify development site..."
        netlify deploy --dir=netlify-dev-build --prod --site=careerdart-dev
        print_success "Deployed to Netlify development site!"
    fi
else
    print_warning "Netlify CLI not found. Install with: npm install -g netlify-cli"
    print_info "Manual deployment: Upload netlify-dev-build/ directory to your Netlify development site"
fi

print_info "Development site will be available at:"
echo "  • Primary: https://careerdart-dev.netlify.app"
echo "  • Custom: https://dev.careerdart.com" 