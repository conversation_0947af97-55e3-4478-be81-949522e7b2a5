#!/bin/bash

# Environment Switching Script for CareerDart
# Usage: ./scripts/deployment/switch-env.sh [development|staging|production]

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

ENV=${1:-development}

case $ENV in
  "development")
    export REACT_APP_API_URL="https://careerdart-dev.fly.dev"
    export REACT_APP_ENV="development"
    export REACT_APP_ENABLE_ANALYTICS="false"
    export REACT_APP_ENABLE_DEBUG="true"
    export GENERATE_SOURCEMAP="true"
    ;;
  "staging")
    export REACT_APP_API_URL="https://careerdart-staging.fly.dev"
    export REACT_APP_ENV="staging"
    export REACT_APP_ENABLE_ANALYTICS="true"
    export REACT_APP_ENABLE_DEBUG="false"
    export GENERATE_SOURCEMAP="true"
    ;;
  "production")
    export REACT_APP_API_URL="https://careerdart.fly.dev"
    export REACT_APP_ENV="production"
    export REACT_APP_ENABLE_ANALYTICS="true"
    export REACT_APP_ENABLE_DEBUG="false"
    export GENERATE_SOURCEMAP="false"
    ;;
  *)
    print_error "Invalid environment: $ENV"
    print_info "Usage: $0 [development|staging|production]"
    exit 1
    ;;
esac

print_success "Switched to $ENV environment"
echo "================================================="
print_info "Environment Variables Set:"
echo "  • API URL: $REACT_APP_API_URL"
echo "  • Environment: $REACT_APP_ENV"
echo "  • Analytics: $REACT_APP_ENABLE_ANALYTICS"
echo "  • Debug Mode: $REACT_APP_ENABLE_DEBUG"
echo "  • Source Maps: $GENERATE_SOURCEMAP"
echo ""
print_warning "These environment variables are set for this shell session only."
print_info "To make them permanent, add them to your .env file or shell profile." 