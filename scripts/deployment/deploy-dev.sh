#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

echo -e "${BLUE}🚀 Deploying CareerDart to Development Environment...${NC}"
echo "================================================="

# Check if required tools are installed
if ! command -v wasp &> /dev/null; then
    print_error "Wasp is not installed. Please install Wasp first."
fi

if ! command -v fly &> /dev/null; then
    print_error "Fly CLI is not installed. Please install it first."
fi

if ! command -v netlify &> /dev/null; then
    print_error "Netlify CLI is not installed. Please install it first."
fi

# Deploy backend to Fly.io development
print_info "📦 Deploying backend to Fly.io development environment..."
if fly deploy -a careerdart-dev -c deployment/config/fly.dev.toml; then
    print_status "Backend deployed successfully to Fly.io"
else
    print_error "Backend deployment to Fly.io failed"
fi

# Build Wasp application
print_info "🔨 Building Wasp application..."
if wasp build; then
    print_status "Wasp build completed successfully"
else
    print_error "Wasp build failed"
fi

# Build and deploy frontend to Netlify
print_info "🌐 Building and deploying frontend to Netlify development..."
cd .wasp/build/web-app

# Install dependencies
if npm install; then
    print_status "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
fi

# Build React application
if npm run build; then
    print_status "Frontend build completed successfully"
else
    print_error "Frontend build failed"
fi

# Deploy to Netlify (you'll need to set NETLIFY_SITE_ID environment variable)
if netlify deploy --prod --dir=build --site=${NETLIFY_DEV_SITE_ID:-careerdart-dev}; then
    print_status "Frontend deployed successfully to Netlify"
else
    print_error "Frontend deployment to Netlify failed"
fi

# Go back to project root
cd ../../../

print_status "🎉 Development deployment completed successfully!"
echo "================================================="
echo -e "${GREEN}🔗 Frontend URL: https://careerdart-dev.netlify.app${NC}"
echo -e "${GREEN}🔗 Backend URL: https://careerdart-dev.fly.dev${NC}"
echo ""
print_info "You can monitor the deployments with:"
echo "  • Backend logs: fly logs -a careerdart-dev --follow"
echo "  • Backend status: fly status -a careerdart-dev"
echo "  • Netlify dashboard: https://app.netlify.com/sites/careerdart-dev" 