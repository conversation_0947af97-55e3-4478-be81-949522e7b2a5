#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

echo -e "${BLUE}🛠️  Setting up CareerDart Development Environment...${NC}"
echo "================================================="

# Check if required tools are installed
print_info "Checking required tools..."

if ! command -v fly &> /dev/null; then
    print_warning "Fly CLI not found. Installing..."
    curl -L https://fly.io/install.sh | sh
    export PATH=$PATH:~/.fly/bin
fi

if ! command -v netlify &> /dev/null; then
    print_warning "Netlify CLI not found. Installing..."
    npm install -g netlify-cli
fi

print_status "Required tools are available"

# Setup Fly.io development environment
print_info "🚀 Setting up Fly.io development environment..."

# Check if already logged in to Fly.io
if ! fly auth whoami &> /dev/null; then
    print_info "Please log in to Fly.io..."
    fly auth login
fi

# Create development app if it doesn't exist
if ! fly apps list | grep -q "careerdart-dev"; then
    print_info "Creating Fly.io development app..."
    fly apps create careerdart-dev --org personal
    print_status "Development app 'careerdart-dev' created"
else
    print_info "Development app 'careerdart-dev' already exists"
fi

# Create development database if it doesn't exist
if ! fly apps list | grep -q "careerdart-dev-db"; then
    print_info "Creating development PostgreSQL database..."
    fly postgres create careerdart-dev-db --region sjc --vm-size shared-cpu-1x --volume-size 10 --org personal
    print_status "Development database 'careerdart-dev-db' created"
    
    print_info "Attaching database to development app..."
    fly postgres attach careerdart-dev-db --app careerdart-dev
    print_status "Database attached to development app"
else
    print_info "Development database 'careerdart-dev-db' already exists"
fi

# Setup Netlify development environment
print_info "🌐 Setting up Netlify development environment..."

# Check if already logged in to Netlify
if ! netlify status | grep -q "Logged in"; then
    print_info "Please log in to Netlify..."
    netlify login
fi

# Create development site if it doesn't exist
print_info "Creating Netlify development site..."
if netlify sites:create --name careerdart-dev 2>/dev/null; then
    print_status "Development site 'careerdart-dev' created"
else
    print_info "Development site may already exist or name is taken"
fi

# Create development branch
print_info "🌿 Setting up development branch..."

if git rev-parse --verify develop &>/dev/null; then
    print_info "Development branch 'develop' already exists"
else
    git checkout -b develop
    git push -u origin develop
    print_status "Development branch 'develop' created and pushed"
fi

# Make deployment script executable
chmod +x scripts/deployment/*.sh
print_status "Made deployment scripts executable"

echo ""
print_status "🎉 Development environment setup completed!"
echo "================================================="
echo ""
print_info "Next steps:"
echo "1. Set environment variables in Fly.io using template:"
echo "   cat deployment/templates/env-development.template"
echo "   fly secrets set -a careerdart-dev JWT_SECRET=your-secret ..."
echo ""
echo "2. Set environment variables in Netlify Dashboard:"
echo "   https://app.netlify.com/sites/careerdart-dev/settings/deploys#environment-variables"
echo "   Use: deployment/templates/env-development.template"
echo ""
echo "3. Set up GitHub Actions (optional):"
echo "   cp deployment/templates/deploy-dev-workflow.yml .github/workflows/deploy-dev.yml"
echo "   cp deployment/templates/deploy-prod-workflow.yml .github/workflows/deploy-prod.yml"
echo ""
echo "4. Add GitHub secrets for CI/CD:"
echo "   • FLY_API_TOKEN (get from: fly auth token)"
echo "   • NETLIFY_AUTH_TOKEN (get from: netlify auth --help)"
echo "   • NETLIFY_DEV_SITE_ID (get from: netlify sites:list)"
echo ""
echo "5. Deploy to development:"
echo "   ./scripts/deployment/deploy-dev.sh"
echo ""
print_warning "Don't forget to review and update deployment/templates/env-development.template with your actual API keys!" 