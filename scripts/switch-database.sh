#!/bin/bash

# CareerDart Database Switcher
# Safely switch between local and production databases

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "main.wasp" ]; then
    print_error "main.wasp not found. Please run this script from the project root."
    exit 1
fi

# Function to backup current .env.server
backup_env() {
    if [ -f ".env.server" ]; then
        cp .env.server .env.server.backup
        print_status "Backed up current .env.server to .env.server.backup"
    fi
}

# Function to switch to local database
switch_to_local() {
    print_status "Switching to local database..."
    
    backup_env
    
    # Update .env.server to use local database
    sed -i.tmp 's|^DATABASE_URL=postgresql://neondb_owner.*|#DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require|' .env.server
    sed -i.tmp 's|^#DATABASE_URL=postgresql://postgres:postgres@localhost:5432/careerdart|DATABASE_URL=postgresql://postgres:postgres@localhost:5432/careerdart|' .env.server
    rm -f .env.server.tmp
    
    print_success "Switched to local database"
    print_status "Running database setup..."
    
    # Generate Prisma client for local database
    wasp db generate
    
    # Run migrations on local database
    wasp db migrate-dev --name "sync-with-local"
    
    print_success "Local database is ready!"
    print_status "You can now run: wasp start"
}

# Function to switch to production database
switch_to_production() {
    print_status "Switching to production database..."
    
    backup_env
    
    # Update .env.server to use production database
    sed -i.tmp 's|^DATABASE_URL=postgresql://postgres:postgres@localhost:5432/careerdart|#DATABASE_URL=postgresql://postgres:postgres@localhost:5432/careerdart|' .env.server
    sed -i.tmp 's|^#DATABASE_URL=postgresql://neondb_owner.*|DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require|' .env.server
    rm -f .env.server.tmp
    
    print_success "Switched to production database"
    print_status "Running database setup..."
    
    # Generate Prisma client for production database
    wasp db generate
    
    # Run migrations on production database
    print_warning "This will apply migrations to your PRODUCTION database!"
    read -p "Are you sure you want to continue? (y/N): " confirm
    if [[ $confirm =~ ^[Yy]$ ]]; then
        wasp db migrate-deploy
        print_success "Production database is ready!"
    else
        print_warning "Skipped migration deployment. You may need to run 'wasp db migrate-deploy' manually."
    fi
    
    print_status "You can now run: wasp start"
}

# Function to show current database status
show_status() {
    print_status "Current database configuration:"
    echo ""
    
    if grep -q "^DATABASE_URL=postgresql://postgres:postgres@localhost" .env.server; then
        print_success "Currently using: LOCAL database (localhost:5432)"
    elif grep -q "^DATABASE_URL=postgresql://neondb_owner" .env.server; then
        print_success "Currently using: PRODUCTION database (Neon)"
    else
        print_warning "Database configuration unclear. Please check .env.server"
    fi
    
    echo ""
    print_status "Available commands:"
    echo "  ./scripts/switch-database.sh local      - Switch to local database"
    echo "  ./scripts/switch-database.sh production - Switch to production database"
    echo "  ./scripts/switch-database.sh status     - Show current status"
}

# Main script logic
case "${1:-status}" in
    "local")
        switch_to_local
        ;;
    "production")
        switch_to_production
        ;;
    "status")
        show_status
        ;;
    *)
        print_error "Invalid option: $1"
        echo ""
        print_status "Usage: $0 [local|production|status]"
        echo ""
        show_status
        exit 1
        ;;
esac
