#!/bin/bash

# Development Environment Setup Script for CareerDart
# Prevents common dependency and environment issues

set -e

echo "🚀 Setting up CareerDart development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check Node.js version
REQUIRED_NODE_VERSION="18.0.0"
CURRENT_NODE_VERSION=$(node -v | sed 's/v//')

print_status "Checking Node.js version..."
if [ "$(printf '%s\n' "$REQUIRED_NODE_VERSION" "$CURRENT_NODE_VERSION" | sort -V | head -n1)" = "$REQUIRED_NODE_VERSION" ]; then
    print_success "Node.js $CURRENT_NODE_VERSION meets requirement (>= $REQUIRED_NODE_VERSION)"
else
    print_error "Node.js $CURRENT_NODE_VERSION is below required $REQUIRED_NODE_VERSION"
    if [ -f ".nvmrc" ]; then
        print_status "Found .nvmrc file. Run 'nvm use' to switch to correct version"
    fi
    exit 1
fi

# Install Wasp 0.15.0
print_status "Installing Wasp 0.15.0..."
curl -sSL https://get.wasp.sh/installer.sh | sh -s -- -v 0.15.0

# Add Wasp to PATH for this session
export PATH="$HOME/.local/bin:$PATH"

# Verify Wasp installation
WASP_VERSION=$(wasp version 2>/dev/null | head -n1)
if [ "$WASP_VERSION" = "0.15.0" ]; then
    print_success "Wasp $WASP_VERSION installed correctly"
else
    print_error "Wasp installation failed or wrong version"
    exit 1
fi

# Clean any existing artifacts
print_status "Cleaning existing artifacts..."
wasp clean 2>/dev/null || true
rm -rf node_modules package-lock.json 2>/dev/null || true
npm cache clean --force 2>/dev/null || true

# Check for problematic overrides
print_status "Checking package.json for problematic overrides..."
if grep -q '"@tanstack/react-query"' package.json; then
    print_warning "Found @tanstack/react-query override in package.json"
    print_warning "Consider removing it to let Wasp manage this dependency"
fi

print_success "Environment setup complete!"
print_status "Run 'wasp start' to begin development"

# Add Wasp to shell profile if not already there
SHELL_PROFILE=""
if [ -f "$HOME/.zshrc" ]; then
    SHELL_PROFILE="$HOME/.zshrc"
elif [ -f "$HOME/.bashrc" ]; then
    SHELL_PROFILE="$HOME/.bashrc"
elif [ -f "$HOME/.bash_profile" ]; then
    SHELL_PROFILE="$HOME/.bash_profile"
fi

if [ -n "$SHELL_PROFILE" ]; then
    if ! grep -q 'HOME/.local/bin' "$SHELL_PROFILE"; then
        echo 'export PATH="$HOME/.local/bin:$PATH"' >> "$SHELL_PROFILE"
        print_success "Added Wasp to PATH in $SHELL_PROFILE"
        print_status "Restart your terminal or run: source $SHELL_PROFILE"
    fi
fi 