import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/coverlettergpt'
    }
  }
});

async function verifyAdminEmail() {
  const adminEmail = '<EMAIL>';
  
  try {
    console.log('🔍 Looking for user with email:', adminEmail);
    
    // First, find the user with this email
    const user = await prisma.user.findUnique({
      where: { email: adminEmail },
      include: {
        auth: {
          include: {
            identities: true
          }
        }
      }
    });

    if (!user) {
      console.log('❌ User not found with email:', adminEmail);
      console.log('📝 Available users:');
      const allUsers = await prisma.user.findMany({
        select: { id: true, email: true, username: true }
      });
      allUsers.forEach(u => console.log(`  - ${u.email} (${u.username})`));
      return;
    }

    console.log('✅ Found user:', {
      id: user.id,
      email: user.email,
      username: user.username
    });

    if (!user.auth || !user.auth.identities || user.auth.identities.length === 0) {
      console.log('❌ No auth identities found for this user');
      return;
    }

    // Find the email provider identity
    const emailIdentity = user.auth.identities.find(identity => 
      identity.providerName === 'email'
    );

    if (!emailIdentity) {
      console.log('❌ No email provider identity found');
      console.log('Available identities:', user.auth.identities.map(i => i.providerName));
      return;
    }

    console.log('📧 Found email identity:', {
      providerName: emailIdentity.providerName,
      providerUserId: emailIdentity.providerUserId
    });

    // Parse current providerData
    let providerData;
    try {
      providerData = JSON.parse(emailIdentity.providerData);
    } catch (e) {
      console.log('⚠️  Could not parse providerData, creating new object');
      providerData = {};
    }

    console.log('📊 Current providerData:', providerData);

    // Check if already verified
    if (providerData.isEmailVerified === true) {
      console.log('✅ Email is already verified!');
      return;
    }

    // Update the providerData to mark email as verified
    const updatedProviderData = {
      ...providerData,
      isEmailVerified: true,
      emailVerified: true,
      emailVerifiedAt: new Date().toISOString()
    };

    console.log('🔄 Updating email verification status...');

    await prisma.authIdentity.update({
      where: {
        providerName_providerUserId: {
          providerName: emailIdentity.providerName,
          providerUserId: emailIdentity.providerUserId
        }
      },
      data: {
        providerData: JSON.stringify(updatedProviderData)
      }
    });

    console.log('✅ Successfully updated email verification status!');
    console.log('📊 New providerData:', updatedProviderData);

    // Verify the update
    const updatedIdentity = await prisma.authIdentity.findUnique({
      where: {
        providerName_providerUserId: {
          providerName: emailIdentity.providerName,
          providerUserId: emailIdentity.providerUserId
        }
      }
    });

    const verificationData = JSON.parse(updatedIdentity.providerData);
    console.log('🔍 Verification successful:', {
      isEmailVerified: verificationData.isEmailVerified,
      emailVerified: verificationData.emailVerified,
      emailVerifiedAt: verificationData.emailVerifiedAt
    });

  } catch (error) {
    console.error('❌ Error verifying admin email:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
verifyAdminEmail(); 