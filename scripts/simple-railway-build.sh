#!/bin/bash

# Simple Railway Build Script for Wasp Backend
# This script prepares the backend for Railway deployment

set -e  # Exit on any error

echo "🚂 Building for Railway deployment..."

# Build the Wasp project
echo "🔨 Building Wasp project..."
wasp build

echo "✅ Build complete! Ready for Railway deployment."
echo ""
echo "📝 Next steps:"
echo "1. Make sure Railway CLI is installed: npm install -g @railway/cli"
echo "2. Login to Railway: railway login"
echo "3. Deploy: railway up"
echo ""
echo "🔧 Environment variables needed in Railway:"
echo "- DATABASE_URL"
echo "- OPENAI_API_KEY" 
echo "- GOOGLE_CLIENT_ID"
echo "- GOOGLE_CLIENT_SECRET"
echo "- RESEND_API_KEY"
echo "- JWT_SECRET"
echo "- STRIPE_KEY (optional)"
echo "- WASP_WEB_CLIENT_URL (frontend URL)" 