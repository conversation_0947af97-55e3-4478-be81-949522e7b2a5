#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import https from 'https';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create public/images directory if it doesn't exist
const imagesDir = path.join(__dirname, '../public/images/illustrations');
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// List of illustrations to download
const illustrations = [
  // Empty States
  { name: 'job_hunt_re_q203', category: 'empty' },
  { name: 'newsletter_re_wrob', category: 'empty' },
  { name: 'resume_re_hkth', category: 'empty' },
  { name: 'interview_re_5jn2', category: 'empty' },
  { name: 'searching_re_3ra9', category: 'empty' },

  // Error States
  { name: 'page_not_found_re_e9o6', category: 'error' },
  { name: 'warning_re_eoyh', category: 'error' },
  { name: 'server_down_re_8yzk', category: 'error' },

  // Loading & Success
  { name: 'loading_re_5axr', category: 'loading' },
  { name: 'completed_re_cisp', category: 'success' },

  // Welcome & Features
  { name: 'career_progress_re_99p2', category: 'welcome' },
  { name: 'mobile_login_re_9ntv', category: 'welcome' },
  { name: 'artificial_intelligence_re_enpp', category: 'feature' },
  { name: 'online_learning_re_qw08', category: 'feature' },

  // Generic
  { name: 'empty_re_opql', category: 'generic' },
];

// Color variations to download
const colors = [
  '6366f1', // Purple (primary)
  'ef4444', // Red (errors)
  '10b981', // Green (success)
  '3b82f6', // Blue (info)
  'f59e0b', // Orange (warning)
];

function downloadFile(url, filepath) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(filepath);

    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${url}: ${response.statusCode}`));
        return;
      }

      response.pipe(file);

      file.on('finish', () => {
        file.close();
        resolve();
      });

      file.on('error', (err) => {
        fs.unlink(filepath, () => {}); // Delete the file on error
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

async function downloadIllustrations() {
  console.log('🎨 Downloading undraw.co illustrations...\n');

  let downloaded = 0;
  let failed = 0;

  for (const illustration of illustrations) {
    for (const color of colors) {
      const url = `https://undraw.co/api/illustrations/${illustration.name}.svg?color=${color}`;
      const filename = `${illustration.name}_${color}.svg`;
      const filepath = path.join(imagesDir, filename);

      try {
        console.log(`📥 Downloading: ${filename}`);
        await downloadFile(url, filepath);
        downloaded++;
      } catch (error) {
        console.log(`❌ Failed: ${filename} - ${error.message}`);
        failed++;
      }
    }
  }

  // Also download default versions without color
  for (const illustration of illustrations) {
    const url = `https://undraw.co/api/illustrations/${illustration.name}.svg`;
    const filename = `${illustration.name}.svg`;
    const filepath = path.join(imagesDir, filename);

    try {
      console.log(`📥 Downloading: ${filename}`);
      await downloadFile(url, filepath);
      downloaded++;
    } catch (error) {
      console.log(`❌ Failed: ${filename} - ${error.message}`);
      failed++;
    }
  }

  console.log(`\n✅ Download complete!`);
  console.log(`📊 Downloaded: ${downloaded} files`);
  console.log(`❌ Failed: ${failed} files`);
  console.log(`📁 Location: ${imagesDir}`);
}

// Run the download
downloadIllustrations().catch(console.error);
