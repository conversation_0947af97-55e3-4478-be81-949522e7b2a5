#!/bin/bash

echo "==================================================================="
echo "SETTING UP VERCEL ENVIRONMENT VARIABLES FOR CAREERDART"
echo "==================================================================="

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    print_warning "Vercel CLI not found. Installing..."
    npm install -g vercel
fi

# Check if logged into Vercel
if ! vercel whoami &> /dev/null; then
    print_warning "Not logged into Vercel. Please log in..."
    vercel login
fi

print_info "Setting up environment variables in Vercel..."

echo ""
echo "You'll need to provide the following environment variables:"
echo "1. DATABASE_URL (from Neon)"
echo "2. OPENAI_API_KEY"
echo "3. GOOGLE_CLIENT_ID"
echo "4. GOOGLE_CLIENT_SECRET"
echo "5. SENDGRID_API_KEY"
echo "6. STRIPE_KEY"
echo "7. PRODUCT_PRICE_ID"
echo "8. PRODUCT_CREDITS_PRICE_ID"
echo ""

# Function to set environment variable
set_env_var() {
    local var_name=$1
    local var_description=$2
    
    echo ""
    print_info "Setting $var_name ($var_description)"
    read -p "Enter value for $var_name: " var_value
    
    if [ ! -z "$var_value" ]; then
        echo "$var_value" | vercel env add "$var_name" production
        print_success "$var_name set successfully"
    else
        print_warning "$var_name skipped (empty value)"
    fi
}

# Set each environment variable
set_env_var "DATABASE_URL" "Neon PostgreSQL connection string"
set_env_var "OPENAI_API_KEY" "OpenAI API key"
set_env_var "GOOGLE_CLIENT_ID" "Google OAuth client ID"
set_env_var "GOOGLE_CLIENT_SECRET" "Google OAuth client secret"
set_env_var "SENDGRID_API_KEY" "SendGrid API key"
set_env_var "STRIPE_KEY" "Stripe secret key"
set_env_var "PRODUCT_PRICE_ID" "Stripe product price ID"
set_env_var "PRODUCT_CREDITS_PRICE_ID" "Stripe credits price ID"

# Set application URLs
echo ""
print_info "Setting application URLs..."
read -p "Enter your Vercel app domain (e.g., careerdart.vercel.app): " app_domain

if [ ! -z "$app_domain" ]; then
    app_url="https://$app_domain"
    echo "$app_url" | vercel env add "WASP_WEB_CLIENT_URL" production
    echo "$app_url" | vercel env add "WASP_SERVER_URL" production
    print_success "Application URLs set to $app_url"
fi

echo ""
print_success "Environment variables setup completed!"
print_info "You can view all environment variables with: vercel env ls"
print_info "To redeploy with new environment variables: vercel --prod"
