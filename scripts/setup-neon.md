# Neon Database Setup for CareerDart

## 1. Create Neon Account and Database

1. Go to [Neon Console](https://console.neon.tech/)
2. Sign up or log in
3. Create a new project named "careerdart"
4. Select the region closest to your users
5. Choose PostgreSQL version 15 or later

## 2. Get Database Connection String

After creating the project:

1. Go to your project dashboard
2. Click on "Connection Details"
3. Copy the connection string that looks like:
   ```
   postgresql://username:<EMAIL>/neondb?sslmode=require
   ```

## 3. Update Environment Variables

Replace the DATABASE_URL in your `.env.server` file:

```env
# Replace this line:
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/careerdart

# With your Neon connection string:
DATABASE_URL=postgresql://username:<EMAIL>/neondb?sslmode=require
```

## 4. Test Connection

Run the following command to test the connection:

```bash
wasp db migrate-dev
```

If successful, you should see migrations being applied to your Neon database.

## 5. Production Environment Variables

For production deployment, you'll need these environment variables:

```env
# Database
DATABASE_URL=your_neon_connection_string

# Application URLs (update these with your Vercel domain)
WASP_WEB_CLIENT_URL=https://your-app.vercel.app
WASP_SERVER_URL=https://your-app.vercel.app

# AI Services
OPENAI_API_KEY=your_openai_api_key

# Authentication
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Email
SENDGRID_API_KEY=your_sendgrid_api_key
SEND_EMAILS_IN_DEVELOPMENT=false

# Stripe
STRIPE_KEY=your_stripe_secret_key
PRODUCT_PRICE_ID=your_stripe_price_id
PRODUCT_CREDITS_PRICE_ID=your_credits_price_id
```

## 6. Database Scaling

Neon offers:
- **Free tier**: 0.5 GB storage, 1 compute unit
- **Pro tier**: Autoscaling, branching, point-in-time recovery
- **Scale tier**: Higher limits and performance

For production, consider upgrading to Pro tier for better performance and features.
