[build]
  # Use pre-built files instead of building on Netlify
  command = "echo 'Using pre-built files from netlify-client-build directory'"
  # Publish directory (where the built files are)
  publish = "netlify-client-build"
  functions = "netlify/functions"

# Performance optimizations
[build.processing]
  skip_processing = false

[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true

[build.processing.html]
  pretty_urls = true

[build.processing.images]
  compress = true

# Build environment with increased memory for large builds
[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "8"
  # Environment for production
  NODE_ENV = "production"
  GENERATE_SOURCEMAP = "false"
  SKIP_PREFLIGHT_CHECK = "true"
  CI = "true"
  # Build performance optimizations
  NODE_OPTIONS = "--max-old-space-size=4096"
  # Cache optimization
  NPM_CONFIG_CACHE = ".npm-cache"
  NETLIFY_CACHE_DIR = ".netlify/cache"
  # TypeScript build optimizations
  TSC_COMPILE_ON_ERROR = "true"
  SKIP_TYPE_CHECK = "true"
  TS_NODE_SKIP_IGNORE = "true"
  DISABLE_ESLINT_PLUGIN = "true"

# Headers for better caching and performance
[[headers]]
  for = "/assets/*.js"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"
    X-Content-Type-Options = "nosniff"

[[headers]]
  for = "/assets/*.css"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"
    X-Content-Type-Options = "nosniff"

[[headers]]
  for = "/assets/fonts/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"
    Access-Control-Allow-Origin = "*"

[[headers]]
  for = "/assets/images/*"
  [headers.values]
    Cache-Control = "public, max-age=********"

[[headers]]
  for = "/index.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-src 'none';"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=()"

# API redirects to Fly.io backend - MUST BE FIRST for proper routing
[[redirects]]
  from = "/api/*"
  to = "https://api.careerdart.com/api/:splat"
  status = 200
  force = true
  headers = {X-From = "Netlify"}

[[redirects]]
  from = "/auth/*"
  to = "https://api.careerdart.com/auth/:splat"
  status = 200
  force = true
  headers = {X-From = "Netlify"}

[[redirects]]
  from = "/operations/*"
  to = "https://api.careerdart.com/operations/:splat"
  status = 200
  force = true
  headers = {X-From = "Netlify"}

# Redirect all other routes to index.html for SPA routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = false

# Static asset caching
[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.woff2"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Dev settings
[dev]
  command = "npm run dev"
  port = 3000
  targetPort = 3001
  autoLaunch = false

# Error page
[[redirects]]
  from = "/404"
  to = "/404.html"
  status = 404 