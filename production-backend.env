# Production Backend Environment Variables
# Set these in your backend hosting service (Fly.io, Railway, Render, etc.)

# === PRODUCTION ENVIRONMENT URLS (CURRENT SETUP) ===
# careerdart.com -> careerdart.netlify.app (Frontend)
# api.careerdart.com -> careerdart-prod.fly.dev (Backend)
WASP_WEB_CLIENT_URL=https://careerdart.com
WASP_SERVER_URL=https://api.careerdart.com

# Environment
NODE_ENV=production
WASP_ENV=production

# Database (replace with your actual production database URL)
DATABASE_URL=********************************************/database_name

# JWT Secret for email verification (generate with: openssl rand -base64 32)
JWT_SECRET=your-production-jwt-secret-super-secure

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-your-google-client-secret

# Email Configuration (RESEND)
SMTP_HOST=smtp.resend.com
SMTP_PORT=587
SMTP_USERNAME=resend
SMTP_PASSWORD=re_your-resend-api-key-here
RESEND_API_KEY=re_your-resend-api-key-here

# OpenAI API Key
OPENAI_API_KEY=sk-your-openai-api-key-here

# Stripe Configuration (if using payments)
STRIPE_KEY=sk_live_your-stripe-secret-key-here
PRODUCT_PRICE_ID=price_your-product-price-id-here
PRODUCT_CREDITS_PRICE_ID=price_your-credits-price-id-here 