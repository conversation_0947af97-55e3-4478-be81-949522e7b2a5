# CareerDart URL Structure & Environment Setup

## 🌐 Current Production Setup

| Domain | Points to | Service | Purpose |
|--------|-----------|---------|---------|
| `careerdart.com` | `careerdart.netlify.app` | Netlify | Frontend (React App) |
| `api.careerdart.com` | `careerdart-prod.fly.dev` | Fly.io | Backend API |

## 🔧 Development Setup

| Domain | Service | Purpose |
|--------|---------|---------|
| `careerdart-dev.netlify.app` | Netlify | Development Frontend |
| `careerdart-dev.fly.dev` | Fly.io | Development Backend |

## 📋 Environment Variables

### Production Backend (Fly.io: careerdart-prod)
```bash
WASP_WEB_CLIENT_URL=https://careerdart.com
WASP_SERVER_URL=https://api.careerdart.com
NODE_ENV=production
```

### Development Backend (Fly.io: careerdart-dev)
```bash
WASP_WEB_CLIENT_URL=https://careerdart-dev.netlify.app
WASP_SERVER_URL=https://careerdart-dev.fly.dev
NODE_ENV=development
```

### Production Frontend (Netlify: careerdart.netlify.app)
```bash
REACT_APP_API_URL=https://api.careerdart.com
NODE_ENV=production
```

### Development Frontend (Netlify: careerdart-dev.netlify.app)
```bash
REACT_APP_API_URL=https://careerdart-dev.fly.dev
NODE_ENV=development
```

## 🔄 OAuth Redirect Flow

### Production
1. User visits: `https://careerdart.com`
2. Clicks Google Sign In
3. OAuth redirect to: `https://api.careerdart.com/auth/google/callback`
4. After auth, redirect back to: `https://careerdart.com/dashboard`

### Development
1. User visits: `https://careerdart-dev.netlify.app`
2. Clicks Google Sign In
3. OAuth redirect to: `https://careerdart-dev.fly.dev/auth/google/callback`
4. After auth, redirect back to: `https://careerdart-dev.netlify.app/dashboard`

## 🚀 Deployment Commands

### Production
```bash
# Frontend (builds for careerdart.netlify.app, serves careerdart.com)
./scripts/deploy-to-netlify.sh

# Backend (deploys to careerdart-prod.fly.dev, serves api.careerdart.com)
fly deploy -a careerdart-prod
```

### Development
```bash
# Frontend (deploys to careerdart-dev.netlify.app)
./scripts/deploy-to-dev.sh

# Backend (deploys to careerdart-dev.fly.dev)
fly deploy -a careerdart-dev
```

## 🔑 Google OAuth Configuration

### Production OAuth Client
- **Authorized JavaScript origins:**
  - `https://careerdart.com`
  - `https://api.careerdart.com`
- **Authorized redirect URIs:**
  - `https://api.careerdart.com/auth/google/callback`

### Development OAuth Client
- **Authorized JavaScript origins:**
  - `https://careerdart-dev.netlify.app`
  - `https://careerdart-dev.fly.dev`
- **Authorized redirect URIs:**
  - `https://careerdart-dev.fly.dev/auth/google/callback`

## 📊 DNS Configuration

### Production DNS Records
```dns
# Main domain points to Netlify
Type: A
Name: @
Value: *********

# API subdomain points to Fly.io
Type: CNAME
Name: api
Value: careerdart-prod.fly.dev

# WWW redirect
Type: CNAME
Name: www
Value: careerdart.netlify.app
```

## ⚠️ Important Notes

1. **Production Environment Variables**: The `WASP_WEB_CLIENT_URL` on the backend MUST be `https://careerdart.com` (not the Netlify URL) for OAuth redirects to work correctly.

2. **Frontend API URL**: The React app must use `https://api.careerdart.com` as the API URL, which gets proxied to the Fly.io backend.

3. **Netlify Redirects**: The production `netlify.toml` redirects all `/api/*`, `/auth/*`, and `/operations/*` requests to `https://api.careerdart.com`.

4. **Separation of Environments**: Production and development use completely separate:
   - OAuth credentials
   - Databases
   - Environment variables
   - Domain names 