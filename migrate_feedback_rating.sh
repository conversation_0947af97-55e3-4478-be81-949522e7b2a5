#!/bin/bash

# CareerDart Database Migration Script
# Adds rating column to Feedback table for both DEV and PROD environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Database URLs
DEV_DB_URL="postgresql://postgres:postgres@localhost:5432/coverlettergpt"
PROD_DB_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

# SQL Migration
MIGRATION_SQL="
-- Create Feedback table and add rating column for star rating system
DO \$\$
BEGIN
    -- Check if Feedback table exists, if not create it
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'Feedback'
    ) THEN
        -- Create the entire Feedback table
        CREATE TABLE \"Feedback\" (
            \"id\" TEXT NOT NULL,
            \"userId\" INTEGER,
            \"guestEmail\" TEXT,
            \"type\" TEXT NOT NULL,
            \"description\" TEXT NOT NULL,
            \"rating\" INTEGER,
            \"screenshot\" TEXT,
            \"url\" TEXT,
            \"userAgent\" TEXT,
            \"status\" TEXT NOT NULL DEFAULT 'new',
            \"adminNotes\" TEXT,
            \"priority\" TEXT NOT NULL DEFAULT 'medium',
            \"assignedTo\" TEXT,
            \"resolvedAt\" TIMESTAMP(3),
            \"createdAt\" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
            \"updatedAt\" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

            CONSTRAINT \"Feedback_pkey\" PRIMARY KEY (\"id\")
        );

        -- Create indexes for better performance
        CREATE INDEX \"Feedback_status_createdAt_idx\" ON \"Feedback\"(\"status\", \"createdAt\");
        CREATE INDEX \"Feedback_type_createdAt_idx\" ON \"Feedback\"(\"type\", \"createdAt\");
        CREATE INDEX \"Feedback_userId_createdAt_idx\" ON \"Feedback\"(\"userId\", \"createdAt\");
        CREATE INDEX \"Feedback_createdAt_idx\" ON \"Feedback\"(\"createdAt\");
        CREATE INDEX \"Feedback_rating_idx\" ON \"Feedback\"(\"rating\");

        -- Add foreign key constraint (if User table exists)
        IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'User') THEN
            ALTER TABLE \"Feedback\" ADD CONSTRAINT \"Feedback_userId_fkey\"
            FOREIGN KEY (\"userId\") REFERENCES \"User\"(\"id\") ON DELETE SET NULL ON UPDATE CASCADE;
        END IF;

        -- Add check constraint for rating
        ALTER TABLE \"Feedback\" ADD CONSTRAINT \"Feedback_rating_check\"
        CHECK (\"rating\" >= 1 AND \"rating\" <= 5);

        RAISE NOTICE 'Feedback table created successfully with rating column';
    ELSE
        -- Table exists, check if rating column exists
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns
            WHERE table_name = 'Feedback' AND column_name = 'rating'
        ) THEN
            -- Add the rating column
            ALTER TABLE \"Feedback\" ADD COLUMN \"rating\" INTEGER;

            -- Add a check constraint to ensure rating is between 1 and 5
            ALTER TABLE \"Feedback\" ADD CONSTRAINT \"Feedback_rating_check\"
            CHECK (\"rating\" >= 1 AND \"rating\" <= 5);

            -- Create an index for better performance when filtering by rating
            CREATE INDEX \"Feedback_rating_idx\" ON \"Feedback\"(\"rating\");

            RAISE NOTICE 'Rating column added successfully to existing Feedback table';
        ELSE
            RAISE NOTICE 'Feedback table and rating column already exist';
        END IF;
    END IF;
END
\$\$;

-- Verify the migration
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'Feedback' AND column_name = 'rating';
"

# Function to run migration on a database
run_migration() {
    local db_url=$1
    local env_name=$2
    
    print_status "Running migration on $env_name database..."
    
    if psql "$db_url" -c "$MIGRATION_SQL"; then
        print_success "$env_name database migration completed successfully!"
    else
        print_error "$env_name database migration failed!"
        return 1
    fi
}

# Function to test database connection
test_connection() {
    local db_url=$1
    local env_name=$2
    
    print_status "Testing connection to $env_name database..."
    
    if psql "$db_url" -c "SELECT 1;" > /dev/null 2>&1; then
        print_success "$env_name database connection successful!"
        return 0
    else
        print_error "$env_name database connection failed!"
        return 1
    fi
}

# Function to verify migration
verify_migration() {
    local db_url=$1
    local env_name=$2
    
    print_status "Verifying migration on $env_name database..."
    
    local result=$(psql "$db_url" -t -c "
        SELECT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'Feedback' AND column_name = 'rating'
        );
    " 2>/dev/null | xargs)
    
    if [ "$result" = "t" ]; then
        print_success "$env_name: Rating column exists and is ready!"
        
        # Show column details
        print_status "$env_name column details:"
        psql "$db_url" -c "
            SELECT 
                column_name, 
                data_type, 
                is_nullable,
                column_default
            FROM information_schema.columns 
            WHERE table_name = 'Feedback' AND column_name = 'rating';
        "
    else
        print_error "$env_name: Rating column not found!"
        return 1
    fi
}

# Main execution
main() {
    echo "🗄️  CareerDart Database Migration"
    echo "=================================="
    echo "Adding rating column to Feedback table"
    echo ""
    
    # Check if psql is available
    if ! command -v psql &> /dev/null; then
        print_error "PostgreSQL client (psql) not found. Please install it first."
        echo "  macOS: brew install postgresql"
        echo "  Ubuntu: sudo apt-get install postgresql-client"
        exit 1
    fi
    
    # Migrate Development Database
    echo "🔧 DEVELOPMENT ENVIRONMENT"
    echo "=========================="
    
    if test_connection "$DEV_DB_URL" "DEV"; then
        run_migration "$DEV_DB_URL" "DEV"
        verify_migration "$DEV_DB_URL" "DEV"
    else
        print_warning "Skipping DEV migration due to connection failure"
        print_status "Make sure your local PostgreSQL is running and the database exists"
    fi
    
    echo ""
    
    # Migrate Production Database
    echo "🚀 PRODUCTION ENVIRONMENT"
    echo "========================="
    
    print_warning "About to migrate PRODUCTION database!"
    read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if test_connection "$PROD_DB_URL" "PROD"; then
            run_migration "$PROD_DB_URL" "PROD"
            verify_migration "$PROD_DB_URL" "PROD"
        else
            print_error "PROD migration failed due to connection issues"
            exit 1
        fi
    else
        print_status "PROD migration skipped by user"
    fi
    
    echo ""
    echo "✅ Migration Summary"
    echo "==================="
    print_success "Feedback table now supports star ratings (1-5)!"
    print_status "New features available:"
    echo "  ⭐ Users can rate their experience"
    echo "  📊 Admin dashboard shows ratings"
    echo "  📧 Email notifications include ratings"
    echo "  🎯 Better user satisfaction tracking"
    echo ""
    print_status "Next steps:"
    echo "  1. Restart your development server"
    echo "  2. Test the feedback system with ratings"
    echo "  3. Check admin dashboard at /admin/feedback"
    echo "  4. Deploy updated code to production"
}

# Run main function
main "$@"
