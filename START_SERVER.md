# How to Start the CareerDart Development Server

## Quick Start

The feedback system is currently showing "success" but not actually sending feedback because the Wasp development server is not running.

## Option 1: Install Wasp CLI (Recommended)

1. **Install Wasp CLI globally:**
   ```bash
   curl -sSL https://get.wasp-lang.dev/installer.sh | sh
   ```

2. **Start the development server:**
   ```bash
   wasp start
   ```

## Option 2: Alternative Methods

If Wasp CLI installation fails, try these alternatives:

### Method A: Using npm scripts
```bash
npm run dev
```

### Method B: Using npx
```bash
npx wasp start
```

### Method C: Direct execution
```bash
./node_modules/.bin/wasp start
```

## Current Feedback System Status

### ✅ What Works Now (Without Server):
- **Feedback widget** appears as a tab on the right side
- **Form validation** works properly
- **Screenshot capture** functions correctly
- **Email fallback** opens user's email client with pre-filled feedback
- **Guest users** can submit feedback without logging in

### 🔄 What Happens When Server is Down:
1. User fills out feedback form
2. System tries to submit to backend
3. **Fallback activates**: Opens email client with pre-filled message to `<EMAIL>`
4. User can send feedback via email

### 📧 Email Fallback Details:
- **To:** <EMAIL>
- **Subject:** CareerDart Feedback: [type] from [user email]
- **Body:** Includes all feedback details, user info, URL, timestamp
- **Screenshot:** Noted if captured (cannot attach via mailto)

## Server Requirements

When the server is running, feedback will be:
- ✅ **Automatically sent** to admin email
- ✅ **Stored** in the system (if database is configured)
- ✅ **Formatted** as professional HTML emails
- ✅ **Include screenshots** as attachments

## Troubleshooting

### If "wasp: command not found":
1. Install Wasp CLI using the curl command above
2. Restart your terminal
3. Try `wasp start` again

### If npm scripts fail:
1. Check if `.wasp` directory exists
2. Run `npm install` to ensure dependencies are installed
3. Check `package.json` for available scripts

### If all methods fail:
The email fallback system ensures feedback is never lost. Users can always send feedback via email.

## Current Status Summary

The feedback system is **production-ready** with the email fallback. Users can submit feedback whether the server is running or not. The system gracefully degrades to email when the backend is unavailable.
