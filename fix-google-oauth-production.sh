#!/bin/bash

# Google OAuth Production Fix Script for CareerDart
# This script helps fix Google OAuth issues in production

echo "🔧 CareerDart Google OAuth Production Fix"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Step 1: Checking current Fly.io configuration...${NC}"
echo "Current secrets in careerdart-prod:"
fly secrets list -a careerdart-prod

echo -e "\n${BLUE}Step 2: Setting correct backend environment variables...${NC}"
echo "Setting WASP URLs and Google OAuth credentials..."

# Prompt for Google OAuth credentials
echo -e "${YELLOW}Please enter your Google OAuth credentials:${NC}"
read -p "Google Client ID: " GOOGLE_CLIENT_ID
read -s -p "Google Client Secret: " GOOGLE_CLIENT_SECRET
echo

# Set backend environment variables
echo "Setting backend environment variables..."
fly secrets set \
  WASP_WEB_CLIENT_URL=https://careerdart.com \
  WASP_SERVER_URL=https://api.careerdart.com \
  GOOGLE_CLIENT_ID="$GOOGLE_CLIENT_ID" \
  GOOGLE_CLIENT_SECRET="$GOOGLE_CLIENT_SECRET" \
  NODE_ENV=production \
  -a careerdart-prod

echo -e "${GREEN}✅ Backend environment variables set!${NC}"

echo -e "\n${BLUE}Step 3: Checking domain resolution...${NC}"
echo "Checking if api.careerdart.com points to Fly.io..."
nslookup api.careerdart.com

echo -e "\n${BLUE}Step 4: Verifying SSL certificates...${NC}"
echo "Checking SSL certificate for api.careerdart.com..."
fly certs list -a careerdart-prod

echo -e "\n${BLUE}Step 5: Testing API endpoint...${NC}"
echo "Testing if backend is accessible..."
curl -I https://api.careerdart.com/auth/me

echo -e "\n${YELLOW}Manual Steps Required:${NC}"
echo "1. Update Google Cloud Console:"
echo "   - Go to: https://console.cloud.google.com/apis/credentials"
echo "   - Edit your OAuth 2.0 Client ID"
echo "   - Set Authorized JavaScript origins: https://careerdart.com"
echo "   - Set Authorized redirect URIs: https://api.careerdart.com/auth/google/callback"
echo ""
echo "2. Update Netlify Environment Variables:"
echo "   - Go to: Netlify Dashboard > Site Settings > Environment Variables"
echo "   - Set REACT_APP_API_URL=https://api.careerdart.com"
echo "   - Trigger a new deployment"
echo ""
echo "3. Deploy backend changes:"
echo "   fly deploy -a careerdart-prod"

echo -e "\n${GREEN}🎯 Next Steps:${NC}"
echo "1. Complete the manual steps above"
echo "2. Test Google OAuth at: https://careerdart.com/login"
echo "3. Check browser console for any remaining errors"

echo -e "\n${BLUE}Troubleshooting:${NC}"
echo "If issues persist, check:"
echo "- Google Cloud Console OAuth configuration"
echo "- Netlify environment variables"
echo "- Browser network tab for redirect URLs"
echo "- Fly.io logs: fly logs -a careerdart-prod"
