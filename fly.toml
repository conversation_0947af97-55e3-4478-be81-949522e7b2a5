# fly.toml app configuration file generated for careerdart-prod on 2025-06-01T18:16:25-04:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = "careerdart-prod"
primary_region = "iad"

[build]
  dockerfile = "Dockerfile.production"

[env]
  NODE_ENV = "production"
  PORT = "8080"

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0

[[vm]]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 512
