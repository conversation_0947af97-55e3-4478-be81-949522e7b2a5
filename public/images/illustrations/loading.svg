<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="300" fill="#f8fafc"/>
  <circle cx="200" cy="150" r="30" stroke="#6366f1" stroke-width="4" fill="none" stroke-dasharray="47" stroke-dashoffset="47">
    <animateTransform attributeName="transform" type="rotate" values="0 200 150;360 200 150" dur="1s" repeatCount="indefinite"/>
  </circle>
  <circle cx="200" cy="150" r="15" fill="#6366f1" opacity="0.3"/>
  <text x="200" y="200" font-family="Arial, sans-serif" font-size="14" fill="#6b7280" text-anchor="middle">Loading...</text>
  <circle cx="120" cy="100" r="6" fill="#10b981" opacity="0.4"/>
  <circle cx="300" cy="80" r="8" fill="#fbbf24" opacity="0.4"/>
  <circle cx="80" cy="220" r="10" fill="#ef4444" opacity="0.3"/>
</svg>