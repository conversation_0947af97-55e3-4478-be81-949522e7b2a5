<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="theme-color" content="#3182CE" />
  
  <!-- Primary Meta Tags -->
  <title>CareerDart - AI-Powered Career Tools | Cover Letters, Resumes & Job Search</title>
  <meta name="title" content="CareerDart - AI-Powered Career Tools | Cover Letters, Resumes & Job Search" />
  <meta name="description" content="Transform your job search with AI-powered tools for creating professional cover letters, optimizing resumes, and managing job applications. Get hired faster with CareerDart's intelligent career platform." />
  <meta name="keywords" content="AI cover letter generator, resume optimization, job search tools, career development, interview preparation, job application tracker, professional resume builder, career coaching, job search platform" />
  <meta name="author" content="CareerDart" />
  <meta name="robots" content="index, follow" />
  <meta name="language" content="English" />
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://careerdart.com/" />
  <meta property="og:title" content="CareerDart - AI-Powered Career Tools" />
  <meta property="og:description" content="Transform your job search with AI-powered tools for creating professional cover letters, optimizing resumes, and managing job applications." />
  <meta property="og:image" content="https://careerdart.com/images/og-image.png" />
  <meta property="og:site_name" content="CareerDart" />
  <meta property="og:locale" content="en_US" />
  
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content="https://careerdart.com/" />
  <meta property="twitter:title" content="CareerDart - AI-Powered Career Tools" />
  <meta property="twitter:description" content="Transform your job search with AI-powered tools for creating professional cover letters, optimizing resumes, and managing job applications." />
  <meta property="twitter:image" content="https://careerdart.com/images/twitter-image.png" />
  <meta property="twitter:creator" content="@careerdart" />
  
  <!-- LinkedIn -->
  <meta property="article:author" content="CareerDart" />
  <meta property="article:publisher" content="https://www.linkedin.com/company/careerdart" />
  
  <!-- Canonical URL -->
  <link rel="canonical" href="https://careerdart.com/" />
  
  <!-- Alternate URLs for different languages (if applicable) -->
  <link rel="alternate" hreflang="en" href="https://careerdart.com/" />
  
  <!-- Preconnect to external domains for performance -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="dns-prefetch" href="//fonts.googleapis.com">
  <link rel="dns-prefetch" href="//fonts.gstatic.com">
  
  <!-- Structured Data (JSON-LD) -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "CareerDart",
    "description": "AI-powered career tools for creating professional cover letters, optimizing resumes, and managing job applications",
    "url": "https://careerdart.com",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "category": "Career Development Tools"
    },
    "publisher": {
      "@type": "Organization",
      "name": "CareerDart",
      "url": "https://careerdart.com",
      "logo": {
        "@type": "ImageObject",
        "url": "https://careerdart.com/images/logo.png"
      }
    },
    "featureList": [
      "AI Cover Letter Generator",
      "Resume Optimization",
      "Job Search Management",
      "Interview Preparation",
      "Career Tracking",
      "Professional Development"
    ],
    "screenshot": "https://careerdart.com/images/app-screenshot.png"
  }
  </script>
  
  <!-- Critical CSS inlined for immediate rendering -->
  <style data-critical="true">
    /* Critical above-the-fold styles */
    *, *::before, *::after {
      box-sizing: border-box;
    }
    
    html {
      line-height: 1.15;
      -webkit-text-size-adjust: 100%;
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    }
    
    body {
      margin: 0;
      padding: 0;
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      line-height: 1.5;
      color: #1A202C;
      background-color: #FFFFFF;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    #root {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    /* Loading skeleton for better perceived performance */
    .loading-skeleton {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
      border-radius: 4px;
    }
    
    @keyframes loading {
      0% { background-position: 200% 0; }
      100% { background-position: -200% 0; }
    }
    
    /* Immediate visual feedback */
    .app-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      background: #ffffff;
    }
    
    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3182CE;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* Optimize rendering performance */
    img {
      max-width: 100%;
      height: auto;
    }
    
    /* Reduce motion for users who prefer it */
    @media (prefers-reduced-motion: reduce) {
      *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    }
    
    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
      body {
        background-color: #1A202C;
        color: #FFFFFF;
      }
    }
  </style>
  
  <!-- Preload critical fonts with font-display: swap -->
  <link rel="preload" as="font" type="font/woff2" 
        href="https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2" 
        crossorigin="anonymous">
  
  <!-- Load fonts with display: swap for better performance -->
  <link rel="preload" as="style"
        href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
        onload="this.onload=null;this.rel='stylesheet'">

  <!-- Fallback for font loading -->
  <noscript>
    <link rel="stylesheet"
          href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
  </noscript>
</head>
<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  
  <!-- Initial loading state -->
  <div id="root">
    <div class="app-loading">
      <div class="spinner"></div>
    </div>
  </div>
  
  <!-- Script to optimize initial loading -->
  <script>
    // Performance optimization: mark when HTML is parsed
    if (window.performance && window.performance.mark) {
      window.performance.mark('html-parsed');
    }
    
    // Optimize font loading
    if ('fonts' in document) {
      document.fonts.ready.then(() => {
        document.body.classList.add('fonts-loaded');
      });
    }
    
    // Service worker registration for caching (if available)
    if ('serviceWorker' in navigator && window.location.protocol === 'https:') {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js').catch(() => {
          // Fail silently if service worker is not available
        });
      });
    }
  </script>
</body>
</html> 