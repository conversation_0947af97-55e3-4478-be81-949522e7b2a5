# Netlify Development Environment Configuration
# For dev.careerdart.com

[build]
  publish = "netlify-dev-build"
  command = "echo 'Using prebuilt files from netlify-dev-build directory'"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "8"
  NODE_ENV = "development"
  REACT_APP_ENV = "development"
  REACT_APP_API_URL = "https://careerdart-dev.fly.dev"
  REACT_APP_ENABLE_ANALYTICS = "false"
  REACT_APP_ENABLE_DEBUG = "true"
  GENERATE_SOURCEMAP = "true"

# Security Headers (relaxed for development)
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "SAMEORIGIN"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    X-Development-Environment = "true"

# Static asset caching (shorter for development)
[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=3600, immutable"

# API redirects to development backend (MUST BE FIRST for proper routing)
[[redirects]]
  from = "/api/*"
  to = "https://careerdart-dev.fly.dev/api/:splat"
  status = 200
  force = true
  headers = {X-From = "Netlify-Dev"}

[[redirects]]
  from = "/auth/*"
  to = "https://careerdart-dev.fly.dev/auth/:splat"
  status = 200
  force = true
  headers = {X-From = "Netlify-Dev"}

[[redirects]]
  from = "/operations/*"
  to = "https://careerdart-dev.fly.dev/operations/:splat"
  status = 200
  force = true
  headers = {X-From = "Netlify-Dev"}

# Development debugging endpoints
[[redirects]]
  from = "/debug/*"
  to = "https://careerdart-dev.fly.dev/debug/:splat"
  status = 200
  force = true
  headers = {X-From = "Netlify-Dev"}

# SPA routing (must be last)
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Development-specific edge functions or redirects
[context.branch-deploy.environment]
  REACT_APP_BRANCH_DEPLOY = "true"

# Development environment variables for branch deploys
[context.branch-deploy]
  command = "echo 'Branch deploy for development'" 