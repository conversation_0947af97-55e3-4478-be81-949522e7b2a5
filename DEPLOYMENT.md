# 🚀 Deployment Guide

## ✅ Working Solution: Netlify + Fly.io

This project successfully deploys using:
- **Frontend**: Netlify (static React build)
- **Backend**: Fly.io (containerized Node.js + PostgreSQL)

## 🎯 Key Learning

**The solution was simple**: Use <PERSON><PERSON>'s original build exactly as-is.

❌ **What didn't work**: Custom QueryClient setups, complex build scripts, overriding <PERSON><PERSON>'s internals
✅ **What works**: Let <PERSON><PERSON> handle everything - it works perfectly!

## 📋 Deploy to Netlify

```bash
# Simple one-command deployment
bash scripts/deploy-to-netlify.sh

# Then commit and push
git add netlify-client-build/
git commit -m "Deploy to Netlify"
git push
```

## 🌐 Custom Domain Setup (Optional)

### **Recommended Architecture:**
- **Frontend**: `https://careerdart.com` (Netlify)  
- **Backend**: `https://api.careerdart.com` (Fly.io)

### **Why `api.careerdart.com`?**
- ✅ Standard industry convention
- ✅ Clear purpose identification
- ✅ SEO and developer friendly
- ✅ Professional appearance (no more `.fly.dev` domains)

### **Step 1: Add Custom Domain to Fly.io**
```bash
# Add custom domain certificate
fly certs add api.careerdart.com --app careerdart-prod

# Check certificate status
fly certs show api.careerdart.com --app careerdart-prod
```

### **Step 2: Update DNS Records**
In your domain registrar, add:
```
CNAME api.careerdart.com -> careerdart-prod.fly.dev
```

### **Step 3: Update Backend Environment Variables**
```bash
# Update server URL to use custom domain
fly secrets set WASP_SERVER_URL=https://api.careerdart.com --app careerdart-prod

# Restart app to pick up changes
fly apps restart careerdart-prod
```

### **Step 4: Update Netlify Configuration**
Update `netlify.toml` redirects:
```toml
# API proxy redirects to custom domain
[[redirects]]
  from = "/api/*"
  to = "https://api.careerdart.com/api/:splat"
  status = 200
  force = true

[[redirects]]
  from = "/auth/*"
  to = "https://api.careerdart.com/auth/:splat"
  status = 200
  force = true

[[redirects]]
  from = "/operations/*"
  to = "https://api.careerdart.com/operations/:splat"
  status = 200
  force = true
```

### **Step 5: Deploy Updated Configuration**
```bash
# Deploy Netlify changes
bash scripts/deploy-to-netlify.sh
git add netlify-client-build/ netlify.toml
git commit -m "Update to use custom API domain"
git push
```

## 🛠️ Backend (Already deployed)

Backend is running on Fly.io at: `https://careerdart-prod.fly.dev`  
(Or `https://api.careerdart.com` if custom domain is configured)

## 🎉 Result

- ✅ Frontend: https://careerdart.netlify.app  
- ✅ Backend: https://careerdart-prod.fly.dev
- ✅ 100% Working with zero custom modifications

## 🔧 Troubleshooting

### **Google OAuth Domain Issue**
If Google OAuth shows wrong domain, update backend environment:
```bash
fly secrets set WASP_WEB_CLIENT_URL=https://careerdart.com --app careerdart-prod
fly apps restart careerdart-prod
```

### **Backend Showing "Hello World"**
Check deployment status:
```bash
fly status --app careerdart-prod
fly logs --app careerdart-prod
```

## 💡 Lesson Learned

Sometimes the best solution is the simplest one. Wasp's build system works perfectly when left alone! 