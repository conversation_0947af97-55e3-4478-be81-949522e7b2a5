-- Add rating column to Feedback table
-- This migration adds the rating field for the star rating system

-- Add the rating column
ALTER TABLE "Feedback" ADD COLUMN "rating" INTEGER;

-- Add a check constraint to ensure rating is between 1 and 5
ALTER TABLE "Feedback" ADD CONSTRAINT "Feedback_rating_check" CHECK ("rating" >= 1 AND "rating" <= 5);

-- Create an index for better performance when filtering by rating
CREATE INDEX "Feedback_rating_idx" ON "Feedback"("rating");

-- Update the comment for the type field to include 'rating'
COMMENT ON COLUMN "Feedback"."type" IS 'Feedback type: rating, bug, feature, improvement, general, other';
