# Lighthouse Performance Monitoring Guide

## Overview

The CareerDart application includes a Lighthouse performance monitoring system that generates detailed performance reports and displays them in the admin dashboard.

## Current Status

✅ **Sample Report Available**: A sample Lighthouse report has been generated and is available in `lighthouse-report.json`  
✅ **Admin Dashboard Integration**: The admin dashboard can now display Lighthouse metrics  
✅ **Automated Report Script**: A script for generating production reports is available  

## How It Works

### 1. Report Generation

The application looks for a Lighthouse report file in the following locations:
- `./lighthouse-report.json` (root directory)
- `./.wasp/out/server/lighthouse-report.json`
- `./.wasp/lighthouse-report.json`

### 2. Admin Dashboard Display

When an admin user accesses the admin dashboard, the system:
1. Attempts to load the Lighthouse report from the available locations
2. Displays performance metrics including:
   - Performance Score (0-100)
   - Accessibility Score (0-100)
   - Best Practices Score (0-100)
   - SEO Score (0-100)
   - Core Web Vitals (FCP, LCP, CLS, etc.)

## Usage Instructions

### For Development (Current Setup)

A sample report with realistic performance data is already available:

```bash
# The sample report shows:
# - Performance: 85%
# - Accessibility: 92%
# - Best Practices: 88%
# - SEO: 94%
```

### For Production Deployment

#### Option 1: Manual Generation

```bash
# Install Lighthouse globally (if not already installed)
npm install -g lighthouse

# Generate report for your production domain
lighthouse https://your-domain.com --output=json --output-path=./lighthouse-report.json
```

#### Option 2: Using the Provided Script

```bash
# Make the script executable (already done)
chmod +x scripts/generate-lighthouse-report.sh

# Generate report for production
./scripts/generate-lighthouse-report.sh https://your-domain.com

# Generate report for staging
./scripts/generate-lighthouse-report.sh https://staging.your-domain.com
```

#### Option 3: Automated CI/CD Integration

Add to your deployment pipeline:

```yaml
# Example GitHub Actions workflow
- name: Generate Lighthouse Report
  run: |
    npm install -g lighthouse
    lighthouse https://your-domain.com --output=json --output-path=./lighthouse-report.json
    
- name: Upload Report
  run: |
    # Upload lighthouse-report.json to your server
    scp lighthouse-report.json user@server:/path/to/app/
```

### For Local Development with Public Access

If you want to test Lighthouse on your local development server, you need to make it publicly accessible:

#### Using ngrok:

```bash
# Install ngrok
brew install ngrok  # or download from ngrok.com

# Start your Wasp server
wasp start

# In another terminal, expose port 3002
ngrok http 3002

# Use the ngrok URL for Lighthouse
lighthouse https://abc123.ngrok.io --output=json --output-path=./lighthouse-report.json
```

## Sample Metrics Explanation

The sample report includes realistic performance metrics:

### Performance Scores
- **Performance: 85%** - Good overall performance
- **Accessibility: 92%** - Excellent accessibility compliance
- **Best Practices: 88%** - Good adherence to web best practices
- **SEO: 94%** - Excellent search engine optimization

### Core Web Vitals
- **First Contentful Paint (FCP): 1.2s** - Time for first content to appear
- **Largest Contentful Paint (LCP): 2.1s** - Time for largest content to load
- **Speed Index: 1.8s** - How quickly content is visually populated
- **Time to Interactive (TTI): 2.3s** - Time until page is fully interactive
- **Total Blocking Time (TBT): 150ms** - Time main thread is blocked
- **Cumulative Layout Shift (CLS): 0.05** - Visual stability metric

## Troubleshooting

### "Lighthouse report not available" Error

This error occurs when:
1. No `lighthouse-report.json` file exists in the expected locations
2. The file exists but is corrupted or invalid JSON
3. The file has wrong permissions

**Solutions:**
1. Run `./scripts/generate-lighthouse-report.sh` to create a new report
2. Check file permissions: `chmod 644 lighthouse-report.json`
3. Validate JSON syntax: `cat lighthouse-report.json | jq .`

### Chrome Interstitial Errors (Local Development)

When running Lighthouse on localhost, you might see Chrome security errors:

**Solutions:**
1. Use the sample report for development (recommended)
2. Use ngrok or similar to create a public tunnel
3. Deploy to a staging environment for real testing

### Report Not Updating in Dashboard

**Solutions:**
1. Restart the Wasp server after generating a new report
2. Clear browser cache
3. Check server logs for file access issues

## Best Practices

### Regular Monitoring
- Generate reports after each deployment
- Set up alerts for performance degradation
- Track metrics over time

### Performance Targets
- **Performance Score**: > 90%
- **LCP**: < 2.5s
- **FID**: < 100ms
- **CLS**: < 0.1

### Automation
- Include Lighthouse in CI/CD pipeline
- Generate reports for both staging and production
- Store historical reports for trend analysis

## Next Steps

1. **Production Deployment**: Replace the sample report with real data from your deployed application
2. **Automated Monitoring**: Set up regular report generation
3. **Performance Optimization**: Use the insights to improve application performance
4. **Historical Tracking**: Store multiple reports to track performance trends over time

## Files Created/Modified

- ✅ `lighthouse-report.json` - Sample performance report
- ✅ `scripts/generate-lighthouse-report.sh` - Report generation script
- ✅ `LIGHTHOUSE_GUIDE.md` - This documentation

The Lighthouse reporting system is now ready to use! The admin dashboard will display the current sample metrics, and you can generate real reports when deploying to production. 