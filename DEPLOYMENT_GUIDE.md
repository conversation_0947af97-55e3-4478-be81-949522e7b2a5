# CareerDart Deployment Guide

This guide will help you deploy your Wasp application to production. Since Wasp applications have both frontend and backend components, we'll deploy them separately:

- **Frontend (React)**: Deploy to Netlify
- **Backend (Node.js API)**: Deploy to Render or Railway

## Prerequisites

- Git repository (GitHub, GitLab, or Bitbucket)
- Netlify account
- Render or Railway account
- Database (PostgreSQL) - we'll use Render's managed database

## Step 1: Prepare Your Repository

Make sure your code is committed to a Git repository:

```bash
git add .
git commit -m "Prepare for deployment"
git push origin main
```

## Step 2: Deploy Backend to Render

### 2.1 Create Database on Render

1. Go to [Render Dashboard](https://dashboard.render.com/)
2. Click "New" → "PostgreSQL"
3. Configure your database:
   - **Name**: `careerdart-db`
   - **Database**: `careerdart`
   - **User**: `careerdart`
   - **Region**: Choose closest to your users
   - **Plan**: Free (for testing) or Starter (for production)
4. Click "Create Database"
5. Note down the connection details (you'll need them for environment variables)

### 2.2 Deploy Backend Service

1. In Render Dashboard, click "New" → "Web Service"
2. Connect your Git repository
3. Configure the service:
   - **Name**: `careerdart-api`
   - **Environment**: `Node`
   - **Region**: Same as your database
   - **Branch**: `main`
   - **Root Directory**: `.wasp/build/server`
   - **Build Command**: `npm install && npm run bundle`
   - **Start Command**: `npm start`

### 2.3 Set Environment Variables

In your Render web service settings, add these environment variables:

```env
# Database
DATABASE_URL=<your-render-postgres-connection-string>

# Auth
JWT_SECRET=<generate-a-strong-random-string>

# Email (using Resend)
RESEND_API_KEY=<your-resend-api-key>

# Google OAuth (optional)
GOOGLE_CLIENT_ID=<your-google-client-id>
GOOGLE_CLIENT_SECRET=<your-google-client-secret>

# Stripe (for payments)
STRIPE_KEY=<your-stripe-secret-key>
STRIPE_WEBHOOK_SECRET=<your-stripe-webhook-secret>

# App URLs
WASP_WEB_CLIENT_URL=https://your-netlify-site.netlify.app
WASP_SERVER_URL=https://careerdart-api.onrender.com

# Node Environment
NODE_ENV=production
```

### 2.4 Get Your Backend URL

Once deployed, your backend will be available at:
`https://careerdart-api.onrender.com`

## Step 3: Deploy Frontend to Netlify

### 3.1 Update Netlify Configuration

Update the `netlify.toml` file with your backend URL:

```toml
# Replace 'your-backend-url.onrender.com' with your actual Render URL
[[redirects]]
  from = "/api/*"
  to = "https://careerdart-api.onrender.com/api/:splat"
  status = 200
  force = false

[[redirects]]
  from = "/auth/*"
  to = "https://careerdart-api.onrender.com/auth/:splat"
  status = 200
  force = false

[[redirects]]
  from = "/operations/*"
  to = "https://careerdart-api.onrender.com/operations/:splat"
  status = 200
  force = false
```

### 3.2 Create Environment File for Frontend

Create `.env.client` file in your root directory:

```env
# Backend API URL
REACT_APP_API_URL=https://careerdart-api.onrender.com

# Stripe Public Key
REACT_APP_STRIPE_PUBLISHABLE_KEY=<your-stripe-publishable-key>

# Google Analytics (optional)
REACT_APP_GA_MEASUREMENT_ID=<your-ga-measurement-id>

# Environment
NODE_ENV=production
```

### 3.3 Deploy to Netlify

1. Go to [Netlify](https://app.netlify.com/)
2. Click "Add new site" → "Import an existing project"
3. Connect your Git repository
4. Configure build settings:
   - **Build command**: `wasp build && cd .wasp/build/web-app && npm install --save-dev terser && npm run build`
   - **Publish directory**: `.wasp/build/web-app/build`
   - **Environment variables**: Add the variables from `.env.client`

### 3.4 Set Up Custom Domain (Optional)

1. In Netlify site settings, go to "Domain management"
2. Add your custom domain
3. Configure DNS records as instructed
4. Enable HTTPS (automatic with Let's Encrypt)

## Step 4: Database Migration

After your backend is deployed, run database migrations:

1. In your Render service dashboard, go to "Shell"
2. Run: `npm run db:migrate:prod`

Or connect to your database directly and run the migration SQL.

## Step 5: Configure Webhooks

### Stripe Webhooks

1. In Stripe Dashboard, go to "Developers" → "Webhooks"
2. Add endpoint: `https://careerdart-api.onrender.com/stripe-webhook`
3. Select events: `invoice.payment_succeeded`, `customer.subscription.updated`, etc.
4. Update your `STRIPE_WEBHOOK_SECRET` environment variable

## Step 6: Verification

1. Visit your Netlify URL
2. Test user registration and login
3. Test payment functionality
4. Check admin dashboard functionality
5. Monitor logs for any errors

## Environment Variables Summary

### Backend (Render)
- `DATABASE_URL` - PostgreSQL connection string
- `JWT_SECRET` - Random string for JWT signing
- `RESEND_API_KEY` - Email service API key
- `GOOGLE_CLIENT_ID` & `GOOGLE_CLIENT_SECRET` - OAuth
- `STRIPE_KEY` & `STRIPE_WEBHOOK_SECRET` - Payment processing
- `WASP_WEB_CLIENT_URL` - Your Netlify URL
- `WASP_SERVER_URL` - Your Render backend URL

### Frontend (Netlify)
- `REACT_APP_API_URL` - Backend API URL
- `REACT_APP_STRIPE_PUBLISHABLE_KEY` - Stripe public key
- `REACT_APP_GA_MEASUREMENT_ID` - Google Analytics

## Troubleshooting

### Common Issues

1. **Build failures**: Check Node.js version compatibility
2. **Database connection**: Verify DATABASE_URL format
3. **CORS errors**: Ensure WASP_WEB_CLIENT_URL matches your Netlify URL
4. **Admin access**: Verify admin email in `isAdmin` function

### Monitoring

- **Backend logs**: Available in Render dashboard
- **Frontend errors**: Check browser console and Netlify function logs
- **Database**: Monitor performance in Render database dashboard

## Production Optimizations

1. **Enable gzip compression** (automatic on Netlify)
2. **Set up CDN** (automatic on Netlify)
3. **Database connection pooling** (configure in Render)
4. **Monitor performance** with tools like Sentry or LogRocket
5. **Set up alerts** for downtime and errors

## Security Checklist

- [ ] All environment variables are secure
- [ ] Database has strong credentials
- [ ] JWT_SECRET is complex and unique
- [ ] HTTPS is enabled on both frontend and backend
- [ ] CORS is properly configured
- [ ] Rate limiting is enabled (consider adding)
- [ ] Input validation is implemented
- [ ] SQL injection protection (Prisma provides this)

## Scaling Considerations

- **Database**: Upgrade to higher tier as needed
- **Backend**: Render auto-scales within plan limits
- **Frontend**: Netlify handles CDN and scaling automatically
- **File storage**: Consider AWS S3 or Cloudinary for user uploads

Your application should now be live and accessible to users worldwide! 🚀 