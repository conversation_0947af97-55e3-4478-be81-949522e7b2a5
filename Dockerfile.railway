# Railway-specific Dockerfile for Wasp backend
FROM node:18-alpine

# Install system dependencies
RUN apk add --no-cache python3 make g++ openssl

# Set working directory
WORKDIR /app

# Copy the built Wasp project
COPY .wasp/build/server ./server
COPY .wasp/out/sdk ./sdk

# Set working directory to server
WORKDIR /app/server

# Install dependencies
RUN npm install

# Copy database schema
COPY schema.prisma ./prisma/schema.prisma

# Generate Prisma client
RUN npx prisma generate

# Expose port
EXPOSE 8080

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080

# Start the server
CMD ["npm", "run", "start-production"] 