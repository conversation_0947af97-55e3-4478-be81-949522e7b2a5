# CareerDart Full Deployment Guide

Complete step-by-step guide for deploying the CareerDart Wasp application with Fly.io (Backend + Database), Netlify (Frontend), and RESEND (Email Services).

## 📋 Table of Contents

- [Prerequisites](#prerequisites)
- [Architecture Overview](#architecture-overview)
- [Part 1: Fly.io Backend Setup](#part-1-flyio-backend-setup)
- [Part 2: Database Configuration](#part-2-database-configuration)
- [Part 3: RESEND Email Setup](#part-3-resend-email-setup)
- [Part 4: Google OAuth Setup](#part-4-google-oauth-setup)
- [Part 5: Backend Deployment](#part-5-backend-deployment)
- [Part 6: Netlify Frontend Setup](#part-6-netlify-frontend-setup)
- [Part 7: Custom Domain Configuration](#part-7-custom-domain-configuration)
- [Part 8: Testing & Verification](#part-8-testing--verification)
- [Part 9: Maintenance & Updates](#part-9-maintenance--updates)
- [Troubleshooting](#troubleshooting)

---

## 🛠️ Prerequisites

### Required Tools
```bash
# Install Fly CLI
curl -L https://fly.io/install.sh | sh

# Install Wasp
curl -sSL https://get.wasp.sh/installer.sh | sh -s -- -v 0.15.0

# Install Netlify CLI (optional but recommended)
npm install -g netlify-cli

# Verify installations
fly version
wasp version
netlify --version
```

### Required Accounts
- **Fly.io account** (free tier available) - https://fly.io/
- **Netlify account** (free tier available) - https://netlify.com/
- **Google Cloud Console** (for OAuth) - https://console.cloud.google.com/
- **RESEND account** (for email services) - https://resend.com/
- **Domain registrar access** (for DNS configuration)
- **GitHub account** (for code repository)

---

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │     Backend      │    │    Database     │
│   (Netlify)     │◄──►│    (Fly.io)      │◄──►│  (Fly.io PG)    │
│ careerdart.com  │    │ api.careerdart.com│    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │
         │              ┌──────────────────┐
         │              │   Email Service  │
         └──────────────►│    (RESEND)      │
                        │                  │
                        └──────────────────┘
```

**Flow:**
1. **Frontend (Netlify)**: Serves the React client application
2. **Backend (Fly.io)**: Wasp server with API endpoints and authentication
3. **Database (Fly.io PostgreSQL)**: Stores user data, jobs, cover letters
4. **Email (RESEND)**: Handles email verification, password resets, notifications

---

## 🚀 Part 1: Fly.io Backend Setup

### 1.1. Fly.io Authentication
```bash
# Login to Fly.io
fly auth login

# Verify authentication
fly auth whoami
```

### 1.2. Prepare Application
```bash
# Navigate to your project directory
cd /path/to/careerdart

# Build the application
wasp build

# Verify build completed successfully
ls .wasp/build/
```

### 1.3. Create Fly Application
```bash
# Create new Fly app (replace with your desired name)
fly apps create careerdart-prod --region iad

# Verify app creation
fly apps list
```

### 1.4. Create Production Dockerfile
Create `Dockerfile.production` in your project root:

```dockerfile
FROM node:18.18.0-alpine3.17 AS node

FROM node AS base
RUN apk --no-cache -U upgrade

FROM base AS server-builder
RUN apk add --no-cache python3 build-base libtool autoconf automake
WORKDIR /app
COPY src ./src
COPY package.json .
COPY package-lock.json .
COPY .wasp/build/server .wasp/build/server
COPY .wasp/out/sdk .wasp/out/sdk
RUN npm install && cd .wasp/build/server && npm install
COPY .wasp/build/db/schema.prisma .wasp/build/db/
RUN cd .wasp/build/server && npx prisma generate --schema='../db/schema.prisma'
RUN cd .wasp/build/server && npm run bundle

FROM base AS server-production
RUN apk add --no-cache python3
ENV NODE_ENV production
WORKDIR /app
COPY --from=server-builder /app/node_modules ./node_modules
COPY --from=server-builder /app/.wasp/out/sdk .wasp/out/sdk
COPY --from=server-builder /app/.wasp/build/server/node_modules .wasp/build/server/node_modules
COPY --from=server-builder /app/.wasp/build/server/bundle .wasp/build/server/bundle
COPY --from=server-builder /app/.wasp/build/server/package*.json .wasp/build/server/
COPY --from=server-builder /app/.wasp/build/server/scripts .wasp/build/server/scripts
COPY .wasp/build/db/ .wasp/build/db/
EXPOSE 8080
WORKDIR /app/.wasp/build/server
ENTRYPOINT ["npm", "run", "start-production"]
```

### 1.5. Configure fly.toml
Create `fly.toml` in your project root:

```toml
app = "careerdart-prod"
primary_region = "iad"

[build]
  dockerfile = "Dockerfile.production"

[env]
  NODE_ENV = "production"
  PORT = "8080"

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0

[[vm]]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 512
```

---

## 🗄️ Part 2: Database Configuration

### 2.1. Create PostgreSQL Database
```bash
# Create database cluster
fly postgres create --name careerdart-db --region iad

# Follow the prompts:
# - Database name: careerdart-db
# - Region: iad (or your preferred region)
# - Configuration: Development (for testing) or Production

# Save the connection details provided
```

### 2.2. Attach Database to App
```bash
# Attach database to your app
fly postgres attach careerdart-db --app careerdart-prod

# This automatically sets DATABASE_URL environment variable
```

### 2.3. Verify Database Connection
```bash
# Check if DATABASE_URL was set
fly secrets list -a careerdart-prod

# Connect to database (optional verification)
fly postgres connect -a careerdart-db
```

---

## 📧 Part 3: RESEND Email Setup

### 3.1. Create RESEND Account
1. **Sign up**: Go to https://resend.com/
2. **Verify email**: Check your inbox and verify your account
3. **Get API key**: Navigate to API Keys section

### 3.2. Domain Verification in RESEND
1. **Add Domain**: 
   - Go to RESEND Dashboard → Domains
   - Click "Add Domain"
   - Enter your domain: `careerdart.com`

2. **Add DNS Records**: Add these TXT records to your domain DNS:

```dns
# SPF Record
Type: TXT
Name: @
Value: v=spf1 include:_spf.resend.com ~all

# DKIM Record (provided by RESEND)
Type: TXT
Name: resend._domainkey
Value: [Copy exact value from RESEND dashboard]

# DMARC Record
Type: TXT
Name: _dmarc
Value: v=DMARC1; p=none; rua=mailto:<EMAIL>
```

3. **Verify Domain**: Click "Verify" in RESEND dashboard

### 3.3. Configure Email in Application
Update `main.wasp`:

```wasp
emailSender: {
  provider: SMTP,
  defaultFrom: {
    name: "CareerDart",
    email: "<EMAIL>",
  },
},
```

### 3.4. Set Email Environment Variables
```bash
# Get your RESEND API key from dashboard
fly secrets set \
  RESEND_API_KEY=re_your-resend-api-key \
  SMTP_HOST=smtp.resend.com \
  SMTP_PORT=587 \
  SMTP_USERNAME=resend \
  SMTP_PASSWORD=re_your-resend-api-key \
  -a careerdart-prod
```

---

## 🔐 Part 4: Google OAuth Setup

### 4.1. Google Cloud Console Setup
1. **Create Project**:
   - Go to https://console.cloud.google.com/
   - Create new project or select existing
   - Name: "CareerDart"

2. **Enable APIs**:
   - Go to APIs & Services → Library
   - Search for "Google+ API" or "People API"
   - Click "Enable"

3. **Configure OAuth Consent Screen**:
   - Go to APIs & Services → OAuth consent screen
   - Choose "External" user type
   - Fill in application details:
     - App name: CareerDart
     - User support email: <EMAIL>
     - Developer contact: <EMAIL>

4. **Create OAuth 2.0 Client ID**:
   - Go to APIs & Services → Credentials
   - Click "Create Credentials" → "OAuth 2.0 Client IDs"
   - Application type: Web application
   - Name: CareerDart Production

5. **Configure URLs**:
   ```
   Authorized JavaScript origins:
   - https://careerdart.com
   - https://api.careerdart.com

   Authorized redirect URIs:
   - https://api.careerdart.com/auth/google/callback
   ```

6. **Save Credentials**: Copy Client ID and Client Secret

### 4.2. Set Google OAuth Environment Variables
```bash
fly secrets set \
  GOOGLE_CLIENT_ID=123456789-abcdef.apps.googleusercontent.com \
  GOOGLE_CLIENT_SECRET=GOCSPX-your-client-secret \
  -a careerdart-prod
```

---

## 🚀 Part 5: Backend Deployment

### 5.1. Set Core Environment Variables
```bash
# Core application settings (CRITICAL: Use production URLs)
fly secrets set \
  NODE_ENV=production \
  PORT=8080 \
  WASP_SERVER_URL=https://api.careerdart.com \
  WASP_WEB_CLIENT_URL=https://careerdart.com \
  -a careerdart-prod

# JWT Secret for email verification
fly secrets set JWT_SECRET=$(openssl rand -base64 32) -a careerdart-prod

# OpenAI API Key (get from https://platform.openai.com/api-keys)
fly secrets set OPENAI_API_KEY=sk-your-openai-api-key -a careerdart-prod
```

**⚠️ IMPORTANT:** The `WASP_WEB_CLIENT_URL` must be set to your production frontend URL (`https://careerdart.com`) for OAuth redirects to work correctly. If this is set to a development URL, users will be redirected to the wrong site after authentication.

### 5.2. Optional: Payment Integration
```bash
# Stripe configuration (if using payments)
fly secrets set \
  STRIPE_KEY=sk_live_your-stripe-secret-key \
  PRODUCT_PRICE_ID=price_your-product-price-id \
  PRODUCT_CREDITS_PRICE_ID=price_your-credits-price-id \
  -a careerdart-prod
```

### 5.3. Deploy Backend
```bash
# Build application
wasp build

# Deploy to Fly.io
fly deploy -a careerdart-prod

# Monitor deployment logs
fly logs -a careerdart-prod

# Check deployment status
fly status -a careerdart-prod
```

### 5.4. Run Database Migrations
```bash
# SSH into your app
fly ssh console -a careerdart-prod

# Inside the container, run migrations
cd .wasp/build/server
npm run db-migrate-prod

# Exit the container
exit
```

### 5.5. Add Custom Domain to Backend
```bash
# Add your API domain
fly certs add api.careerdart.com -a careerdart-prod

# Check certificate status
fly certs show api.careerdart.com -a careerdart-prod
```

---

## 🌐 Part 6: Netlify Frontend Setup

### 6.1. Prepare Frontend Build
```bash
# Navigate to project directory
cd /path/to/careerdart

# Build the frontend
wasp build

# Navigate to frontend build
cd .wasp/build/web-app

# Set production environment variables
export REACT_APP_API_URL="https://api.careerdart.com"
export NODE_ENV=production

# Build the client
npm run build

# Create deployment directory
cd ../../../
mkdir -p netlify-client-build
cp -r .wasp/build/web-app/build/* netlify-client-build/
```

### 6.2. Create Netlify Configuration
Create `netlify.toml` in your project root:

```toml
[build]
  publish = "netlify-client-build"
  command = "echo 'Using prebuilt files from netlify-client-build directory'"

[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "8"
  NODE_ENV = "production"
  REACT_APP_API_URL = "https://api.careerdart.com"

# Security Headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Static asset caching
[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# API redirects to backend
[[redirects]]
  from = "/api/*"
  to = "https://api.careerdart.com/api/:splat"
  status = 200
  force = true

[[redirects]]
  from = "/auth/*"
  to = "https://api.careerdart.com/auth/:splat"
  status = 200
  force = true

[[redirects]]
  from = "/operations/*"
  to = "https://api.careerdart.com/operations/:splat"
  status = 200
  force = true

# SPA routing (must be last)
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### 6.3. Deploy to Netlify

#### Option A: GitHub Integration (Recommended)
1. **Push to GitHub**:
   ```bash
   git add .
   git commit -m "Prepare for Netlify deployment"
   git push origin main
   ```

2. **Netlify Dashboard**:
   - Go to https://netlify.com/
   - Click "New site from Git"
   - Connect GitHub repository
   - Configure build settings:
     - Build command: `echo 'Using prebuilt files'`
     - Publish directory: `netlify-client-build`
   - Deploy site

#### Option B: Direct Upload
```bash
# Install Netlify CLI
npm install -g netlify-cli

# Login to Netlify
netlify login

# Deploy site
netlify deploy --dir=netlify-client-build --prod

# Note the site URL provided
```

### 6.4. Configure Custom Domain on Netlify
1. **Netlify Dashboard**:
   - Go to Site settings → Domain management
   - Click "Add custom domain"
   - Enter: `careerdart.com`
   - Follow DNS configuration instructions

2. **Update DNS**: Add these records to your domain:
   ```dns
   # Main domain
   Type: A
   Name: @
   Value: 75.2.60.5

   # WWW subdomain
   Type: CNAME
   Name: www
   Value: careerdart.netlify.app

   # API subdomain (for backend)
   Type: CNAME
   Name: api
   Value: careerdart-prod.fly.dev
   ```

---

## 🌍 Part 7: Custom Domain Configuration

### 7.1. DNS Configuration Summary
Configure these DNS records with your domain provider:

```dns
# Frontend (Netlify)
Type: A
Name: @
Value: 75.2.60.5

Type: CNAME
Name: www
Value: careerdart.netlify.app

# Backend (Fly.io)
Type: CNAME
Name: api
Value: careerdart-prod.fly.dev

# Email Authentication (RESEND)
Type: TXT
Name: @
Value: v=spf1 include:_spf.resend.com ~all

Type: TXT
Name: resend._domainkey
Value: [From RESEND dashboard]

Type: TXT
Name: _dmarc
Value: v=DMARC1; p=none; rua=mailto:<EMAIL>
```

### 7.2. SSL Certificate Verification
```bash
# Check backend SSL
curl -I https://api.careerdart.com

# Check frontend SSL
curl -I https://careerdart.com

# Both should return valid SSL certificates
```

---

## ✅ Part 8: Testing & Verification

### 8.1. Backend API Testing
```bash
# Test health endpoint
curl -I https://api.careerdart.com/health

# Test authentication endpoint
curl -I https://api.careerdart.com/auth/me

# Test CORS
curl -H "Origin: https://careerdart.com" \
     -H "Access-Control-Request-Method: GET" \
     -X OPTIONS https://api.careerdart.com/auth/me
```

### 8.2. Email Functionality Testing
```bash
# Test email signup
curl -X POST https://api.careerdart.com/auth/email/signup \
  -H "Content-Type: application/json" \
  -H "Origin: https://careerdart.com" \
  -d '{"email": "<EMAIL>", "password": "TestPassword123!"}'

# Test password reset
curl -X POST https://api.careerdart.com/auth/email/request-password-reset \
  -H "Content-Type: application/json" \
  -H "Origin: https://careerdart.com" \
  -d '{"email": "<EMAIL>"}'
```

### 8.3. Frontend Testing
1. **Visit**: https://careerdart.com
2. **Test signup**: Create new account with email verification
3. **Test Google OAuth**: Sign in with Google
4. **Test features**: Create cover letter, manage jobs
5. **Check responsiveness**: Test on mobile devices

### 8.4. Full Integration Testing
1. **User Registration**: Complete email signup flow
2. **Email Verification**: Check inbox and verify email
3. **OAuth Login**: Test Google sign-in
4. **Core Features**: Test main application functionality
5. **Payment Flow**: Test subscription features (if applicable)

---

## 🔄 Part 9: Maintenance & Updates

### 9.1. Backend Updates
```bash
# Update code
git pull origin main

# Rebuild and deploy
wasp build
fly deploy -a careerdart-prod

# Monitor deployment
fly logs -a careerdart-prod

# Run new migrations if needed
fly ssh console -a careerdart-prod
cd .wasp/build/server
npm run db-migrate-prod
```

### 9.2. Frontend Updates
```bash
# Rebuild frontend
wasp build
cd .wasp/build/web-app
export REACT_APP_API_URL="https://api.careerdart.com"
npm run build

# Update deployment files
cd ../../../
rm -rf netlify-client-build/*
cp -r .wasp/build/web-app/build/* netlify-client-build/

# Commit and push (for GitHub integration)
git add .
git commit -m "Update frontend"
git push origin main

# Or manual deploy
netlify deploy --dir=netlify-client-build --prod
```

### 9.3. Environment Variable Updates
```bash
# Update backend environment variables
fly secrets set NEW_VARIABLE=value -a careerdart-prod

# Update frontend environment variables
# (Update in Netlify dashboard under Site settings → Environment variables)
```

### 9.4. Database Maintenance
```bash
# Check database status
fly postgres connect -a careerdart-db

# Backup database
fly postgres backup -a careerdart-db

# Monitor database metrics
fly logs -a careerdart-db
```

### 9.5. Monitoring and Scaling
```bash
# Monitor application performance
fly status -a careerdart-prod
fly machine list -a careerdart-prod

# Scale up for high traffic
fly machine clone -a careerdart-prod

# Scale down during low traffic
fly machine destroy MACHINE_ID -a careerdart-prod
```

---

## 🚨 Troubleshooting

### Common Issues & Solutions

#### Backend Issues

**Build Failures:**
```bash
# Clear cache and rebuild
wasp clean
rm -rf node_modules package-lock.json
npm install
wasp build
```

**Database Connection Issues:**
```bash
# Check DATABASE_URL
fly secrets list -a careerdart-prod | grep DATABASE_URL

# Reconnect database
fly postgres attach careerdart-db --app careerdart-prod
```

**Memory Issues:**
```bash
# Increase memory allocation in fly.toml
[[vm]]
  memory_mb = 1024  # Increase from 512
```

#### Frontend Issues

**Build Failures:**
```bash
# Check Node.js version
node --version  # Should be 18+

# Clear cache
rm -rf .wasp/build/web-app/node_modules
rm -rf .wasp/build/web-app/package-lock.json
```

**API Connection Issues:**
```bash
# Verify API URL in environment
echo $REACT_APP_API_URL

# Check CORS configuration in backend
fly logs -a careerdart-prod | grep CORS
```

#### Email Issues

**Emails Not Delivered:**
```bash
# Check RESEND API key
fly secrets list -a careerdart-prod | grep RESEND

# Test RESEND directly
curl -X POST https://api.resend.com/emails \
  -H "Authorization: Bearer re_your-api-key" \
  -H "Content-Type: application/json" \
  -d '{"from": "<EMAIL>", "to": ["<EMAIL>"], "subject": "Test"}'
```

**Domain Verification Issues:**
```bash
# Check DNS records
nslookup -type=TXT yourdomain.com
nslookup -type=TXT resend._domainkey.yourdomain.com
```

#### OAuth Issues

**Google OAuth Errors:**
1. **Check redirect URIs** in Google Cloud Console
2. **Verify client ID/secret** in environment variables
3. **Ensure domains match** in OAuth configuration

**Invalid Client Error:**
```bash
# Update OAuth credentials
fly secrets set \
  GOOGLE_CLIENT_ID=new-client-id \
  GOOGLE_CLIENT_SECRET=new-client-secret \
  -a careerdart-prod
```

#### SSL Certificate Issues

**Certificate Not Issued:**
```bash
# Check certificate status
fly certs list -a careerdart-prod

# Refresh certificate
fly certs refresh api.yourdomain.com -a careerdart-prod
```

**DNS Propagation:**
```bash
# Check DNS propagation
dig api.yourdomain.com
nslookup api.yourdomain.com
```

### Emergency Procedures

**Rollback Deployment:**
```bash
# List previous releases
fly releases -a careerdart-prod

# Rollback to previous version
fly deploy -a careerdart-prod --image registry.fly.io/careerdart-prod:deployment-PREVIOUS_ID
```

**Application Recovery:**
```bash
# Restart application
fly machine restart -a careerdart-prod

# Scale to zero (maintenance mode)
fly scale count 0 -a careerdart-prod

# Scale back up
fly scale count 1 -a careerdart-prod
```

### Monitoring Commands

```bash
# Real-time logs
fly logs -a careerdart-prod -f

# Application metrics
fly status -a careerdart-prod

# Database status
fly postgres connect -a careerdart-db

# SSL certificate status
fly certs list -a careerdart-prod
```

---

## 📚 Additional Resources

- **Fly.io Documentation**: https://fly.io/docs/
- **Netlify Documentation**: https://docs.netlify.com/
- **Wasp Documentation**: https://wasp-lang.dev/docs/
- **RESEND Documentation**: https://resend.com/docs/
- **Google OAuth Setup**: https://developers.google.com/identity/protocols/oauth2/

---

## 🏁 Deployment Checklist

### Pre-Deployment Checklist
- [ ] Fly.io CLI installed and authenticated
- [ ] Netlify account created
- [ ] Wasp CLI installed (v0.15.0+)
- [ ] Domain purchased and accessible
- [ ] RESEND account created
- [ ] Google Cloud Console project created
- [ ] GitHub repository set up

### Backend (Fly.io) Checklist
- [ ] Fly.io app created
- [ ] PostgreSQL database created and attached
- [ ] Environment variables configured
- [ ] Dockerfile.production created
- [ ] fly.toml configured
- [ ] Application deployed successfully
- [ ] Database migrations completed
- [ ] Custom domain added and SSL certificate issued

### Frontend (Netlify) Checklist
- [ ] Frontend built and prepared
- [ ] netlify.toml configured
- [ ] Site deployed to Netlify
- [ ] Custom domain configured
- [ ] SSL certificate enabled
- [ ] API redirects working

### Email (RESEND) Checklist
- [ ] RESEND account created
- [ ] Domain verified in RESEND
- [ ] DNS records added (SPF, DKIM, DMARC)
- [ ] Email configuration updated in app
- [ ] Email functionality tested

### OAuth (Google) Checklist
- [ ] Google Cloud Console project created
- [ ] OAuth consent screen configured
- [ ] OAuth client created with correct URIs
- [ ] Credentials added to environment variables
- [ ] OAuth flow tested

### Final Verification Checklist
- [ ] Frontend accessible at https://yourdomain.com
- [ ] Backend API accessible at https://api.yourdomain.com
- [ ] User registration working with email verification
- [ ] Google OAuth sign-in working
- [ ] Password reset emails delivered
- [ ] All core application features functional
- [ ] Mobile responsiveness verified
- [ ] Performance acceptable
- [ ] Error monitoring in place

---

**Congratulations!** 🎉 

Your CareerDart application is now fully deployed with:
- ✅ **Frontend** on Netlify with custom domain and SSL
- ✅ **Backend** on Fly.io with PostgreSQL database
- ✅ **Email services** through RESEND with domain verification
- ✅ **OAuth authentication** with Google
- ✅ **Custom domains** with proper SSL certificates

Your application is production-ready and scalable!

---

## 📞 Support

If you encounter issues during deployment:

1. **Check logs**: Use monitoring commands provided above
2. **Review configuration**: Ensure all environment variables are set correctly
3. **Test components individually**: Isolate issues to specific services
4. **Consult documentation**: Check service-specific documentation
5. **Community support**: Reach out to Wasp, Fly.io, or Netlify communities

Remember to keep your API keys and credentials secure, and regularly update your application and dependencies. 