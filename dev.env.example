# CareerDart Development Environment Variables
# Copy this file to .env and fill in your actual values

# ==============================================
# SERVER ENVIRONMENT VARIABLES (Backend)
# ==============================================

# Server URL for development (usually localhost:3001)
WASP_SERVER_URL=http://localhost:3001
WASP_WEB_CLIENT_URL=http://localhost:3000

# Database URL (use local PostgreSQL or hosted database like Railway/Neon)
# For local development: postgresql://username:password@localhost:5432/careerdart_dev
DATABASE_URL=postgresql://username:password@localhost:5432/careerdart_dev

# OpenAI API Key (get from https://platform.openai.com/api-keys)
OPENAI_API_KEY=sk-your-openai-api-key-here

# Google OAuth Configuration (get from https://console.cloud.google.com/)
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=GOCSPX-your-google-client-secret

# Email Configuration (RESEND)
# Get your RESEND API key from https://resend.com/api-keys
SMTP_HOST=smtp.resend.com
SMTP_PORT=587
SMTP_USERNAME=resend
SMTP_PASSWORD=re_your-resend-api-key-here
RESEND_API_KEY=re_your-resend-api-key-here
SEND_EMAILS_IN_DEVELOPMENT=true

# JWT Secret for email verification (generate with: openssl rand -base64 32)
JWT_SECRET=your-jwt-secret-for-email-verification

# Stripe Configuration (get from https://stripe.com)
STRIPE_KEY=sk_test_your-stripe-secret-key-here
PRODUCT_PRICE_ID=price_your-product-price-id-here
PRODUCT_CREDITS_PRICE_ID=price_your-credits-price-id-here

# ==============================================
# CLIENT ENVIRONMENT VARIABLES (Frontend)
# ==============================================

# Environment
NODE_ENV=development

# Backend API URL for frontend (should match WASP_SERVER_URL)
REACT_APP_API_URL=http://localhost:3001

# Stripe Public Key (test key for development)
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key-here

# Google Analytics (optional for development)
REACT_APP_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Feature flags for development
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_ENABLE_PERFORMANCE_MONITORING=false

# Development optimizations
GENERATE_SOURCEMAP=true
SKIP_PREFLIGHT_CHECK=true
FAST_REFRESH=true

# ==============================================
# DEVELOPMENT SETTINGS
# ==============================================

# Enable detailed error messages
DEBUG=true
WASP_LOG_LEVEL=debug

# Development database settings
DB_LOG_QUERIES=true

# Hot reload settings
CHOKIDAR_USEPOLLING=false
WATCHPACK_POLLING=false

# ==============================================
# OPTIONAL INTEGRATIONS
# ==============================================

# Sentry for error tracking (optional)
REACT_APP_SENTRY_DSN=your-sentry-dsn-here

# PostHog for analytics (optional)
REACT_APP_POSTHOG_KEY=your-posthog-key-here
REACT_APP_POSTHOG_HOST=https://app.posthog.com

# ==============================================
# INSTRUCTIONS
# ==============================================
# 1. Copy this file to .env in your project root
# 2. Fill in your actual API keys and credentials
# 3. Never commit .env to version control
# 4. For local database, install PostgreSQL and create a database
# 5. Run `wasp db migrate-dev` to set up your database schema 