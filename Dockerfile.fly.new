# Fixed Dockerfile for Wasp CareerDart
FROM node:18-alpine AS base
RUN apk --no-cache -U upgrade

# Build stage
FROM base AS server-builder
RUN apk add --no-cache python3 build-base libtool autoconf automake
WORKDIR /app

# Copy source files with correct structure
COPY src ./src
COPY package.json package-lock.json ./
COPY .wasp/build/server .wasp/build/server
COPY .wasp/out/sdk .wasp/out/sdk
COPY .wasp/build/db/ .wasp/build/db/

# Install dependencies
RUN npm install && cd .wasp/build/server && npm install

# Generate Prisma client
RUN cd .wasp/build/server && npx prisma generate --schema='../db/schema.prisma'

# Build the server
RUN cd .wasp/build/server && npm run bundle

# Production stage
FROM base AS production
RUN apk add --no-cache python3
ENV NODE_ENV=production
ENV PORT=8080
WORKDIR /app

# Copy built artifacts
COPY --from=server-builder /app/node_modules ./node_modules
COPY --from=server-builder /app/.wasp/out/sdk .wasp/out/sdk
COPY --from=server-builder /app/.wasp/build/server/node_modules .wasp/build/server/node_modules
COPY --from=server-builder /app/.wasp/build/server/bundle .wasp/build/server/bundle
COPY --from=server-builder /app/.wasp/build/server/package*.json .wasp/build/server/
COPY --from=server-builder /app/.wasp/build/server/scripts .wasp/build/server/scripts
COPY --from=server-builder /app/.wasp/build/db/ .wasp/build/db/

EXPOSE 8080
WORKDIR /app/.wasp/build/server
ENTRYPOINT ["npm", "run", "start-production"] 