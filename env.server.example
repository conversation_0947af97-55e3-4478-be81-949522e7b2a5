# defaults to localhost:3001 during development. make sure to add the deployed server address in production.
WASP_SERVER_URL=http://localhost:3001

# create an api key at https://platform.openai.com/
OPENAI_API_KEY=

# Guide for setting up google OAuth2: https://wasp-lang.dev/docs/integrations/google
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# you can easily set up a hosted database at https://railway.app
DATABASE_URL=

# RESEND SMTP Configuration for email verification
# Get your RESEND API key from https://resend.com/api-keys
SMTP_HOST=smtp.resend.com
SMTP_PORT=587
SMTP_USERNAME=resend
SMTP_PASSWORD=your_resend_api_key_here
SEND_EMAILS_IN_DEVELOPMENT=true

# Alternative: Direct RESEND API key (for custom actions)
RESEND_API_KEY=your_resend_api_key_here

# Stripe Payment Keys https://stripe.com
STRIPE_KEY=
PRODUCT_PRICE_ID=
PRODUCT_CREDITS_PRICE_ID=

