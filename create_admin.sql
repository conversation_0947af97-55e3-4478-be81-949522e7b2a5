-- Create admin user and auth setup
BEGIN;

-- Insert user
INSERT INTO "User" (username, email, "hasPaid", "gptModel", "notifyPaymentExpires", credits, "yearsOfExperience", "autoSave", "createdAt", "darkMode", "featureUpdates", "jobReminders", "marketingEmails", "subscriptionReminders", "updatedAt", "wordCountDisplay") 
VALUES ('admin', '<EMAIL>', true, 'gpt-4o-mini', false, 1000, 0, true, NOW(), false, true, true, false, true, NOW(), true) 
ON CONFLICT (email) DO UPDATE SET 
  username = EXCLUDED.username,
  "hasPaid" = EXCLUDED."hasPaid",
  credits = EXCLUDED.credits
RETURNING id;

-- Get the user ID
WITH user_data AS (
  SELECT id FROM "User" WHERE email = '<EMAIL>'
)
-- Insert or update Auth record  
INSERT INTO "Auth" (id, "userId")
SELECT gen_random_uuid()::text, user_data.id 
FROM user_data
ON CONFLICT ("userId") DO NOTHING;

-- Get auth ID and insert/update AuthIdentity with verified status
WITH user_data AS (
  SELECT u.id as user_id, a.id as auth_id 
  FROM "User" u 
  JOIN "Auth" a ON u.id = a."userId" 
  WHERE u.email = '<EMAIL>'
)
INSERT INTO "AuthIdentity" ("providerName", "providerUserId", "providerData", "authId")
SELECT 'email', user_data.user_id::text, '{"isVerified": true}', user_data.auth_id
FROM user_data
ON CONFLICT ("providerName", "providerUserId") DO UPDATE SET
  "providerData" = '{"isVerified": true}';

COMMIT;

-- Show the result
SELECT u.id, u.email, u.username, ai."providerData" 
FROM "User" u 
JOIN "Auth" a ON u.id = a."userId" 
JOIN "AuthIdentity" ai ON a.id = ai."authId" 
WHERE u.email = '<EMAIL>'; 