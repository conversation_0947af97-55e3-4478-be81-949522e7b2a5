# Working production deployment for Wasp application - Option 1
FROM node:18-alpine

# Install runtime dependencies
RUN apk add --no-cache \
    dumb-init \
    curl \
    tini

# Create app user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S careerdart -u 1001

# Set working directory
WORKDIR /app

# Copy server source (not the broken bundle)
COPY --chown=careerdart:nodejs .wasp/build/server/src/ ./src/
COPY --chown=careerdart:nodejs .wasp/build/server/package*.json ./
COPY --chown=careerdart:nodejs .wasp/build/db/ ./db/

# Install dependencies and TypeScript runner
RUN npm ci && \
    npm install -g tsx && \
    npm cache clean --force

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080
ENV NODE_OPTIONS="--max-old-space-size=1024"
ENV TINI_SUBREAPER=1

# Switch to non-root user
USER careerdart

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/ || exit 1

# Use tini as init system with proper subreaper setup
ENTRYPOINT ["/sbin/tini", "-s", "--"]

# Run server directly with tsx (bypasses broken bundle)
CMD ["sh", "-c", "npx prisma migrate deploy --schema=db/schema.prisma && tsx src/server.ts"] 