function e(e,t){for(var n=0;n<t.length;n++){const r=t[n];if("string"!=typeof r&&!Array.isArray(r))for(const t in r)if("default"!==t&&!(t in e)){const n=Object.getOwnPropertyDescriptor(r,t);n&&Object.defineProperty(e,t,n.get?n:{enumerable:!0,get:()=>r[t]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function r(e){if(Object.prototype.hasOwnProperty.call(e,"__esModule"))return e;var t=e.default;if("function"==typeof t){var n=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach((function(t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})})),n}var i,o,a={exports:{}},s={};function l(){if(i)return s;i=1;var e=Symbol.for("react.element"),t=Symbol.for("react.portal"),n=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var x=b.prototype=new v;x.constructor=b,m(x,y.prototype),x.isPureReactComponent=!0;var w=Array.isArray,k=Object.prototype.hasOwnProperty,S={current:null},P={key:!0,ref:!0,__self:!0,__source:!0};function E(t,n,r){var i,o={},a=null,s=null;if(null!=n)for(i in void 0!==n.ref&&(s=n.ref),void 0!==n.key&&(a=""+n.key),n)k.call(n,i)&&!P.hasOwnProperty(i)&&(o[i]=n[i]);var l=arguments.length-2;if(1===l)o.children=r;else if(1<l){for(var u=Array(l),c=0;c<l;c++)u[c]=arguments[c+2];o.children=u}if(t&&t.defaultProps)for(i in l=t.defaultProps)void 0===o[i]&&(o[i]=l[i]);return{$$typeof:e,type:t,key:a,ref:s,props:o,_owner:S.current}}function C(t){return"object"==typeof t&&null!==t&&t.$$typeof===e}var T=/\/+/g;function L(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function M(n,r,i,o,a){var s=typeof n;"undefined"!==s&&"boolean"!==s||(n=null);var l=!1;if(null===n)l=!0;else switch(s){case"string":case"number":l=!0;break;case"object":switch(n.$$typeof){case e:case t:l=!0}}if(l)return a=a(l=n),n=""===o?"."+L(l,0):o,w(a)?(i="",null!=n&&(i=n.replace(T,"$&/")+"/"),M(a,r,i,"",(function(e){return e}))):null!=a&&(C(a)&&(a=function(t,n){return{$$typeof:e,type:t.type,key:n,ref:t.ref,props:t.props,_owner:t._owner}}(a,i+(!a.key||l&&l.key===a.key?"":(""+a.key).replace(T,"$&/")+"/")+n)),r.push(a)),1;if(l=0,o=""===o?".":o+":",w(n))for(var u=0;u<n.length;u++){var c=o+L(s=n[u],u);l+=M(s,r,i,c,a)}else if(c=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=p&&e[p]||e["@@iterator"])?e:null}(n),"function"==typeof c)for(n=c.call(n),u=0;!(s=n.next()).done;)l+=M(s=s.value,r,i,c=o+L(s,u++),a);else if("object"===s)throw r=String(n),Error("Objects are not valid as a React child (found: "+("[object Object]"===r?"object with keys {"+Object.keys(n).join(", ")+"}":r)+"). If you meant to render a collection of children, use an array instead.");return l}function R(e,t,n){if(null==e)return e;var r=[],i=0;return M(e,r,"","",(function(e){return t.call(n,e,i++)})),r}function D(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var A={current:null},_={transition:null},V={ReactCurrentDispatcher:A,ReactCurrentBatchConfig:_,ReactCurrentOwner:S};function N(){throw Error("act(...) is not supported in production builds of React.")}return s.Children={map:R,forEach:function(e,t,n){R(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return R(e,(function(){t++})),t},toArray:function(e){return R(e,(function(e){return e}))||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},s.Component=y,s.Fragment=n,s.Profiler=o,s.PureComponent=b,s.StrictMode=r,s.Suspense=c,s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=V,s.act=N,s.cloneElement=function(t,n,r){if(null==t)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+t+".");var i=m({},t.props),o=t.key,a=t.ref,s=t._owner;if(null!=n){if(void 0!==n.ref&&(a=n.ref,s=S.current),void 0!==n.key&&(o=""+n.key),t.type&&t.type.defaultProps)var l=t.type.defaultProps;for(u in n)k.call(n,u)&&!P.hasOwnProperty(u)&&(i[u]=void 0===n[u]&&void 0!==l?l[u]:n[u])}var u=arguments.length-2;if(1===u)i.children=r;else if(1<u){l=Array(u);for(var c=0;c<u;c++)l[c]=arguments[c+2];i.children=l}return{$$typeof:e,type:t.type,key:o,ref:a,props:i,_owner:s}},s.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:a,_context:e},e.Consumer=e},s.createElement=E,s.createFactory=function(e){var t=E.bind(null,e);return t.type=e,t},s.createRef=function(){return{current:null}},s.forwardRef=function(e){return{$$typeof:u,render:e}},s.isValidElement=C,s.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:D}},s.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},s.startTransition=function(e){var t=_.transition;_.transition={};try{e()}finally{_.transition=t}},s.unstable_act=N,s.useCallback=function(e,t){return A.current.useCallback(e,t)},s.useContext=function(e){return A.current.useContext(e)},s.useDebugValue=function(){},s.useDeferredValue=function(e){return A.current.useDeferredValue(e)},s.useEffect=function(e,t){return A.current.useEffect(e,t)},s.useId=function(){return A.current.useId()},s.useImperativeHandle=function(e,t,n){return A.current.useImperativeHandle(e,t,n)},s.useInsertionEffect=function(e,t){return A.current.useInsertionEffect(e,t)},s.useLayoutEffect=function(e,t){return A.current.useLayoutEffect(e,t)},s.useMemo=function(e,t){return A.current.useMemo(e,t)},s.useReducer=function(e,t,n){return A.current.useReducer(e,t,n)},s.useRef=function(e){return A.current.useRef(e)},s.useState=function(e){return A.current.useState(e)},s.useSyncExternalStore=function(e,t,n){return A.current.useSyncExternalStore(e,t,n)},s.useTransition=function(){return A.current.useTransition()},s.version="18.3.1",s}function u(){return o||(o=1,a.exports=l()),a.exports}var c=u();const d=n(c),f=e({__proto__:null,default:d},[c]);var p,h,m,g,y={exports:{}},v={},b={exports:{}},x={};function w(){return h||(h=1,b.exports=(p||(p=1,function(e){function t(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,o=e[r];if(!(0<i(o,t)))break e;e[r]=t,e[n]=o,n=r}}function n(e){return 0===e.length?null:e[0]}function r(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,o=e.length,a=o>>>1;r<a;){var s=2*(r+1)-1,l=e[s],u=s+1,c=e[u];if(0>i(l,n))u<o&&0>i(c,l)?(e[r]=c,e[u]=n,r=u):(e[r]=l,e[s]=n,r=s);else{if(!(u<o&&0>i(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"==typeof performance&&"function"==typeof performance.now){var o=performance;e.unstable_now=function(){return o.now()}}else{var a=Date,s=a.now();e.unstable_now=function(){return a.now()-s}}var l=[],u=[],c=1,d=null,f=3,p=!1,h=!1,m=!1,g="function"==typeof setTimeout?setTimeout:null,y="function"==typeof clearTimeout?clearTimeout:null,v="undefined"!=typeof setImmediate?setImmediate:null;function b(e){for(var i=n(u);null!==i;){if(null===i.callback)r(u);else{if(!(i.startTime<=e))break;r(u),i.sortIndex=i.expirationTime,t(l,i)}i=n(u)}}function x(e){if(m=!1,b(e),!h)if(null!==n(l))h=!0,A(w);else{var t=n(u);null!==t&&_(x,t.startTime-e)}}function w(t,i){h=!1,m&&(m=!1,y(E),E=-1),p=!0;var o=f;try{for(b(i),d=n(l);null!==d&&(!(d.expirationTime>i)||t&&!L());){var a=d.callback;if("function"==typeof a){d.callback=null,f=d.priorityLevel;var s=a(d.expirationTime<=i);i=e.unstable_now(),"function"==typeof s?d.callback=s:d===n(l)&&r(l),b(i)}else r(l);d=n(l)}if(null!==d)var c=!0;else{var g=n(u);null!==g&&_(x,g.startTime-i),c=!1}return c}finally{d=null,f=o,p=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var k,S=!1,P=null,E=-1,C=5,T=-1;function L(){return!(e.unstable_now()-T<C)}function M(){if(null!==P){var t=e.unstable_now();T=t;var n=!0;try{n=P(!0,t)}finally{n?k():(S=!1,P=null)}}else S=!1}if("function"==typeof v)k=function(){v(M)};else if("undefined"!=typeof MessageChannel){var R=new MessageChannel,D=R.port2;R.port1.onmessage=M,k=function(){D.postMessage(null)}}else k=function(){g(M,0)};function A(e){P=e,S||(S=!0,k())}function _(t,n){E=g((function(){t(e.unstable_now())}),n)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(e){e.callback=null},e.unstable_continueExecution=function(){h||p||(h=!0,A(w))},e.unstable_forceFrameRate=function(e){0>e||125<e||(C=0<e?Math.floor(1e3/e):5)},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(e){switch(f){case 1:case 2:case 3:var t=3;break;default:t=f}var n=f;f=t;try{return e()}finally{f=n}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=f;f=e;try{return t()}finally{f=n}},e.unstable_scheduleCallback=function(r,i,o){var a=e.unstable_now();switch(o="object"==typeof o&&null!==o&&"number"==typeof(o=o.delay)&&0<o?a+o:a,r){case 1:var s=-1;break;case 2:s=250;break;case 5:s=**********;break;case 4:s=1e4;break;default:s=5e3}return r={id:c++,callback:i,priorityLevel:r,startTime:o,expirationTime:s=o+s,sortIndex:-1},o>a?(r.sortIndex=o,t(u,r),null===n(l)&&r===n(u)&&(m?(y(E),E=-1):m=!0,_(x,o-a))):(r.sortIndex=s,t(l,r),h||p||(h=!0,A(w))),r},e.unstable_shouldYield=L,e.unstable_wrapCallback=function(e){var t=f;return function(){var n=f;f=t;try{return e.apply(this,arguments)}finally{f=n}}}}(x)),x)),b.exports}
/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */function k(){if(m)return v;m=1;var e=u(),t=w();function n(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var r=new Set,i={};function o(e,t){a(e,t),a(e+"Capture",t)}function a(e,t){for(i[e]=t,e=0;e<t.length;e++)r.add(t[e])}var s=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),l=Object.prototype.hasOwnProperty,c=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,d={},f={};function p(e,t,n,r,i,o,a){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=a}var h={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){h[e]=new p(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];h[t]=new p(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){h[e]=new p(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){h[e]=new p(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){h[e]=new p(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){h[e]=new p(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){h[e]=new p(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){h[e]=new p(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){h[e]=new p(e,5,!1,e.toLowerCase(),null,!1,!1)}));var g=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function b(e,t,n,r){var i=h.hasOwnProperty(t)?h[t]:null;(null!==i?0!==i.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null==t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,i,r)&&(n=null),r||null===i?function(e){return!!l.call(f,e)||!l.call(d,e)&&(c.test(e)?f[e]=!0:(d[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=null===n?3!==i.type&&"":n:(t=i.attributeName,r=i.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(i=i.type)||4===i&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(g,y);h[t]=new p(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(g,y);h[t]=new p(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(g,y);h[t]=new p(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){h[e]=new p(e,1,!1,e.toLowerCase(),null,!1,!1)})),h.xlinkHref=new p("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){h[e]=new p(e,1,!1,e.toLowerCase(),null,!0,!0)}));var x=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,k=Symbol.for("react.element"),S=Symbol.for("react.portal"),P=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),T=Symbol.for("react.provider"),L=Symbol.for("react.context"),M=Symbol.for("react.forward_ref"),R=Symbol.for("react.suspense"),D=Symbol.for("react.suspense_list"),A=Symbol.for("react.memo"),_=Symbol.for("react.lazy"),V=Symbol.for("react.offscreen"),N=Symbol.iterator;function z(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=N&&e[N]||e["@@iterator"])?e:null}var F,O=Object.assign;function I(e){if(void 0===F)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);F=t&&t[1]||""}return"\n"+F+e}var j=!1;function B(e,t){if(!e||j)return"";j=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"==typeof u.stack){for(var i=u.stack.split("\n"),o=r.stack.split("\n"),a=i.length-1,s=o.length-1;1<=a&&0<=s&&i[a]!==o[s];)s--;for(;1<=a&&0<=s;a--,s--)if(i[a]!==o[s]){if(1!==a||1!==s)do{if(a--,0>--s||i[a]!==o[s]){var l="\n"+i[a].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}}while(1<=a&&0<=s);break}}}finally{j=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?I(e):""}function U(e){switch(e.tag){case 5:return I(e.type);case 16:return I("Lazy");case 13:return I("Suspense");case 19:return I("SuspenseList");case 0:case 2:case 15:return e=B(e.type,!1);case 11:return e=B(e.type.render,!1);case 1:return e=B(e.type,!0);default:return""}}function $(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case P:return"Fragment";case S:return"Portal";case C:return"Profiler";case E:return"StrictMode";case R:return"Suspense";case D:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case L:return(e.displayName||"Context")+".Consumer";case T:return(e._context.displayName||"Context")+".Provider";case M:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case A:return null!==(t=e.displayName||null)?t:$(e.type)||"Memo";case _:t=e._payload,e=e._init;try{return $(e(t))}catch(n){}}return null}function W(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return $(t);case 8:return t===E?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"==typeof t)return t.displayName||t.name||null;if("string"==typeof t)return t}return null}function H(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function Q(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function Y(e){e._valueTracker||(e._valueTracker=function(e){var t=Q(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Q(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function X(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function K(e,t){var n=t.checked;return O({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function G(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=H(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Z(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function J(e,t){Z(e,t);var n=H(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?te(e,t.type,n):t.hasOwnProperty("defaultValue")&&te(e,t.type,H(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function ee(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function te(e,t,n){"number"===t&&X(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var ne=Array.isArray;function re(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+H(n),t=null,i=0;i<e.length;i++){if(e[i].value===n)return e[i].selected=!0,void(r&&(e[i].defaultSelected=!0));null!==t||e[i].disabled||(t=e[i])}null!==t&&(t.selected=!0)}}function ie(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(n(91));return O({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function oe(e,t){var r=t.value;if(null==r){if(r=t.children,t=t.defaultValue,null!=r){if(null!=t)throw Error(n(92));if(ne(r)){if(1<r.length)throw Error(n(93));r=r[0]}t=r}null==t&&(t=""),r=t}e._wrapperState={initialValue:H(r)}}function ae(e,t){var n=H(t.value),r=H(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function se(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function le(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ue(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?le(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ce,de,fe=(de=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ce=ce||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ce.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!=typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return de(e,t)}))}:de);function pe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var he={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},me=["Webkit","ms","Moz","O"];function ge(e,t,n){return null==t||"boolean"==typeof t||""===t?"":n||"number"!=typeof t||0===t||he.hasOwnProperty(e)&&he[e]?(""+t).trim():t+"px"}function ye(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),i=ge(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}Object.keys(he).forEach((function(e){me.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),he[t]=he[e]}))}));var ve=O({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function be(e,t){if(t){if(ve[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(n(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(n(60));if("object"!=typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(n(61))}if(null!=t.style&&"object"!=typeof t.style)throw Error(n(62))}}function xe(e,t){if(-1===e.indexOf("-"))return"string"==typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var we=null;function ke(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Se=null,Pe=null,Ee=null;function Ce(e){if(e=xi(e)){if("function"!=typeof Se)throw Error(n(280));var t=e.stateNode;t&&(t=ki(t),Se(e.stateNode,e.type,t))}}function Te(e){Pe?Ee?Ee.push(e):Ee=[e]:Pe=e}function Le(){if(Pe){var e=Pe,t=Ee;if(Ee=Pe=null,Ce(e),t)for(e=0;e<t.length;e++)Ce(t[e])}}function Me(e,t){return e(t)}function Re(){}var De=!1;function Ae(e,t,n){if(De)return e(t,n);De=!0;try{return Me(e,t,n)}finally{De=!1,(null!==Pe||null!==Ee)&&(Re(),Le())}}function _e(e,t){var r=e.stateNode;if(null===r)return null;var i=ki(r);if(null===i)return null;r=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(i=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!i;break e;default:e=!1}if(e)return null;if(r&&"function"!=typeof r)throw Error(n(231,t,typeof r));return r}var Ve=!1;if(s)try{var Ne={};Object.defineProperty(Ne,"passive",{get:function(){Ve=!0}}),window.addEventListener("test",Ne,Ne),window.removeEventListener("test",Ne,Ne)}catch(de){Ve=!1}function ze(e,t,n,r,i,o,a,s,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Fe=!1,Oe=null,Ie=!1,je=null,Be={onError:function(e){Fe=!0,Oe=e}};function Ue(e,t,n,r,i,o,a,s,l){Fe=!1,Oe=null,ze.apply(Be,arguments)}function $e(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function We(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function He(e){if($e(e)!==e)throw Error(n(188))}function Qe(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=$e(e)))throw Error(n(188));return t!==e?null:e}for(var r=e,i=t;;){var o=r.return;if(null===o)break;var a=o.alternate;if(null===a){if(null!==(i=o.return)){r=i;continue}break}if(o.child===a.child){for(a=o.child;a;){if(a===r)return He(o),e;if(a===i)return He(o),t;a=a.sibling}throw Error(n(188))}if(r.return!==i.return)r=o,i=a;else{for(var s=!1,l=o.child;l;){if(l===r){s=!0,r=o,i=a;break}if(l===i){s=!0,i=o,r=a;break}l=l.sibling}if(!s){for(l=a.child;l;){if(l===r){s=!0,r=a,i=o;break}if(l===i){s=!0,i=a,r=o;break}l=l.sibling}if(!s)throw Error(n(189))}}if(r.alternate!==i)throw Error(n(190))}if(3!==r.tag)throw Error(n(188));return r.stateNode.current===r?e:t}(e))?Ye(e):null}function Ye(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Ye(e);if(null!==t)return t;e=e.sibling}return null}var qe=t.unstable_scheduleCallback,Xe=t.unstable_cancelCallback,Ke=t.unstable_shouldYield,Ge=t.unstable_requestPaint,Ze=t.unstable_now,Je=t.unstable_getCurrentPriorityLevel,et=t.unstable_ImmediatePriority,tt=t.unstable_UserBlockingPriority,nt=t.unstable_NormalPriority,rt=t.unstable_LowPriority,it=t.unstable_IdlePriority,ot=null,at=null;var st=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(lt(e)/ut|0)|0},lt=Math.log,ut=Math.LN2;var ct=64,dt=4194304;function ft(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function pt(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,a=268435455&n;if(0!==a){var s=a&~i;0!==s?r=ft(s):0!==(o&=a)&&(r=ft(o))}else 0!==(a=n&~i)?r=ft(a):0!==o&&(r=ft(o));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&i)&&((i=r&-r)>=(o=t&-t)||16===i&&4194240&o))return t;if(4&r&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)i=1<<(n=31-st(t)),r|=e[n],t&=~i;return r}function ht(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function mt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function gt(){var e=ct;return!(4194240&(ct<<=1))&&(ct=64),e}function yt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-st(t)]=n}function bt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-st(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var xt=0;function wt(e){return 1<(e&=-e)?4<e?268435455&e?16:536870912:4:1}var kt,St,Pt,Et,Ct,Tt=!1,Lt=[],Mt=null,Rt=null,Dt=null,At=new Map,_t=new Map,Vt=[],Nt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function zt(e,t){switch(e){case"focusin":case"focusout":Mt=null;break;case"dragenter":case"dragleave":Rt=null;break;case"mouseover":case"mouseout":Dt=null;break;case"pointerover":case"pointerout":At.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":_t.delete(t.pointerId)}}function Ft(e,t,n,r,i,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},null!==t&&(null!==(t=xi(t))&&St(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==i&&-1===t.indexOf(i)&&t.push(i),e)}function Ot(e){var t=bi(e.target);if(null!==t){var n=$e(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=We(n)))return e.blockedOn=t,void Ct(e.priority,(function(){Pt(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function It(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Kt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=xi(n))&&St(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);we=r,n.target.dispatchEvent(r),we=null,t.shift()}return!0}function jt(e,t,n){It(e)&&n.delete(t)}function Bt(){Tt=!1,null!==Mt&&It(Mt)&&(Mt=null),null!==Rt&&It(Rt)&&(Rt=null),null!==Dt&&It(Dt)&&(Dt=null),At.forEach(jt),_t.forEach(jt)}function Ut(e,n){e.blockedOn===n&&(e.blockedOn=null,Tt||(Tt=!0,t.unstable_scheduleCallback(t.unstable_NormalPriority,Bt)))}function $t(e){function t(t){return Ut(t,e)}if(0<Lt.length){Ut(Lt[0],e);for(var n=1;n<Lt.length;n++){var r=Lt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Mt&&Ut(Mt,e),null!==Rt&&Ut(Rt,e),null!==Dt&&Ut(Dt,e),At.forEach(t),_t.forEach(t),n=0;n<Vt.length;n++)(r=Vt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Vt.length&&null===(n=Vt[0]).blockedOn;)Ot(n),null===n.blockedOn&&Vt.shift()}var Wt=x.ReactCurrentBatchConfig,Ht=!0;function Qt(e,t,n,r){var i=xt,o=Wt.transition;Wt.transition=null;try{xt=1,qt(e,t,n,r)}finally{xt=i,Wt.transition=o}}function Yt(e,t,n,r){var i=xt,o=Wt.transition;Wt.transition=null;try{xt=4,qt(e,t,n,r)}finally{xt=i,Wt.transition=o}}function qt(e,t,n,r){if(Ht){var i=Kt(e,t,n,r);if(null===i)Hr(e,t,r,Xt,n),zt(e,r);else if(function(e,t,n,r,i){switch(t){case"focusin":return Mt=Ft(Mt,e,t,n,r,i),!0;case"dragenter":return Rt=Ft(Rt,e,t,n,r,i),!0;case"mouseover":return Dt=Ft(Dt,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return At.set(o,Ft(At.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,_t.set(o,Ft(_t.get(o)||null,e,t,n,r,i)),!0}return!1}(i,e,t,n,r))r.stopPropagation();else if(zt(e,r),4&t&&-1<Nt.indexOf(e)){for(;null!==i;){var o=xi(i);if(null!==o&&kt(o),null===(o=Kt(e,t,n,r))&&Hr(e,t,r,Xt,n),o===i)break;i=o}null!==i&&r.stopPropagation()}else Hr(e,t,r,null,n)}}var Xt=null;function Kt(e,t,n,r){if(Xt=null,null!==(e=bi(e=ke(r))))if(null===(t=$e(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=We(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Xt=e,null}function Gt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Je()){case et:return 1;case tt:return 4;case nt:case rt:return 16;case it:return 536870912;default:return 16}default:return 16}}var Zt=null,Jt=null,en=null;function tn(){if(en)return en;var e,t,n=Jt,r=n.length,i="value"in Zt?Zt.value:Zt.textContent,o=i.length;for(e=0;e<r&&n[e]===i[e];e++);var a=r-e;for(t=1;t<=a&&n[r-t]===i[o-t];t++);return en=i.slice(e,1<t?1-t:void 0)}function nn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function rn(){return!0}function on(){return!1}function an(e){function t(t,n,r,i,o){for(var a in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=i,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(a)&&(t=e[a],this[a]=t?t(i):i[a]);return this.isDefaultPrevented=(null!=i.defaultPrevented?i.defaultPrevented:!1===i.returnValue)?rn:on,this.isPropagationStopped=on,this}return O(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=rn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=rn)},persist:function(){},isPersistent:rn}),t}var sn,ln,un,cn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},dn=an(cn),fn=O({},cn,{view:0,detail:0}),pn=an(fn),hn=O({},fn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Cn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==un&&(un&&"mousemove"===e.type?(sn=e.screenX-un.screenX,ln=e.screenY-un.screenY):ln=sn=0,un=e),sn)},movementY:function(e){return"movementY"in e?e.movementY:ln}}),mn=an(hn),gn=an(O({},hn,{dataTransfer:0})),yn=an(O({},fn,{relatedTarget:0})),vn=an(O({},cn,{animationName:0,elapsedTime:0,pseudoElement:0})),bn=O({},cn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),xn=an(bn),wn=an(O({},cn,{data:0})),kn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Pn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function En(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Pn[e])&&!!t[e]}function Cn(){return En}var Tn=O({},fn,{key:function(e){if(e.key){var t=kn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=nn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Sn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Cn,charCode:function(e){return"keypress"===e.type?nn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?nn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Ln=an(Tn),Mn=an(O({},hn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Rn=an(O({},fn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Cn})),Dn=an(O({},cn,{propertyName:0,elapsedTime:0,pseudoElement:0})),An=O({},hn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),_n=an(An),Vn=[9,13,27,32],Nn=s&&"CompositionEvent"in window,zn=null;s&&"documentMode"in document&&(zn=document.documentMode);var Fn=s&&"TextEvent"in window&&!zn,On=s&&(!Nn||zn&&8<zn&&11>=zn),In=String.fromCharCode(32),jn=!1;function Bn(e,t){switch(e){case"keyup":return-1!==Vn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Un(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var $n=!1;var Wn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Hn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Wn[e.type]:"textarea"===t}function Qn(e,t,n,r){Te(r),0<(t=Yr(t,"onChange")).length&&(n=new dn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Yn=null,qn=null;function Xn(e){Ir(e,0)}function Kn(e){if(q(wi(e)))return e}function Gn(e,t){if("change"===e)return t}var Zn=!1;if(s){var Jn;if(s){var er="oninput"in document;if(!er){var tr=document.createElement("div");tr.setAttribute("oninput","return;"),er="function"==typeof tr.oninput}Jn=er}else Jn=!1;Zn=Jn&&(!document.documentMode||9<document.documentMode)}function nr(){Yn&&(Yn.detachEvent("onpropertychange",rr),qn=Yn=null)}function rr(e){if("value"===e.propertyName&&Kn(qn)){var t=[];Qn(t,qn,e,ke(e)),Ae(Xn,t)}}function ir(e,t,n){"focusin"===e?(nr(),qn=n,(Yn=t).attachEvent("onpropertychange",rr)):"focusout"===e&&nr()}function or(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Kn(qn)}function ar(e,t){if("click"===e)return Kn(t)}function sr(e,t){if("input"===e||"change"===e)return Kn(t)}var lr="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function ur(e,t){if(lr(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!l.call(t,i)||!lr(e[i],t[i]))return!1}return!0}function cr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function dr(e,t){var n,r=cr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=cr(r)}}function fr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?fr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function pr(){for(var e=window,t=X();t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=X((e=t.contentWindow).document)}return t}function hr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function mr(e){var t=pr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&fr(n.ownerDocument.documentElement,n)){if(null!==r&&hr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=void 0===r.end?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=dr(n,o);var a=dr(n,r);i&&a&&(1!==e.rangeCount||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&((t=t.createRange()).setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"==typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var gr=s&&"documentMode"in document&&11>=document.documentMode,yr=null,vr=null,br=null,xr=!1;function wr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;xr||null==yr||yr!==X(r)||("selectionStart"in(r=yr)&&hr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},br&&ur(br,r)||(br=r,0<(r=Yr(vr,"onSelect")).length&&(t=new dn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=yr)))}function kr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Sr={animationend:kr("Animation","AnimationEnd"),animationiteration:kr("Animation","AnimationIteration"),animationstart:kr("Animation","AnimationStart"),transitionend:kr("Transition","TransitionEnd")},Pr={},Er={};function Cr(e){if(Pr[e])return Pr[e];if(!Sr[e])return e;var t,n=Sr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Er)return Pr[e]=n[t];return e}s&&(Er=document.createElement("div").style,"AnimationEvent"in window||(delete Sr.animationend.animation,delete Sr.animationiteration.animation,delete Sr.animationstart.animation),"TransitionEvent"in window||delete Sr.transitionend.transition);var Tr=Cr("animationend"),Lr=Cr("animationiteration"),Mr=Cr("animationstart"),Rr=Cr("transitionend"),Dr=new Map,Ar="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function _r(e,t){Dr.set(e,t),o(t,[e])}for(var Vr=0;Vr<Ar.length;Vr++){var Nr=Ar[Vr];_r(Nr.toLowerCase(),"on"+(Nr[0].toUpperCase()+Nr.slice(1)))}_r(Tr,"onAnimationEnd"),_r(Lr,"onAnimationIteration"),_r(Mr,"onAnimationStart"),_r("dblclick","onDoubleClick"),_r("focusin","onFocus"),_r("focusout","onBlur"),_r(Rr,"onTransitionEnd"),a("onMouseEnter",["mouseout","mouseover"]),a("onMouseLeave",["mouseout","mouseover"]),a("onPointerEnter",["pointerout","pointerover"]),a("onPointerLeave",["pointerout","pointerover"]),o("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),o("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),o("onBeforeInput",["compositionend","keypress","textInput","paste"]),o("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),o("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),o("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Fr=new Set("cancel close invalid load scroll toggle".split(" ").concat(zr));function Or(e,t,r){var i=e.type||"unknown-event";e.currentTarget=r,function(e,t,r,i,o,a,s,l,u){if(Ue.apply(this,arguments),Fe){if(!Fe)throw Error(n(198));var c=Oe;Fe=!1,Oe=null,Ie||(Ie=!0,je=c)}}(i,t,void 0,e),e.currentTarget=null}function Ir(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var a=r.length-1;0<=a;a--){var s=r[a],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==o&&i.isPropagationStopped())break e;Or(i,s,u),o=l}else for(a=0;a<r.length;a++){if(l=(s=r[a]).instance,u=s.currentTarget,s=s.listener,l!==o&&i.isPropagationStopped())break e;Or(i,s,u),o=l}}}if(Ie)throw e=je,Ie=!1,je=null,e}function jr(e,t){var n=t[gi];void 0===n&&(n=t[gi]=new Set);var r=e+"__bubble";n.has(r)||(Wr(t,e,2,!1),n.add(r))}function Br(e,t,n){var r=0;t&&(r|=4),Wr(n,e,r,t)}var Ur="_reactListening"+Math.random().toString(36).slice(2);function $r(e){if(!e[Ur]){e[Ur]=!0,r.forEach((function(t){"selectionchange"!==t&&(Fr.has(t)||Br(t,!1,e),Br(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Ur]||(t[Ur]=!0,Br("selectionchange",!1,t))}}function Wr(e,t,n,r){switch(Gt(t)){case 1:var i=Qt;break;case 4:i=Yt;break;default:i=qt}n=i.bind(null,t,n,e),i=void 0,!Ve||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(i=!0),r?void 0!==i?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):void 0!==i?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Hr(e,t,n,r,i){var o=r;if(!(1&t||2&t||null===r))e:for(;;){if(null===r)return;var a=r.tag;if(3===a||4===a){var s=r.stateNode.containerInfo;if(s===i||8===s.nodeType&&s.parentNode===i)break;if(4===a)for(a=r.return;null!==a;){var l=a.tag;if((3===l||4===l)&&((l=a.stateNode.containerInfo)===i||8===l.nodeType&&l.parentNode===i))return;a=a.return}for(;null!==s;){if(null===(a=bi(s)))return;if(5===(l=a.tag)||6===l){r=o=a;continue e}s=s.parentNode}}r=r.return}Ae((function(){var r=o,i=ke(n),a=[];e:{var s=Dr.get(e);if(void 0!==s){var l=dn,u=e;switch(e){case"keypress":if(0===nn(n))break e;case"keydown":case"keyup":l=Ln;break;case"focusin":u="focus",l=yn;break;case"focusout":u="blur",l=yn;break;case"beforeblur":case"afterblur":l=yn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":l=mn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":l=gn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":l=Rn;break;case Tr:case Lr:case Mr:l=vn;break;case Rr:l=Dn;break;case"scroll":l=pn;break;case"wheel":l=_n;break;case"copy":case"cut":case"paste":l=xn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":l=Mn}var c=!!(4&t),d=!c&&"scroll"===e,f=c?null!==s?s+"Capture":null:s;c=[];for(var p,h=r;null!==h;){var m=(p=h).stateNode;if(5===p.tag&&null!==m&&(p=m,null!==f&&(null!=(m=_e(h,f))&&c.push(Qr(h,m,p)))),d)break;h=h.return}0<c.length&&(s=new l(s,u,null,n,i),a.push({event:s,listeners:c}))}}if(!(7&t)){if(l="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===we||!(u=n.relatedTarget||n.fromElement)||!bi(u)&&!u[mi])&&(l||s)&&(s=i.window===i?i:(s=i.ownerDocument)?s.defaultView||s.parentWindow:window,l?(l=r,null!==(u=(u=n.relatedTarget||n.toElement)?bi(u):null)&&(u!==(d=$e(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(l=null,u=r),l!==u)){if(c=mn,m="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(c=Mn,m="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==l?s:wi(l),p=null==u?s:wi(u),(s=new c(m,h+"leave",l,n,i)).target=d,s.relatedTarget=p,m=null,bi(i)===r&&((c=new c(f,h+"enter",u,n,i)).target=p,c.relatedTarget=d,m=c),d=m,l&&u)e:{for(f=u,h=0,p=c=l;p;p=qr(p))h++;for(p=0,m=f;m;m=qr(m))p++;for(;0<h-p;)c=qr(c),h--;for(;0<p-h;)f=qr(f),p--;for(;h--;){if(c===f||null!==f&&c===f.alternate)break e;c=qr(c),f=qr(f)}c=null}else c=null;null!==l&&Xr(a,s,l,c,!1),null!==u&&null!==d&&Xr(a,d,u,c,!0)}if("select"===(l=(s=r?wi(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===l&&"file"===s.type)var g=Gn;else if(Hn(s))if(Zn)g=sr;else{g=or;var y=ir}else(l=s.nodeName)&&"input"===l.toLowerCase()&&("checkbox"===s.type||"radio"===s.type)&&(g=ar);switch(g&&(g=g(e,r))?Qn(a,g,n,i):(y&&y(e,s,r),"focusout"===e&&(y=s._wrapperState)&&y.controlled&&"number"===s.type&&te(s,"number",s.value)),y=r?wi(r):window,e){case"focusin":(Hn(y)||"true"===y.contentEditable)&&(yr=y,vr=r,br=null);break;case"focusout":br=vr=yr=null;break;case"mousedown":xr=!0;break;case"contextmenu":case"mouseup":case"dragend":xr=!1,wr(a,n,i);break;case"selectionchange":if(gr)break;case"keydown":case"keyup":wr(a,n,i)}var v;if(Nn)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else $n?Bn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(On&&"ko"!==n.locale&&($n||"onCompositionStart"!==b?"onCompositionEnd"===b&&$n&&(v=tn()):(Jt="value"in(Zt=i)?Zt.value:Zt.textContent,$n=!0)),0<(y=Yr(r,b)).length&&(b=new wn(b,e,null,n,i),a.push({event:b,listeners:y}),v?b.data=v:null!==(v=Un(n))&&(b.data=v))),(v=Fn?function(e,t){switch(e){case"compositionend":return Un(t);case"keypress":return 32!==t.which?null:(jn=!0,In);case"textInput":return(e=t.data)===In&&jn?null:e;default:return null}}(e,n):function(e,t){if($n)return"compositionend"===e||!Nn&&Bn(e,t)?(e=tn(),en=Jt=Zt=null,$n=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return On&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Yr(r,"onBeforeInput")).length&&(i=new wn("onBeforeInput","beforeinput",null,n,i),a.push({event:i,listeners:r}),i.data=v))}Ir(a,t)}))}function Qr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Yr(e,t){for(var n=t+"Capture",r=[];null!==e;){var i=e,o=i.stateNode;5===i.tag&&null!==o&&(i=o,null!=(o=_e(e,n))&&r.unshift(Qr(e,o,i)),null!=(o=_e(e,t))&&r.push(Qr(e,o,i))),e=e.return}return r}function qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Xr(e,t,n,r,i){for(var o=t._reactName,a=[];null!==n&&n!==r;){var s=n,l=s.alternate,u=s.stateNode;if(null!==l&&l===r)break;5===s.tag&&null!==u&&(s=u,i?null!=(l=_e(n,o))&&a.unshift(Qr(n,l,s)):i||null!=(l=_e(n,o))&&a.push(Qr(n,l,s))),n=n.return}0!==a.length&&e.push({event:t,listeners:a})}var Kr=/\r\n?/g,Gr=/\u0000|\uFFFD/g;function Zr(e){return("string"==typeof e?e:""+e).replace(Kr,"\n").replace(Gr,"")}function Jr(e,t,r){if(t=Zr(t),Zr(e)!==t&&r)throw Error(n(425))}function ei(){}var ti=null,ni=null;function ri(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ii="function"==typeof setTimeout?setTimeout:void 0,oi="function"==typeof clearTimeout?clearTimeout:void 0,ai="function"==typeof Promise?Promise:void 0,si="function"==typeof queueMicrotask?queueMicrotask:void 0!==ai?function(e){return ai.resolve(null).then(e).catch(li)}:ii;function li(e){setTimeout((function(){throw e}))}function ui(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&8===i.nodeType)if("/$"===(n=i.data)){if(0===r)return e.removeChild(i),void $t(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=i}while(n);$t(t)}function ci(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function di(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var fi=Math.random().toString(36).slice(2),pi="__reactFiber$"+fi,hi="__reactProps$"+fi,mi="__reactContainer$"+fi,gi="__reactEvents$"+fi,yi="__reactListeners$"+fi,vi="__reactHandles$"+fi;function bi(e){var t=e[pi];if(t)return t;for(var n=e.parentNode;n;){if(t=n[mi]||n[pi]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=di(e);null!==e;){if(n=e[pi])return n;e=di(e)}return t}n=(e=n).parentNode}return null}function xi(e){return!(e=e[pi]||e[mi])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function wi(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(n(33))}function ki(e){return e[hi]||null}var Si=[],Pi=-1;function Ei(e){return{current:e}}function Ci(e){0>Pi||(e.current=Si[Pi],Si[Pi]=null,Pi--)}function Ti(e,t){Pi++,Si[Pi]=e.current,e.current=t}var Li={},Mi=Ei(Li),Ri=Ei(!1),Di=Li;function Ai(e,t){var n=e.type.contextTypes;if(!n)return Li;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i,o={};for(i in n)o[i]=t[i];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function _i(e){return null!=(e=e.childContextTypes)}function Vi(){Ci(Ri),Ci(Mi)}function Ni(e,t,r){if(Mi.current!==Li)throw Error(n(168));Ti(Mi,t),Ti(Ri,r)}function zi(e,t,r){var i=e.stateNode;if(t=t.childContextTypes,"function"!=typeof i.getChildContext)return r;for(var o in i=i.getChildContext())if(!(o in t))throw Error(n(108,W(e)||"Unknown",o));return O({},r,i)}function Fi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Li,Di=Mi.current,Ti(Mi,e),Ti(Ri,Ri.current),!0}function Oi(e,t,r){var i=e.stateNode;if(!i)throw Error(n(169));r?(e=zi(e,t,Di),i.__reactInternalMemoizedMergedChildContext=e,Ci(Ri),Ci(Mi),Ti(Mi,e)):Ci(Ri),Ti(Ri,r)}var Ii=null,ji=!1,Bi=!1;function Ui(e){null===Ii?Ii=[e]:Ii.push(e)}function $i(){if(!Bi&&null!==Ii){Bi=!0;var e=0,t=xt;try{var n=Ii;for(xt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Ii=null,ji=!1}catch(i){throw null!==Ii&&(Ii=Ii.slice(e+1)),qe(et,$i),i}finally{xt=t,Bi=!1}}return null}var Wi=[],Hi=0,Qi=null,Yi=0,qi=[],Xi=0,Ki=null,Gi=1,Zi="";function Ji(e,t){Wi[Hi++]=Yi,Wi[Hi++]=Qi,Qi=e,Yi=t}function eo(e,t,n){qi[Xi++]=Gi,qi[Xi++]=Zi,qi[Xi++]=Ki,Ki=e;var r=Gi;e=Zi;var i=32-st(r)-1;r&=~(1<<i),n+=1;var o=32-st(t)+i;if(30<o){var a=i-i%5;o=(r&(1<<a)-1).toString(32),r>>=a,i-=a,Gi=1<<32-st(t)+i|n<<i|r,Zi=o+e}else Gi=1<<o|n<<i|r,Zi=e}function to(e){null!==e.return&&(Ji(e,1),eo(e,1,0))}function no(e){for(;e===Qi;)Qi=Wi[--Hi],Wi[Hi]=null,Yi=Wi[--Hi],Wi[Hi]=null;for(;e===Ki;)Ki=qi[--Xi],qi[Xi]=null,Zi=qi[--Xi],qi[Xi]=null,Gi=qi[--Xi],qi[Xi]=null}var ro=null,io=null,oo=!1,ao=null;function so(e,t){var n=Du(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function lo(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ro=e,io=ci(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ro=e,io=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ki?{id:Gi,overflow:Zi}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Du(18,null,null,0)).stateNode=t,n.return=e,e.child=n,ro=e,io=null,!0);default:return!1}}function uo(e){return!(!(1&e.mode)||128&e.flags)}function co(e){if(oo){var t=io;if(t){var r=t;if(!lo(e,t)){if(uo(e))throw Error(n(418));t=ci(r.nextSibling);var i=ro;t&&lo(e,t)?so(i,r):(e.flags=-4097&e.flags|2,oo=!1,ro=e)}}else{if(uo(e))throw Error(n(418));e.flags=-4097&e.flags|2,oo=!1,ro=e}}}function fo(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ro=e}function po(e){if(e!==ro)return!1;if(!oo)return fo(e),oo=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!ri(e.type,e.memoizedProps)),t&&(t=io)){if(uo(e))throw ho(),Error(n(418));for(;t;)so(e,t),t=ci(t.nextSibling)}if(fo(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(n(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var r=e.data;if("/$"===r){if(0===t){io=ci(e.nextSibling);break e}t--}else"$"!==r&&"$!"!==r&&"$?"!==r||t++}e=e.nextSibling}io=null}}else io=ro?ci(e.stateNode.nextSibling):null;return!0}function ho(){for(var e=io;e;)e=ci(e.nextSibling)}function mo(){io=ro=null,oo=!1}function go(e){null===ao?ao=[e]:ao.push(e)}var yo=x.ReactCurrentBatchConfig;function vo(e,t,r){if(null!==(e=r.ref)&&"function"!=typeof e&&"object"!=typeof e){if(r._owner){if(r=r._owner){if(1!==r.tag)throw Error(n(309));var i=r.stateNode}if(!i)throw Error(n(147,e));var o=i,a=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===a?t.ref:((t=function(e){var t=o.refs;null===e?delete t[a]:t[a]=e})._stringRef=a,t)}if("string"!=typeof e)throw Error(n(284));if(!r._owner)throw Error(n(290,e))}return e}function bo(e,t){throw e=Object.prototype.toString.call(t),Error(n(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function xo(e){return(0,e._init)(e._payload)}function wo(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function r(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function i(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function o(e,t){return(e=_u(e,t)).index=0,e.sibling=null,e}function a(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function s(t){return e&&null===t.alternate&&(t.flags|=2),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Fu(n,e.mode,r)).return=e,t):((t=o(t,n)).return=e,t)}function u(e,t,n,r){var i=n.type;return i===P?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===i||"object"==typeof i&&null!==i&&i.$$typeof===_&&xo(i)===t.type)?((r=o(t,n.props)).ref=vo(e,t,n),r.return=e,r):((r=Vu(n.type,n.key,n.props,null,e.mode,r)).ref=vo(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Ou(n,e.mode,r)).return=e,t):((t=o(t,n.children||[])).return=e,t)}function d(e,t,n,r,i){return null===t||7!==t.tag?((t=Nu(n,e.mode,r,i)).return=e,t):((t=o(t,n)).return=e,t)}function f(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t)return(t=Fu(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case k:return(n=Vu(t.type,t.key,t.props,null,e.mode,n)).ref=vo(e,null,t),n.return=e,n;case S:return(t=Ou(t,e.mode,n)).return=e,t;case _:return f(e,(0,t._init)(t._payload),n)}if(ne(t)||z(t))return(t=Nu(t,e.mode,n,null)).return=e,t;bo(e,t)}return null}function p(e,t,n,r){var i=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n)return null!==i?null:l(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case k:return n.key===i?u(e,t,n,r):null;case S:return n.key===i?c(e,t,n,r):null;case _:return p(e,t,(i=n._init)(n._payload),r)}if(ne(n)||z(n))return null!==i?null:d(e,t,n,r,null);bo(e,n)}return null}function h(e,t,n,r,i){if("string"==typeof r&&""!==r||"number"==typeof r)return l(t,e=e.get(n)||null,""+r,i);if("object"==typeof r&&null!==r){switch(r.$$typeof){case k:return u(t,e=e.get(null===r.key?n:r.key)||null,r,i);case S:return c(t,e=e.get(null===r.key?n:r.key)||null,r,i);case _:return h(e,t,n,(0,r._init)(r._payload),i)}if(ne(r)||z(r))return d(t,e=e.get(n)||null,r,i,null);bo(t,r)}return null}return function l(u,c,d,m){if("object"==typeof d&&null!==d&&d.type===P&&null===d.key&&(d=d.props.children),"object"==typeof d&&null!==d){switch(d.$$typeof){case k:e:{for(var g=d.key,y=c;null!==y;){if(y.key===g){if((g=d.type)===P){if(7===y.tag){r(u,y.sibling),(c=o(y,d.props.children)).return=u,u=c;break e}}else if(y.elementType===g||"object"==typeof g&&null!==g&&g.$$typeof===_&&xo(g)===y.type){r(u,y.sibling),(c=o(y,d.props)).ref=vo(u,y,d),c.return=u,u=c;break e}r(u,y);break}t(u,y),y=y.sibling}d.type===P?((c=Nu(d.props.children,u.mode,m,d.key)).return=u,u=c):((m=Vu(d.type,d.key,d.props,null,u.mode,m)).ref=vo(u,c,d),m.return=u,u=m)}return s(u);case S:e:{for(y=d.key;null!==c;){if(c.key===y){if(4===c.tag&&c.stateNode.containerInfo===d.containerInfo&&c.stateNode.implementation===d.implementation){r(u,c.sibling),(c=o(c,d.children||[])).return=u,u=c;break e}r(u,c);break}t(u,c),c=c.sibling}(c=Ou(d,u.mode,m)).return=u,u=c}return s(u);case _:return l(u,c,(y=d._init)(d._payload),m)}if(ne(d))return function(n,o,s,l){for(var u=null,c=null,d=o,m=o=0,g=null;null!==d&&m<s.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var y=p(n,d,s[m],l);if(null===y){null===d&&(d=g);break}e&&d&&null===y.alternate&&t(n,d),o=a(y,o,m),null===c?u=y:c.sibling=y,c=y,d=g}if(m===s.length)return r(n,d),oo&&Ji(n,m),u;if(null===d){for(;m<s.length;m++)null!==(d=f(n,s[m],l))&&(o=a(d,o,m),null===c?u=d:c.sibling=d,c=d);return oo&&Ji(n,m),u}for(d=i(n,d);m<s.length;m++)null!==(g=h(d,n,m,s[m],l))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),o=a(g,o,m),null===c?u=g:c.sibling=g,c=g);return e&&d.forEach((function(e){return t(n,e)})),oo&&Ji(n,m),u}(u,c,d,m);if(z(d))return function(o,s,l,u){var c=z(l);if("function"!=typeof c)throw Error(n(150));if(null==(l=c.call(l)))throw Error(n(151));for(var d=c=null,m=s,g=s=0,y=null,v=l.next();null!==m&&!v.done;g++,v=l.next()){m.index>g?(y=m,m=null):y=m.sibling;var b=p(o,m,v.value,u);if(null===b){null===m&&(m=y);break}e&&m&&null===b.alternate&&t(o,m),s=a(b,s,g),null===d?c=b:d.sibling=b,d=b,m=y}if(v.done)return r(o,m),oo&&Ji(o,g),c;if(null===m){for(;!v.done;g++,v=l.next())null!==(v=f(o,v.value,u))&&(s=a(v,s,g),null===d?c=v:d.sibling=v,d=v);return oo&&Ji(o,g),c}for(m=i(o,m);!v.done;g++,v=l.next())null!==(v=h(m,o,g,v.value,u))&&(e&&null!==v.alternate&&m.delete(null===v.key?g:v.key),s=a(v,s,g),null===d?c=v:d.sibling=v,d=v);return e&&m.forEach((function(e){return t(o,e)})),oo&&Ji(o,g),c}(u,c,d,m);bo(u,d)}return"string"==typeof d&&""!==d||"number"==typeof d?(d=""+d,null!==c&&6===c.tag?(r(u,c.sibling),(c=o(c,d)).return=u,u=c):(r(u,c),(c=Fu(d,u.mode,m)).return=u,u=c),s(u)):r(u,c)}}var ko=wo(!0),So=wo(!1),Po=Ei(null),Eo=null,Co=null,To=null;function Lo(){To=Co=Eo=null}function Mo(e){var t=Po.current;Ci(Po),e._currentValue=t}function Ro(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Do(e,t){Eo=e,To=Co=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bs=!0),e.firstContext=null)}function Ao(e){var t=e._currentValue;if(To!==e)if(e={context:e,memoizedValue:t,next:null},null===Co){if(null===Eo)throw Error(n(308));Co=e,Eo.dependencies={lanes:0,firstContext:e}}else Co=Co.next=e;return t}var _o=null;function Vo(e){null===_o?_o=[e]:_o.push(e)}function No(e,t,n,r){var i=t.interleaved;return null===i?(n.next=n,Vo(t)):(n.next=i.next,i.next=n),t.interleaved=n,zo(e,r)}function zo(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Fo=!1;function Oo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Io(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function jo(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Bo(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,2&Ll){var i=r.pending;return null===i?t.next=t:(t.next=i.next,i.next=t),r.pending=t,zo(e,n)}return null===(i=r.interleaved)?(t.next=t,Vo(r)):(t.next=i.next,i.next=t),r.interleaved=t,zo(e,n)}function Uo(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,bt(e,n)}}function $o(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var i=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===o?i=o=a:o=o.next=a,n=n.next}while(null!==n);null===o?i=o=t:o=o.next=t}else i=o=t;return n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Wo(e,t,n,r){var i=e.updateQueue;Fo=!1;var o=i.firstBaseUpdate,a=i.lastBaseUpdate,s=i.shared.pending;if(null!==s){i.shared.pending=null;var l=s,u=l.next;l.next=null,null===a?o=u:a.next=u,a=l;var c=e.alternate;null!==c&&((s=(c=c.updateQueue).lastBaseUpdate)!==a&&(null===s?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=l))}if(null!==o){var d=i.baseState;for(a=0,c=u=l=null,s=o;;){var f=s.lane,p=s.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var h=e,m=s;switch(f=t,p=n,m.tag){case 1:if("function"==typeof(h=m.payload)){d=h.call(p,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null==(f="function"==typeof(h=m.payload)?h.call(p,d,f):h))break e;d=O({},d,f);break e;case 2:Fo=!0}}null!==s.callback&&0!==s.lane&&(e.flags|=64,null===(f=i.effects)?i.effects=[s]:f.push(s))}else p={eventTime:p,lane:f,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===c?(u=c=p,l=d):c=c.next=p,a|=f;if(null===(s=s.next)){if(null===(s=i.shared.pending))break;s=(f=s).next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}if(null===c&&(l=d),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,null!==(t=i.shared.interleaved)){i=t;do{a|=i.lane,i=i.next}while(i!==t)}else null===o&&(i.shared.lanes=0);zl|=a,e.lanes=a,e.memoizedState=d}}function Ho(e,t,r){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var i=e[t],o=i.callback;if(null!==o){if(i.callback=null,i=r,"function"!=typeof o)throw Error(n(191,o));o.call(i)}}}var Qo={},Yo=Ei(Qo),qo=Ei(Qo),Xo=Ei(Qo);function Ko(e){if(e===Qo)throw Error(n(174));return e}function Go(e,t){switch(Ti(Xo,t),Ti(qo,e),Ti(Yo,Qo),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ue(null,"");break;default:t=ue(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Ci(Yo),Ti(Yo,t)}function Zo(){Ci(Yo),Ci(qo),Ci(Xo)}function Jo(e){Ko(Xo.current);var t=Ko(Yo.current),n=ue(t,e.type);t!==n&&(Ti(qo,e),Ti(Yo,n))}function ea(e){qo.current===e&&(Ci(Yo),Ci(qo))}var ta=Ei(0);function na(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ra=[];function ia(){for(var e=0;e<ra.length;e++)ra[e]._workInProgressVersionPrimary=null;ra.length=0}var oa=x.ReactCurrentDispatcher,aa=x.ReactCurrentBatchConfig,sa=0,la=null,ua=null,ca=null,da=!1,fa=!1,pa=0,ha=0;function ma(){throw Error(n(321))}function ga(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!lr(e[n],t[n]))return!1;return!0}function ya(e,t,r,i,o,a){if(sa=a,la=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,oa.current=null===e||null===e.memoizedState?es:ts,e=r(i,o),fa){a=0;do{if(fa=!1,pa=0,25<=a)throw Error(n(301));a+=1,ca=ua=null,t.updateQueue=null,oa.current=ns,e=r(i,o)}while(fa)}if(oa.current=Ja,t=null!==ua&&null!==ua.next,sa=0,ca=ua=la=null,da=!1,t)throw Error(n(300));return e}function va(){var e=0!==pa;return pa=0,e}function ba(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ca?la.memoizedState=ca=e:ca=ca.next=e,ca}function xa(){if(null===ua){var e=la.alternate;e=null!==e?e.memoizedState:null}else e=ua.next;var t=null===ca?la.memoizedState:ca.next;if(null!==t)ca=t,ua=e;else{if(null===e)throw Error(n(310));e={memoizedState:(ua=e).memoizedState,baseState:ua.baseState,baseQueue:ua.baseQueue,queue:ua.queue,next:null},null===ca?la.memoizedState=ca=e:ca=ca.next=e}return ca}function wa(e,t){return"function"==typeof t?t(e):t}function ka(e){var t=xa(),r=t.queue;if(null===r)throw Error(n(311));r.lastRenderedReducer=e;var i=ua,o=i.baseQueue,a=r.pending;if(null!==a){if(null!==o){var s=o.next;o.next=a.next,a.next=s}i.baseQueue=o=a,r.pending=null}if(null!==o){a=o.next,i=i.baseState;var l=s=null,u=null,c=a;do{var d=c.lane;if((sa&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),i=c.hasEagerState?c.eagerState:e(i,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(l=u=f,s=i):u=u.next=f,la.lanes|=d,zl|=d}c=c.next}while(null!==c&&c!==a);null===u?s=i:u.next=l,lr(i,t.memoizedState)||(bs=!0),t.memoizedState=i,t.baseState=s,t.baseQueue=u,r.lastRenderedState=i}if(null!==(e=r.interleaved)){o=e;do{a=o.lane,la.lanes|=a,zl|=a,o=o.next}while(o!==e)}else null===o&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function Sa(e){var t=xa(),r=t.queue;if(null===r)throw Error(n(311));r.lastRenderedReducer=e;var i=r.dispatch,o=r.pending,a=t.memoizedState;if(null!==o){r.pending=null;var s=o=o.next;do{a=e(a,s.action),s=s.next}while(s!==o);lr(a,t.memoizedState)||(bs=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),r.lastRenderedState=a}return[a,i]}function Pa(){}function Ea(e,t){var r=la,i=xa(),o=t(),a=!lr(i.memoizedState,o);if(a&&(i.memoizedState=o,bs=!0),i=i.queue,Fa(La.bind(null,r,i,e),[e]),i.getSnapshot!==t||a||null!==ca&&1&ca.memoizedState.tag){if(r.flags|=2048,Aa(9,Ta.bind(null,r,i,o,t),void 0,null),null===Ml)throw Error(n(349));30&sa||Ca(r,t,o)}return o}function Ca(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=la.updateQueue)?(t={lastEffect:null,stores:null},la.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ta(e,t,n,r){t.value=n,t.getSnapshot=r,Ma(t)&&Ra(e)}function La(e,t,n){return n((function(){Ma(t)&&Ra(e)}))}function Ma(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!lr(e,n)}catch(r){return!0}}function Ra(e){var t=zo(e,1);null!==t&&nu(t,e,1,-1)}function Da(e){var t=ba();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:wa,lastRenderedState:e},t.queue=e,e=e.dispatch=Xa.bind(null,la,e),[t.memoizedState,e]}function Aa(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=la.updateQueue)?(t={lastEffect:null,stores:null},la.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function _a(){return xa().memoizedState}function Va(e,t,n,r){var i=ba();la.flags|=e,i.memoizedState=Aa(1|t,n,void 0,void 0===r?null:r)}function Na(e,t,n,r){var i=xa();r=void 0===r?null:r;var o=void 0;if(null!==ua){var a=ua.memoizedState;if(o=a.destroy,null!==r&&ga(r,a.deps))return void(i.memoizedState=Aa(t,n,o,r))}la.flags|=e,i.memoizedState=Aa(1|t,n,o,r)}function za(e,t){return Va(8390656,8,e,t)}function Fa(e,t){return Na(2048,8,e,t)}function Oa(e,t){return Na(4,2,e,t)}function Ia(e,t){return Na(4,4,e,t)}function ja(e,t){return"function"==typeof t?(e=e(),t(e),function(){t(null)}):null!=t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Ba(e,t,n){return n=null!=n?n.concat([e]):null,Na(4,4,ja.bind(null,t,e),n)}function Ua(){}function $a(e,t){var n=xa();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ga(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Wa(e,t){var n=xa();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ga(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Ha(e,t,n){return 21&sa?(lr(n,t)||(n=gt(),la.lanes|=n,zl|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,bs=!0),e.memoizedState=n)}function Qa(e,t){var n=xt;xt=0!==n&&4>n?n:4,e(!0);var r=aa.transition;aa.transition={};try{e(!1),t()}finally{xt=n,aa.transition=r}}function Ya(){return xa().memoizedState}function qa(e,t,n){var r=tu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Ka(e))Ga(t,n);else if(null!==(n=No(e,t,n,r))){nu(n,e,r,eu()),Za(n,t,r)}}function Xa(e,t,n){var r=tu(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ka(e))Ga(t,i);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var a=t.lastRenderedState,s=o(a,n);if(i.hasEagerState=!0,i.eagerState=s,lr(s,a)){var l=t.interleaved;return null===l?(i.next=i,Vo(t)):(i.next=l.next,l.next=i),void(t.interleaved=i)}}catch(u){}null!==(n=No(e,t,i,r))&&(nu(n,e,r,i=eu()),Za(n,t,r))}}function Ka(e){var t=e.alternate;return e===la||null!==t&&t===la}function Ga(e,t){fa=da=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Za(e,t,n){if(4194240&n){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,bt(e,n)}}var Ja={readContext:Ao,useCallback:ma,useContext:ma,useEffect:ma,useImperativeHandle:ma,useInsertionEffect:ma,useLayoutEffect:ma,useMemo:ma,useReducer:ma,useRef:ma,useState:ma,useDebugValue:ma,useDeferredValue:ma,useTransition:ma,useMutableSource:ma,useSyncExternalStore:ma,useId:ma,unstable_isNewReconciler:!1},es={readContext:Ao,useCallback:function(e,t){return ba().memoizedState=[e,void 0===t?null:t],e},useContext:Ao,useEffect:za,useImperativeHandle:function(e,t,n){return n=null!=n?n.concat([e]):null,Va(4194308,4,ja.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Va(4194308,4,e,t)},useInsertionEffect:function(e,t){return Va(4,2,e,t)},useMemo:function(e,t){var n=ba();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=ba();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=qa.bind(null,la,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},ba().memoizedState=e},useState:Da,useDebugValue:Ua,useDeferredValue:function(e){return ba().memoizedState=e},useTransition:function(){var e=Da(!1),t=e[0];return e=Qa.bind(null,e[1]),ba().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var i=la,o=ba();if(oo){if(void 0===r)throw Error(n(407));r=r()}else{if(r=t(),null===Ml)throw Error(n(349));30&sa||Ca(i,t,r)}o.memoizedState=r;var a={value:r,getSnapshot:t};return o.queue=a,za(La.bind(null,i,a,e),[e]),i.flags|=2048,Aa(9,Ta.bind(null,i,a,r,t),void 0,null),r},useId:function(){var e=ba(),t=Ml.identifierPrefix;if(oo){var n=Zi;t=":"+t+"R"+(n=(Gi&~(1<<32-st(Gi)-1)).toString(32)+n),0<(n=pa++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=ha++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ts={readContext:Ao,useCallback:$a,useContext:Ao,useEffect:Fa,useImperativeHandle:Ba,useInsertionEffect:Oa,useLayoutEffect:Ia,useMemo:Wa,useReducer:ka,useRef:_a,useState:function(){return ka(wa)},useDebugValue:Ua,useDeferredValue:function(e){return Ha(xa(),ua.memoizedState,e)},useTransition:function(){return[ka(wa)[0],xa().memoizedState]},useMutableSource:Pa,useSyncExternalStore:Ea,useId:Ya,unstable_isNewReconciler:!1},ns={readContext:Ao,useCallback:$a,useContext:Ao,useEffect:Fa,useImperativeHandle:Ba,useInsertionEffect:Oa,useLayoutEffect:Ia,useMemo:Wa,useReducer:Sa,useRef:_a,useState:function(){return Sa(wa)},useDebugValue:Ua,useDeferredValue:function(e){var t=xa();return null===ua?t.memoizedState=e:Ha(t,ua.memoizedState,e)},useTransition:function(){return[Sa(wa)[0],xa().memoizedState]},useMutableSource:Pa,useSyncExternalStore:Ea,useId:Ya,unstable_isNewReconciler:!1};function rs(e,t){if(e&&e.defaultProps){for(var n in t=O({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function is(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:O({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var os={isMounted:function(e){return!!(e=e._reactInternals)&&$e(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=eu(),i=tu(e),o=jo(r,i);o.payload=t,null!=n&&(o.callback=n),null!==(t=Bo(e,o,i))&&(nu(t,e,i,r),Uo(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=eu(),i=tu(e),o=jo(r,i);o.tag=1,o.payload=t,null!=n&&(o.callback=n),null!==(t=Bo(e,o,i))&&(nu(t,e,i,r),Uo(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=eu(),r=tu(e),i=jo(n,r);i.tag=2,null!=t&&(i.callback=t),null!==(t=Bo(e,i,r))&&(nu(t,e,r,n),Uo(t,e,r))}};function as(e,t,n,r,i,o,a){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,a):!t.prototype||!t.prototype.isPureReactComponent||(!ur(n,r)||!ur(i,o))}function ss(e,t,n){var r=!1,i=Li,o=t.contextType;return"object"==typeof o&&null!==o?o=Ao(o):(i=_i(t)?Di:Mi.current,o=(r=null!=(r=t.contextTypes))?Ai(e,i):Li),t=new t(n,o),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=os,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function ls(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&os.enqueueReplaceState(t,t.state,null)}function us(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Oo(e);var o=t.contextType;"object"==typeof o&&null!==o?i.context=Ao(o):(o=_i(t)?Di:Mi.current,i.context=Ai(e,o)),i.state=e.memoizedState,"function"==typeof(o=t.getDerivedStateFromProps)&&(is(e,t,o,n),i.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof i.getSnapshotBeforeUpdate||"function"!=typeof i.UNSAFE_componentWillMount&&"function"!=typeof i.componentWillMount||(t=i.state,"function"==typeof i.componentWillMount&&i.componentWillMount(),"function"==typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount(),t!==i.state&&os.enqueueReplaceState(i,i.state,null),Wo(e,n,i,r),i.state=e.memoizedState),"function"==typeof i.componentDidMount&&(e.flags|=4194308)}function cs(e,t){try{var n="",r=t;do{n+=U(r),r=r.return}while(r);var i=n}catch(o){i="\nError generating stack: "+o.message+"\n"+o.stack}return{value:e,source:t,stack:i,digest:null}}function ds(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}var fs="function"==typeof WeakMap?WeakMap:Map;function ps(e,t,n){(n=jo(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Wl||(Wl=!0,Hl=r)},n}function hs(e,t,n){(n=jo(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"==typeof r){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){}}var o=e.stateNode;return null!==o&&"function"==typeof o.componentDidCatch&&(n.callback=function(){"function"!=typeof r&&(null===Ql?Ql=new Set([this]):Ql.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function ms(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fs;var i=new Set;r.set(t,i)}else void 0===(i=r.get(t))&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=Eu.bind(null,e,t,n),t.then(e,e))}function gs(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function ys(e,t,n,r,i){return 1&e.mode?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=jo(-1,1)).tag=2,Bo(n,t,1))),n.lanes|=1),e)}var vs=x.ReactCurrentOwner,bs=!1;function xs(e,t,n,r){t.child=null===e?So(t,null,n,r):ko(t,e.child,n,r)}function ws(e,t,n,r,i){n=n.render;var o=t.ref;return Do(t,i),r=ya(e,t,n,r,o,i),n=va(),null===e||bs?(oo&&n&&to(t),t.flags|=1,xs(e,t,r,i),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Ws(e,t,i))}function ks(e,t,n,r,i){if(null===e){var o=n.type;return"function"!=typeof o||Au(o)||void 0!==o.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Vu(n.type,null,r,t,t.mode,i)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,Ss(e,t,o,r,i))}if(o=e.child,0===(e.lanes&i)){var a=o.memoizedProps;if((n=null!==(n=n.compare)?n:ur)(a,r)&&e.ref===t.ref)return Ws(e,t,i)}return t.flags|=1,(e=_u(o,r)).ref=t.ref,e.return=t,t.child=e}function Ss(e,t,n,r,i){if(null!==e){var o=e.memoizedProps;if(ur(o,r)&&e.ref===t.ref){if(bs=!1,t.pendingProps=r=o,0===(e.lanes&i))return t.lanes=e.lanes,Ws(e,t,i);131072&e.flags&&(bs=!0)}}return Cs(e,t,n,r,i)}function Ps(e,t,n){var r=t.pendingProps,i=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(1&t.mode){if(!(1073741824&n))return e=null!==o?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ti(_l,Al),Al|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==o?o.baseLanes:n,Ti(_l,Al),Al|=r}else t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ti(_l,Al),Al|=n;else null!==o?(r=o.baseLanes|n,t.memoizedState=null):r=n,Ti(_l,Al),Al|=r;return xs(e,t,i,n),t.child}function Es(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Cs(e,t,n,r,i){var o=_i(n)?Di:Mi.current;return o=Ai(t,o),Do(t,i),n=ya(e,t,n,r,o,i),r=va(),null===e||bs?(oo&&r&&to(t),t.flags|=1,xs(e,t,n,i),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Ws(e,t,i))}function Ts(e,t,n,r,i){if(_i(n)){var o=!0;Fi(t)}else o=!1;if(Do(t,i),null===t.stateNode)$s(e,t),ss(t,n,r),us(t,n,r,i),r=!0;else if(null===e){var a=t.stateNode,s=t.memoizedProps;a.props=s;var l=a.context,u=n.contextType;"object"==typeof u&&null!==u?u=Ao(u):u=Ai(t,u=_i(n)?Di:Mi.current);var c=n.getDerivedStateFromProps,d="function"==typeof c||"function"==typeof a.getSnapshotBeforeUpdate;d||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(s!==r||l!==u)&&ls(t,a,r,u),Fo=!1;var f=t.memoizedState;a.state=f,Wo(t,r,a,i),l=t.memoizedState,s!==r||f!==l||Ri.current||Fo?("function"==typeof c&&(is(t,n,c,r),l=t.memoizedState),(s=Fo||as(t,n,s,r,f,l,u))?(d||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||("function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"==typeof a.componentDidMount&&(t.flags|=4194308)):("function"==typeof a.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),a.props=r,a.state=l,a.context=u,r=s):("function"==typeof a.componentDidMount&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Io(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:rs(t.type,s),a.props=u,d=t.pendingProps,f=a.context,"object"==typeof(l=n.contextType)&&null!==l?l=Ao(l):l=Ai(t,l=_i(n)?Di:Mi.current);var p=n.getDerivedStateFromProps;(c="function"==typeof p||"function"==typeof a.getSnapshotBeforeUpdate)||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(s!==d||f!==l)&&ls(t,a,r,l),Fo=!1,f=t.memoizedState,a.state=f,Wo(t,r,a,i);var h=t.memoizedState;s!==d||f!==h||Ri.current||Fo?("function"==typeof p&&(is(t,n,p,r),h=t.memoizedState),(u=Fo||as(t,n,u,r,f,h,l)||!1)?(c||"function"!=typeof a.UNSAFE_componentWillUpdate&&"function"!=typeof a.componentWillUpdate||("function"==typeof a.componentWillUpdate&&a.componentWillUpdate(r,h,l),"function"==typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(r,h,l)),"function"==typeof a.componentDidUpdate&&(t.flags|=4),"function"==typeof a.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof a.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),a.props=r,a.state=h,a.context=l,r=u):("function"!=typeof a.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Ls(e,t,n,r,o,i)}function Ls(e,t,n,r,i,o){Es(e,t);var a=!!(128&t.flags);if(!r&&!a)return i&&Oi(t,n,!1),Ws(e,t,o);r=t.stateNode,vs.current=t;var s=a&&"function"!=typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&a?(t.child=ko(t,e.child,null,o),t.child=ko(t,null,s,o)):xs(e,t,s,o),t.memoizedState=r.state,i&&Oi(t,n,!0),t.child}function Ms(e){var t=e.stateNode;t.pendingContext?Ni(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Ni(0,t.context,!1),Go(e,t.containerInfo)}function Rs(e,t,n,r,i){return mo(),go(i),t.flags|=256,xs(e,t,n,r),t.child}var Ds,As,_s,Vs,Ns={dehydrated:null,treeContext:null,retryLane:0};function zs(e){return{baseLanes:e,cachePool:null,transitions:null}}function Fs(e,t,r){var i,o=t.pendingProps,a=ta.current,s=!1,l=!!(128&t.flags);if((i=l)||(i=(null===e||null!==e.memoizedState)&&!!(2&a)),i?(s=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(a|=1),Ti(ta,1&a),null===e)return co(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(1&t.mode?"$!"===e.data?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(l=o.children,e=o.fallback,s?(o=t.mode,s=t.child,l={mode:"hidden",children:l},1&o||null===s?s=zu(l,o,0,null):(s.childLanes=0,s.pendingProps=l),e=Nu(e,o,r,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=zs(r),t.memoizedState=Ns,e):Os(t,l));if(null!==(a=e.memoizedState)&&null!==(i=a.dehydrated))return function(e,t,r,i,o,a,s){if(r)return 256&t.flags?(t.flags&=-257,Is(e,t,s,i=ds(Error(n(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(a=i.fallback,o=t.mode,i=zu({mode:"visible",children:i.children},o,0,null),(a=Nu(a,o,s,null)).flags|=2,i.return=t,a.return=t,i.sibling=a,t.child=i,1&t.mode&&ko(t,e.child,null,s),t.child.memoizedState=zs(s),t.memoizedState=Ns,a);if(!(1&t.mode))return Is(e,t,s,null);if("$!"===o.data){if(i=o.nextSibling&&o.nextSibling.dataset)var l=i.dgst;return i=l,Is(e,t,s,i=ds(a=Error(n(419)),i,void 0))}if(l=0!==(s&e.childLanes),bs||l){if(null!==(i=Ml)){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}0!==(o=0!==(o&(i.suspendedLanes|s))?0:o)&&o!==a.retryLane&&(a.retryLane=o,zo(e,o),nu(i,e,o,-1))}return mu(),Is(e,t,s,i=ds(Error(n(421))))}return"$?"===o.data?(t.flags|=128,t.child=e.child,t=Tu.bind(null,e),o._reactRetry=t,null):(e=a.treeContext,io=ci(o.nextSibling),ro=t,oo=!0,ao=null,null!==e&&(qi[Xi++]=Gi,qi[Xi++]=Zi,qi[Xi++]=Ki,Gi=e.id,Zi=e.overflow,Ki=t),t=Os(t,i.children),t.flags|=4096,t)}(e,t,l,o,i,a,r);if(s){s=o.fallback,l=t.mode,i=(a=e.child).sibling;var u={mode:"hidden",children:o.children};return 1&l||t.child===a?(o=_u(a,u)).subtreeFlags=14680064&a.subtreeFlags:((o=t.child).childLanes=0,o.pendingProps=u,t.deletions=null),null!==i?s=_u(i,s):(s=Nu(s,l,r,null)).flags|=2,s.return=t,o.return=t,o.sibling=s,t.child=o,o=s,s=t.child,l=null===(l=e.child.memoizedState)?zs(r):{baseLanes:l.baseLanes|r,cachePool:null,transitions:l.transitions},s.memoizedState=l,s.childLanes=e.childLanes&~r,t.memoizedState=Ns,o}return e=(s=e.child).sibling,o=_u(s,{mode:"visible",children:o.children}),!(1&t.mode)&&(o.lanes=r),o.return=t,o.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=o,t.memoizedState=null,o}function Os(e,t){return(t=zu({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Is(e,t,n,r){return null!==r&&go(r),ko(t,e.child,null,n),(e=Os(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function js(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Ro(e.return,t,n)}function Bs(e,t,n,r,i){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function Us(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(xs(e,t,r.children,n),2&(r=ta.current))r=1&r|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&js(e,n,t);else if(19===e.tag)js(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ti(ta,r),1&t.mode)switch(i){case"forwards":for(n=t.child,i=null;null!==n;)null!==(e=n.alternate)&&null===na(e)&&(i=n),n=n.sibling;null===(n=i)?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Bs(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;null!==i;){if(null!==(e=i.alternate)&&null===na(e)){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Bs(t,!0,n,null,o);break;case"together":Bs(t,!1,null,null,void 0);break;default:t.memoizedState=null}else t.memoizedState=null;return t.child}function $s(e,t){!(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ws(e,t,r){if(null!==e&&(t.dependencies=e.dependencies),zl|=t.lanes,0===(r&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(n(153));if(null!==t.child){for(r=_u(e=t.child,e.pendingProps),t.child=r,r.return=t;null!==e.sibling;)e=e.sibling,(r=r.sibling=_u(e,e.pendingProps)).return=t;r.sibling=null}return t.child}function Hs(e,t){if(!oo)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Qs(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;null!==i;)n|=i.lanes|i.childLanes,r|=14680064&i.subtreeFlags,r|=14680064&i.flags,i.return=e,i=i.sibling;else for(i=e.child;null!==i;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Ys(e,t,r){var o=t.pendingProps;switch(no(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Qs(t),null;case 1:case 17:return _i(t.type)&&Vi(),Qs(t),null;case 3:return o=t.stateNode,Zo(),Ci(Ri),Ci(Mi),ia(),o.pendingContext&&(o.context=o.pendingContext,o.pendingContext=null),null!==e&&null!==e.child||(po(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,null!==ao&&(au(ao),ao=null))),As(e,t),Qs(t),null;case 5:ea(t);var a=Ko(Xo.current);if(r=t.type,null!==e&&null!=t.stateNode)_s(e,t,r,o,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!o){if(null===t.stateNode)throw Error(n(166));return Qs(t),null}if(e=Ko(Yo.current),po(t)){o=t.stateNode,r=t.type;var s=t.memoizedProps;switch(o[pi]=t,o[hi]=s,e=!!(1&t.mode),r){case"dialog":jr("cancel",o),jr("close",o);break;case"iframe":case"object":case"embed":jr("load",o);break;case"video":case"audio":for(a=0;a<zr.length;a++)jr(zr[a],o);break;case"source":jr("error",o);break;case"img":case"image":case"link":jr("error",o),jr("load",o);break;case"details":jr("toggle",o);break;case"input":G(o,s),jr("invalid",o);break;case"select":o._wrapperState={wasMultiple:!!s.multiple},jr("invalid",o);break;case"textarea":oe(o,s),jr("invalid",o)}for(var l in be(r,s),a=null,s)if(s.hasOwnProperty(l)){var u=s[l];"children"===l?"string"==typeof u?o.textContent!==u&&(!0!==s.suppressHydrationWarning&&Jr(o.textContent,u,e),a=["children",u]):"number"==typeof u&&o.textContent!==""+u&&(!0!==s.suppressHydrationWarning&&Jr(o.textContent,u,e),a=["children",""+u]):i.hasOwnProperty(l)&&null!=u&&"onScroll"===l&&jr("scroll",o)}switch(r){case"input":Y(o),ee(o,s,!0);break;case"textarea":Y(o),se(o);break;case"select":case"option":break;default:"function"==typeof s.onClick&&(o.onclick=ei)}o=a,t.updateQueue=o,null!==o&&(t.flags|=4)}else{l=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=le(r)),"http://www.w3.org/1999/xhtml"===e?"script"===r?((e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"==typeof o.is?e=l.createElement(r,{is:o.is}):(e=l.createElement(r),"select"===r&&(l=e,o.multiple?l.multiple=!0:o.size&&(l.size=o.size))):e=l.createElementNS(e,r),e[pi]=t,e[hi]=o,Ds(e,t,!1,!1),t.stateNode=e;e:{switch(l=xe(r,o),r){case"dialog":jr("cancel",e),jr("close",e),a=o;break;case"iframe":case"object":case"embed":jr("load",e),a=o;break;case"video":case"audio":for(a=0;a<zr.length;a++)jr(zr[a],e);a=o;break;case"source":jr("error",e),a=o;break;case"img":case"image":case"link":jr("error",e),jr("load",e),a=o;break;case"details":jr("toggle",e),a=o;break;case"input":G(e,o),a=K(e,o),jr("invalid",e);break;case"option":default:a=o;break;case"select":e._wrapperState={wasMultiple:!!o.multiple},a=O({},o,{value:void 0}),jr("invalid",e);break;case"textarea":oe(e,o),a=ie(e,o),jr("invalid",e)}for(s in be(r,a),u=a)if(u.hasOwnProperty(s)){var c=u[s];"style"===s?ye(e,c):"dangerouslySetInnerHTML"===s?null!=(c=c?c.__html:void 0)&&fe(e,c):"children"===s?"string"==typeof c?("textarea"!==r||""!==c)&&pe(e,c):"number"==typeof c&&pe(e,""+c):"suppressContentEditableWarning"!==s&&"suppressHydrationWarning"!==s&&"autoFocus"!==s&&(i.hasOwnProperty(s)?null!=c&&"onScroll"===s&&jr("scroll",e):null!=c&&b(e,s,c,l))}switch(r){case"input":Y(e),ee(e,o,!1);break;case"textarea":Y(e),se(e);break;case"option":null!=o.value&&e.setAttribute("value",""+H(o.value));break;case"select":e.multiple=!!o.multiple,null!=(s=o.value)?re(e,!!o.multiple,s,!1):null!=o.defaultValue&&re(e,!!o.multiple,o.defaultValue,!0);break;default:"function"==typeof a.onClick&&(e.onclick=ei)}switch(r){case"button":case"input":case"select":case"textarea":o=!!o.autoFocus;break e;case"img":o=!0;break e;default:o=!1}}o&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Qs(t),null;case 6:if(e&&null!=t.stateNode)Vs(e,t,e.memoizedProps,o);else{if("string"!=typeof o&&null===t.stateNode)throw Error(n(166));if(r=Ko(Xo.current),Ko(Yo.current),po(t)){if(o=t.stateNode,r=t.memoizedProps,o[pi]=t,(s=o.nodeValue!==r)&&null!==(e=ro))switch(e.tag){case 3:Jr(o.nodeValue,r,!!(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Jr(o.nodeValue,r,!!(1&e.mode))}s&&(t.flags|=4)}else(o=(9===r.nodeType?r:r.ownerDocument).createTextNode(o))[pi]=t,t.stateNode=o}return Qs(t),null;case 13:if(Ci(ta),o=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(oo&&null!==io&&1&t.mode&&!(128&t.flags))ho(),mo(),t.flags|=98560,s=!1;else if(s=po(t),null!==o&&null!==o.dehydrated){if(null===e){if(!s)throw Error(n(318));if(!(s=null!==(s=t.memoizedState)?s.dehydrated:null))throw Error(n(317));s[pi]=t}else mo(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Qs(t),s=!1}else null!==ao&&(au(ao),ao=null),s=!0;if(!s)return 65536&t.flags?t:null}return 128&t.flags?(t.lanes=r,t):((o=null!==o)!==(null!==e&&null!==e.memoizedState)&&o&&(t.child.flags|=8192,1&t.mode&&(null===e||1&ta.current?0===Vl&&(Vl=3):mu())),null!==t.updateQueue&&(t.flags|=4),Qs(t),null);case 4:return Zo(),As(e,t),null===e&&$r(t.stateNode.containerInfo),Qs(t),null;case 10:return Mo(t.type._context),Qs(t),null;case 19:if(Ci(ta),null===(s=t.memoizedState))return Qs(t),null;if(o=!!(128&t.flags),null===(l=s.rendering))if(o)Hs(s,!1);else{if(0!==Vl||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(l=na(e))){for(t.flags|=128,Hs(s,!1),null!==(o=l.updateQueue)&&(t.updateQueue=o,t.flags|=4),t.subtreeFlags=0,o=r,r=t.child;null!==r;)e=o,(s=r).flags&=14680066,null===(l=s.alternate)?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=l.childLanes,s.lanes=l.lanes,s.child=l.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=l.memoizedProps,s.memoizedState=l.memoizedState,s.updateQueue=l.updateQueue,s.type=l.type,e=l.dependencies,s.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return Ti(ta,1&ta.current|2),t.child}e=e.sibling}null!==s.tail&&Ze()>Ul&&(t.flags|=128,o=!0,Hs(s,!1),t.lanes=4194304)}else{if(!o)if(null!==(e=na(l))){if(t.flags|=128,o=!0,null!==(r=e.updateQueue)&&(t.updateQueue=r,t.flags|=4),Hs(s,!0),null===s.tail&&"hidden"===s.tailMode&&!l.alternate&&!oo)return Qs(t),null}else 2*Ze()-s.renderingStartTime>Ul&&1073741824!==r&&(t.flags|=128,o=!0,Hs(s,!1),t.lanes=4194304);s.isBackwards?(l.sibling=t.child,t.child=l):(null!==(r=s.last)?r.sibling=l:t.child=l,s.last=l)}return null!==s.tail?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=Ze(),t.sibling=null,r=ta.current,Ti(ta,o?1&r|2:1&r),t):(Qs(t),null);case 22:case 23:return du(),o=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==o&&(t.flags|=8192),o&&1&t.mode?!!(1073741824&Al)&&(Qs(t),6&t.subtreeFlags&&(t.flags|=8192)):Qs(t),null;case 24:case 25:return null}throw Error(n(156,t.tag))}function qs(e,t){switch(no(t),t.tag){case 1:return _i(t.type)&&Vi(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Zo(),Ci(Ri),Ci(Mi),ia(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 5:return ea(t),null;case 13:if(Ci(ta),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(n(340));mo()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Ci(ta),null;case 4:return Zo(),null;case 10:return Mo(t.type._context),null;case 22:case 23:return du(),null;default:return null}}Ds=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},As=function(){},_s=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Ko(Yo.current);var a,s=null;switch(n){case"input":o=K(e,o),r=K(e,r),s=[];break;case"select":o=O({},o,{value:void 0}),r=O({},r,{value:void 0}),s=[];break;case"textarea":o=ie(e,o),r=ie(e,r),s=[];break;default:"function"!=typeof o.onClick&&"function"==typeof r.onClick&&(e.onclick=ei)}for(c in be(n,r),n=null,o)if(!r.hasOwnProperty(c)&&o.hasOwnProperty(c)&&null!=o[c])if("style"===c){var l=o[c];for(a in l)l.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(i.hasOwnProperty(c)?s||(s=[]):(s=s||[]).push(c,null));for(c in r){var u=r[c];if(l=null!=o?o[c]:void 0,r.hasOwnProperty(c)&&u!==l&&(null!=u||null!=l))if("style"===c)if(l){for(a in l)!l.hasOwnProperty(a)||u&&u.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in u)u.hasOwnProperty(a)&&l[a]!==u[a]&&(n||(n={}),n[a]=u[a])}else n||(s||(s=[]),s.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,l=l?l.__html:void 0,null!=u&&l!==u&&(s=s||[]).push(c,u)):"children"===c?"string"!=typeof u&&"number"!=typeof u||(s=s||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(i.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&jr("scroll",e),s||l===u||(s=[])):(s=s||[]).push(c,u))}n&&(s=s||[]).push("style",n);var c=s;(t.updateQueue=c)&&(t.flags|=4)}},Vs=function(e,t,n,r){n!==r&&(t.flags|=4)};var Xs=!1,Ks=!1,Gs="function"==typeof WeakSet?WeakSet:Set,Zs=null;function Js(e,t){var n=e.ref;if(null!==n)if("function"==typeof n)try{n(null)}catch(r){Pu(e,t,r)}else n.current=null}function el(e,t,n){try{n()}catch(r){Pu(e,t,r)}}var tl=!1;function nl(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,void 0!==o&&el(t,n,o)}i=i.next}while(i!==r)}}function rl(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function il(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"==typeof t?t(e):t.current=e}}function ol(e){var t=e.alternate;null!==t&&(e.alternate=null,ol(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[pi],delete t[hi],delete t[gi],delete t[yi],delete t[vi])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function al(e){return 5===e.tag||3===e.tag||4===e.tag}function sl(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||al(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ll(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=ei));else if(4!==r&&null!==(e=e.child))for(ll(e,t,n),e=e.sibling;null!==e;)ll(e,t,n),e=e.sibling}function ul(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(ul(e,t,n),e=e.sibling;null!==e;)ul(e,t,n),e=e.sibling}var cl=null,dl=!1;function fl(e,t,n){for(n=n.child;null!==n;)pl(e,t,n),n=n.sibling}function pl(e,t,n){if(at&&"function"==typeof at.onCommitFiberUnmount)try{at.onCommitFiberUnmount(ot,n)}catch(s){}switch(n.tag){case 5:Ks||Js(n,t);case 6:var r=cl,i=dl;cl=null,fl(e,t,n),dl=i,null!==(cl=r)&&(dl?(e=cl,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):cl.removeChild(n.stateNode));break;case 18:null!==cl&&(dl?(e=cl,n=n.stateNode,8===e.nodeType?ui(e.parentNode,n):1===e.nodeType&&ui(e,n),$t(e)):ui(cl,n.stateNode));break;case 4:r=cl,i=dl,cl=n.stateNode.containerInfo,dl=!0,fl(e,t,n),cl=r,dl=i;break;case 0:case 11:case 14:case 15:if(!Ks&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){i=r=r.next;do{var o=i,a=o.destroy;o=o.tag,void 0!==a&&(2&o||4&o)&&el(n,t,a),i=i.next}while(i!==r)}fl(e,t,n);break;case 1:if(!Ks&&(Js(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){Pu(n,t,s)}fl(e,t,n);break;case 21:fl(e,t,n);break;case 22:1&n.mode?(Ks=(r=Ks)||null!==n.memoizedState,fl(e,t,n),Ks=r):fl(e,t,n);break;default:fl(e,t,n)}}function hl(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Gs),t.forEach((function(t){var r=Lu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function ml(e,t){var r=t.deletions;if(null!==r)for(var i=0;i<r.length;i++){var o=r[i];try{var a=e,s=t,l=s;e:for(;null!==l;){switch(l.tag){case 5:cl=l.stateNode,dl=!1;break e;case 3:case 4:cl=l.stateNode.containerInfo,dl=!0;break e}l=l.return}if(null===cl)throw Error(n(160));pl(a,s,o),cl=null,dl=!1;var u=o.alternate;null!==u&&(u.return=null),o.return=null}catch(c){Pu(o,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gl(t,e),t=t.sibling}function gl(e,t){var r=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ml(t,e),yl(e),4&i){try{nl(3,e,e.return),rl(3,e)}catch(g){Pu(e,e.return,g)}try{nl(5,e,e.return)}catch(g){Pu(e,e.return,g)}}break;case 1:ml(t,e),yl(e),512&i&&null!==r&&Js(r,r.return);break;case 5:if(ml(t,e),yl(e),512&i&&null!==r&&Js(r,r.return),32&e.flags){var o=e.stateNode;try{pe(o,"")}catch(g){Pu(e,e.return,g)}}if(4&i&&null!=(o=e.stateNode)){var a=e.memoizedProps,s=null!==r?r.memoizedProps:a,l=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===l&&"radio"===a.type&&null!=a.name&&Z(o,a),xe(l,s);var c=xe(l,a);for(s=0;s<u.length;s+=2){var d=u[s],f=u[s+1];"style"===d?ye(o,f):"dangerouslySetInnerHTML"===d?fe(o,f):"children"===d?pe(o,f):b(o,d,f,c)}switch(l){case"input":J(o,a);break;case"textarea":ae(o,a);break;case"select":var p=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!a.multiple;var h=a.value;null!=h?re(o,!!a.multiple,h,!1):p!==!!a.multiple&&(null!=a.defaultValue?re(o,!!a.multiple,a.defaultValue,!0):re(o,!!a.multiple,a.multiple?[]:"",!1))}o[hi]=a}catch(g){Pu(e,e.return,g)}}break;case 6:if(ml(t,e),yl(e),4&i){if(null===e.stateNode)throw Error(n(162));o=e.stateNode,a=e.memoizedProps;try{o.nodeValue=a}catch(g){Pu(e,e.return,g)}}break;case 3:if(ml(t,e),yl(e),4&i&&null!==r&&r.memoizedState.isDehydrated)try{$t(t.containerInfo)}catch(g){Pu(e,e.return,g)}break;case 4:default:ml(t,e),yl(e);break;case 13:ml(t,e),yl(e),8192&(o=e.child).flags&&(a=null!==o.memoizedState,o.stateNode.isHidden=a,!a||null!==o.alternate&&null!==o.alternate.memoizedState||(Bl=Ze())),4&i&&hl(e);break;case 22:if(d=null!==r&&null!==r.memoizedState,1&e.mode?(Ks=(c=Ks)||d,ml(t,e),Ks=c):ml(t,e),yl(e),8192&i){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&1&e.mode)for(Zs=e,d=e.child;null!==d;){for(f=Zs=d;null!==Zs;){switch(h=(p=Zs).child,p.tag){case 0:case 11:case 14:case 15:nl(4,p,p.return);break;case 1:Js(p,p.return);var m=p.stateNode;if("function"==typeof m.componentWillUnmount){i=p,r=p.return;try{t=i,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(g){Pu(i,r,g)}}break;case 5:Js(p,p.return);break;case 22:if(null!==p.memoizedState){wl(f);continue}}null!==h?(h.return=p,Zs=h):wl(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{o=f.stateNode,c?"function"==typeof(a=o.style).setProperty?a.setProperty("display","none","important"):a.display="none":(l=f.stateNode,s=null!=(u=f.memoizedProps.style)&&u.hasOwnProperty("display")?u.display:null,l.style.display=ge("display",s))}catch(g){Pu(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(g){Pu(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ml(t,e),yl(e),4&i&&hl(e);case 21:}}function yl(e){var t=e.flags;if(2&t){try{e:{for(var r=e.return;null!==r;){if(al(r)){var i=r;break e}r=r.return}throw Error(n(160))}switch(i.tag){case 5:var o=i.stateNode;32&i.flags&&(pe(o,""),i.flags&=-33),ul(e,sl(e),o);break;case 3:case 4:var a=i.stateNode.containerInfo;ll(e,sl(e),a);break;default:throw Error(n(161))}}catch(s){Pu(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function vl(e,t,n){Zs=e,bl(e)}function bl(e,t,n){for(var r=!!(1&e.mode);null!==Zs;){var i=Zs,o=i.child;if(22===i.tag&&r){var a=null!==i.memoizedState||Xs;if(!a){var s=i.alternate,l=null!==s&&null!==s.memoizedState||Ks;s=Xs;var u=Ks;if(Xs=a,(Ks=l)&&!u)for(Zs=i;null!==Zs;)l=(a=Zs).child,22===a.tag&&null!==a.memoizedState?kl(i):null!==l?(l.return=a,Zs=l):kl(i);for(;null!==o;)Zs=o,bl(o),o=o.sibling;Zs=i,Xs=s,Ks=u}xl(e)}else 8772&i.subtreeFlags&&null!==o?(o.return=i,Zs=o):xl(e)}}function xl(e){for(;null!==Zs;){var t=Zs;if(8772&t.flags){var r=t.alternate;try{if(8772&t.flags)switch(t.tag){case 0:case 11:case 15:Ks||rl(5,t);break;case 1:var i=t.stateNode;if(4&t.flags&&!Ks)if(null===r)i.componentDidMount();else{var o=t.elementType===t.type?r.memoizedProps:rs(t.type,r.memoizedProps);i.componentDidUpdate(o,r.memoizedState,i.__reactInternalSnapshotBeforeUpdate)}var a=t.updateQueue;null!==a&&Ho(t,a,i);break;case 3:var s=t.updateQueue;if(null!==s){if(r=null,null!==t.child)switch(t.child.tag){case 5:case 1:r=t.child.stateNode}Ho(t,s,r)}break;case 5:var l=t.stateNode;if(null===r&&4&t.flags){r=l;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&r.focus();break;case"img":u.src&&(r.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&$t(f)}}}break;default:throw Error(n(163))}Ks||512&t.flags&&il(t)}catch(p){Pu(t,t.return,p)}}if(t===e){Zs=null;break}if(null!==(r=t.sibling)){r.return=t.return,Zs=r;break}Zs=t.return}}function wl(e){for(;null!==Zs;){var t=Zs;if(t===e){Zs=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Zs=n;break}Zs=t.return}}function kl(e){for(;null!==Zs;){var t=Zs;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rl(4,t)}catch(l){Pu(t,n,l)}break;case 1:var r=t.stateNode;if("function"==typeof r.componentDidMount){var i=t.return;try{r.componentDidMount()}catch(l){Pu(t,i,l)}}var o=t.return;try{il(t)}catch(l){Pu(t,o,l)}break;case 5:var a=t.return;try{il(t)}catch(l){Pu(t,a,l)}}}catch(l){Pu(t,t.return,l)}if(t===e){Zs=null;break}var s=t.sibling;if(null!==s){s.return=t.return,Zs=s;break}Zs=t.return}}var Sl,Pl=Math.ceil,El=x.ReactCurrentDispatcher,Cl=x.ReactCurrentOwner,Tl=x.ReactCurrentBatchConfig,Ll=0,Ml=null,Rl=null,Dl=0,Al=0,_l=Ei(0),Vl=0,Nl=null,zl=0,Fl=0,Ol=0,Il=null,jl=null,Bl=0,Ul=1/0,$l=null,Wl=!1,Hl=null,Ql=null,Yl=!1,ql=null,Xl=0,Kl=0,Gl=null,Zl=-1,Jl=0;function eu(){return 6&Ll?Ze():-1!==Zl?Zl:Zl=Ze()}function tu(e){return 1&e.mode?2&Ll&&0!==Dl?Dl&-Dl:null!==yo.transition?(0===Jl&&(Jl=gt()),Jl):0!==(e=xt)?e:e=void 0===(e=window.event)?16:Gt(e.type):1}function nu(e,t,r,i){if(50<Kl)throw Kl=0,Gl=null,Error(n(185));vt(e,r,i),2&Ll&&e===Ml||(e===Ml&&(!(2&Ll)&&(Fl|=r),4===Vl&&su(e,Dl)),ru(e,i),1===r&&0===Ll&&!(1&t.mode)&&(Ul=Ze()+500,ji&&$i()))}function ru(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var a=31-st(o),s=1<<a,l=i[a];-1===l?0!==(s&n)&&0===(s&r)||(i[a]=ht(s,t)):l<=t&&(e.expiredLanes|=s),o&=~s}}(e,t);var r=pt(e,e===Ml?Dl:0);if(0===r)null!==n&&Xe(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Xe(n),1===t)0===e.tag?function(e){ji=!0,Ui(e)}(lu.bind(null,e)):Ui(lu.bind(null,e)),si((function(){!(6&Ll)&&$i()})),n=null;else{switch(wt(r)){case 1:n=et;break;case 4:n=tt;break;case 16:default:n=nt;break;case 536870912:n=it}n=Mu(n,iu.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function iu(e,t){if(Zl=-1,Jl=0,6&Ll)throw Error(n(327));var r=e.callbackNode;if(ku()&&e.callbackNode!==r)return null;var i=pt(e,e===Ml?Dl:0);if(0===i)return null;if(30&i||0!==(i&e.expiredLanes)||t)t=gu(e,i);else{t=i;var o=Ll;Ll|=2;var a=hu();for(Ml===e&&Dl===t||($l=null,Ul=Ze()+500,fu(e,t));;)try{vu();break}catch(l){pu(e,l)}Lo(),El.current=a,Ll=o,null!==Rl?t=0:(Ml=null,Dl=0,t=Vl)}if(0!==t){if(2===t&&(0!==(o=mt(e))&&(i=o,t=ou(e,o))),1===t)throw r=Nl,fu(e,0),su(e,i),ru(e,Ze()),r;if(6===t)su(e,i);else{if(o=e.current.alternate,!(30&i||function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!lr(o(),i))return!1}catch(s){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(o)||(t=gu(e,i),2===t&&(a=mt(e),0!==a&&(i=a,t=ou(e,a))),1!==t)))throw r=Nl,fu(e,0),su(e,i),ru(e,Ze()),r;switch(e.finishedWork=o,e.finishedLanes=i,t){case 0:case 1:throw Error(n(345));case 2:case 5:wu(e,jl,$l);break;case 3:if(su(e,i),(130023424&i)===i&&10<(t=Bl+500-Ze())){if(0!==pt(e,0))break;if(((o=e.suspendedLanes)&i)!==i){eu(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=ii(wu.bind(null,e,jl,$l),t);break}wu(e,jl,$l);break;case 4:if(su(e,i),(4194240&i)===i)break;for(t=e.eventTimes,o=-1;0<i;){var s=31-st(i);a=1<<s,(s=t[s])>o&&(o=s),i&=~a}if(i=o,10<(i=(120>(i=Ze()-i)?120:480>i?480:1080>i?1080:1920>i?1920:3e3>i?3e3:4320>i?4320:1960*Pl(i/1960))-i)){e.timeoutHandle=ii(wu.bind(null,e,jl,$l),i);break}wu(e,jl,$l);break;default:throw Error(n(329))}}}return ru(e,Ze()),e.callbackNode===r?iu.bind(null,e):null}function ou(e,t){var n=Il;return e.current.memoizedState.isDehydrated&&(fu(e,t).flags|=256),2!==(e=gu(e,t))&&(t=jl,jl=n,null!==t&&au(t)),e}function au(e){null===jl?jl=e:jl.push.apply(jl,e)}function su(e,t){for(t&=~Ol,t&=~Fl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-st(t),r=1<<n;e[n]=-1,t&=~r}}function lu(e){if(6&Ll)throw Error(n(327));ku();var t=pt(e,0);if(!(1&t))return ru(e,Ze()),null;var r=gu(e,t);if(0!==e.tag&&2===r){var i=mt(e);0!==i&&(t=i,r=ou(e,i))}if(1===r)throw r=Nl,fu(e,0),su(e,t),ru(e,Ze()),r;if(6===r)throw Error(n(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wu(e,jl,$l),ru(e,Ze()),null}function uu(e,t){var n=Ll;Ll|=1;try{return e(t)}finally{0===(Ll=n)&&(Ul=Ze()+500,ji&&$i())}}function cu(e){null!==ql&&0===ql.tag&&!(6&Ll)&&ku();var t=Ll;Ll|=1;var n=Tl.transition,r=xt;try{if(Tl.transition=null,xt=1,e)return e()}finally{xt=r,Tl.transition=n,!(6&(Ll=t))&&$i()}}function du(){Al=_l.current,Ci(_l)}function fu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,oi(n)),null!==Rl)for(n=Rl.return;null!==n;){var r=n;switch(no(r),r.tag){case 1:null!=(r=r.type.childContextTypes)&&Vi();break;case 3:Zo(),Ci(Ri),Ci(Mi),ia();break;case 5:ea(r);break;case 4:Zo();break;case 13:case 19:Ci(ta);break;case 10:Mo(r.type._context);break;case 22:case 23:du()}n=n.return}if(Ml=e,Rl=e=_u(e.current,null),Dl=Al=t,Vl=0,Nl=null,Ol=Fl=zl=0,jl=Il=null,null!==_o){for(t=0;t<_o.length;t++)if(null!==(r=(n=_o[t]).interleaved)){n.interleaved=null;var i=r.next,o=n.pending;if(null!==o){var a=o.next;o.next=i,r.next=a}n.pending=r}_o=null}return e}function pu(e,t){for(;;){var r=Rl;try{if(Lo(),oa.current=Ja,da){for(var i=la.memoizedState;null!==i;){var o=i.queue;null!==o&&(o.pending=null),i=i.next}da=!1}if(sa=0,ca=ua=la=null,fa=!1,pa=0,Cl.current=null,null===r||null===r.return){Vl=1,Nl=t,Rl=null;break}e:{var a=e,s=r.return,l=r,u=t;if(t=Dl,l.flags|=32768,null!==u&&"object"==typeof u&&"function"==typeof u.then){var c=u,d=l,f=d.tag;if(!(1&d.mode||0!==f&&11!==f&&15!==f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=gs(s);if(null!==h){h.flags&=-257,ys(h,s,l,0,t),1&h.mode&&ms(a,c,t),u=c;var m=(t=h).updateQueue;if(null===m){var g=new Set;g.add(u),t.updateQueue=g}else m.add(u);break e}if(!(1&t)){ms(a,c,t),mu();break e}u=Error(n(426))}else if(oo&&1&l.mode){var y=gs(s);if(null!==y){!(65536&y.flags)&&(y.flags|=256),ys(y,s,l,0,t),go(cs(u,l));break e}}a=u=cs(u,l),4!==Vl&&(Vl=2),null===Il?Il=[a]:Il.push(a),a=s;do{switch(a.tag){case 3:a.flags|=65536,t&=-t,a.lanes|=t,$o(a,ps(0,u,t));break e;case 1:l=u;var v=a.type,b=a.stateNode;if(!(128&a.flags||"function"!=typeof v.getDerivedStateFromError&&(null===b||"function"!=typeof b.componentDidCatch||null!==Ql&&Ql.has(b)))){a.flags|=65536,t&=-t,a.lanes|=t,$o(a,hs(a,l,t));break e}}a=a.return}while(null!==a)}xu(r)}catch(x){t=x,Rl===r&&null!==r&&(Rl=r=r.return);continue}break}}function hu(){var e=El.current;return El.current=Ja,null===e?Ja:e}function mu(){0!==Vl&&3!==Vl&&2!==Vl||(Vl=4),null===Ml||!(268435455&zl)&&!(268435455&Fl)||su(Ml,Dl)}function gu(e,t){var r=Ll;Ll|=2;var i=hu();for(Ml===e&&Dl===t||($l=null,fu(e,t));;)try{yu();break}catch(o){pu(e,o)}if(Lo(),Ll=r,El.current=i,null!==Rl)throw Error(n(261));return Ml=null,Dl=0,Vl}function yu(){for(;null!==Rl;)bu(Rl)}function vu(){for(;null!==Rl&&!Ke();)bu(Rl)}function bu(e){var t=Sl(e.alternate,e,Al);e.memoizedProps=e.pendingProps,null===t?xu(e):Rl=t,Cl.current=null}function xu(e){var t=e;do{var n=t.alternate;if(e=t.return,32768&t.flags){if(null!==(n=qs(n,t)))return n.flags&=32767,void(Rl=n);if(null===e)return Vl=6,void(Rl=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}else if(null!==(n=Ys(n,t,Al)))return void(Rl=n);if(null!==(t=t.sibling))return void(Rl=t);Rl=t=e}while(null!==t);0===Vl&&(Vl=5)}function wu(e,t,r){var i=xt,o=Tl.transition;try{Tl.transition=null,xt=1,function(e,t,r,i){do{ku()}while(null!==ql);if(6&Ll)throw Error(n(327));r=e.finishedWork;var o=e.finishedLanes;if(null===r)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(n(177));e.callbackNode=null,e.callbackPriority=0;var a=r.lanes|r.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-st(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}(e,a),e===Ml&&(Rl=Ml=null,Dl=0),!(2064&r.subtreeFlags)&&!(2064&r.flags)||Yl||(Yl=!0,Mu(nt,(function(){return ku(),null}))),a=!!(15990&r.flags),!!(15990&r.subtreeFlags)||a){a=Tl.transition,Tl.transition=null;var s=xt;xt=1;var l=Ll;Ll|=4,Cl.current=null,function(e,t){if(ti=Ht,hr(e=pr())){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{var i=(r=(r=e.ownerDocument)&&r.defaultView||window).getSelection&&r.getSelection();if(i&&0!==i.rangeCount){r=i.anchorNode;var o=i.anchorOffset,a=i.focusNode;i=i.focusOffset;try{r.nodeType,a.nodeType}catch(w){r=null;break e}var s=0,l=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==r||0!==o&&3!==f.nodeType||(l=s+o),f!==a||0!==i&&3!==f.nodeType||(u=s+i),3===f.nodeType&&(s+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===r&&++c===o&&(l=s),p===a&&++d===i&&(u=s),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}r=-1===l||-1===u?null:{start:l,end:u}}else r=null}r=r||{start:0,end:0}}else r=null;for(ni={focusedElem:e,selectionRange:r},Ht=!1,Zs=t;null!==Zs;)if(e=(t=Zs).child,1028&t.subtreeFlags&&null!==e)e.return=t,Zs=e;else for(;null!==Zs;){t=Zs;try{var m=t.alternate;if(1024&t.flags)switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,y=m.memoizedState,v=t.stateNode,b=v.getSnapshotBeforeUpdate(t.elementType===t.type?g:rs(t.type,g),y);v.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var x=t.stateNode.containerInfo;1===x.nodeType?x.textContent="":9===x.nodeType&&x.documentElement&&x.removeChild(x.documentElement);break;default:throw Error(n(163))}}catch(w){Pu(t,t.return,w)}if(null!==(e=t.sibling)){e.return=t.return,Zs=e;break}Zs=t.return}m=tl,tl=!1}(e,r),gl(r,e),mr(ni),Ht=!!ti,ni=ti=null,e.current=r,vl(r),Ge(),Ll=l,xt=s,Tl.transition=a}else e.current=r;if(Yl&&(Yl=!1,ql=e,Xl=o),a=e.pendingLanes,0===a&&(Ql=null),function(e){if(at&&"function"==typeof at.onCommitFiberRoot)try{at.onCommitFiberRoot(ot,e,void 0,!(128&~e.current.flags))}catch(t){}}(r.stateNode),ru(e,Ze()),null!==t)for(i=e.onRecoverableError,r=0;r<t.length;r++)o=t[r],i(o.value,{componentStack:o.stack,digest:o.digest});if(Wl)throw Wl=!1,e=Hl,Hl=null,e;!!(1&Xl)&&0!==e.tag&&ku(),a=e.pendingLanes,1&a?e===Gl?Kl++:(Kl=0,Gl=e):Kl=0,$i()}(e,t,r,i)}finally{Tl.transition=o,xt=i}return null}function ku(){if(null!==ql){var e=wt(Xl),t=Tl.transition,r=xt;try{if(Tl.transition=null,xt=16>e?16:e,null===ql)var i=!1;else{if(e=ql,ql=null,Xl=0,6&Ll)throw Error(n(331));var o=Ll;for(Ll|=4,Zs=e.current;null!==Zs;){var a=Zs,s=a.child;if(16&Zs.flags){var l=a.deletions;if(null!==l){for(var u=0;u<l.length;u++){var c=l[u];for(Zs=c;null!==Zs;){var d=Zs;switch(d.tag){case 0:case 11:case 15:nl(8,d,a)}var f=d.child;if(null!==f)f.return=d,Zs=f;else for(;null!==Zs;){var p=(d=Zs).sibling,h=d.return;if(ol(d),d===c){Zs=null;break}if(null!==p){p.return=h,Zs=p;break}Zs=h}}}var m=a.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var y=g.sibling;g.sibling=null,g=y}while(null!==g)}}Zs=a}}if(2064&a.subtreeFlags&&null!==s)s.return=a,Zs=s;else e:for(;null!==Zs;){if(2048&(a=Zs).flags)switch(a.tag){case 0:case 11:case 15:nl(9,a,a.return)}var v=a.sibling;if(null!==v){v.return=a.return,Zs=v;break e}Zs=a.return}}var b=e.current;for(Zs=b;null!==Zs;){var x=(s=Zs).child;if(2064&s.subtreeFlags&&null!==x)x.return=s,Zs=x;else e:for(s=b;null!==Zs;){if(2048&(l=Zs).flags)try{switch(l.tag){case 0:case 11:case 15:rl(9,l)}}catch(k){Pu(l,l.return,k)}if(l===s){Zs=null;break e}var w=l.sibling;if(null!==w){w.return=l.return,Zs=w;break e}Zs=l.return}}if(Ll=o,$i(),at&&"function"==typeof at.onPostCommitFiberRoot)try{at.onPostCommitFiberRoot(ot,e)}catch(k){}i=!0}return i}finally{xt=r,Tl.transition=t}}return!1}function Su(e,t,n){e=Bo(e,t=ps(0,t=cs(n,t),1),1),t=eu(),null!==e&&(vt(e,1,t),ru(e,t))}function Pu(e,t,n){if(3===e.tag)Su(e,e,n);else for(;null!==t;){if(3===t.tag){Su(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===Ql||!Ql.has(r))){t=Bo(t,e=hs(t,e=cs(n,e),1),1),e=eu(),null!==t&&(vt(t,1,e),ru(t,e));break}}t=t.return}}function Eu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=eu(),e.pingedLanes|=e.suspendedLanes&n,Ml===e&&(Dl&n)===n&&(4===Vl||3===Vl&&(130023424&Dl)===Dl&&500>Ze()-Bl?fu(e,0):Ol|=n),ru(e,t)}function Cu(e,t){0===t&&(1&e.mode?(t=dt,!(130023424&(dt<<=1))&&(dt=4194304)):t=1);var n=eu();null!==(e=zo(e,t))&&(vt(e,t,n),ru(e,n))}function Tu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Cu(e,n)}function Lu(e,t){var r=0;switch(e.tag){case 13:var i=e.stateNode,o=e.memoizedState;null!==o&&(r=o.retryLane);break;case 19:i=e.stateNode;break;default:throw Error(n(314))}null!==i&&i.delete(t),Cu(e,r)}function Mu(e,t){return qe(e,t)}function Ru(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Du(e,t,n,r){return new Ru(e,t,n,r)}function Au(e){return!(!(e=e.prototype)||!e.isReactComponent)}function _u(e,t){var n=e.alternate;return null===n?((n=Du(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Vu(e,t,r,i,o,a){var s=2;if(i=e,"function"==typeof e)Au(e)&&(s=1);else if("string"==typeof e)s=5;else e:switch(e){case P:return Nu(r.children,o,a,t);case E:s=8,o|=8;break;case C:return(e=Du(12,r,t,2|o)).elementType=C,e.lanes=a,e;case R:return(e=Du(13,r,t,o)).elementType=R,e.lanes=a,e;case D:return(e=Du(19,r,t,o)).elementType=D,e.lanes=a,e;case V:return zu(r,o,a,t);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case T:s=10;break e;case L:s=9;break e;case M:s=11;break e;case A:s=14;break e;case _:s=16,i=null;break e}throw Error(n(130,null==e?e:typeof e,""))}return(t=Du(s,r,t,o)).elementType=e,t.type=i,t.lanes=a,t}function Nu(e,t,n,r){return(e=Du(7,e,r,t)).lanes=n,e}function zu(e,t,n,r){return(e=Du(22,e,r,t)).elementType=V,e.lanes=n,e.stateNode={isHidden:!1},e}function Fu(e,t,n){return(e=Du(6,e,null,t)).lanes=n,e}function Ou(e,t,n){return(t=Du(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Iu(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=yt(0),this.expirationTimes=yt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=yt(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function ju(e,t,n,r,i,o,a,s,l){return e=new Iu(e,t,n,s,l),1===t?(t=1,!0===o&&(t|=8)):t=0,o=Du(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Oo(o),e}function Bu(e){if(!e)return Li;e:{if($e(e=e._reactInternals)!==e||1!==e.tag)throw Error(n(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(_i(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(n(171))}if(1===e.tag){var r=e.type;if(_i(r))return zi(e,r,t)}return t}function Uu(e,t,n,r,i,o,a,s,l){return(e=ju(n,r,!0,e,0,o,0,s,l)).context=Bu(null),n=e.current,(o=jo(r=eu(),i=tu(n))).callback=null!=t?t:null,Bo(n,o,i),e.current.lanes=i,vt(e,i,r),ru(e,r),e}function $u(e,t,n,r){var i=t.current,o=eu(),a=tu(i);return n=Bu(n),null===t.context?t.context=n:t.pendingContext=n,(t=jo(o,a)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Bo(i,t,a))&&(nu(e,i,a,o),Uo(e,i,a)),a}function Wu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Hu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Qu(e,t){Hu(e,t),(e=e.alternate)&&Hu(e,t)}Sl=function(e,t,r){if(null!==e)if(e.memoizedProps!==t.pendingProps||Ri.current)bs=!0;else{if(0===(e.lanes&r)&&!(128&t.flags))return bs=!1,function(e,t,n){switch(t.tag){case 3:Ms(t),mo();break;case 5:Jo(t);break;case 1:_i(t.type)&&Fi(t);break;case 4:Go(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;Ti(Po,r._currentValue),r._currentValue=i;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Ti(ta,1&ta.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Fs(e,t,n):(Ti(ta,1&ta.current),null!==(e=Ws(e,t,n))?e.sibling:null);Ti(ta,1&ta.current);break;case 19:if(r=0!==(n&t.childLanes),128&e.flags){if(r)return Us(e,t,n);t.flags|=128}if(null!==(i=t.memoizedState)&&(i.rendering=null,i.tail=null,i.lastEffect=null),Ti(ta,ta.current),r)break;return null;case 22:case 23:return t.lanes=0,Ps(e,t,n)}return Ws(e,t,n)}(e,t,r);bs=!!(131072&e.flags)}else bs=!1,oo&&1048576&t.flags&&eo(t,Yi,t.index);switch(t.lanes=0,t.tag){case 2:var i=t.type;$s(e,t),e=t.pendingProps;var o=Ai(t,Mi.current);Do(t,r),o=ya(null,t,i,e,o,r);var a=va();return t.flags|=1,"object"==typeof o&&null!==o&&"function"==typeof o.render&&void 0===o.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,_i(i)?(a=!0,Fi(t)):a=!1,t.memoizedState=null!==o.state&&void 0!==o.state?o.state:null,Oo(t),o.updater=os,t.stateNode=o,o._reactInternals=t,us(t,i,e,r),t=Ls(null,t,i,!0,a,r)):(t.tag=0,oo&&a&&to(t),xs(null,t,o,r),t=t.child),t;case 16:i=t.elementType;e:{switch($s(e,t),e=t.pendingProps,i=(o=i._init)(i._payload),t.type=i,o=t.tag=function(e){if("function"==typeof e)return Au(e)?1:0;if(null!=e){if((e=e.$$typeof)===M)return 11;if(e===A)return 14}return 2}(i),e=rs(i,e),o){case 0:t=Cs(null,t,i,e,r);break e;case 1:t=Ts(null,t,i,e,r);break e;case 11:t=ws(null,t,i,e,r);break e;case 14:t=ks(null,t,i,rs(i.type,e),r);break e}throw Error(n(306,i,""))}return t;case 0:return i=t.type,o=t.pendingProps,Cs(e,t,i,o=t.elementType===i?o:rs(i,o),r);case 1:return i=t.type,o=t.pendingProps,Ts(e,t,i,o=t.elementType===i?o:rs(i,o),r);case 3:e:{if(Ms(t),null===e)throw Error(n(387));i=t.pendingProps,o=(a=t.memoizedState).element,Io(e,t),Wo(t,i,null,r);var s=t.memoizedState;if(i=s.element,a.isDehydrated){if(a={element:i,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=a,t.memoizedState=a,256&t.flags){t=Rs(e,t,i,r,o=cs(Error(n(423)),t));break e}if(i!==o){t=Rs(e,t,i,r,o=cs(Error(n(424)),t));break e}for(io=ci(t.stateNode.containerInfo.firstChild),ro=t,oo=!0,ao=null,r=So(t,null,i,r),t.child=r;r;)r.flags=-3&r.flags|4096,r=r.sibling}else{if(mo(),i===o){t=Ws(e,t,r);break e}xs(e,t,i,r)}t=t.child}return t;case 5:return Jo(t),null===e&&co(t),i=t.type,o=t.pendingProps,a=null!==e?e.memoizedProps:null,s=o.children,ri(i,o)?s=null:null!==a&&ri(i,a)&&(t.flags|=32),Es(e,t),xs(e,t,s,r),t.child;case 6:return null===e&&co(t),null;case 13:return Fs(e,t,r);case 4:return Go(t,t.stateNode.containerInfo),i=t.pendingProps,null===e?t.child=ko(t,null,i,r):xs(e,t,i,r),t.child;case 11:return i=t.type,o=t.pendingProps,ws(e,t,i,o=t.elementType===i?o:rs(i,o),r);case 7:return xs(e,t,t.pendingProps,r),t.child;case 8:case 12:return xs(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(i=t.type._context,o=t.pendingProps,a=t.memoizedProps,s=o.value,Ti(Po,i._currentValue),i._currentValue=s,null!==a)if(lr(a.value,s)){if(a.children===o.children&&!Ri.current){t=Ws(e,t,r);break e}}else for(null!==(a=t.child)&&(a.return=t);null!==a;){var l=a.dependencies;if(null!==l){s=a.child;for(var u=l.firstContext;null!==u;){if(u.context===i){if(1===a.tag){(u=jo(-1,r&-r)).tag=2;var c=a.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}a.lanes|=r,null!==(u=a.alternate)&&(u.lanes|=r),Ro(a.return,r,t),l.lanes|=r;break}u=u.next}}else if(10===a.tag)s=a.type===t.type?null:a.child;else if(18===a.tag){if(null===(s=a.return))throw Error(n(341));s.lanes|=r,null!==(l=s.alternate)&&(l.lanes|=r),Ro(s,r,t),s=a.sibling}else s=a.child;if(null!==s)s.return=a;else for(s=a;null!==s;){if(s===t){s=null;break}if(null!==(a=s.sibling)){a.return=s.return,s=a;break}s=s.return}a=s}xs(e,t,o.children,r),t=t.child}return t;case 9:return o=t.type,i=t.pendingProps.children,Do(t,r),i=i(o=Ao(o)),t.flags|=1,xs(e,t,i,r),t.child;case 14:return o=rs(i=t.type,t.pendingProps),ks(e,t,i,o=rs(i.type,o),r);case 15:return Ss(e,t,t.type,t.pendingProps,r);case 17:return i=t.type,o=t.pendingProps,o=t.elementType===i?o:rs(i,o),$s(e,t),t.tag=1,_i(i)?(e=!0,Fi(t)):e=!1,Do(t,r),ss(t,i,o),us(t,i,o,r),Ls(null,t,i,!0,e,r);case 19:return Us(e,t,r);case 22:return Ps(e,t,r)}throw Error(n(156,t.tag))};var Yu="function"==typeof reportError?reportError:function(e){};function qu(e){this._internalRoot=e}function Xu(e){this._internalRoot=e}function Ku(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Gu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Zu(){}function Ju(e,t,n,r,i){var o=n._reactRootContainer;if(o){var a=o;if("function"==typeof i){var s=i;i=function(){var e=Wu(a);s.call(e)}}$u(t,a,e,i)}else a=function(e,t,n,r,i){if(i){if("function"==typeof r){var o=r;r=function(){var e=Wu(a);o.call(e)}}var a=Uu(t,r,e,0,null,!1,0,"",Zu);return e._reactRootContainer=a,e[mi]=a.current,$r(8===e.nodeType?e.parentNode:e),cu(),a}for(;i=e.lastChild;)e.removeChild(i);if("function"==typeof r){var s=r;r=function(){var e=Wu(l);s.call(e)}}var l=ju(e,0,!1,null,0,!1,0,"",Zu);return e._reactRootContainer=l,e[mi]=l.current,$r(8===e.nodeType?e.parentNode:e),cu((function(){$u(t,l,n,r)})),l}(n,t,e,i,r);return Wu(a)}Xu.prototype.render=qu.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(n(409));$u(e,t,null,null)},Xu.prototype.unmount=qu.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;cu((function(){$u(null,e,null,null)})),t[mi]=null}},Xu.prototype.unstable_scheduleHydration=function(e){if(e){var t=Et();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Vt.length&&0!==t&&t<Vt[n].priority;n++);Vt.splice(n,0,e),0===n&&Ot(e)}},kt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=ft(t.pendingLanes);0!==n&&(bt(t,1|n),ru(t,Ze()),!(6&Ll)&&(Ul=Ze()+500,$i()))}break;case 13:cu((function(){var t=zo(e,1);if(null!==t){var n=eu();nu(t,e,1,n)}})),Qu(e,1)}},St=function(e){if(13===e.tag){var t=zo(e,134217728);if(null!==t)nu(t,e,134217728,eu());Qu(e,134217728)}},Pt=function(e){if(13===e.tag){var t=tu(e),n=zo(e,t);if(null!==n)nu(n,e,t,eu());Qu(e,t)}},Et=function(){return xt},Ct=function(e,t){var n=xt;try{return xt=e,t()}finally{xt=n}},Se=function(e,t,r){switch(t){case"input":if(J(e,r),t=r.name,"radio"===r.type&&null!=t){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var i=r[t];if(i!==e&&i.form===e.form){var o=ki(i);if(!o)throw Error(n(90));q(i),J(i,o)}}}break;case"textarea":ae(e,r);break;case"select":null!=(t=r.value)&&re(e,!!r.multiple,t,!1)}},Me=uu,Re=cu;var ec={usingClientEntryPoint:!1,Events:[xi,wi,ki,Te,Le,uu]},tc={findFiberByHostInstance:bi,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nc={bundleType:tc.bundleType,version:tc.version,rendererPackageName:tc.rendererPackageName,rendererConfig:tc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:x.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Qe(e))?null:e.stateNode},findFiberByHostInstance:tc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var rc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!rc.isDisabled&&rc.supportsFiber)try{ot=rc.inject(nc),at=rc}catch(de){}}return v.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ec,v.createPortal=function(e,t){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Ku(t))throw Error(n(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:S,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,r)},v.createRoot=function(e,t){if(!Ku(e))throw Error(n(299));var r=!1,i="",o=Yu;return null!=t&&(!0===t.unstable_strictMode&&(r=!0),void 0!==t.identifierPrefix&&(i=t.identifierPrefix),void 0!==t.onRecoverableError&&(o=t.onRecoverableError)),t=ju(e,1,!1,null,0,r,0,i,o),e[mi]=t.current,$r(8===e.nodeType?e.parentNode:e),new qu(t)},v.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(n(188));throw e=Object.keys(e).join(","),Error(n(268,e))}return e=null===(e=Qe(t))?null:e.stateNode},v.flushSync=function(e){return cu(e)},v.hydrate=function(e,t,r){if(!Gu(t))throw Error(n(200));return Ju(null,e,t,!0,r)},v.hydrateRoot=function(e,t,r){if(!Ku(e))throw Error(n(405));var i=null!=r&&r.hydratedSources||null,o=!1,a="",s=Yu;if(null!=r&&(!0===r.unstable_strictMode&&(o=!0),void 0!==r.identifierPrefix&&(a=r.identifierPrefix),void 0!==r.onRecoverableError&&(s=r.onRecoverableError)),t=Uu(t,null,e,1,null!=r?r:null,o,0,a,s),e[mi]=t.current,$r(e),i)for(e=0;e<i.length;e++)o=(o=(r=i[e])._getVersion)(r._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[r,o]:t.mutableSourceEagerHydrationData.push(r,o);return new Xu(t)},v.render=function(e,t,r){if(!Gu(t))throw Error(n(200));return Ju(null,e,t,!1,r)},v.unmountComponentAtNode=function(e){if(!Gu(e))throw Error(n(40));return!!e._reactRootContainer&&(cu((function(){Ju(null,null,e,!1,(function(){e._reactRootContainer=null,e[mi]=null}))})),!0)},v.unstable_batchedUpdates=uu,v.unstable_renderSubtreeIntoContainer=function(e,t,r,i){if(!Gu(r))throw Error(n(200));if(null==e||void 0===e._reactInternals)throw Error(n(38));return Ju(e,t,r,!1,i)},v.version="18.3.1-next-f1338f8080-20240426",v}function S(){if(g)return y.exports;return g=1,function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){}}(),y.exports=k(),y.exports}const P=c.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),E=c.createContext({}),C=c.createContext(null),T="undefined"!=typeof document,L=T?c.useLayoutEffect:c.useEffect,M=c.createContext({strict:!1});function R(e){return"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function D(e){return"string"==typeof e||Array.isArray(e)}function A(e){return"object"==typeof e&&"function"==typeof e.start}const _=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],V=["initial",..._];function N(e){return A(e.animate)||V.some((t=>D(e[t])))}function z(e){return Boolean(N(e)||e.variants)}function F(e){const{initial:t,animate:n}=function(e,t){if(N(e)){const{initial:t,animate:n}=e;return{initial:!1===t||D(t)?t:void 0,animate:D(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,c.useContext(E));return c.useMemo((()=>({initial:t,animate:n})),[O(t),O(n)])}function O(e){return Array.isArray(e)?e.join(" "):e}const I={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},j={};for(const wa in I)j[wa]={isEnabled:e=>I[wa].some((t=>!!e[t]))};const B=c.createContext({}),U=c.createContext({}),$=Symbol.for("motionComponentSymbol");function W({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){e&&function(e){for(const t in e)j[t]={...j[t],...e[t]}}(e);const o=c.forwardRef((function(o,a){let s;const l={...c.useContext(P),...o,layoutId:H(o)},{isStatic:u}=l,d=F(o),f=r(o,u);if(!u&&T){d.visualElement=function(e,t,n,r){const{visualElement:i}=c.useContext(E),o=c.useContext(M),a=c.useContext(C),s=c.useContext(P).reducedMotion,l=c.useRef();r=r||o.renderer,!l.current&&r&&(l.current=r(e,{visualState:t,parent:i,props:n,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:s}));const u=l.current;return c.useInsertionEffect((()=>{u&&u.update(n,a)})),L((()=>{u&&u.render()})),c.useEffect((()=>{u&&u.updateFeatures()})),(window.HandoffAppearAnimations?L:c.useEffect)((()=>{u&&u.animationState&&u.animationState.animateChanges()})),u}(i,f,l,t);const n=c.useContext(U),r=c.useContext(M).strict;d.visualElement&&(s=d.visualElement.loadFeatures(l,r,e,n))}return c.createElement(E.Provider,{value:d},s&&d.visualElement?c.createElement(s,{visualElement:d.visualElement,...l}):null,n(i,o,function(e,t,n){return c.useCallback((r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&("function"==typeof n?n(r):R(n)&&(n.current=r))}),[t])}(f,d.visualElement,a),f,u,d.visualElement))}));return o[$]=i,o}function H({layoutId:e}){const t=c.useContext(B).id;return t&&void 0!==e?t+"-"+e:e}function Q(e){function t(t,n={}){return W(e(t,n))}if("undefined"==typeof Proxy)return t;const n=new Map;return new Proxy(t,{get:(e,r)=>(n.has(r)||n.set(r,t(r)),n.get(r))})}const Y=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function q(e){return"string"==typeof e&&!e.includes("-")&&!!(Y.indexOf(e)>-1||/[A-Z]/.test(e))}const X={};const K=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],G=new Set(K);function Z(e,{layout:t,layoutId:n}){return G.has(e)||e.startsWith("origin")||(t||void 0!==n)&&(!!X[e]||"opacity"===e)}const J=e=>Boolean(e&&e.getVelocity),ee={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},te=K.length;const ne=e=>t=>"string"==typeof t&&t.startsWith(e),re=ne("--"),ie=ne("var(--"),oe=(e,t)=>t&&"number"==typeof e?t.transform(e):e,ae=(e,t,n)=>Math.min(Math.max(n,e),t),se={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},le={...se,transform:e=>ae(0,1,e)},ue={...se,default:1},ce=e=>Math.round(1e5*e)/1e5,de=/(-)?([\d]*\.?[\d])+/g,fe=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,pe=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function he(e){return"string"==typeof e}const me=e=>({test:t=>he(t)&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),ge=me("deg"),ye=me("%"),ve=me("px"),be=me("vh"),xe=me("vw"),we={...ye,parse:e=>ye.parse(e)/100,transform:e=>ye.transform(100*e)},ke={...se,transform:Math.round},Se={borderWidth:ve,borderTopWidth:ve,borderRightWidth:ve,borderBottomWidth:ve,borderLeftWidth:ve,borderRadius:ve,radius:ve,borderTopLeftRadius:ve,borderTopRightRadius:ve,borderBottomRightRadius:ve,borderBottomLeftRadius:ve,width:ve,maxWidth:ve,height:ve,maxHeight:ve,size:ve,top:ve,right:ve,bottom:ve,left:ve,padding:ve,paddingTop:ve,paddingRight:ve,paddingBottom:ve,paddingLeft:ve,margin:ve,marginTop:ve,marginRight:ve,marginBottom:ve,marginLeft:ve,rotate:ge,rotateX:ge,rotateY:ge,rotateZ:ge,scale:ue,scaleX:ue,scaleY:ue,scaleZ:ue,skew:ge,skewX:ge,skewY:ge,distance:ve,translateX:ve,translateY:ve,translateZ:ve,x:ve,y:ve,z:ve,perspective:ve,transformPerspective:ve,opacity:le,originX:we,originY:we,originZ:ve,zIndex:ke,fillOpacity:le,strokeOpacity:le,numOctaves:ke};function Pe(e,t,n,r){const{style:i,vars:o,transform:a,transformOrigin:s}=e;let l=!1,u=!1,c=!0;for(const d in t){const e=t[d];if(re(d)){o[d]=e;continue}const n=Se[d],r=oe(e,n);if(G.has(d)){if(l=!0,a[d]=r,!c)continue;e!==(n.default||0)&&(c=!1)}else d.startsWith("origin")?(u=!0,s[d]=r):i[d]=r}if(t.transform||(l||r?i.transform=function(e,{enableHardwareAcceleration:t=!0,allowTransformNone:n=!0},r,i){let o="";for(let a=0;a<te;a++){const t=K[a];void 0!==e[t]&&(o+=`${ee[t]||t}(${e[t]}) `)}return t&&!e.z&&(o+="translateZ(0)"),o=o.trim(),i?o=i(e,r?"":o):n&&r&&(o="none"),o}(e.transform,n,c,r):i.transform&&(i.transform="none")),u){const{originX:e="50%",originY:t="50%",originZ:n=0}=s;i.transformOrigin=`${e} ${t} ${n}`}}const Ee=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Ce(e,t,n){for(const r in t)J(t[r])||Z(r,n)||(e[r]=t[r])}function Te(e,t,n){const r={};return Ce(r,e.style||{},e),Object.assign(r,function({transformTemplate:e},t,n){return c.useMemo((()=>{const r={style:{},transform:{},transformOrigin:{},vars:{}};return Pe(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)}),[t])}(e,t,n)),e.transformValues?e.transformValues(r):r}function Le(e,t,n){const r={},i=Te(e,t,n);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===e.drag?"none":"pan-"+("x"===e.drag?"y":"x")),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=i,r}const Me=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onLayoutAnimationStart","onLayoutAnimationComplete","onLayoutMeasure","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","ignoreStrict","viewport"]);function Re(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||Me.has(e)}let De=e=>!Re(e);try{(Ae=require("@emotion/is-prop-valid").default)&&(De=e=>e.startsWith("on")?!Re(e):Ae(e))}catch(xa){}var Ae;function _e(e,t,n){return"string"==typeof e?e:ve.transform(t+n*e)}const Ve={offset:"stroke-dashoffset",array:"stroke-dasharray"},Ne={offset:"strokeDashoffset",array:"strokeDasharray"};function ze(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:o,pathLength:a,pathSpacing:s=1,pathOffset:l=0,...u},c,d,f){if(Pe(e,u,c,f),d)return void(e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox));e.attrs=e.style,e.style={};const{attrs:p,style:h,dimensions:m}=e;p.transform&&(m&&(h.transform=p.transform),delete p.transform),m&&(void 0!==i||void 0!==o||h.transform)&&(h.transformOrigin=function(e,t,n){return`${_e(t,e.x,e.width)} ${_e(n,e.y,e.height)}`}(m,void 0!==i?i:.5,void 0!==o?o:.5)),void 0!==t&&(p.x=t),void 0!==n&&(p.y=n),void 0!==r&&(p.scale=r),void 0!==a&&function(e,t,n=1,r=0,i=!0){e.pathLength=1;const o=i?Ve:Ne;e[o.offset]=ve.transform(-r);const a=ve.transform(t),s=ve.transform(n);e[o.array]=`${a} ${s}`}(p,a,s,l,!1)}const Fe=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),Oe=e=>"string"==typeof e&&"svg"===e.toLowerCase();function Ie(e,t,n,r){const i=c.useMemo((()=>{const n={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return ze(n,t,{enableHardwareAcceleration:!1},Oe(r),e.transformTemplate),{...n.attrs,style:{...n.style}}}),[t]);if(e.style){const t={};Ce(t,e.style,e),i.style={...t,...i.style}}return i}function je(e=!1){return(t,n,r,{latestValues:i},o)=>{const a=(q(t)?Ie:Le)(n,i,o,t),s=function(e,t,n){const r={};for(const i in e)"values"===i&&"object"==typeof e.values||(De(i)||!0===n&&Re(i)||!t&&!Re(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(n,"string"==typeof t,e),l={...s,...a,ref:r},{children:u}=n,d=c.useMemo((()=>J(u)?u.get():u),[u]);return c.createElement(t,{...l,children:d})}}const Be=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase();function Ue(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const o in n)e.style.setProperty(o,n[o])}const $e=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function We(e,t,n,r){Ue(e,t,void 0,r);for(const i in t.attrs)e.setAttribute($e.has(i)?i:Be(i),t.attrs[i])}function He(e,t){const{style:n}=e,r={};for(const i in n)(J(n[i])||t.style&&J(t.style[i])||Z(i,e))&&(r[i]=n[i]);return r}function Qe(e,t){const n=He(e,t);for(const r in e)if(J(e[r])||J(t[r])){n[-1!==K.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]}return n}function Ye(e,t,n,r={},i={}){return"function"==typeof t&&(t=t(void 0!==n?n:e.custom,r,i)),"string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t&&(t=t(void 0!==n?n:e.custom,r,i)),t}function qe(e){const t=c.useRef(null);return null===t.current&&(t.current=e()),t.current}const Xe=e=>Array.isArray(e);function Ke(e){const t=J(e)?e.get():e;return n=t,Boolean(n&&"object"==typeof n&&n.mix&&n.toValue)?t.toValue():t;var n}const Ge=e=>(t,n)=>{const r=c.useContext(E),i=c.useContext(C),o=()=>function({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,i,o){const a={latestValues:Ze(r,i,o,e),renderState:t()};return n&&(a.mount=e=>n(r,e,a)),a}(e,t,r,i);return n?o():qe(o)};function Ze(e,t,n,r){const i={},o=r(e,{});for(const f in o)i[f]=Ke(o[f]);let{initial:a,animate:s}=e;const l=N(e),u=z(e);t&&u&&!l&&!1!==e.inherit&&(void 0===a&&(a=t.initial),void 0===s&&(s=t.animate));let c=!!n&&!1===n.initial;c=c||!1===a;const d=c?s:a;if(d&&"boolean"!=typeof d&&!A(d)){(Array.isArray(d)?d:[d]).forEach((t=>{const n=Ye(e,t);if(!n)return;const{transitionEnd:r,transition:o,...a}=n;for(const e in a){let t=a[e];if(Array.isArray(t)){t=t[c?t.length-1:0]}null!==t&&(i[e]=t)}for(const e in r)i[e]=r[e]}))}return i}const Je={useVisualState:Ge({scrapeMotionValuesFromProps:Qe,createRenderState:Fe,onMount:(e,t,{renderState:n,latestValues:r})=>{try{n.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(i){n.dimensions={x:0,y:0,width:0,height:0}}ze(n,r,{enableHardwareAcceleration:!1},Oe(t.tagName),e.transformTemplate),We(t,n)}})},et={useVisualState:Ge({scrapeMotionValuesFromProps:He,createRenderState:Ee})};function tt(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const nt=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function rt(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}function it(e,t,n,r){return tt(e,t,(e=>t=>nt(t)&&e(t,rt(t)))(n),r)}const ot=(e,t)=>n=>t(e(n)),at=(...e)=>e.reduce(ot);function st(e){let t=null;return()=>{const n=()=>{t=null};return null===t&&(t=e,n)}}const lt=st("dragHorizontal"),ut=st("dragVertical");function ct(e){let t=!1;if("y"===e)t=ut();else if("x"===e)t=lt();else{const e=lt(),n=ut();e&&n?t=()=>{e(),n()}:(e&&e(),n&&n())}return t}function dt(){const e=ct(!0);return!e||(e(),!1)}class ft{constructor(e){this.isMounted=!1,this.node=e}update(){}}const pt={delta:0,timestamp:0,isProcessing:!1};let ht=!0,mt=!1;const gt=["read","update","preRender","render","postRender"],yt=gt.reduce(((e,t)=>(e[t]=function(e){let t=[],n=[],r=0,i=!1,o=!1;const a=new WeakSet,s={schedule:(e,o=!1,s=!1)=>{const l=s&&i,u=l?t:n;return o&&a.add(e),-1===u.indexOf(e)&&(u.push(e),l&&i&&(r=t.length)),e},cancel:e=>{const t=n.indexOf(e);-1!==t&&n.splice(t,1),a.delete(e)},process:l=>{if(i)o=!0;else{if(i=!0,[t,n]=[n,t],n.length=0,r=t.length,r)for(let n=0;n<r;n++){const r=t[n];r(l),a.has(r)&&(s.schedule(r),e())}i=!1,o&&(o=!1,s.process(l))}}};return s}((()=>mt=!0)),e)),{}),vt=e=>yt[e].process(pt),bt=e=>{mt=!1,pt.delta=ht?1e3/60:Math.max(Math.min(e-pt.timestamp,40),1),pt.timestamp=e,pt.isProcessing=!0,gt.forEach(vt),pt.isProcessing=!1,mt&&(ht=!1,requestAnimationFrame(bt))},xt=gt.reduce(((e,t)=>{const n=yt[t];return e[t]=(e,t=!1,r=!1)=>(mt||(mt=!0,ht=!0,pt.isProcessing||requestAnimationFrame(bt)),n.schedule(e,t,r)),e}),{});function wt(e){gt.forEach((t=>yt[t].cancel(e)))}function kt(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End");return it(e.current,n,((n,i)=>{if("touch"===n.type||dt())return;const o=e.getProps();e.animationState&&o.whileHover&&e.animationState.setActive("whileHover",t),o[r]&&xt.update((()=>o[r](n,i)))}),{passive:!e.getProps()[r]})}const St=(e,t)=>!!t&&(e===t||St(e,t.parentElement)),Pt=e=>e;function Et(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,rt(n))}const Ct=new WeakMap,Tt=new WeakMap,Lt=e=>{const t=Ct.get(e.target);t&&t(e)},Mt=e=>{e.forEach(Lt)};function Rt(e,t,n){const r=function({root:e,...t}){const n=e||document;Tt.has(n)||Tt.set(n,{});const r=Tt.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(Mt,{root:e,...t})),r[i]}(t);return Ct.set(e,n),r.observe(e),()=>{Ct.delete(e),r.unobserve(e)}}const Dt={some:0,all:1};const At={inView:{Feature:class extends ft{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:n,threshold:"number"==typeof r?r:Dt[r]};return Rt(this.node.current,o,(e=>{const{isIntersecting:t}=e;if(this.isInView===t)return;if(this.isInView=t,i&&!t&&this.hasEnteredView)return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);const{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),o=t?n:r;o&&o(e)}))}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}(e,t))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends ft{constructor(){super(...arguments),this.removeStartListeners=Pt,this.removeEndListeners=Pt,this.removeAccessibleListeners=Pt,this.startPointerPress=(e,t)=>{if(this.removeEndListeners(),this.isPressing)return;const n=this.node.getProps(),r=it(window,"pointerup",((e,t)=>{if(!this.checkPressEnd())return;const{onTap:n,onTapCancel:r}=this.node.getProps();xt.update((()=>{St(this.node.current,e.target)?n&&n(e,t):r&&r(e,t)}))}),{passive:!(n.onTap||n.onPointerUp)}),i=it(window,"pointercancel",((e,t)=>this.cancelPress(e,t)),{passive:!(n.onTapCancel||n.onPointerCancel)});this.removeEndListeners=at(r,i),this.startPress(e,t)},this.startAccessiblePress=()=>{const e=tt(this.node.current,"keydown",(e=>{if("Enter"!==e.key||this.isPressing)return;this.removeEndListeners(),this.removeEndListeners=tt(this.node.current,"keyup",(e=>{"Enter"===e.key&&this.checkPressEnd()&&Et("up",((e,t)=>{const{onTap:n}=this.node.getProps();n&&xt.update((()=>n(e,t)))}))})),Et("down",((e,t)=>{this.startPress(e,t)}))})),t=tt(this.node.current,"blur",(()=>{this.isPressing&&Et("cancel",((e,t)=>this.cancelPress(e,t)))}));this.removeAccessibleListeners=at(e,t)}}startPress(e,t){this.isPressing=!0;const{onTapStart:n,whileTap:r}=this.node.getProps();r&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),n&&xt.update((()=>n(e,t)))}checkPressEnd(){this.removeEndListeners(),this.isPressing=!1;return this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!dt()}cancelPress(e,t){if(!this.checkPressEnd())return;const{onTapCancel:n}=this.node.getProps();n&&xt.update((()=>n(e,t)))}mount(){const e=this.node.getProps(),t=it(this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),n=tt(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=at(t,n)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}},focus:{Feature:class extends ft{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=at(tt(this.node.current,"focus",(()=>this.onFocus())),tt(this.node.current,"blur",(()=>this.onBlur())))}unmount(){}}},hover:{Feature:class extends ft{mount(){this.unmount=at(kt(this.node,!0),kt(this.node,!1))}unmount(){}}}};function _t(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function Vt(e,t,n){const r=e.getProps();return Ye(r,t,void 0!==n?n:r.custom,function(e){const t={};return e.values.forEach(((e,n)=>t[n]=e.get())),t}(e),function(e){const t={};return e.values.forEach(((e,n)=>t[n]=e.getVelocity())),t}(e))}const Nt="data-"+Be("framerAppearId");let zt=Pt;const Ft=e=>1e3*e,Ot=e=>e/1e3,It=!1,jt=e=>Array.isArray(e)&&"number"==typeof e[0];function Bt(e){return Boolean(!e||"string"==typeof e&&$t[e]||jt(e)||Array.isArray(e)&&e.every(Bt))}const Ut=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,$t={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ut([0,.65,.55,1]),circOut:Ut([.55,0,1,.45]),backIn:Ut([.31,.01,.66,-.59]),backOut:Ut([.33,1.53,.69,.99])};function Wt(e){if(e)return jt(e)?Ut(e):Array.isArray(e)?e.map(Wt):$t[e]}const Ht={waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate")},Qt={},Yt={};for(const wa in Ht)Yt[wa]=()=>(void 0===Qt[wa]&&(Qt[wa]=Ht[wa]()),Qt[wa]);const qt=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function Xt(e,t,n,r){if(e===t&&n===r)return Pt;const i=t=>function(e,t,n,r,i){let o,a,s=0;do{a=t+(n-t)/2,o=qt(a,r,i)-e,o>0?n=a:t=a}while(Math.abs(o)>1e-7&&++s<12);return a}(t,0,1,e,n);return e=>0===e||1===e?e:qt(i(e),t,r)}const Kt=Xt(.42,0,1,1),Gt=Xt(0,0,.58,1),Zt=Xt(.42,0,.58,1),Jt=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,en=e=>t=>1-e(1-t),tn=e=>1-Math.sin(Math.acos(e)),nn=en(tn),rn=Jt(nn),on=Xt(.33,1.53,.69,.99),an=en(on),sn=Jt(an),ln={linear:Pt,easeIn:Kt,easeInOut:Zt,easeOut:Gt,circIn:tn,circInOut:rn,circOut:nn,backIn:an,backInOut:sn,backOut:on,anticipate:e=>(e*=2)<1?.5*an(e):.5*(2-Math.pow(2,-10*(e-1)))},un=e=>{if(Array.isArray(e)){zt(4===e.length);const[t,n,r,i]=e;return Xt(t,n,r,i)}return"string"==typeof e?ln[e]:e},cn=(e,t)=>n=>Boolean(he(n)&&pe.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),dn=(e,t,n)=>r=>{if(!he(r))return r;const[i,o,a,s]=r.match(de);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(a),alpha:void 0!==s?parseFloat(s):1}},fn={...se,transform:e=>Math.round((e=>ae(0,255,e))(e))},pn={test:cn("rgb","red"),parse:dn("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+fn.transform(e)+", "+fn.transform(t)+", "+fn.transform(n)+", "+ce(le.transform(r))+")"};const hn={test:cn("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:pn.transform},mn={test:cn("hsl","hue"),parse:dn("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+ye.transform(ce(t))+", "+ye.transform(ce(n))+", "+ce(le.transform(r))+")"},gn={test:e=>pn.test(e)||hn.test(e)||mn.test(e),parse:e=>pn.test(e)?pn.parse(e):mn.test(e)?mn.parse(e):hn.parse(e),transform:e=>he(e)?e:e.hasOwnProperty("red")?pn.transform(e):mn.transform(e)},yn=(e,t,n)=>-n*e+n*t+e;function vn(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}const bn=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},xn=[hn,pn,mn];function wn(e){const t=(n=e,xn.find((e=>e.test(n))));var n;let r=t.parse(e);return t===mn&&(r=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let i=0,o=0,a=0;if(t/=100){const r=n<.5?n*(1+t):n+t-n*t,s=2*n-r;i=vn(s,r,e+1/3),o=vn(s,r,e),a=vn(s,r,e-1/3)}else i=o=a=n;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*a),alpha:r}}(r)),r}const kn=(e,t)=>{const n=wn(e),r=wn(t),i={...n};return e=>(i.red=bn(n.red,r.red,e),i.green=bn(n.green,r.green,e),i.blue=bn(n.blue,r.blue,e),i.alpha=yn(n.alpha,r.alpha,e),pn.transform(i))};const Sn={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:Pt},Pn={regex:fe,countKey:"Colors",token:"${c}",parse:gn.parse},En={regex:de,countKey:"Numbers",token:"${n}",parse:se.parse};function Cn(e,{regex:t,countKey:n,token:r,parse:i}){const o=e.tokenised.match(t);o&&(e["num"+n]=o.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...o.map(i)))}function Tn(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&Cn(n,Sn),Cn(n,Pn),Cn(n,En),n}function Ln(e){return Tn(e).values}function Mn(e){const{values:t,numColors:n,numVars:r,tokenised:i}=Tn(e),o=t.length;return e=>{let t=i;for(let i=0;i<o;i++)t=i<r?t.replace(Sn.token,e[i]):i<r+n?t.replace(Pn.token,gn.transform(e[i])):t.replace(En.token,ce(e[i]));return t}}const Rn=e=>"number"==typeof e?0:e;const Dn={test:function(e){var t,n;return isNaN(e)&&he(e)&&((null===(t=e.match(de))||void 0===t?void 0:t.length)||0)+((null===(n=e.match(fe))||void 0===n?void 0:n.length)||0)>0},parse:Ln,createTransformer:Mn,getAnimatableNone:function(e){const t=Ln(e);return Mn(e)(t.map(Rn))}},An=(e,t)=>n=>`${n>0?t:e}`;function _n(e,t){return"number"==typeof e?n=>yn(e,t,n):gn.test(e)?kn(e,t):e.startsWith("var(")?An(e,t):zn(e,t)}const Vn=(e,t)=>{const n=[...e],r=n.length,i=e.map(((e,n)=>_n(e,t[n])));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}},Nn=(e,t)=>{const n={...e,...t},r={};for(const i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=_n(e[i],t[i]));return e=>{for(const t in r)n[t]=r[t](e);return n}},zn=(e,t)=>{const n=Dn.createTransformer(t),r=Tn(e),i=Tn(t);return r.numVars===i.numVars&&r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?at(Vn(r.values,i.values),n):An(e,t)},Fn=(e,t,n)=>{const r=t-e;return 0===r?1:(n-e)/r},On=(e,t)=>n=>yn(e,t,n);function In(e,t,n){const r=[],i=n||("number"==typeof(o=e[0])?On:"string"==typeof o?gn.test(o)?kn:zn:Array.isArray(o)?Vn:"object"==typeof o?Nn:On);var o;const a=e.length-1;for(let s=0;s<a;s++){let n=i(e[s],e[s+1]);if(t){const e=Array.isArray(t)?t[s]||Pt:t;n=at(e,n)}r.push(n)}return r}function jn(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const o=e.length;if(zt(o===t.length),1===o)return()=>t[0];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const a=In(t,r,i),s=a.length,l=t=>{let n=0;if(s>1)for(;n<e.length-2&&!(t<e[n+1]);n++);const r=Fn(e[n],e[n+1],t);return a[n](r)};return n?t=>l(ae(e[0],e[o-1],t)):l}function Bn(e){const t=[0];return function(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=Fn(0,t,r);e.push(yn(n,1,i))}}(t,e.length-1),t}function Un({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=(e=>Array.isArray(e)&&"number"!=typeof e[0])(r)?r.map(un):un(r),o={done:!1,value:t[0]},a=function(e,t){return e.map((e=>e*t))}(n&&n.length===t.length?n:Bn(t),e),s=jn(a,t,{ease:Array.isArray(i)?i:(l=t,u=i,l.map((()=>u||Zt)).splice(0,l.length-1))});var l,u;return{calculatedDuration:e,next:t=>(o.value=s(t),o.done=t>=e,o)}}function $n(e,t){return t?e*(1e3/t):0}function Wn(e,t,n){const r=Math.max(t-5,0);return $n(n-e(r),t-r)}function Hn({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,o,a=1-t;a=ae(.05,1,a),e=ae(.01,10,Ot(e)),a<1?(i=t=>{const r=t*a,i=r*e;return.001-(r-n)/Yn(t,a)*Math.exp(-i)},o=t=>{const r=t*a*e,o=r*n+n,s=Math.pow(a,2)*Math.pow(t,2)*e,l=Math.exp(-r),u=Yn(Math.pow(t,2),a);return(.001-i(t)>0?-1:1)*((o-s)*l)/u}):(i=t=>Math.exp(-t*e)*((t-n)*e+1)-.001,o=t=>Math.exp(-t*e)*(e*e*(n-t)));const s=function(e,t,n){let r=n;for(let i=1;i<Qn;i++)r-=e(r)/t(r);return r}(i,o,5/e);if(e=Ft(e),isNaN(s))return{stiffness:100,damping:10,duration:e};{const t=Math.pow(s,2)*r;return{stiffness:t,damping:2*a*Math.sqrt(r*t),duration:e}}}const Qn=12;function Yn(e,t){return e*Math.sqrt(1-t*t)}const qn=["duration","bounce"],Xn=["stiffness","damping","mass"];function Kn(e,t){return t.some((t=>void 0!==e[t]))}function Gn({keyframes:e,restDelta:t,restSpeed:n,...r}){const i=e[0],o=e[e.length-1],a={done:!1,value:i},{stiffness:s,damping:l,mass:u,velocity:c,duration:d,isResolvedFromDuration:f}=function(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!Kn(e,Xn)&&Kn(e,qn)){const n=Hn(e);t={...t,...n,velocity:0,mass:1},t.isResolvedFromDuration=!0}return t}(r),p=c?-Ot(c):0,h=l/(2*Math.sqrt(s*u)),m=o-i,g=Ot(Math.sqrt(s/u)),y=Math.abs(m)<5;let v;if(n||(n=y?.01:2),t||(t=y?.005:.5),h<1){const e=Yn(g,h);v=t=>{const n=Math.exp(-h*g*t);return o-n*((p+h*g*m)/e*Math.sin(e*t)+m*Math.cos(e*t))}}else if(1===h)v=e=>o-Math.exp(-g*e)*(m+(p+g*m)*e);else{const e=g*Math.sqrt(h*h-1);v=t=>{const n=Math.exp(-h*g*t),r=Math.min(e*t,300);return o-n*((p+h*g*m)*Math.sinh(r)+e*m*Math.cosh(r))/e}}return{calculatedDuration:f&&d||null,next:e=>{const r=v(e);if(f)a.done=e>=d;else{let i=p;0!==e&&(i=h<1?Wn(v,e,r):0);const s=Math.abs(i)<=n,l=Math.abs(o-r)<=t;a.done=s&&l}return a.value=a.done?o:r,a}}}function Zn({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:a,min:s,max:l,restDelta:u=.5,restSpeed:c}){const d=e[0],f={done:!1,value:d},p=e=>void 0===s?l:void 0===l||Math.abs(s-e)<Math.abs(l-e)?s:l;let h=n*t;const m=d+h,g=void 0===a?m:a(m);g!==m&&(h=g-d);const y=e=>-h*Math.exp(-e/r),v=e=>g+y(e),b=e=>{const t=y(e),n=v(e);f.done=Math.abs(t)<=u,f.value=f.done?g:n};let x,w;const k=e=>{var t;(t=f.value,void 0!==s&&t<s||void 0!==l&&t>l)&&(x=e,w=Gn({keyframes:[f.value,p(f.value)],velocity:Wn(v,e,f.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return k(0),{calculatedDuration:null,next:e=>{let t=!1;return w||void 0!==x||(t=!0,b(e),k(e)),void 0!==x&&e>x?w.next(e-x):(!t&&b(e),f)}}}const Jn=e=>{const t=({timestamp:t})=>e(t);return{start:()=>xt.update(t,!0),stop:()=>wt(t),now:()=>pt.isProcessing?pt.timestamp:performance.now()}};function er(e){let t=0;let n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}const tr={decay:Zn,inertia:Zn,tween:Un,keyframes:Un,spring:Gn};function nr({autoplay:e=!0,delay:t=0,driver:n=Jn,keyframes:r,type:i="keyframes",repeat:o=0,repeatDelay:a=0,repeatType:s="loop",onPlay:l,onStop:u,onComplete:c,onUpdate:d,...f}){let p,h,m=1,g=!1;const y=()=>{p&&p(),h=new Promise((e=>{p=e}))};let v;y();const b=tr[i]||Un;let x;b!==Un&&"number"!=typeof r[0]&&(x=jn([0,100],r,{clamp:!1}),r=[0,100]);const w=b({...f,keyframes:r});let k;"mirror"===s&&(k=b({...f,keyframes:[...r].reverse(),velocity:-(f.velocity||0)}));let S="idle",P=null,E=null,C=null;null===w.calculatedDuration&&o&&(w.calculatedDuration=er(w));const{calculatedDuration:T}=w;let L=1/0,M=1/0;null!==T&&(L=T+a,M=L*(o+1)-a);let R=0;const D=e=>{if(null===E)return;m>0&&(E=Math.min(E,e)),R=null!==P?P:(e-E)*m;const n=R-t,i=n<0;R=Math.max(n,0),"finished"===S&&null===P&&(R=M);let l=R,u=w;if(o){const e=R/L;let t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,t=Math.min(t,o+1);const r=Boolean(t%2);r&&("reverse"===s?(n=1-n,a&&(n-=a/L)):"mirror"===s&&(u=k));let i=ae(0,1,n);R>M&&(i="reverse"===s&&r?1:0),l=i*L}const c=i?{done:!1,value:r[0]}:u.next(l);x&&(c.value=x(c.value));let{done:f}=c;i||null===T||(f=R>=M);const p=null===P&&("finished"===S||"running"===S&&f||m<0&&R<=0);return d&&d(c.value),p&&V(),c},A=()=>{v&&v.stop(),v=void 0},_=()=>{S="idle",A(),y(),E=C=null},V=()=>{S="finished",c&&c(),A(),y()},N=()=>{if(g)return;v||(v=n(D));const e=v.now();l&&l(),null!==P?E=e-P:E&&"finished"!==S||(E=e),C=E,P=null,S="running",v.start()};e&&N();const z={then:(e,t)=>h.then(e,t),get time(){return Ot(R)},set time(e){e=Ft(e),R=e,null===P&&v&&0!==m?E=v.now()-e/m:P=e},get duration(){const e=null===w.calculatedDuration?er(w):w.calculatedDuration;return Ot(e)},get speed(){return m},set speed(e){e!==m&&v&&(m=e,z.time=Ot(R))},get state(){return S},play:N,pause:()=>{S="paused",P=R},stop:()=>{g=!0,"idle"!==S&&(S="idle",u&&u(),_())},cancel:()=>{null!==C&&D(C),_()},complete:()=>{S="finished"},sample:e=>(E=0,D(e))};return z}const rr=new Set(["opacity","clipPath","filter","transform","backgroundColor"]);function ir(e,t,{onUpdate:n,onComplete:r,...i}){if(!(Yt.waapi()&&rr.has(t)&&!i.repeatDelay&&"mirror"!==i.repeatType&&0!==i.damping&&"inertia"!==i.type))return!1;let o,a,s=!1;const l=()=>{a=new Promise((e=>{o=e}))};l();let{keyframes:u,duration:c=300,ease:d,times:f}=i;if(((e,t)=>"spring"===t.type||"backgroundColor"===e||!Bt(t.ease))(t,i)){const e=nr({...i,repeat:0,delay:0});let t={done:!1,value:u[0]};const n=[];let r=0;for(;!t.done&&r<2e4;)t=e.sample(r),n.push(t.value),r+=10;f=void 0,u=n,c=r-10,d="linear"}const p=function(e,t,n,{delay:r=0,duration:i,repeat:o=0,repeatType:a="loop",ease:s,times:l}={}){const u={[t]:n};l&&(u.offset=l);const c=Wt(s);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===a?"alternate":"normal"})}(e.owner.current,t,u,{...i,duration:c,ease:d,times:f}),h=()=>p.cancel(),m=()=>{xt.update(h),o(),l()};return p.onfinish=()=>{e.set(function(e,{repeat:t,repeatType:n="loop"}){return e[t&&"loop"!==n&&t%2==1?0:e.length-1]}(u,i)),r&&r(),m()},{then:(e,t)=>a.then(e,t),get time(){return Ot(p.currentTime||0)},set time(e){p.currentTime=Ft(e)},get speed(){return p.playbackRate},set speed(e){p.playbackRate=e},get duration(){return Ot(c)},play:()=>{s||(p.play(),wt(h))},pause:()=>p.pause(),stop:()=>{if(s=!0,"idle"===p.playState)return;const{currentTime:t}=p;if(t){const n=nr({...i,autoplay:!1});e.setWithVelocity(n.sample(t-10).value,n.sample(t).value,10)}m()},complete:()=>p.finish(),cancel:m}}const or={type:"spring",stiffness:500,damping:25,restSpeed:10},ar={type:"keyframes",duration:.8},sr={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},lr=(e,{keyframes:t})=>t.length>2?ar:G.has(e)?e.startsWith("scale")?{type:"spring",stiffness:550,damping:0===t[1]?2*Math.sqrt(550):30,restSpeed:10}:or:sr,ur=(e,t)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!Dn.test(t)&&"0"!==t||t.startsWith("url("))),cr=new Set(["brightness","contrast","saturate","opacity"]);function dr(e){const[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;const[r]=n.match(de)||[];if(!r)return e;const i=n.replace(r,"");let o=cr.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+i+")"}const fr=/([a-z-]*)\(.*?\)/g,pr={...Dn,getAnimatableNone:e=>{const t=e.match(fr);return t?t.map(dr).join(" "):e}},hr={...Se,color:gn,backgroundColor:gn,outlineColor:gn,fill:gn,stroke:gn,borderColor:gn,borderTopColor:gn,borderRightColor:gn,borderBottomColor:gn,borderLeftColor:gn,filter:pr,WebkitFilter:pr},mr=e=>hr[e];function gr(e,t){let n=mr(e);return n!==pr&&(n=Dn),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const yr=e=>/^0[^.\s]+$/.test(e);function vr(e){return"number"==typeof e?0===e:null!==e?"none"===e||"0"===e||yr(e):void 0}function br(e,t){return e[t]||e.default||e}const xr=(e,t,n,r={})=>i=>{const o=br(r,e)||{},a=o.delay||r.delay||0;let{elapsed:s=0}=r;s-=Ft(a);const l=function(e,t,n,r){const i=ur(t,n);let o;o=Array.isArray(n)?[...n]:[null,n];const a=void 0!==r.from?r.from:e.get();let s;const l=[];for(let u=0;u<o.length;u++)null===o[u]&&(o[u]=0===u?a:o[u-1]),vr(o[u])&&l.push(u),"string"==typeof o[u]&&"none"!==o[u]&&"0"!==o[u]&&(s=o[u]);if(i&&l.length&&s)for(let u=0;u<l.length;u++)o[l[u]]=gr(t,s);return o}(t,e,n,o),u=l[0],c=l[l.length-1],d=ur(e,u),f=ur(e,c);let p={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...o,delay:-s,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(function({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:a,repeatDelay:s,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(o)||(p={...p,...lr(e,p)}),p.duration&&(p.duration=Ft(p.duration)),p.repeatDelay&&(p.repeatDelay=Ft(p.repeatDelay)),!d||!f||It||!1===o.type)return function({keyframes:e,delay:t,onUpdate:n,onComplete:r}){const i=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:Pt,pause:Pt,stop:Pt,then:e=>(e(),Promise.resolve()),cancel:Pt,complete:Pt});return t?nr({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}(p);if(t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const n=ir(t,e,p);if(n)return n}return nr(p)};function wr(e){return Boolean(J(e)&&e.add)}function kr(e,t){-1===e.indexOf(t)&&e.push(t)}function Sr(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class Pr{constructor(){this.subscriptions=[]}add(e){return kr(this.subscriptions,e),()=>Sr(this.subscriptions,e)}notify(e,t,n){const r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){const r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}class Er{constructor(e,t={}){var n;this.version="10.12.16",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(e,t=!0)=>{this.prev=this.current,this.current=e;const{delta:n,timestamp:r}=pt;this.lastUpdated!==r&&(this.timeDelta=n,this.lastUpdated=r,xt.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>xt.postRender(this.velocityCheck),this.velocityCheck=({timestamp:e})=>{e!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=(n=this.current,!isNaN(parseFloat(n))),this.owner=t.owner}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new Pr);const n=this.events[e].add(t);return"change"===e?()=>{n(),xt.read((()=>{this.events.change.getSize()||this.stop()}))}:n}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=e,this.timeDelta=n}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?$n(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise((t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Cr(e,t){return new Er(e,t)}const Tr=e=>t=>t.test(e),Lr=[se,ve,ye,ge,xe,be,{test:e=>"auto"===e,parse:e=>e}],Mr=e=>Lr.find(Tr(e)),Rr=[...Lr,gn,Dn],Dr=e=>Rr.find(Tr(e));function Ar(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Cr(n))}function _r(e,t){if(!t)return;return(t[e]||t.default||t).from}function Vr({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,r}function Nr(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:a,...s}=e.makeTargetAnimatable(t);const l=e.getValue("willChange");r&&(o=r);const u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(const d in s){const t=e.getValue(d),r=s[d];if(!t||void 0===r||c&&Vr(c,d))continue;const i={delay:n,elapsed:0,...o};if(window.HandoffAppearAnimations&&!t.hasAnimated){const n=e.getProps()[Nt];n&&(i.elapsed=window.HandoffAppearAnimations(n,d,t,xt))}t.start(xr(d,t,r,e.shouldReduceMotion&&G.has(d)?{type:!1}:i));const a=t.animation;wr(l)&&(l.add(d),a.then((()=>l.remove(d)))),u.push(a)}return a&&Promise.all(u).then((()=>{a&&function(e,t){const n=Vt(e,t);let{transitionEnd:r={},transition:i={},...o}=n?e.makeTargetAnimatable(n,!1):{};o={...o,...r};for(const s in o)Ar(e,s,(a=o[s],Xe(a)?a[a.length-1]||0:a));var a}(e,a)})),u}function zr(e,t,n={}){const r=Vt(e,t,n.custom);let{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);const o=r?()=>Promise.all(Nr(e,r,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(r=0)=>{const{delayChildren:o=0,staggerChildren:a,staggerDirection:s}=i;return function(e,t,n=0,r=0,i=1,o){const a=[],s=(e.variantChildren.size-1)*r,l=1===i?(e=0)=>e*r:(e=0)=>s-e*r;return Array.from(e.variantChildren).sort(Fr).forEach(((e,r)=>{e.notify("AnimationStart",t),a.push(zr(e,t,{...o,delay:n+l(r)}).then((()=>e.notify("AnimationComplete",t))))})),Promise.all(a)}(e,t,o+r,a,s,n)}:()=>Promise.resolve(),{when:s}=i;if(s){const[e,t]="beforeChildren"===s?[o,a]:[a,o];return e().then((()=>t()))}return Promise.all([o(),a(n.delay)])}function Fr(e,t){return e.sortNodePosition(t)}const Or=[..._].reverse(),Ir=_.length;function jr(e){return t=>Promise.all(t.map((({animation:t,options:n})=>function(e,t,n={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t)){const i=t.map((t=>zr(e,t,n)));r=Promise.all(i)}else if("string"==typeof t)r=zr(e,t,n);else{const i="function"==typeof t?Vt(e,t,n.custom):t;r=Promise.all(Nr(e,i,n))}return r.then((()=>e.notify("AnimationComplete",t)))}(e,t,n))))}function Br(e){let t=jr(e);const n={animate:$r(!0),whileInView:$r(),whileHover:$r(),whileTap:$r(),whileDrag:$r(),whileFocus:$r(),exit:$r()};let r=!0;const i=(t,n)=>{const r=Vt(e,n);if(r){const{transition:e,transitionEnd:n,...i}=r;t={...t,...i,...n}}return t};function o(o,a){const s=e.getProps(),l=e.getVariantContext(!0)||{},u=[],c=new Set;let d={},f=1/0;for(let t=0;t<Ir;t++){const p=Or[t],h=n[p],m=void 0!==s[p]?s[p]:l[p],g=D(m),y=p===a?h.isActive:null;!1===y&&(f=t);let v=m===l[p]&&m!==s[p]&&g;if(v&&r&&e.manuallyAnimateOnMount&&(v=!1),h.protectedKeys={...d},!h.isActive&&null===y||!m&&!h.prevProp||A(m)||"boolean"==typeof m)continue;const b=Ur(h.prevProp,m);let x=b||p===a&&h.isActive&&!v&&g||t>f&&g;const w=Array.isArray(m)?m:[m];let k=w.reduce(i,{});!1===y&&(k={});const{prevResolvedValues:S={}}=h,P={...S,...k},E=e=>{x=!0,c.delete(e),h.needsAnimating[e]=!0};for(const e in P){const t=k[e],n=S[e];d.hasOwnProperty(e)||(t!==n?Xe(t)&&Xe(n)?!_t(t,n)||b?E(e):h.protectedKeys[e]=!0:void 0!==t?E(e):c.add(e):void 0!==t&&c.has(e)?E(e):h.protectedKeys[e]=!0)}h.prevProp=m,h.prevResolvedValues=k,h.isActive&&(d={...d,...k}),r&&e.blockInitialAnimation&&(x=!1),x&&!v&&u.push(...w.map((e=>({animation:e,options:{type:p,...o}}))))}if(c.size){const t={};c.forEach((n=>{const r=e.getBaseTarget(n);void 0!==r&&(t[n]=r)})),u.push({animation:t})}let p=Boolean(u.length);return r&&!1===s.initial&&!e.manuallyAnimateOnMount&&(p=!1),r=!1,p?t(u):Promise.resolve()}return{animateChanges:o,setActive:function(t,r,i){var a;if(n[t].isActive===r)return Promise.resolve();null===(a=e.variantChildren)||void 0===a||a.forEach((e=>{var n;return null===(n=e.animationState)||void 0===n?void 0:n.setActive(t,r)})),n[t].isActive=r;const s=o(i,t);for(const e in n)n[e].protectedKeys={};return s},setAnimateFunction:function(n){t=n(e)},getState:()=>n}}function Ur(e,t){return"string"==typeof t?t!==e:!!Array.isArray(t)&&!_t(t,e)}function $r(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}let Wr=0;const Hr={animation:{Feature:class extends ft{constructor(e){super(e),e.animationState||(e.animationState=Br(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();this.unmount(),A(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){}}},exit:{Feature:class extends ft{constructor(){super(...arguments),this.id=Wr++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:t,custom:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;const i=this.node.animationState.setActive("exit",!e,{custom:null!=n?n:this.node.getProps().custom});t&&!e&&i.then((()=>t(this.id)))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}}},Qr=(e,t)=>Math.abs(e-t);class Yr{constructor(e,t,{transformPagePoint:n}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const e=Kr(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){const n=Qr(e.x,t.x),r=Qr(e.y,t.y);return Math.sqrt(n**2+r**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;const{point:r}=e,{timestamp:i}=pt;this.history.push({...r,timestamp:i});const{onStart:o,onMove:a}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=qr(t,this.transformPagePoint),xt.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{if(this.end(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const{onEnd:n,onSessionEnd:r}=this.handlers,i=Kr("pointercancel"===e.type?this.lastMoveEventInfo:qr(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,i),r&&r(e,i)},!nt(e))return;this.handlers=t,this.transformPagePoint=n;const r=qr(rt(e),this.transformPagePoint),{point:i}=r,{timestamp:o}=pt;this.history=[{...i,timestamp:o}];const{onSessionStart:a}=t;a&&a(e,Kr(r,this.history)),this.removeListeners=at(it(window,"pointermove",this.handlePointerMove),it(window,"pointerup",this.handlePointerUp),it(window,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),wt(this.updatePoint)}}function qr(e,t){return t?{point:t(e.point)}:e}function Xr(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Kr({point:e},t){return{point:e,delta:Xr(e,Zr(t)),offset:Xr(e,Gr(t)),velocity:Jr(t,.1)}}function Gr(e){return e[0]}function Zr(e){return e[e.length-1]}function Jr(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=Zr(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>Ft(t)));)n--;if(!r)return{x:0,y:0};const o=Ot(i.timestamp-r.timestamp);if(0===o)return{x:0,y:0};const a={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}function ei(e){return e.max-e.min}function ti(e,t=0,n=.01){return Math.abs(e-t)<=n}function ni(e,t,n,r=.5){e.origin=r,e.originPoint=yn(t.min,t.max,e.origin),e.scale=ei(n)/ei(t),(ti(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=yn(n.min,n.max,e.origin)-e.originPoint,(ti(e.translate)||isNaN(e.translate))&&(e.translate=0)}function ri(e,t,n,r){ni(e.x,t.x,n.x,r?r.originX:void 0),ni(e.y,t.y,n.y,r?r.originY:void 0)}function ii(e,t,n){e.min=n.min+t.min,e.max=e.min+ei(t)}function oi(e,t,n){e.min=t.min-n.min,e.max=e.min+ei(t)}function ai(e,t,n){oi(e.x,t.x,n.x),oi(e.y,t.y,n.y)}function si(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function li(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}const ui=.35;function ci(e,t,n){return{min:di(e,t),max:di(e,n)}}function di(e,t){return"number"==typeof e?e:e[t]||0}function fi(e){return[e("x"),e("y")]}function pi({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function hi(e){return void 0===e||1===e}function mi({scale:e,scaleX:t,scaleY:n}){return!hi(e)||!hi(t)||!hi(n)}function gi(e){return mi(e)||yi(e)||e.z||e.rotate||e.rotateX||e.rotateY}function yi(e){return vi(e.x)||vi(e.y)}function vi(e){return e&&"0%"!==e}function bi(e,t,n){return n+t*(e-n)}function xi(e,t,n,r,i){return void 0!==i&&(e=bi(e,i,r)),bi(e,n,r)+t}function wi(e,t=0,n=1,r,i){e.min=xi(e.min,t,n,r,i),e.max=xi(e.max,t,n,r,i)}function ki(e,{x:t,y:n}){wi(e.x,t.translate,t.scale,t.originPoint),wi(e.y,n.translate,n.scale,n.originPoint)}function Si(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Pi(e,t){e.min=e.min+t,e.max=e.max+t}function Ei(e,t,[n,r,i]){const o=void 0!==t[i]?t[i]:.5,a=yn(e.min,e.max,o);wi(e,t[n],t[r],a,t.scale)}const Ci=["x","scaleX","originX"],Ti=["y","scaleY","originY"];function Li(e,t){Ei(e.x,t,Ci),Ei(e.y,t,Ti)}function Mi(e,t){return pi(function(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}const Ri=new WeakMap;class Di{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=e}start(e,{snapToCursor:t=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;this.panSession=new Yr(e,{onSessionStart:e=>{this.stopAnimation(),t&&this.snapToCursor(rt(e,"page").point)},onStart:(e,t)=>{const{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=ct(n),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),fi((e=>{let t=this.getAxisMotionValue(e).get()||0;if(ye.test(t)){const{projection:n}=this.visualElement;if(n&&n.layout){const r=n.layout.layoutBox[e];if(r){t=ei(r)*(parseFloat(t)/100)}}}this.originPoint[e]=t})),i&&xt.update((()=>i(e,t)),!1,!0);const{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{const{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:o}=this.getProps();if(!n&&!this.openGlobalLock)return;const{offset:a}=t;if(r&&null===this.currentDirection)return this.currentDirection=function(e,t=10){let n=null;Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x");return n}(a),void(null!==this.currentDirection&&i&&i(this.currentDirection));this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>this.stop(e,t)},{transformPagePoint:this.visualElement.getTransformPagePoint()})}stop(e,t){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:r}=t;this.startAnimation(r);const{onDragEnd:i}=this.getProps();i&&xt.update((()=>i(e,t)))}cancel(){this.isDragging=!1;const{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){const{drag:r}=this.getProps();if(!n||!Ai(e,r,this.currentDirection))return;const i=this.getAxisMotionValue(e);let o=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:n},r){return void 0!==t&&e<t?e=r?yn(t,e,r.min):Math.max(e,t):void 0!==n&&e>n&&(e=r?yn(n,e,r.max):Math.min(e,n)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){const{dragConstraints:e,dragElastic:t}=this.getProps(),{layout:n}=this.visualElement.projection||{},r=this.constraints;e&&R(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!e||!n)&&function(e,{top:t,left:n,bottom:r,right:i}){return{x:si(e.x,n,i),y:si(e.y,t,r)}}(n.layoutBox,e),this.elastic=function(e=ui){return!1===e?e=0:!0===e&&(e=ui),{x:ci(e,"left","right"),y:ci(e,"top","bottom")}}(t),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&fi((e=>{this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){const n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(n.layoutBox[e],this.constraints[e]))}))}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:t}=this.getProps();if(!e||!R(e))return!1;const n=e.current,{projection:r}=this.visualElement;if(!r||!r.layout)return!1;const i=function(e,t,n){const r=Mi(e,n),{scroll:i}=t;return i&&(Pi(r.x,i.offset.x),Pi(r.y,i.offset.y)),r}(n,r.root,this.visualElement.getTransformPagePoint());let o=function(e,t){return{x:li(e.x,t.x),y:li(e.y,t.y)}}(r.layout.layoutBox,i);if(t){const e=t(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(o));this.hasMutatedConstraints=!!e,e&&(o=pi(e))}return o}startAnimation(e){const{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),s=this.constraints||{},l=fi((a=>{if(!Ai(a,t,this.currentDirection))return;let l=s&&s[a]||{};o&&(l={min:0,max:0});const u=r?200:1e6,c=r?40:1e7,d={type:"inertia",velocity:n?e[a]:0,bounceStiffness:u,bounceDamping:c,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(a,d)}));return Promise.all(l).then(a)}startAxisValueAnimation(e,t){const n=this.getAxisMotionValue(e);return n.start(xr(e,n,0,t))}stopAnimation(){fi((e=>this.getAxisMotionValue(e).stop()))}getAxisMotionValue(e){const t="_drag"+e.toUpperCase(),n=this.visualElement.getProps(),r=n[t];return r||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){fi((t=>{const{drag:n}=this.getProps();if(!Ai(t,n,this.currentDirection))return;const{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){const{min:n,max:o}=r.layout.layoutBox[t];i.set(e[t]-yn(n,o,.5))}}))}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!R(t)||!n||!this.constraints)return;this.stopAnimation();const r={x:0,y:0};fi((e=>{const t=this.getAxisMotionValue(e);if(t){const n=t.get();r[e]=function(e,t){let n=.5;const r=ei(e),i=ei(t);return i>r?n=Fn(t.min,t.max-r,e.min):r>i&&(n=Fn(e.min,e.max-i,t.min)),ae(0,1,n)}({min:n,max:n},this.constraints[e])}}));const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),fi((t=>{if(!Ai(t,e,null))return;const n=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];n.set(yn(i,o,r[t]))}))}addListeners(){if(!this.visualElement.current)return;Ri.set(this.visualElement,this);const e=it(this.visualElement.current,"pointerdown",(e=>{const{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)})),t=()=>{const{dragConstraints:e}=this.getProps();R(e)&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),t();const i=tt(window,"resize",(()=>this.scalePositionWithinConstraints())),o=n.addEventListener("didUpdate",(({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(fi((t=>{const n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))})),this.visualElement.render())}));return()=>{i(),e(),r(),o&&o()}}getProps(){const e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:o=ui,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:o,dragMomentum:a}}}function Ai(e,t,n){return!(!0!==t&&t!==e||null!==n&&n!==e)}const _i=e=>(t,n)=>{e&&xt.update((()=>e(t,n)))};function Vi(){const e=c.useContext(C);if(null===e)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=c.useId();c.useEffect((()=>r(i)),[]);return!t&&n?[!1,()=>n&&n(i)]:[!0]}function Ni(){return null===(e=c.useContext(C))||e.isPresent;var e}const zi={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Fi(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const Oi={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!ve.test(e))return e;e=parseFloat(e)}return`${Fi(e,t.target.x)}% ${Fi(e,t.target.y)}%`}},Ii={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=Dn.parse(e);if(i.length>5)return r;const o=Dn.createTransformer(e),a="number"!=typeof i[0]?1:0,s=n.x.scale*t.x,l=n.y.scale*t.y;i[0+a]/=s,i[1+a]/=l;const u=yn(s,l,.5);return"number"==typeof i[2+a]&&(i[2+a]/=u),"number"==typeof i[3+a]&&(i[3+a]/=u),o(i)}};class ji extends d.Component{componentDidMount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;var o;o=Ui,Object.assign(X,o),i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",(()=>{this.safeToRemove()})),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),zi.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,o=n.projection;return o?(o.isPresent=i,r||e.layoutDependency!==t||void 0===t?o.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?o.promote():o.relegate()||xt.postRender((()=>{const e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null):null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask((()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()})))}componentWillUnmount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function Bi(e){const[t,n]=Vi(),r=c.useContext(B);return d.createElement(ji,{...e,layoutGroup:r,switchLayoutGroup:c.useContext(U),isPresent:t,safeToRemove:n})}const Ui={borderRadius:{...Oi,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Oi,borderTopRightRadius:Oi,borderBottomLeftRadius:Oi,borderBottomRightRadius:Oi,boxShadow:Ii},$i=["TopLeft","TopRight","BottomLeft","BottomRight"],Wi=$i.length,Hi=e=>"string"==typeof e?parseFloat(e):e,Qi=e=>"number"==typeof e||ve.test(e);function Yi(e,t){return void 0!==e[t]?e[t]:e.borderRadius}const qi=Ki(0,.5,nn),Xi=Ki(.5,.95,Pt);function Ki(e,t,n){return r=>r<e?0:r>t?1:n(Fn(e,t,r))}function Gi(e,t){e.min=t.min,e.max=t.max}function Zi(e,t){Gi(e.x,t.x),Gi(e.y,t.y)}function Ji(e,t,n,r,i){return e=bi(e-=t,1/n,r),void 0!==i&&(e=bi(e,1/i,r)),e}function eo(e,t,[n,r,i],o,a){!function(e,t=0,n=1,r=.5,i,o=e,a=e){ye.test(t)&&(t=parseFloat(t),t=yn(a.min,a.max,t/100)-a.min);if("number"!=typeof t)return;let s=yn(o.min,o.max,r);e===o&&(s-=t),e.min=Ji(e.min,t,n,s,i),e.max=Ji(e.max,t,n,s,i)}(e,t[n],t[r],t[i],t.scale,o,a)}const to=["x","scaleX","originX"],no=["y","scaleY","originY"];function ro(e,t,n,r){eo(e.x,t,to,n?n.x:void 0,r?r.x:void 0),eo(e.y,t,no,n?n.y:void 0,r?r.y:void 0)}function io(e){return 0===e.translate&&1===e.scale}function oo(e){return io(e.x)&&io(e.y)}function ao(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function so(e){return ei(e.x)/ei(e.y)}class lo{constructor(){this.members=[]}add(e){kr(this.members,e),e.scheduleRender()}remove(e){if(Sr(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){const t=this.members.findIndex((t=>e===t));if(0===t)return!1;let n;for(let r=t;r>=0;r--){const e=this.members[r];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(e,t){const n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach((e=>{const{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()}))}scheduleRender(){this.members.forEach((e=>{e.instance&&e.scheduleRender(!1)}))}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function uo(e,t,n){let r="";const i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(r=`translate3d(${i}px, ${o}px, 0) `),1===t.x&&1===t.y||(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:e,rotateX:t,rotateY:i}=n;e&&(r+=`rotate(${e}deg) `),t&&(r+=`rotateX(${t}deg) `),i&&(r+=`rotateY(${i}deg) `)}const a=e.x.scale*t.x,s=e.y.scale*t.y;return 1===a&&1===s||(r+=`scale(${a}, ${s})`),r||"none"}const co=(e,t)=>e.depth-t.depth;class fo{constructor(){this.children=[],this.isDirty=!1}add(e){kr(this.children,e),this.isDirty=!0}remove(e){Sr(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(co),this.isDirty=!1,this.children.forEach(e)}}const po=["","X","Y","Z"];let ho=0;const mo={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function go({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},n=(null==t?void 0:t())){this.id=ho++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{var e;mo.totalNodes=mo.resolvedTargetDeltas=mo.recalculatedProjection=0,this.nodes.forEach(bo),this.nodes.forEach(Co),this.nodes.forEach(To),this.nodes.forEach(xo),e=mo,window.MotionDebug&&window.MotionDebug.record(e)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new fo)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new Pr),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){const n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,n=this.root.hasTreeAnimated){if(this.instance)return;var r;this.isSVG=(r=t)instanceof SVGElement&&"svg"!==r.tagName,this.instance=t;const{layoutId:i,layout:o,visualElement:a}=this.options;if(a&&!a.current&&a.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),n&&(o||i)&&(this.isLayoutDirty=!0),e){let n;const r=()=>this.root.updateBlockedByResize=!1;e(t,(()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){const n=performance.now(),r=({timestamp:i})=>{const o=i-n;o>=t&&(wt(r),e(o-t))};return xt.read(r,!0),()=>wt(r)}(r,250),zi.hasAnimatedSinceResize&&(zi.hasAnimatedSinceResize=!1,this.nodes.forEach(Eo))}))}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&a&&(i||o)&&this.addEventListener("didUpdate",(({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const i=this.options.transition||a.getDefaultTransition()||_o,{onLayoutAnimationStart:o,onLayoutAnimationComplete:s}=a.getProps(),l=!this.targetLayout||!ao(this.targetLayout,r)||n,u=!t&&n;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,u);const t={...br(i,"layout"),onPlay:o,onComplete:s};(a.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||Eo(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r}))}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,wt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Lo),this.animationId++)}getTransformTemplate(){const{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let i=0;i<this.path.length;i++){const e=this.path[i];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;const r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(ko);this.isUpdating||this.nodes.forEach(So),this.isUpdating=!1,this.nodes.forEach(Po),this.nodes.forEach(yo),this.nodes.forEach(vo),this.clearAllSnapshots();const e=performance.now();pt.delta=ae(0,1e3/60,e-pt.timestamp),pt.timestamp=e,pt.isProcessing=!0,yt.update.process(pt),yt.preRender.process(pt),yt.render.process(pt),pt.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask((()=>this.update())))}clearAllSnapshots(){this.nodes.forEach(wo),this.sharedNodes.forEach(Mo)}scheduleUpdateProjection(){xt.preRender(this.updateProjection,!1,!0)}scheduleCheckAfterUnmount(){xt.postRender((()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()}))}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let n=0;n<this.path.length;n++){this.path[n].updateScroll()}const e=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=Boolean(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&(this.scroll={animationId:this.root.animationId,phase:e,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const e=this.isLayoutDirty||this.shouldResetTransform,t=this.projectionDelta&&!oo(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,o=r!==this.prevTransformTemplateValue;e&&(t||gi(this.latestValues)||o)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){const t=this.measurePageBox();let n=this.removeElementScroll(t);var r;return e&&(n=this.removeTransform(n)),Vo((r=n).x),Vo(r.y),{animationId:this.root.animationId,measuredBox:t,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:e}=this.options;if(!e)return{x:{min:0,max:0},y:{min:0,max:0}};const t=e.measureViewportBox(),{scroll:n}=this.root;return n&&(Pi(t.x,n.offset.x),Pi(t.y,n.offset.y)),t}removeElementScroll(e){const t={x:{min:0,max:0},y:{min:0,max:0}};Zi(t,e);for(let n=0;n<this.path.length;n++){const r=this.path[n],{scroll:i,options:o}=r;if(r!==this.root&&i&&o.layoutScroll){if(i.isRoot){Zi(t,e);const{scroll:n}=this.root;n&&(Pi(t.x,-n.offset.x),Pi(t.y,-n.offset.y))}Pi(t.x,i.offset.x),Pi(t.y,i.offset.y)}}return t}applyTransform(e,t=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};Zi(n,e);for(let r=0;r<this.path.length;r++){const e=this.path[r];!t&&e.options.layoutScroll&&e.scroll&&e!==e.root&&Li(n,{x:-e.scroll.offset.x,y:-e.scroll.offset.y}),gi(e.latestValues)&&Li(n,e.latestValues)}return gi(this.latestValues)&&Li(n,this.latestValues),n}removeTransform(e){const t={x:{min:0,max:0},y:{min:0,max:0}};Zi(t,e);for(let n=0;n<this.path.length;n++){const e=this.path[n];if(!e.instance)continue;if(!gi(e.latestValues))continue;mi(e.latestValues)&&e.updateSnapshot();const r={x:{min:0,max:0},y:{min:0,max:0}};Zi(r,e.measurePageBox()),ro(t,e.latestValues,e.snapshot?e.snapshot.layoutBox:void 0,r)}return gi(this.latestValues)&&ro(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==pt.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t;const n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);const r=Boolean(this.resumingFrom)||this!==n;if(!(e||r&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;const{layout:i,layoutId:o}=this.options;if(this.layout&&(i||o)){if(this.resolvedRelativeTargetAt=pt.timestamp,!this.targetDelta&&!this.relativeTarget){const e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},ai(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),Zi(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var a,s,l;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),a=this.target,s=this.relativeTarget,l=this.relativeParent.target,ii(a.x,s.x,l.x),ii(a.y,s.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):Zi(this.target,this.layout.layoutBox),ki(this.target,this.targetDelta)):Zi(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const e=this.getClosestProjectingParent();e&&Boolean(e.resumingFrom)===Boolean(this.resumingFrom)&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},ai(this.relativeTargetOrigin,this.target,e.target),Zi(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}mo.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!mi(this.parent.latestValues)&&!yi(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;const t=this.getLead(),n=Boolean(this.resumingFrom)||this!==t;let r=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(r=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===pt.timestamp&&(r=!1),r)return;const{layout:i,layoutId:o}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!i&&!o)return;Zi(this.layoutCorrected,this.layout.layoutBox);const a=this.treeScale.x,s=this.treeScale.y;!function(e,t,n,r=!1){const i=n.length;if(!i)return;let o,a;t.x=t.y=1;for(let s=0;s<i;s++){o=n[s],a=o.projectionDelta;const i=o.instance;i&&i.style&&"contents"===i.style.display||(r&&o.options.layoutScroll&&o.scroll&&o!==o.root&&Li(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),a&&(t.x*=a.x.scale,t.y*=a.y.scale,ki(e,a)),r&&gi(o.latestValues)&&Li(e,o.latestValues))}t.x=Si(t.x),t.y=Si(t.y)}(this.layoutCorrected,this.treeScale,this.path,n),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox);const{target:l}=t;if(!l)return void(this.projectionTransform&&(this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionTransform="none",this.scheduleRender()));this.projectionDelta||(this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}});const u=this.projectionTransform;ri(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=uo(this.projectionDelta,this.treeScale),this.projectionTransform===u&&this.treeScale.x===a&&this.treeScale.y===s||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),mo.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),e){const e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(e,t=!1){const n=this.snapshot,r=n?n.latestValues:{},i={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;const a={x:{min:0,max:0},y:{min:0,max:0}},s=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,c=Boolean(s&&!u&&!0===this.options.crossfade&&!this.path.some(Ao));let d;this.animationProgress=0,this.mixTargetDelta=t=>{const n=t/1e3;var l,f,p,h;Ro(o.x,e.x,n),Ro(o.y,e.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(ai(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),l=this.relativeTarget,f=this.relativeTargetOrigin,p=a,h=n,Do(l.x,f.x,p.x,h),Do(l.y,f.y,p.y,h),d&&ao(this.relativeTarget,d)&&(this.isProjectionDirty=!1),d||(d={x:{min:0,max:0},y:{min:0,max:0}}),Zi(d,this.relativeTarget)),s&&(this.animationValues=i,function(e,t,n,r,i,o){i?(e.opacity=yn(0,void 0!==n.opacity?n.opacity:1,qi(r)),e.opacityExit=yn(void 0!==t.opacity?t.opacity:1,0,Xi(r))):o&&(e.opacity=yn(void 0!==t.opacity?t.opacity:1,void 0!==n.opacity?n.opacity:1,r));for(let a=0;a<Wi;a++){const i=`border${$i[a]}Radius`;let o=Yi(t,i),s=Yi(n,i);void 0===o&&void 0===s||(o||(o=0),s||(s=0),0===o||0===s||Qi(o)===Qi(s)?(e[i]=Math.max(yn(Hi(o),Hi(s),r),0),(ye.test(s)||ye.test(o))&&(e[i]+="%")):e[i]=s)}(t.rotate||n.rotate)&&(e.rotate=yn(t.rotate||0,n.rotate||0,r))}(i,r,this.latestValues,n,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(wt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=xt.update((()=>{zi.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,n){const r=J(e)?e:Cr(e);return r.start(xr("",r,t,n)),r.animation}(0,1e3,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0}))}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const e=this.getLead();let{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&No(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const t=ei(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;const r=ei(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}Zi(t,n),Li(t,i),ri(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new lo);this.sharedNodes.get(e).add(t);const n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){const e=this.getStack();return!e||e.lead===this}getLead(){var e;const{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;const{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){const{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){const r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){const e=this.getStack();return!!e&&e.relegate(this)}resetRotation(){const{visualElement:e}=this.options;if(!e)return;let t=!1;const{latestValues:n}=e;if((n.rotate||n.rotateX||n.rotateY||n.rotateZ)&&(t=!0),!t)return;const r={};for(let i=0;i<po.length;i++){const t="rotate"+po[i];n[t]&&(r[t]=n[t],e.setStaticValue(t,0))}e.render();for(const i in r)e.setStaticValue(i,r[i]);e.scheduleRender()}getProjectionStyles(e={}){var t,n;const r={};if(!this.instance||this.isSVG)return r;if(!this.isVisible)return{visibility:"hidden"};r.visibility="";const i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,r.opacity="",r.pointerEvents=Ke(e.pointerEvents)||"",r.transform=i?i(this.latestValues,""):"none",r;const o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){const t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=Ke(e.pointerEvents)||""),this.hasProjected&&!gi(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}const a=o.animationValues||o.latestValues;this.applyTransformsToTarget(),r.transform=uo(this.projectionDeltaWithTransform,this.treeScale,a),i&&(r.transform=i(a,r.transform));const{x:s,y:l}=this.projectionDelta;r.transformOrigin=`${100*s.origin}% ${100*l.origin}% 0`,o.animationValues?r.opacity=o===this?null!==(n=null!==(t=a.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==n?n:1:this.preserveOpacity?this.latestValues.opacity:a.opacityExit:r.opacity=o===this?void 0!==a.opacity?a.opacity:"":void 0!==a.opacityExit?a.opacityExit:0;for(const u in X){if(void 0===a[u])continue;const{correct:e,applyTo:t}=X[u],n="none"===r.transform?a[u]:e(a[u],o);if(t){const e=t.length;for(let i=0;i<e;i++)r[t[i]]=n}else r[u]=n}return this.options.layoutId&&(r.pointerEvents=o===this?Ke(e.pointerEvents)||"":"none"),r}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach((e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()})),this.root.nodes.forEach(ko),this.root.sharedNodes.clear()}}}function yo(e){e.updateLayout()}function vo(e){var t;const n=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:t,measuredBox:r}=e.layout,{animationType:i}=e.options,o=n.source!==e.layout.source;"size"===i?fi((e=>{const r=o?n.measuredBox[e]:n.layoutBox[e],i=ei(r);r.min=t[e].min,r.max=r.min+i})):No(i,n.layoutBox,t)&&fi((r=>{const i=o?n.measuredBox[r]:n.layoutBox[r],a=ei(t[r]);i.max=i.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+a)}));const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};ri(a,t,n.layoutBox);const s={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?ri(s,e.applyTransform(r,!0),n.measuredBox):ri(s,t,n.layoutBox);const l=!oo(a);let u=!1;if(!e.resumeFrom){const r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){const{snapshot:i,layout:o}=r;if(i&&o){const a={x:{min:0,max:0},y:{min:0,max:0}};ai(a,n.layoutBox,i.layoutBox);const s={x:{min:0,max:0},y:{min:0,max:0}};ai(s,t,o.layoutBox),ao(a,s)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=a,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:n,delta:s,layoutDelta:a,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(e.isLead()){const{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function bo(e){mo.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=Boolean(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function xo(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function wo(e){e.clearSnapshot()}function ko(e){e.clearMeasurements()}function So(e){e.isLayoutDirty=!1}function Po(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Eo(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Co(e){e.resolveTargetDelta()}function To(e){e.calcProjection()}function Lo(e){e.resetRotation()}function Mo(e){e.removeLeadSnapshot()}function Ro(e,t,n){e.translate=yn(t.translate,0,n),e.scale=yn(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Do(e,t,n,r){e.min=yn(t.min,n.min,r),e.max=yn(t.max,n.max,r)}function Ao(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}const _o={duration:.45,ease:[.4,0,.1,1]};function Vo(e){e.min=Math.round(e.min),e.max=Math.round(e.max)}function No(e,t,n){return"position"===e||"preserve-aspect"===e&&!ti(so(t),so(n),.2)}const zo=go({attachResizeListener:(e,t)=>tt(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Fo={current:void 0},Oo=go({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Fo.current){const e=new zo({});e.mount(window),e.setOptions({layoutScroll:!0}),Fo.current=e}return Fo.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>Boolean("fixed"===window.getComputedStyle(e).position)}),Io={pan:{Feature:class extends ft{constructor(){super(...arguments),this.removePointerDownListener=Pt}onPointerDown(e){this.session=new Yr(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint()})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:_i(e),onStart:_i(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&xt.update((()=>r(e,t)))}}}mount(){this.removePointerDownListener=it(this.node.current,"pointerdown",(e=>this.onPointerDown(e)))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends ft{constructor(e){super(e),this.removeGroupControls=Pt,this.removeListeners=Pt,this.controls=new Di(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Pt}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:Oo,MeasureLayout:Bi}},jo=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function Bo(e,t,n=1){const[r,i]=function(e){const t=jo.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);return o?o.trim():ie(i)?Bo(i,t,n+1):i}const Uo=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),$o=e=>Uo.has(e),Wo=e=>e===se||e===ve,Ho=(e,t)=>parseFloat(e.split(", ")[t]),Qo=(e,t)=>(n,{transform:r})=>{if("none"===r||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/);if(i)return Ho(i[1],t);{const t=r.match(/^matrix\((.+)\)$/);return t?Ho(t[1],e):0}},Yo=new Set(["x","y","z"]),qo=K.filter((e=>!Yo.has(e)));const Xo={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:Qo(4,13),y:Qo(5,14)},Ko=(e,t,n={},r={})=>{t={...t},r={...r};const i=Object.keys(t).filter($o);let o=[],a=!1;const s=[];if(i.forEach((i=>{const l=e.getValue(i);if(!e.hasValue(i))return;let u=n[i],c=Mr(u);const d=t[i];let f;if(Xe(d)){const e=d.length,t=null===d[0]?1:0;u=d[t],c=Mr(u);for(let n=t;n<e&&null!==d[n];n++)f?zt(Mr(d[n])===f):f=Mr(d[n])}else f=Mr(d);if(c!==f)if(Wo(c)&&Wo(f)){const e=l.get();"string"==typeof e&&l.set(parseFloat(e)),"string"==typeof d?t[i]=parseFloat(d):Array.isArray(d)&&f===ve&&(t[i]=d.map(parseFloat))}else(null==c?void 0:c.transform)&&(null==f?void 0:f.transform)&&(0===u||0===d)?0===u?l.set(f.transform(u)):t[i]=c.transform(d):(a||(o=function(e){const t=[];return qo.forEach((n=>{const r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))})),t.length&&e.render(),t}(e),a=!0),s.push(i),r[i]=void 0!==r[i]?r[i]:t[i],l.jump(d))})),s.length){const n=s.indexOf("height")>=0?window.pageYOffset:null,i=((e,t,n)=>{const r=t.measureViewportBox(),i=t.current,o=getComputedStyle(i),{display:a}=o,s={};"none"===a&&t.setStaticValue("display",e.display||"block"),n.forEach((e=>{s[e]=Xo[e](r,o)})),t.render();const l=t.measureViewportBox();return n.forEach((n=>{const r=t.getValue(n);r&&r.jump(s[n]),e[n]=Xo[n](l,o)})),e})(t,e,s);return o.length&&o.forEach((([t,n])=>{e.getValue(t).set(n)})),e.render(),T&&null!==n&&window.scrollTo({top:n}),{target:i,transitionEnd:r}}return{target:t,transitionEnd:r}};function Go(e,t,n,r){return(e=>Object.keys(e).some($o))(t)?Ko(e,t,n,r):{target:t,transitionEnd:r}}const Zo=(e,t,n,r)=>{const i=function(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach((e=>{const t=e.get();if(!ie(t))return;const n=Bo(t,r);n&&e.set(n)}));for(const i in t){const e=t[i];if(!ie(e))continue;const o=Bo(e,r);o&&(t[i]=o,n||(n={}),void 0===n[i]&&(n[i]=e))}return{target:t,transitionEnd:n}}(e,t,r);return Go(e,t=i.target,n,r=i.transitionEnd)},Jo={current:null},ea={current:!1};const ta=new WeakMap,na=Object.keys(j),ra=na.length,ia=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],oa=V.length;class aa{constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:r,visualState:i},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>xt.render(this.render,!1,!0);const{latestValues:a,renderState:s}=i;this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=s,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=o,this.isControllingVariants=N(t),this.isVariantNode=z(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(e&&e.current);const{willChange:l,...u}=this.scrapeMotionValuesFromProps(t,{});for(const c in u){const e=u[c];void 0!==a[c]&&J(e)&&(e.set(a[c],!1),wr(l)&&l.add(c))}}scrapeMotionValuesFromProps(e,t){return{}}mount(e){this.current=e,ta.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(((e,t)=>this.bindToMotionValue(t,e))),ea.current||function(){if(ea.current=!0,T)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Jo.current=e.matches;e.addListener(t),t()}else Jo.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Jo.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){ta.delete(this.current),this.projection&&this.projection.unmount(),wt(this.notifyUpdate),wt(this.render),this.valueSubscriptions.forEach((e=>e())),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,t){const n=G.has(e),r=t.on("change",(t=>{this.latestValues[e]=t,this.props.onUpdate&&xt.update(this.notifyUpdate,!1,!0),n&&this.projection&&(this.projection.isTransformDirty=!0)})),i=t.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,(()=>{r(),i()}))}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}loadFeatures({children:e,...t},n,r,i){let o,a;for(let s=0;s<ra;s++){const e=na[s],{isEnabled:n,Feature:r,ProjectionNode:i,MeasureLayout:l}=j[e];i&&(o=i),n(t)&&(!this.features[e]&&r&&(this.features[e]=new r(this)),l&&(a=l))}if(!this.projection&&o){this.projection=new o(this.latestValues,this.parent&&this.parent.projection);const{layoutId:e,layout:n,drag:r,dragConstraints:a,layoutScroll:s,layoutRoot:l}=t;this.projection.setOptions({layoutId:e,layout:n,alwaysMeasureLayout:Boolean(r)||a&&R(a),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof n?n:"both",initialPromotionConfig:i,layoutScroll:s,layoutRoot:l})}return a}updateFeatures(){for(const e in this.features){const t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}makeTargetAnimatable(e,t=!0){return this.makeTargetAnimatableFromInstance(e,this.props,t)}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let n=0;n<ia.length;n++){const t=ia[n];this.propEventSubscriptions[t]&&(this.propEventSubscriptions[t](),delete this.propEventSubscriptions[t]);const r=e["on"+t];r&&(this.propEventSubscriptions[t]=this.on(t,r))}this.prevMotionValues=function(e,t,n){const{willChange:r}=t;for(const i in t){const o=t[i],a=n[i];if(J(o))e.addValue(i,o),wr(r)&&r.add(i);else if(J(a))e.addValue(i,Cr(o,{owner:e})),wr(r)&&r.remove(i);else if(a!==o)if(e.hasValue(i)){const t=e.getValue(i);!t.hasAnimated&&t.set(o)}else{const t=e.getStaticValue(i);e.addValue(i,Cr(void 0!==t?t:o,{owner:e}))}}for(const i in n)void 0===t[i]&&e.removeValue(i);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const e=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(e.initial=this.props.initial),e}const t={};for(let n=0;n<oa;n++){const e=V[n],r=this.props[e];(D(r)||!1===r)&&(t[e]=r)}return t}addVariantChild(e){const t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){t!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,t)),this.values.set(e,t),this.latestValues[e]=t.get()}removeValue(e){this.values.delete(e);const t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=Cr(t,{owner:this}),this.addValue(e,n)),n}readValue(e){return void 0===this.latestValues[e]&&this.current?this.readValueFromInstance(this.current,e,this.options):this.latestValues[e]}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;const{initial:n}=this.props,r="string"==typeof n||"object"==typeof n?null===(t=Ye(this.props,n))||void 0===t?void 0:t[e]:void 0;if(n&&void 0!==r)return r;const i=this.getBaseTargetFromProps(this.props,e);return void 0===i||J(i)?void 0!==this.initialValues[e]&&void 0===r?void 0:this.baseTarget[e]:i}on(e,t){return this.events[e]||(this.events[e]=new Pr),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class sa extends aa{sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:t,...n},{transformValues:r},i){let o=function(e,t,n){const r={};for(const i in e){const e=_r(i,t);if(void 0!==e)r[i]=e;else{const e=n.getValue(i);e&&(r[i]=e.get())}}return r}(n,e||{},this);if(r&&(t&&(t=r(t)),n&&(n=r(n)),o&&(o=r(o))),i){!function(e,t,n){var r,i;const o=Object.keys(t).filter((t=>!e.hasValue(t))),a=o.length;if(a)for(let s=0;s<a;s++){const a=o[s],l=t[a];let u=null;Array.isArray(l)&&(u=l[0]),null===u&&(u=null!==(i=null!==(r=n[a])&&void 0!==r?r:e.readValue(a))&&void 0!==i?i:t[a]),null!=u&&("string"==typeof u&&(/^\-?\d*\.?\d+$/.test(u)||yr(u))?u=parseFloat(u):!Dr(u)&&Dn.test(l)&&(u=gr(a,l)),e.addValue(a,Cr(u,{owner:e})),void 0===n[a]&&(n[a]=u),null!==u&&e.setBaseTarget(a,u))}}(this,n,o);const e=Zo(this,n,o,t);t=e.transitionEnd,n=e.target}return{transition:e,transitionEnd:t,...n}}}class la extends sa{readValueFromInstance(e,t){if(G.has(t)){const e=mr(t);return e&&e.default||0}{const r=(n=e,window.getComputedStyle(n)),i=(re(t)?r.getPropertyValue(t):r[t])||0;return"string"==typeof i?i.trim():i}var n}measureInstanceViewportBox(e,{transformPagePoint:t}){return Mi(e,t)}build(e,t,n,r){Pe(e,t,n,r.transformTemplate)}scrapeMotionValuesFromProps(e,t){return He(e,t)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;J(e)&&(this.childSubscription=e.on("change",(e=>{this.current&&(this.current.textContent=`${e}`)})))}renderInstance(e,t,n,r){Ue(e,t,n,r)}}class ua extends sa{constructor(){super(...arguments),this.isSVGTag=!1}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(G.has(t)){const e=mr(t);return e&&e.default||0}return t=$e.has(t)?t:Be(t),e.getAttribute(t)}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}scrapeMotionValuesFromProps(e,t){return Qe(e,t)}build(e,t,n,r){ze(e,t,n,this.isSVGTag,r.transformTemplate)}renderInstance(e,t,n,r){We(e,t,0,r)}mount(e){this.isSVGTag=Oe(e.tagName),super.mount(e)}}const ca=(e,t)=>q(e)?new ua(t,{enableHardwareAcceleration:!1}):new la(t,{enableHardwareAcceleration:!0}),da={...Hr,...At,...Io,...{layout:{ProjectionNode:Oo,MeasureLayout:Bi}}},fa=Q(((e,t)=>function(e,{forwardMotionProps:t=!1},n,r){return{...q(e)?Je:et,preloadedFeatures:n,useRender:je(t),createVisualElement:r,Component:e}}(e,t,da,ca)));function pa(){const e=c.useRef(!1);return L((()=>(e.current=!0,()=>{e.current=!1})),[]),e}class ha extends c.Component{getSnapshotBeforeUpdate(e){const t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){const e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function ma({children:e,isPresent:t}){const n=c.useId(),r=c.useRef(null),i=c.useRef({width:0,height:0,top:0,left:0});return c.useInsertionEffect((()=>{const{width:e,height:o,top:a,left:s}=i.current;if(t||!r.current||!e||!o)return;r.current.dataset.motionPopId=n;const l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule(`\n          [data-motion-pop-id="${n}"] {\n            position: absolute !important;\n            width: ${e}px !important;\n            height: ${o}px !important;\n            top: ${a}px !important;\n            left: ${s}px !important;\n          }\n        `),()=>{document.head.removeChild(l)}}),[t]),c.createElement(ha,{isPresent:t,childRef:r,sizeRef:i},c.cloneElement(e,{ref:r}))}const ga=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:i,presenceAffectsLayout:o,mode:a})=>{const s=qe(ya),l=c.useId(),u=c.useMemo((()=>({id:l,initial:t,isPresent:n,custom:i,onExitComplete:e=>{s.set(e,!0);for(const t of s.values())if(!t)return;r&&r()},register:e=>(s.set(e,!1),()=>s.delete(e))})),o?void 0:[n]);return c.useMemo((()=>{s.forEach(((e,t)=>s.set(t,!1)))}),[n]),c.useEffect((()=>{!n&&!s.size&&r&&r()}),[n]),"popLayout"===a&&(e=c.createElement(ma,{isPresent:n},e)),c.createElement(C.Provider,{value:u},e)};function ya(){return new Map}const va=e=>e.key||"";const ba=({children:e,custom:t,initial:n=!0,onExitComplete:r,exitBeforeEnter:i,presenceAffectsLayout:o=!0,mode:a="sync"})=>{const s=c.useContext(B).forceRender||function(){const e=pa(),[t,n]=c.useState(0),r=c.useCallback((()=>{e.current&&n(t+1)}),[t]);return[c.useCallback((()=>xt.postRender(r)),[r]),t]}()[0],l=pa(),u=function(e){const t=[];return c.Children.forEach(e,(e=>{c.isValidElement(e)&&t.push(e)})),t}(e);let d=u;const f=c.useRef(new Map).current,p=c.useRef(d),h=c.useRef(new Map).current,m=c.useRef(!0);var g;if(L((()=>{m.current=!1,function(e,t){e.forEach((e=>{const n=va(e);t.set(n,e)}))}(u,h),p.current=d})),g=()=>{m.current=!0,h.clear(),f.clear()},c.useEffect((()=>()=>g()),[]),m.current)return c.createElement(c.Fragment,null,d.map((e=>c.createElement(ga,{key:va(e),isPresent:!0,initial:!!n&&void 0,presenceAffectsLayout:o,mode:a},e))));d=[...d];const y=p.current.map(va),v=u.map(va),b=y.length;for(let c=0;c<b;c++){const e=y[c];-1!==v.indexOf(e)||f.has(e)||f.set(e,void 0)}return"wait"===a&&f.size&&(d=[]),f.forEach(((e,n)=>{if(-1!==v.indexOf(n))return;const i=h.get(n);if(!i)return;const m=y.indexOf(n);let g=e;if(!g){const e=()=>{h.delete(n),f.delete(n);const e=p.current.findIndex((e=>e.key===n));if(p.current.splice(e,1),!f.size){if(p.current=u,!1===l.current)return;s(),r&&r()}};g=c.createElement(ga,{key:va(i),isPresent:!1,onExitComplete:e,custom:t,presenceAffectsLayout:o,mode:a},i),f.set(n,g)}d.splice(m,0,g)})),d=d.map((e=>{const t=e.key;return f.has(t)?e:c.createElement(ga,{key:va(e),isPresent:!0,presenceAffectsLayout:o,mode:a},e)})),c.createElement(c.Fragment,null,f.size?d:d.map((e=>c.cloneElement(e))))};export{ba as A,f as R,S as a,c as b,t as c,d,Vi as e,r as f,n as g,fa as m,u as r,Ni as u};
