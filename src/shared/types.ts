import { type Job } from "wasp/entities";

export type JobPayload = Pick<Job, 'title' | 'company' | 'location' | 'description'>;

export interface Resume {
  id: string;
  userId: number;
  title: string;
  templateId?: string;
  personalInfo: {
    fullName: string;
    email: string;
    phone: string;
    location: string;
    linkedIn?: string;
    website?: string;
  };
  summary: string;
  experience: Array<{
    id: string;
    company: string;
    position: string;
    startDate: string;
    endDate: string;
    current: boolean;
    description: string;
    achievements: string[];
  }>;
  education: Array<{
    id: string;
    institution: string;
    degree: string;
    field: string;
    startDate: string;
    endDate: string;
    current: boolean;
    gpa?: string;
  }>;
  skills: string[];
  certifications?: Array<{
    id: string;
    name: string;
    issuer: string;
    date: string;
  }>;
  fileData?: {
    name: string;
    type: string;
    data: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface ResumeTemplate {
  id: string;
  name: string;
  description: string;
  category: 'professional' | 'creative' | 'modern' | 'classic';
  preview: string; // URL or base64 image
  sampleData: {
    personalInfo: {
      fullName: string;
      email: string;
      phone: string;
      location: string;
      linkedIn?: string;
      website?: string;
    };
    summary: string;
    experience: Array<{
      id: string;
      company: string;
      position: string;
      startDate: string;
      endDate: string;
      current: boolean;
      description: string;
      achievements: string[];
    }>;
    education: Array<{
      id: string;
      institution: string;
      degree: string;
      field: string;
      startDate: string;
      endDate: string;
      current: boolean;
      gpa?: string;
    }>;
    skills: string[];
    certifications?: Array<{
      id: string;
      name: string;
      issuer: string;
      date: string;
    }>;
  };
  styles: {
    // Colors
    primaryColor: string;
    secondaryColor?: string;
    accentColor?: string;
    textColor?: string;
    backgroundColor?: string;

    // Typography
    fontFamily: string;
    headerFontFamily?: string;
    fontSize?: string;
    lineHeight?: string;
    fontWeight?: string;
    headerFontWeight?: string;

    // Layout
    layout: 'single-column' | 'two-column' | 'sidebar';
    spacing?: string;
    sectionSpacing?: string;
    marginTop?: string;
    marginBottom?: string;
    padding?: string;

    // Visual Elements
    borderStyle?: string;
    borderWidth?: string;
    borderRadius?: string;
    shadowStyle?: string;

    // Section Styling
    headerStyle?: 'underline' | 'background' | 'border' | 'minimal';
    skillsStyle?: 'tags' | 'list' | 'grid' | 'inline';
    dateStyle?: 'right' | 'left' | 'inline' | 'separate-line';

    // Advanced Styling
    customCSS?: string;
  };
}