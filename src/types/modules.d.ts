// Manual module declarations for packages that have issues with bundler module resolution

declare module '@lucia-auth/adapter-prisma' {
  import type { Adapter } from 'lucia';
  
  export class PrismaAdapter implements Adapter {
    constructor(sessionModel: any, userModel: any);
    deleteSession(sessionId: string): Promise<void>;
    deleteUserSessions(userId: string): Promise<void>;
    getSessionAndUser(sessionId: string): Promise<[any, any]>;
    getUserSessions(userId: string): Promise<any[]>;
    setSession(value: any): Promise<void>;
    updateSession(sessionId: string, partialValue: any): Promise<void>;
    updateSessionExpiration(sessionId: string, expiresAt: Date): Promise<void>;
    deleteExpiredSessions(): Promise<void>;
  }
}

declare module '@node-rs/argon2' {
  export enum Version {
    V0x10 = 0x10,
    V0x13 = 0x13,
  }
  
  export interface Options {
    memoryCost?: number;
    timeCost?: number;
    outputLen?: number;
    parallelism?: number;
    version?: Version;
  }
  
  export function hash(password: string, options?: Options): Promise<string>;
  export function verify(hashedPassword: string, password: string, options?: Options): Promise<boolean>;
} 