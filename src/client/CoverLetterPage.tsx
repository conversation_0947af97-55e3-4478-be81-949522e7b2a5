import { type CoverLetter } from "wasp/entities";
import { editCoverLetter, useQuery, getCoverLetter } from "wasp/client/operations";
import { Tooltip, Button, Textarea, useClipboard, Spinner, HStack, Alert, AlertIcon, Text, Box } from '@chakra-ui/react';
import { useParams, useNavigate } from 'react-router-dom';
import ContentPageBox from './components/ContentPageBox';
import PageHeader from './components/PageHeader';
import ActionButton from './components/ActionButton';
import ContentContainer from './components/ContentContainer';
import BackButton from './components/BackButton';
import { FaCopy, FaSave } from 'react-icons/fa';
import { useContext } from 'react';
import { TextareaContext } from './App';
import { EditAlert } from './components/AlertDialog';
import { useEffect, useState } from 'react';

export default function CoverLetterPage() {
  const { textareaState, setTextareaState } = useContext(TextareaContext);
  const [editIsLoading, setEditIsLoading] = useState<boolean>(false);
  const [isEdited, setIsEdited] = useState<boolean>(false);
  const navigate = useNavigate();

  const { id } = useParams();
  if (!id) {
    return <ContentPageBox>Error: Cover letter ID is required</ContentPageBox>;
  }

  const {
    data: coverLetter,
    isLoading,
    refetch,
  } = useQuery<{ id: string }, CoverLetter>(getCoverLetter, { id }, { enabled: false });

  const { hasCopied, onCopy } = useClipboard(coverLetter?.content || '');

  // restrains fetching to mount only to avoid re-render issues
  useEffect(() => {
    refetch();
  }, []);

  useEffect(() => {
    if (coverLetter) {
      setTextareaState(coverLetter.content);
    }
  }, [coverLetter]);

  const handleClick = async () => {
    try {
      setEditIsLoading(true);
      if (!id) {
        throw new Error('Cover letter ID is required');
      }

      const editedCoverLetter = await editCoverLetter({ coverLetterId: id, content: textareaState });

      if (!!editedCoverLetter) {
        setIsEdited(true);
        setTimeout(() => {
          setIsEdited(false);
        }, 2500);
      }
    } catch (error) {
      console.error(error);
      alert('An error occured. Please try again.');
    }
    setEditIsLoading(false);
  };

  return (
    <>
      <BackButton />
      <ContentPageBox>
        {isLoading && <Spinner />}

        <PageHeader
          title="Cover Letter"
          subtitle={coverLetter?.title || "Edit your cover letter"}
        >
          {coverLetter && (
            <HStack spacing={3}>
              <Tooltip
                label={isEdited && 'Changes Saved!'}
                placement='top'
                hasArrow
                isOpen={isEdited}
                closeDelay={2500}
                closeOnClick={true}
              >
                <span>
                  <ActionButton
                    icon={FaSave}
                    label="Save"
                    variant="outline"
                    onClick={handleClick}
                    isDisabled={false}
                    isLoading={editIsLoading}
                  />
                </span>
              </Tooltip>
              <Tooltip
                label={hasCopied ? 'Copied!' : 'Copy to Clipboard'}
                placement='top'
                hasArrow
                closeOnClick={false}
              >
                <span>
                  <ActionButton
                    icon={FaCopy}
                    label="Copy"
                    variant="primary"
                    onClick={onCopy}
                  />
                </span>
              </Tooltip>
            </HStack>
          )}
        </PageHeader>

        <ContentContainer delay={0.4}>
          {/* AI Disclaimer */}
          <Alert status="info" borderRadius="md" mb={4} fontSize="sm">
            <AlertIcon />
            <Box>
              <Text fontWeight="medium">AI-Generated Content Notice</Text>
              <Text fontSize="xs" color="gray.600">
                This content was generated by AI and should be reviewed, customized, and verified for accuracy before use.
                Please ensure it aligns with your experience and the specific job requirements.
              </Text>
            </Box>
          </Alert>

          <Textarea
            onChange={(e) => {
              setTextareaState(e.target.value);
            }}
            value={textareaState}
            id='cover-letter-textarea'
            height={['sm', 'lg', 'xl']}
            width="100%"
            overflow='none'
            visibility={isLoading ? 'hidden' : 'visible'}
            bg="transparent"
            border="none"
            _focus={{ boxShadow: "none" }}
            resize="none"
          />
        </ContentContainer>
      </ContentPageBox>
      <EditAlert coverLetter={!!coverLetter} />
    </>
  );
}
