import React, { useEffect, useState } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  VStack,
  HStack,
  SimpleGrid,
  Card,
  CardBody,
  Icon,
  Link,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Badge,
  Button,
  useColorModeValue,
  Divider,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  List,
  ListItem,
  ListIcon,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Code,
  UnorderedList,
} from '@chakra-ui/react';
import { Link as RouterLink } from 'react-router-dom';
import {
  FaQuestionCircle,
  FaRocket,
  FaFileAlt,
  FaEnvelopeOpenText,
  FaComments,
  FaBriefcase,
  FaGraduationCap,
  FaUser,
  FaShieldAlt,
  FaExclamationTriangle,
  FaEnvelope,
  FaBook,
  FaVideo,
  FaDownload,
  FaEdit,
  FaSearch,
  FaLinkedin,
  FaCheckCircle,
  FaArrowRight,
  FaPlay,
  FaUpload,
  FaCog,
  FaCreditCard,
  FaUserEdit,
} from 'react-icons/fa';

const HelpPage: React.FC = () => {
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'gray.200');
  const linkColor = useColorModeValue('blue.600', 'blue.400');
  const [selectedTopic, setSelectedTopic] = useState<string | null>(null);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const helpCategories = [
    {
      id: 'getting-started',
      icon: FaRocket,
      title: 'Getting Started',
      description: 'Learn the basics of CareerDart and how to set up your account',
      color: 'blue.500',
    },
    {
      id: 'resume-builder',
      icon: FaFileAlt,
      title: 'Resume Builder',
      description: 'Create professional resumes with our AI-powered tools',
      color: 'green.500',
    },
    {
      id: 'cover-letters',
      icon: FaEnvelopeOpenText,
      title: 'Cover Letters',
      description: 'Generate personalized cover letters for any job application',
      color: 'purple.500',
    },
    {
      id: 'interview-prep',
      icon: FaComments,
      title: 'Interview Prep',
      description: 'Practice with AI-generated questions and improve your skills',
      color: 'orange.500',
    },
    {
      id: 'job-tracking',
      icon: FaBriefcase,
      title: 'Job Tracking',
      description: 'Organize and manage your job applications effectively',
      color: 'teal.500',
    },
    {
      id: 'account-billing',
      icon: FaUser,
      title: 'Account & Billing',
      description: 'Manage your profile, subscription, and payment settings',
      color: 'pink.500',
    },
  ];

  const helpTopicContent = {
    'getting-started': {
      title: 'Getting Started with CareerDart',
      sections: [
        {
          title: 'Account Setup',
          icon: FaUserEdit,
          content: [
            'Sign up with your email or Google account',
            'Complete your profile with basic information',
            'Add your contact details and location',
            'Set your career preferences and goals',
          ]
        },
        {
          title: 'First Steps',
          icon: FaPlay,
          content: [
            'Navigate to the Resume section to create your first resume',
            'Upload an existing resume or start from scratch',
            'Use the AI assistant to enhance your content',
            'Save and organize your documents',
          ]
        },
        {
          title: 'Platform Overview',
          icon: FaBook,
          content: [
            'Dashboard: Overview of all your career documents',
            'Resume Builder: Create and edit professional resumes',
            'Cover Letters: Generate tailored cover letters',
            'Interview Prep: Practice with AI-generated questions',
            'Job Tracker: Manage your job applications',
          ]
        },
      ]
    },
    'resume-builder': {
      title: 'Resume Builder Guide',
      sections: [
        {
          title: 'Creating Your Resume',
          icon: FaEdit,
          content: [
            'Choose from professional templates',
            'Add sections: Experience, Education, Skills, etc.',
            'Use AI suggestions to improve your content',
            'Customize colors, fonts, and layout',
          ]
        },
        {
          title: 'AI Enhancement Features',
          icon: FaRocket,
          content: [
            'AI-powered content suggestions',
            'Skill extraction from job descriptions',
            'Achievement optimization',
            'ATS-friendly formatting',
          ]
        },
        {
          title: 'Best Practices',
          icon: FaCheckCircle,
          content: [
            'Keep it concise (1-2 pages maximum)',
            'Use action verbs and quantify achievements',
            'Tailor your resume for each job application',
            'Proofread and spell-check thoroughly',
          ]
        },
        {
          title: 'Exporting & Sharing',
          icon: FaDownload,
          content: [
            'Export as PDF for job applications',
            'Multiple format options available',
            'Share link for online viewing',
            'Print-optimized layouts',
          ]
        },
      ]
    },
    'cover-letters': {
      title: 'Cover Letter Generator',
      sections: [
        {
          title: 'Creating Cover Letters',
          icon: FaEnvelopeOpenText,
          content: [
            'Import job details from LinkedIn or manual entry',
            'AI analyzes job requirements automatically',
            'Generates personalized content based on your resume',
            'Customize tone and style preferences',
          ]
        },
        {
          title: 'AI Personalization',
          icon: FaRocket,
          content: [
            'Matches your skills to job requirements',
            'Highlights relevant experience',
            'Adapts writing style to company culture',
            'Includes industry-specific keywords',
          ]
        },
        {
          title: 'Editing & Refinement',
          icon: FaEdit,
          content: [
            'Review and edit AI-generated content',
            'Add personal touches and specific examples',
            'Adjust length and formatting',
            'Multiple revision options available',
          ]
        },
      ]
    },
    'interview-prep': {
      title: 'Interview Preparation',
      sections: [
        {
          title: 'Practice Sessions',
          icon: FaComments,
          content: [
            'AI generates job-specific interview questions',
            'Practice common and behavioral questions',
            'Industry-specific question sets',
            'Mock interview simulations',
          ]
        },
        {
          title: 'Question Types',
          icon: FaQuestionCircle,
          content: [
            'Behavioral questions (STAR method)',
            'Technical questions for your field',
            'Company culture fit questions',
            'Situational and problem-solving scenarios',
          ]
        },
        {
          title: 'Answer Development',
          icon: FaBook,
          content: [
            'Structure your answers effectively',
            'Use examples from your experience',
            'Practice concise and impactful responses',
            'Get AI feedback on your answers',
          ]
        },
      ]
    },
    'job-tracking': {
      title: 'Job Application Tracking',
      sections: [
        {
          title: 'Application Management',
          icon: FaBriefcase,
          content: [
            'Track all your job applications in one place',
            'Monitor application status and progress',
            'Set reminders for follow-ups',
            'Organize applications by status and priority',
          ]
        },
        {
          title: 'Job Discovery',
          icon: FaSearch,
          content: [
            'Browse recommended jobs based on your profile',
            'Import jobs from LinkedIn and other platforms',
            'Save interesting positions for later',
            'Get notified about new matching opportunities',
          ]
        },
        {
          title: 'Analytics & Insights',
          icon: FaCheckCircle,
          content: [
            'Track your application success rate',
            'Analyze response times and patterns',
            'Identify areas for improvement',
            'Export data for personal analysis',
          ]
        },
      ]
    },
    'account-billing': {
      title: 'Account & Billing Management',
      sections: [
        {
          title: 'Profile Settings',
          icon: FaUser,
          content: [
            'Update your personal information',
            'Manage notification preferences',
            'Set privacy and sharing options',
            'Configure AI assistance preferences',
          ]
        },
        {
          title: 'Subscription Management',
          icon: FaCreditCard,
          content: [
            'View your current plan and usage',
            'Upgrade or downgrade your subscription',
            'Manage payment methods',
            'View billing history and invoices',
          ]
        },
        {
          title: 'Security & Privacy',
          icon: FaShieldAlt,
          content: [
            'Two-factor authentication setup',
            'Password management and changes',
            'Data export and deletion options',
            'Privacy controls and permissions',
          ]
        },
      ]
    },
  };

  const faqs = [
    {
      question: 'How accurate is the AI-generated content?',
      answer: 'Our AI generates high-quality content based on best practices and your input. However, all AI-generated content should be reviewed, customized, and verified for accuracy before use. We recommend having important documents reviewed by career professionals.',
    },
    {
      question: 'Can I customize the resume templates?',
      answer: 'Yes! Our resume builder offers full customization including colors, fonts, layouts, and styling. You can also add, remove, or modify sections to match your needs.',
    },
    {
      question: 'How does the LinkedIn integration work?',
      answer: 'Simply paste a LinkedIn job URL into our import tool, and we\'ll automatically extract the job title, company, location, and description to populate your application form.',
    },
    {
      question: 'Is my data secure and private?',
      answer: 'Absolutely. We use enterprise-grade security measures to protect your data. Your personal information is encrypted and never shared with third parties without your consent. See our Privacy Policy for details.',
    },
    {
      question: 'Can I export my resumes and cover letters?',
      answer: 'Yes, you can export your documents as PDF files optimized for both digital applications and printing. Multiple format options are available.',
    },
    {
      question: 'How do I cancel my subscription?',
      answer: 'You can cancel your subscription anytime from your Profile page under the Billing section. Your access will continue until the end of your current billing period.',
    },
  ];

  const renderTopicContent = (topicId: string) => {
    const topic = helpTopicContent[topicId as keyof typeof helpTopicContent];
    if (!topic) return null;

    return (
      <Box>
        <VStack spacing={6} align="stretch">
          <Heading as="h3" size="lg" color={textColor} mb={4}>
            {topic.title}
          </Heading>
          
          {topic.sections.map((section, index) => (
            <Card key={index} bg={cardBg} borderLeft="4px solid" borderLeftColor={helpCategories.find(cat => cat.id === topicId)?.color}>
              <CardBody>
                <VStack spacing={4} align="stretch">
                  <HStack>
                    <Icon as={section.icon} color={helpCategories.find(cat => cat.id === topicId)?.color} boxSize={5} />
                    <Heading as="h4" size="md" color={textColor}>
                      {section.title}
                    </Heading>
                  </HStack>
                  <UnorderedList spacing={2} pl={6}>
                    {section.content.map((item, itemIndex) => (
                      <ListItem key={itemIndex} color="gray.600" fontSize="sm">
                        {item}
                      </ListItem>
                    ))}
                  </UnorderedList>
                </VStack>
              </CardBody>
            </Card>
          ))}
          
          <Button
            onClick={() => setSelectedTopic(null)}
            variant="outline"
            colorScheme="blue"
            alignSelf="flex-start"
            leftIcon={<FaArrowRight style={{ transform: 'rotate(180deg)' }} />}
          >
            Back to Help Topics
          </Button>
        </VStack>
      </Box>
    );
  };

  return (
    <Box minH="100vh" bg={bgColor}>
      <Container maxW="95%" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Header */}
          <VStack spacing={4} textAlign="center">
            <Icon as={FaQuestionCircle} boxSize={12} color={linkColor} />
            <Heading as="h1" size="2xl" color={textColor}>
              Help Center
            </Heading>
            <Text fontSize="xl" color="gray.500" maxW="2xl">
              Find answers to common questions and learn how to make the most of CareerDart's AI-powered career tools.
            </Text>
          </VStack>

          {/* AI Disclaimer Alert */}
          <Alert status="info" borderRadius="lg" bg={useColorModeValue('gray.100', 'gray.700')} borderColor={useColorModeValue('gray.300', 'gray.600')}>
            <AlertIcon color={useColorModeValue('gray.600', 'gray.400')} />
            <Box>
              <AlertTitle fontSize="sm" color={useColorModeValue('gray.800', 'gray.200')}>Important: AI-Generated Content Disclaimer</AlertTitle>
              <AlertDescription fontSize="xs" color={useColorModeValue('gray.700', 'gray.300')}>
                All AI-generated content (resumes, cover letters, interview answers) is for guidance only.
                Please review, customize, and verify accuracy before use. CareerDart does not guarantee
                job placement or interview success.
              </AlertDescription>
            </Box>
          </Alert>

          {/* Help Topics Content */}
          {selectedTopic ? (
            renderTopicContent(selectedTopic)
          ) : (
            <>
              {/* Quick Help Categories */}
              <VStack spacing={6} align="stretch">
                <Heading as="h2" size="lg" color={textColor}>
                  Browse Help Topics
                </Heading>
                <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
                  {helpCategories.map((category, index) => (
                    <Card 
                      key={index} 
                      bg={cardBg} 
                      _hover={{ transform: 'translateY(-2px)', boxShadow: 'lg', cursor: 'pointer' }} 
                      transition="all 0.2s"
                      onClick={() => setSelectedTopic(category.id)}
                    >
                      <CardBody>
                        <VStack spacing={4} align="start">
                          <Icon as={category.icon} boxSize={8} color={category.color} />
                          <VStack spacing={2} align="start">
                            <Heading as="h3" size="md" color={textColor}>
                              {category.title}
                            </Heading>
                            <Text fontSize="sm" color="gray.500">
                              {category.description}
                            </Text>
                          </VStack>
                          <HStack spacing={2} color={category.color}>
                            <Text fontSize="sm" fontWeight="medium">Learn more</Text>
                            <Icon as={FaArrowRight} boxSize={3} />
                          </HStack>
                        </VStack>
                      </CardBody>
                    </Card>
                  ))}
                </SimpleGrid>
              </VStack>

              {/* Quick Start Guide */}
              <VStack spacing={6} align="stretch">
                <Heading as="h2" size="lg" color={textColor}>
                  Quick Start Guide
                </Heading>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={6}>
                  <Card bg={cardBg}>
                    <CardBody>
                      <VStack spacing={4} align="start">
                        <HStack>
                          <Icon as={FaRocket} color="blue.500" />
                          <Heading as="h3" size="md" color={textColor}>
                            New to CareerDart?
                          </Heading>
                        </HStack>
                        <VStack spacing={2} align="start" pl={6}>
                          <Text fontSize="sm">1. Create your account and complete your profile</Text>
                          <Text fontSize="sm">2. Upload or create your first resume</Text>
                          <Text fontSize="sm">3. Generate your first cover letter</Text>
                          <Text fontSize="sm">4. Start tracking job applications</Text>
                        </VStack>
                      </VStack>
                    </CardBody>
                  </Card>

                  <Card bg={cardBg}>
                    <CardBody>
                      <VStack spacing={4} align="start">
                        <HStack>
                          <Icon as={FaBook} color="green.500" />
                          <Heading as="h3" size="md" color={textColor}>
                            Best Practices
                          </Heading>
                        </HStack>
                        <VStack spacing={2} align="start" pl={6}>
                          <Text fontSize="sm">• Always review AI-generated content</Text>
                          <Text fontSize="sm">• Customize templates for each application</Text>
                          <Text fontSize="sm">• Use specific keywords from job descriptions</Text>
                          <Text fontSize="sm">• Keep your profile information updated</Text>
                        </VStack>
                      </VStack>
                    </CardBody>
                  </Card>
                </SimpleGrid>
              </VStack>

              {/* FAQ Section */}
              <VStack spacing={6} align="stretch">
                <Heading as="h2" size="lg" color={textColor}>
                  Frequently Asked Questions
                </Heading>
                <Accordion allowToggle>
                  {faqs.map((faq, index) => (
                    <AccordionItem key={index} border="1px solid" borderColor={useColorModeValue('gray.200', 'gray.700')} borderRadius="lg" mb={2}>
                      <AccordionButton bg={cardBg} _hover={{ bg: useColorModeValue('gray.50', 'gray.700') }} borderRadius="lg">
                        <Box flex="1" textAlign="left">
                          <Text fontWeight="medium" color={textColor}>
                            {faq.question}
                          </Text>
                        </Box>
                        <AccordionIcon />
                      </AccordionButton>
                      <AccordionPanel pb={4} bg={cardBg} borderRadius="lg">
                        <Text color="gray.500">{faq.answer}</Text>
                      </AccordionPanel>
                    </AccordionItem>
                  ))}
                </Accordion>
              </VStack>
            </>
          )}

          {/* Contact Support */}
          <Card bg={cardBg} borderColor={linkColor} borderWidth="2px">
            <CardBody>
              <VStack spacing={4} textAlign="center">
                <Icon as={FaEnvelope} boxSize={8} color={linkColor} />
                <Heading as="h3" size="lg" color={textColor}>
                  Still Need Help?
                </Heading>
                <Text color="gray.500">
                  Can't find what you're looking for? Our support team is here to help.
                </Text>
                <HStack spacing={4}>
                  <Button as="a" href="mailto:<EMAIL>" colorScheme="blue" leftIcon={<FaEnvelope />}>
                    Email Support
                  </Button>
                  <Button as={RouterLink} to="/privacy" variant="outline" colorScheme="blue">
                    Privacy Policy
                  </Button>
                  <Button as={RouterLink} to="/tos" variant="outline" colorScheme="blue">
                    Terms of Service
                  </Button>
                </HStack>
              </VStack>
            </CardBody>
          </Card>
        </VStack>
      </Container>
    </Box>
  );
};

export default HelpPage;
