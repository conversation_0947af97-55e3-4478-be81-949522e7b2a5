import {
  Box,
  VStack,
  Icon,
  Text,
  useColorModeValue,
  Drawer,
  Drawer<PERSON><PERSON>,
  DrawerHeader,
  <PERSON>er<PERSON><PERSON><PERSON>,
  Drawer<PERSON>ontent,
  DrawerCloseButton,
  useBreakpointValue,
  Divider,
  IconButton,
  Flex
} from '@chakra-ui/react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  FaHome,
  FaChartLine,
  FaComments,
  FaGraduationCap,
  FaFileAlt,
  FaEnvelopeOpenText,
  FaBriefcase,
  FaUser,
  FaChevronLeft,
  FaChevronRight,
  FaCog,
  FaShieldAlt
} from 'react-icons/fa';
import { useAuth } from 'wasp/client/auth';
import { useSidebar } from './NavBar';

// Main navigation items
const mainNavItems = [
  { label: 'Dashboard', icon: FaHome, path: '/dashboard' },
];

// Content pages
const contentNavItems = [
  { label: 'Cover Letters', icon: FaEnvelopeOpenText, path: '/cover-letters' },
  { label: 'Resume', icon: FaFileAlt, path: '/resume' },
  { label: 'Jobs', icon: FaBriefcase, path: '/jobs' },
];

// Tools and resources
const toolsNavItems = [
  { label: 'Interview Prep', icon: FaComments, path: '/interview' },
  { label: 'Learning', icon: FaGraduationCap, path: '/learning' },
  { label: 'Tracker', icon: FaChartLine, path: '/tracker' },
];

// User related
const userNavItems = [
  { label: 'Profile', icon: FaUser, path: '/profile' },
];

// Admin navigation items
const adminNavItems = [
  { label: 'Admin Dashboard', icon: FaShieldAlt, path: '/admin' },
];

export default function SidebarNav() {
  const bg = useColorModeValue('white', 'gray.900');
  const hoverBg = useColorModeValue('blue.50', 'gray.700');
  const color = useColorModeValue('gray.700', 'gray.200');
  const borderColor = useColorModeValue('gray.200', 'gray.700');

  const { data: user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Get sidebar state from context
  const { isSidebarOpen, closeSidebar, isCollapsed, toggleCollapse } = useSidebar();

  // Responsive design - Use more specific breakpoints for mobile detection
  const isMobile = useBreakpointValue({ base: true, lg: false });

  // Check if user is admin (temporary check based on email)
  const isAdmin = user?.email?.includes('admin') || user?.email?.endsWith('@admin.careerdart.com');

  const handleNavigation = (path: string) => {
    if (!user && path !== '/') {
      navigate('/login');
    } else {
      navigate(path);
      // Close sidebar on mobile after navigation
      if (isMobile) {
        closeSidebar();
      }
      // Scroll to top of page
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  // Navigation item component
  const NavItem = ({ item }: { item: { label: string; icon: any; path: string } }) => {
    const isActive = location.pathname === item.path;
    const activeBg = useColorModeValue('blue.50', 'blue.900');
    const activeColor = useColorModeValue('blue.600', 'blue.300');

    return (
      <Box
        key={item.label}
        as='button'
        onClick={() => handleNavigation(item.path)}
        display='flex'
        alignItems='center'
        padding='0.5rem'
        borderRadius='0.375rem'
        color={isActive ? activeColor : color}
        bg={isActive ? activeBg : 'transparent'}
        width='100%'
        textAlign='left'
        border='none'
        cursor='pointer'
        _hover={{ bg: isActive ? activeBg : hoverBg }}
        transition='all 0.2s'
        title={isCollapsed ? item.label : undefined}
      >
        <Icon as={item.icon} mr={isCollapsed ? 0 : 3} color='blue.500' boxSize={5} />
        {!isCollapsed && (
          <Text fontWeight={isActive ? 'semibold' : 'medium'}>{item.label}</Text>
        )}
      </Box>
    );
  };

  // Category header component
  const CategoryHeader = ({ title }: { title: string }) => (
    !isCollapsed ? (
      <Flex justify="space-between" align="center" mb={1} mt={3} px={1}>
        <Text
          fontSize="xs"
          fontWeight="medium"
          textTransform="uppercase"
          color="gray.500"
          _dark={{ color: "gray.400" }}
          letterSpacing="wider"
        >
          {title}
        </Text>
        {title === "Main" && (
          <IconButton
            aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
            icon={<FaChevronLeft size="10px" />}
            size="xs"
            onClick={toggleCollapse}
            variant="ghost"
            colorScheme="blue"
            borderRadius="full"
          />
        )}
      </Flex>
    ) : (
      <Divider my={2} />
    )
  );

  // Sidebar content component to reuse in both desktop and mobile views
  const SidebarContent = () => (
    <VStack align='stretch' spacing={2} mt={2}>
      {/* Expand button when collapsed */}
      {isCollapsed && (
        <Flex justify="center" mb={3}>
          <IconButton
            aria-label="Expand sidebar"
            icon={<FaChevronRight size="12px" />}
            size="sm"
            onClick={toggleCollapse}
            variant="ghost"
            colorScheme="blue"
            borderRadius="full"
          />
        </Flex>
      )}

      {/* Main Navigation */}
      <CategoryHeader title="Main" />
      {mainNavItems.map(item => (
        <NavItem key={item.label} item={item} />
      ))}

      {/* Content Pages */}
      <Divider my={2} />
      <CategoryHeader title="Content" />
      {contentNavItems.map(item => (
        <NavItem key={item.label} item={item} />
      ))}

      {/* Tools */}
      <Divider my={2} />
      <CategoryHeader title="Tools & Resources" />
      {toolsNavItems.map(item => (
        <NavItem key={item.label} item={item} />
      ))}

      {/* User */}
      {user && (
        <>
          <Divider my={2} />
          <CategoryHeader title="User" />
          {userNavItems.map(item => (
            <NavItem key={item.label} item={item} />
          ))}
        </>
      )}

      {/* Admin Section */}
      {user && isAdmin && (
        <>
          <Divider my={2} />
          <CategoryHeader title="Admin" />
          {adminNavItems.map(item => (
            <NavItem key={item.label} item={item} />
          ))}
        </>
      )}
    </VStack>
  );

  // For mobile: render a drawer
  if (isMobile) {
    return (
      <Drawer
        isOpen={isSidebarOpen}
        placement="left"
        onClose={closeSidebar}
        size="xs"
      >
        <DrawerOverlay />
        <DrawerContent bg={bg} zIndex={4}>
          <DrawerCloseButton />
          <DrawerHeader
            borderBottomWidth="1px"
            borderColor={borderColor}
            py={3}
            px={4}
            fontSize="md"
            fontWeight="semibold"
            color="blue.600"
            _dark={{ color: "blue.300" }}
          >
            Menu
          </DrawerHeader>
          <DrawerBody px={3} py={2}>
            <SidebarContent />
          </DrawerBody>
        </DrawerContent>
      </Drawer>
    );
  }

  // For desktop: render a fixed sidebar
  return (
    <Box
      as='nav'
      w={isCollapsed ? '60px' : '220px'}
      bg={bg}
      h='100vh'
      p={isCollapsed ? 2 : 4}
      boxShadow='md'
      position='fixed'
      top='0'
      left='0'
      zIndex='5'
      pt='80px' // Add padding top to account for the navbar height
      display={{ base: 'none', lg: location.pathname !== '/' ? 'block' : 'none' }} // Hide on mobile and home page, show on desktop for other pages
      transition="width 0.3s ease, padding 0.3s ease"
    >
      <SidebarContent />
    </Box>
  );
}