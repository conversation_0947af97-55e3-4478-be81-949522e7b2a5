import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Heading,
  Text,
  Button,
  Progress,
  Icon,
  SimpleGrid,
  useColorModeValue,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalBody,
  Badge,
  Flex,
  Card,
  CardBody,
} from '@chakra-ui/react';
import {
  FaBriefcase,
  FaFileAlt,
  FaRocket,
  FaUserGraduate,
  FaBuilding,
  FaLaptopCode,
  FaHeart,
  FaUsers,
  FaChartLine,
  FaArrowRight,
  FaArrowLeft,
  FaCheck,
} from 'react-icons/fa';

interface OnboardingProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (preferences: UserPreferences) => void;
  onSkip?: () => void;
}

interface UserPreferences {
  careerStage: string;
  industries: string[];
  goals: string[];
  experience: string;
}

const OnboardingFlow: React.FC<OnboardingProps> = ({ isOpen, onClose, onComplete, onSkip }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [preferences, setPreferences] = useState<UserPreferences>({
    careerStage: '',
    industries: [],
    goals: [],
    experience: '',
  });

  const bgColor = useColorModeValue('white', 'gray.800');
  const cardBg = useColorModeValue('gray.50', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textPrimary = useColorModeValue('gray.800', 'white');
  const textSecondary = useColorModeValue('gray.600', 'gray.400');

  const steps = [
    {
      title: "Welcome to CareerDart!",
      subtitle: "Let's create your personalized career toolkit",
      content: "WelcomeStep"
    },
    {
      title: "What's your career stage?",
      subtitle: "This helps us tailor our recommendations",
      content: "CareerStageStep"
    },
    {
      title: "Which industries interest you?",
      subtitle: "Select all that apply",
      content: "IndustriesStep"
    },
    {
      title: "What are your main goals?",
      subtitle: "We'll customize features based on your priorities",
      content: "GoalsStep"
    },
    {
      title: "You're all set! 🎉",
      subtitle: "Your personalized CareerDart experience awaits",
      content: "CompletionStep"
    }
  ];

  const careerStages = [
    { id: 'student', label: 'Student/Recent Graduate', icon: FaUserGraduate, color: 'blue' },
    { id: 'entry', label: 'Entry Level (0-2 years)', icon: FaRocket, color: 'green' },
    { id: 'mid', label: 'Mid Level (3-7 years)', icon: FaBriefcase, color: 'purple' },
    { id: 'senior', label: 'Senior Level (8+ years)', icon: FaChartLine, color: 'orange' },
    { id: 'executive', label: 'Executive/Leadership', icon: FaUsers, color: 'red' },
  ];

  const industries = [
    { id: 'tech', label: 'Technology', icon: FaLaptopCode },
    { id: 'finance', label: 'Finance & Banking', icon: FaChartLine },
    { id: 'healthcare', label: 'Healthcare', icon: FaHeart },
    { id: 'education', label: 'Education', icon: FaUserGraduate },
    { id: 'consulting', label: 'Consulting', icon: FaUsers },
    { id: 'retail', label: 'Retail & E-commerce', icon: FaBuilding },
    { id: 'nonprofit', label: 'Non-profit', icon: FaHeart },
    { id: 'manufacturing', label: 'Manufacturing', icon: FaBuilding },
  ];

  const goals = [
    { id: 'findJob', label: 'Find my next job', icon: FaBriefcase },
    { id: 'improveResume', label: 'Improve my resume', icon: FaFileAlt },
    { id: 'interviewPrep', label: 'Prepare for interviews', icon: FaUsers },
    { id: 'careerChange', label: 'Change career paths', icon: FaRocket },
    { id: 'networking', label: 'Build my network', icon: FaUsers },
    { id: 'skillDev', label: 'Develop new skills', icon: FaChartLine },
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    onComplete(preferences);
    onClose();
  };

  const updatePreferences = (key: keyof UserPreferences, value: any) => {
    setPreferences(prev => ({ ...prev, [key]: value }));
  };

  const toggleArrayPreference = (key: keyof UserPreferences, value: string) => {
    setPreferences(prev => ({
      ...prev,
      [key]: (prev[key] as string[]).includes(value)
        ? (prev[key] as string[]).filter(item => item !== value)
        : [...(prev[key] as string[]), value]
    }));
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <VStack spacing={6} textAlign="center">
            <Box p={6} bg={cardBg} borderRadius="xl" border="1px" borderColor={borderColor} w="full">
              <VStack spacing={3}>
                <Text fontSize="sm" fontWeight="medium" color={textPrimary}>
                  🎯 What we'll set up together:
                </Text>
                <VStack spacing={2} align="start" w="full">
                  <HStack spacing={3}>
                    <Text fontSize="sm" color="blue.500">✨</Text>
                    <Text fontSize="sm" color={textSecondary}>Your career stage and experience level</Text>
                  </HStack>
                  <HStack spacing={3}>
                    <Text fontSize="sm" color="blue.500">🏢</Text>
                    <Text fontSize="sm" color={textSecondary}>Industry preferences and interests</Text>
                  </HStack>
                  <HStack spacing={3}>
                    <Text fontSize="sm" color="blue.500">📈</Text>
                    <Text fontSize="sm" color={textSecondary}>Career goals and priorities</Text>
                  </HStack>
                </VStack>
                <Text fontSize="xs" color={textSecondary} fontStyle="italic" mt={2}>
                  ⏱️ Takes just 2 minutes • Completely customizes your dashboard
                </Text>
                <Box mt={3} p={3} bg={useColorModeValue('blue.50', 'blue.900')} borderRadius="md" w="full">
                  <Text fontSize="xs" color="blue.600" textAlign="center" fontWeight="medium">
                    💡 Completing setup unlocks personalized job recommendations, 
                    tailored cover letter templates, and industry-specific interview tips!
                  </Text>
                </Box>
              </VStack>
            </Box>
          </VStack>
        );

      case 1:
        return (
          <VStack spacing={4}>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={3} w="full">
              {careerStages.map((stage) => (
                <Card
                  key={stage.id}
                  variant={preferences.careerStage === stage.id ? "filled" : "outline"}
                  cursor="pointer"
                  onClick={() => updatePreferences('careerStage', stage.id)}
                  bg={preferences.careerStage === stage.id ? `${stage.color}.50` : cardBg}
                  borderColor={preferences.careerStage === stage.id ? `${stage.color}.300` : borderColor}
                  _hover={{ borderColor: `${stage.color}.400`, transform: 'translateY(-2px)' }}
                  transition="all 0.2s"
                >
                  <CardBody>
                    <HStack spacing={3}>
                      <Icon as={stage.icon} boxSize={5} color={`${stage.color}.500`} />
                      <VStack align="start" spacing={0}>
                        <Text fontWeight="medium" fontSize="sm" color={textPrimary}>
                          {stage.label}
                        </Text>
                      </VStack>
                      {preferences.careerStage === stage.id && (
                        <Icon as={FaCheck} color={`${stage.color}.500`} ml="auto" />
                      )}
                    </HStack>
                  </CardBody>
                </Card>
              ))}
            </SimpleGrid>
          </VStack>
        );

      case 2:
        return (
          <VStack spacing={4}>
            <SimpleGrid columns={{ base: 2, md: 3 }} spacing={3} w="full">
              {industries.map((industry) => (
                <Card
                  key={industry.id}
                  variant={preferences.industries.includes(industry.id) ? "filled" : "outline"}
                  cursor="pointer"
                  onClick={() => toggleArrayPreference('industries', industry.id)}
                  bg={preferences.industries.includes(industry.id) ? "blue.50" : cardBg}
                  borderColor={preferences.industries.includes(industry.id) ? "blue.300" : borderColor}
                  _hover={{ borderColor: "blue.400", transform: 'translateY(-2px)' }}
                  transition="all 0.2s"
                  size="sm"
                >
                  <CardBody>
                    <VStack spacing={2} align="center">
                      <Icon as={industry.icon} boxSize={5} color="blue.500" />
                      <Text fontWeight="medium" fontSize="xs" textAlign="center" color={textPrimary}>
                        {industry.label}
                      </Text>
                      {preferences.industries.includes(industry.id) && (
                        <Icon as={FaCheck} color="blue.500" boxSize={3} />
                      )}
                    </VStack>
                  </CardBody>
                </Card>
              ))}
            </SimpleGrid>
            <Text fontSize="sm" color={textSecondary} textAlign="center">
              Selected: {preferences.industries.length} industries
            </Text>
          </VStack>
        );

      case 3:
        return (
          <VStack spacing={4}>
            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={3} w="full">
              {goals.map((goal) => (
                <Card
                  key={goal.id}
                  variant={preferences.goals.includes(goal.id) ? "filled" : "outline"}
                  cursor="pointer"
                  onClick={() => toggleArrayPreference('goals', goal.id)}
                  bg={preferences.goals.includes(goal.id) ? "purple.50" : cardBg}
                  borderColor={preferences.goals.includes(goal.id) ? "purple.300" : borderColor}
                  _hover={{ borderColor: "purple.400", transform: 'translateY(-2px)' }}
                  transition="all 0.2s"
                >
                  <CardBody>
                    <HStack spacing={3}>
                      <Icon as={goal.icon} boxSize={5} color="purple.500" />
                      <Text fontWeight="medium" fontSize="sm" color={textPrimary}>
                        {goal.label}
                      </Text>
                      {preferences.goals.includes(goal.id) && (
                        <Icon as={FaCheck} color="purple.500" ml="auto" />
                      )}
                    </HStack>
                  </CardBody>
                </Card>
              ))}
            </SimpleGrid>
            <Text fontSize="sm" color={textSecondary} textAlign="center">
              Selected: {preferences.goals.length} goals
            </Text>
          </VStack>
        );

      case 4:
        return (
          <VStack spacing={6} textAlign="center">
            <Icon as={FaCheck} boxSize={16} color="green.500" />
            <VStack spacing={3}>
              <Text fontSize="lg" color={textPrimary}>
                Perfect! Your CareerDart is ready 🎯
              </Text>
              <Text color={textSecondary}>
                Based on your preferences, we've customized your dashboard with relevant tools and recommendations.
              </Text>
            </VStack>
            
            {/* Summary */}
            <Box p={4} bg={cardBg} borderRadius="lg" border="1px" borderColor={borderColor} w="full">
              <VStack spacing={2} align="start">
                <Text fontSize="sm" fontWeight="medium" color={textPrimary}>Your Profile:</Text>
                <HStack flexWrap="wrap" spacing={2}>
                  <Badge colorScheme="blue">{careerStages.find(s => s.id === preferences.careerStage)?.label}</Badge>
                  {preferences.industries.slice(0, 3).map(ind => (
                    <Badge key={ind} colorScheme="green" variant="outline">
                      {industries.find(i => i.id === ind)?.label}
                    </Badge>
                  ))}
                  {preferences.industries.length > 3 && (
                    <Badge colorScheme="gray">+{preferences.industries.length - 3} more</Badge>
                  )}
                </HStack>
              </VStack>
            </Box>
          </VStack>
        );

      default:
        return null;
    }
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0: return true;
      case 1: return preferences.careerStage !== '';
      case 2: return preferences.industries.length > 0;
      case 3: return preferences.goals.length > 0;
      case 4: return true;
      default: return false;
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="2xl" closeOnOverlayClick={false}>
      <ModalOverlay backdropFilter="blur(4px)" />
      <ModalContent bg={bgColor} maxW="600px" mx={4}>
        <ModalBody p={8}>
          <VStack spacing={6} align="stretch">
            {/* Header with Skip Option */}
            <Flex justify="space-between" align="center">
              <Box />
              <Button
                variant="ghost"
                size="sm"
                color={textSecondary}
                onClick={() => {
                  if (onSkip) {
                    onSkip();
                  } else {
                    onClose();
                  }
                }}
                _hover={{ color: textPrimary }}
              >
                Skip for now
              </Button>
            </Flex>

            {/* Progress */}
            <VStack spacing={2}>
              <Progress 
                value={(currentStep / (steps.length - 1)) * 100} 
                colorScheme="blue" 
                size="sm" 
                w="full" 
                borderRadius="full"
              />
              <Text fontSize="xs" color={textSecondary} alignSelf="flex-end">
                Step {currentStep + 1} of {steps.length}
              </Text>
            </VStack>

            {/* Header */}
            <VStack spacing={2} textAlign="center">
              <Heading size="lg" color={textPrimary}>
                {steps[currentStep].title}
              </Heading>
              <Text color={textSecondary}>
                {steps[currentStep].subtitle}
              </Text>
            </VStack>

            {/* Content */}
            <Box minH="300px">
              {renderStepContent()}
            </Box>

            {/* Navigation */}
            <Flex justify="space-between" align="center">
              <Button
                variant="ghost"
                onClick={handlePrevious}
                isDisabled={currentStep === 0}
                leftIcon={<FaArrowLeft />}
                size="sm"
              >
                Previous
              </Button>

              {currentStep === steps.length - 1 ? (
                <Button
                  colorScheme="blue"
                  onClick={handleComplete}
                  rightIcon={<FaRocket />}
                  size="md"
                >
                  Get Started!
                </Button>
              ) : (
                <Button
                  colorScheme="blue"
                  onClick={handleNext}
                  isDisabled={!canProceed()}
                  rightIcon={<FaArrowRight />}
                  size="md"
                >
                  {currentStep === 0 ? 'Start Setup' : 'Continue'}
                </Button>
              )}
            </Flex>
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};

export default OnboardingFlow; 