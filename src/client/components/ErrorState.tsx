import { V<PERSON><PERSON>ck, Button, ButtonGroup, Box, Alert, AlertIcon, Code } from '@chakra-ui/react';
import UndrawIllustration, { IllustrationPresets, UndrawIllustrationProps } from './UndrawIllustration';
import { motion } from 'framer-motion';
import { FaRedo, FaHome, FaBug } from 'react-icons/fa';

const MotionBox = motion(Box);

export interface ErrorStateProps extends Omit<UndrawIllustrationProps, 'illustration'> {
  /** Preset error type */
  preset?: 'error404' | 'errorGeneral' | 'errorNetwork';
  /** Custom illustration name if not using preset */
  illustration?: string;
  /** Error message or object */
  error?: string | Error | any;
  /** Whether to show error details */
  showErrorDetails?: boolean;
  /** Retry action */
  onRetry?: () => void;
  /** Home navigation action */
  onGoHome?: () => void;
  /** Report error action */
  onReportError?: () => void;
  /** Whether to show actions */
  showActions?: boolean;
}

/**
 * ErrorState component for displaying error states with illustrations and actions
 */
export default function ErrorState({
  preset = 'errorGeneral',
  illustration,
  title,
  description,
  error,
  showErrorDetails = false,
  onRetry,
  onGoHome,
  onReportError,
  showActions = true,
  size = 'md',
  animated = true,
  primaryColor = 'ef4444', // Red color for errors
  children,
  ...props
}: ErrorStateProps) {
  // Use preset configuration
  const illustrationConfig = IllustrationPresets[preset];
  const finalIllustration = illustration || illustrationConfig.illustration;
  const finalTitle = title || illustrationConfig.title;
  const finalDescription = description || illustrationConfig.description;

  // Extract error message
  const errorMessage = typeof error === 'string'
    ? error
    : error?.message || error?.toString() || 'Unknown error';

  const actionsVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        delay: 0.8,
        ease: 'easeOut',
      },
    },
  };

  return (
    <Box py={12} px={6} textAlign="center" {...props}>
      <UndrawIllustration
        illustration={finalIllustration}
        title={finalTitle}
        description={finalDescription}
        size={size}
        animated={animated}
        primaryColor={primaryColor}
      >
        {/* Error details */}
        {showErrorDetails && error && (
          <Alert status="error" borderRadius="md" textAlign="left" maxW="md" mx="auto">
            <AlertIcon />
            <Box>
              <Code fontSize="sm" colorScheme="red">
                {errorMessage}
              </Code>
            </Box>
          </Alert>
        )}

        {/* Custom children content */}
        {children}

        {/* Action buttons */}
        {showActions && (
          <MotionBox
            variants={animated ? actionsVariants : undefined}
            initial={animated ? 'hidden' : undefined}
            animate={animated ? 'visible' : undefined}
            mt={6}
          >
            <ButtonGroup
              spacing={4}
              flexDirection={{ base: 'column', sm: 'row' }}
              justifyContent="center"
            >
              {onRetry && (
                <Button
                  colorScheme="blue"
                  size="lg"
                  leftIcon={<FaRedo />}
                  onClick={onRetry}
                  width={{ base: 'full', sm: 'auto' }}
                >
                  Try Again
                </Button>
              )}

              {onGoHome && (
                <Button
                  variant="outline"
                  size="lg"
                  leftIcon={<FaHome />}
                  onClick={onGoHome}
                  width={{ base: 'full', sm: 'auto' }}
                >
                  Go Home
                </Button>
              )}

              {onReportError && (
                <Button
                  variant="ghost"
                  size="lg"
                  leftIcon={<FaBug />}
                  onClick={onReportError}
                  width={{ base: 'full', sm: 'auto' }}
                >
                  Report Issue
                </Button>
              )}
            </ButtonGroup>
          </MotionBox>
        )}
      </UndrawIllustration>
    </Box>
  );
}

// Convenience components for common error states
export const Error404 = (props: Omit<ErrorStateProps, 'preset'>) => (
  <ErrorState preset="error404" {...props} />
);

export const NetworkError = (props: Omit<ErrorStateProps, 'preset'>) => (
  <ErrorState preset="errorNetwork" {...props} />
);

export const GeneralError = (props: Omit<ErrorStateProps, 'preset'>) => (
  <ErrorState preset="errorGeneral" {...props} />
);
