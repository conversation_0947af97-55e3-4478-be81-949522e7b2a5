import React, { Suspense } from 'react';
import {
  Container,
  VStack,
  Heading,
  Text,
  Box,
  Badge,
  HStack,
  Icon,
  Grid,
  useColorModeValue,
  Button,
  Flex
} from '@chakra-ui/react';
import {
  FaStar, FaRocket, FaEnvelopeOpenText, FaComments, FaBriefcase, FaChartLine, FaGraduationCap,
  FaUsers, FaTrophy, FaClock, FaFileAlt, FaGoogle, FaMicrosoft, FaApple, FaAmazon, FaFacebook, FaDropbox
} from 'react-icons/fa';
import { LeaveATip, LoginToBegin } from './AlertDialog';
const UpgradeModal = React.lazy(() => import('./UpgradeModal'));

// Copy the arrays here:
const features = [
  {
    icon: FaRocket,
    title: "AI-Powered Resume Builder",
    description: "Create professional resumes in minutes with our intelligent AI that adapts to your industry and role.",
    color: "blue.500"
  },
  {
    icon: FaEnvelopeOpenText,
    title: "Smart Cover Letters",
    description: "Generate personalized cover letters that perfectly match job descriptions and showcase your strengths.",
    color: "purple.500"
  },
  {
    icon: FaComments,
    title: "Interview Preparation",
    description: "Practice with AI-generated questions tailored to your target role and get instant feedback.",
    color: "green.500"
  },
  {
    icon: FaBriefcase,
    title: "Job Application Tracker",
    description: "Organize your job search with our comprehensive tracking system and never miss an opportunity.",
    color: "orange.500"
  },
  {
    icon: FaChartLine,
    title: "Career Analytics",
    description: "Get insights into your job search performance and optimize your strategy for better results.",
    color: "teal.500"
  },
  {
    icon: FaGraduationCap,
    title: "Learning Center",
    description: "Access curated career development resources and courses to advance your professional skills.",
    color: "pink.500"
  }
];

const testimonials = [
  {
    name: "Sarah Chen",
    role: "Software Engineer",
    company: "Google",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150",
    content: "CareerDart helped me land my dream job at Google! The AI-generated cover letters were spot-on and the interview prep was invaluable.",
    rating: 5
  },
  {
    name: "Marcus Johnson",
    role: "Product Manager",
    company: "Microsoft",
    avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150",
    content: "The resume builder is incredible. I went from 2% response rate to 40% after using CareerDart. Absolutely game-changing!",
    rating: 5
  },
  {
    name: "Emily Rodriguez",
    role: "UX Designer",
    company: "Apple",
    avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150",
    content: "I love how CareerDart tracks all my applications and provides insights. It made my job search so much more organized and effective.",
    rating: 5
  }
];

const stats = [
  { label: "Job Seekers Helped", value: "50,000+", icon: FaUsers },
  { label: "Success Rate", value: "89%", icon: FaTrophy },
  { label: "Average Time to Hire", value: "3.2 weeks", icon: FaClock },
  { label: "Cover Letters Generated", value: "250,000+", icon: FaFileAlt }
];

const companies = [
  { name: "Google", icon: FaGoogle },
  { name: "Microsoft", icon: FaMicrosoft },
  { name: "Apple", icon: FaApple },
  { name: "Amazon", icon: FaAmazon },
  { name: "Facebook", icon: FaFacebook },
  { name: "Dropbox", icon: FaDropbox }
];

const BelowTheFold = ({ user, setShowCoverLetterForm, navigate }: any) => {
  const cardBg = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'gray.200');

  return (
    <>
      {/* Company Logos Section */}
      <Container maxW="95%" py={{ base: 8, md: 12 }}>
        <VStack
          spacing={6}
        >
          <Text fontSize="lg" fontWeight="600" color="gray.500" textAlign="center">
            Trusted by job seekers who've landed at top companies
          </Text>
          <Text fontSize="sm" color="gray.400" textAlign="center">
            Our users have secured positions at industry-leading companies such as
          </Text>
          <Box w="full" overflow="hidden">
            <Flex
              animation="scroll 30s linear infinite"
              sx={{
                '@keyframes scroll': {
                  '0%': { transform: 'translateX(0)' },
                  '100%': { transform: 'translateX(-50%)' }
                }
              }}
            >
              {[...companies, ...companies].map((company, index) => (
                <Flex
                  key={index}
                  minW="200px"
                  h="80px"
                  align="center"
                  justify="center"
                  mx={4}
                >
                  <HStack spacing={3} opacity={0.6} _hover={{ opacity: 1 }} transition="opacity 0.3s">
                    <Icon as={company.icon} boxSize={8} color="gray.500" />
                    <Text fontSize="lg" fontWeight="600" color="gray.500">
                      {company.name}
                    </Text>
                  </HStack>
                </Flex>
              ))}
            </Flex>
          </Box>
        </VStack>
      </Container>

      {/* Stats Section */}
      <Container maxW="95%" py={{ base: 10, md: 16 }}>
        <VStack spacing={12}>
          <VStack spacing={4} textAlign="center">
            <Heading size="2xl" fontWeight="black" color={textColor}>
              Intelligent Career Development That Delivers
            </Heading>
            <Text fontSize="xl" color="gray.500" maxW="3xl">
              Our personalized AI approach creates measurable results for professionals across all industries
            </Text>
          </VStack>
          <Button
            size="lg"
            colorScheme="blue"
            px={8}
            py={6}
            fontSize="lg"
            fontWeight="600"
            borderRadius="xl"
            onClick={() => user ? setShowCoverLetterForm(true) : navigate('/login')}
            _hover={{
              transform: 'translateY(-2px)',
              boxShadow: 'xl',
            }}
            transition="all 0.3s"
          >
            {user ? 'Start Your Journey' : 'Start for FREE'}
          </Button>
        </VStack>
      </Container>

      {/* Features Section */}
      <Container maxW="95%" py={{ base: 10, md: 16 }}>
        <VStack spacing={12}>
          <VStack spacing={4} textAlign="center">
            <Heading size="2xl" fontWeight="black" color={textColor}>
              Everything You Need to Land Your Dream Job
            </Heading>
            <Text fontSize="xl" color="gray.500" maxW="3xl">
              Our comprehensive suite of AI-powered tools gives you the competitive edge in today's job market.
            </Text>
          </VStack>
          <Grid templateColumns={{ base: "repeat(2, 1fr)", md: "repeat(2, 1fr)", lg: "repeat(3, 1fr)" }} gap={{ base: 4, md: 8 }}>
            {features.map((feature, index) => (
              <Box
                key={index}
                bg={cardBg}
                borderRadius={{ base: "xl", md: "2xl" }}
                p={{ base: 4, md: 8 }}
                border="1px solid"
                borderColor={useColorModeValue('gray.200', 'gray.700')}
                _hover={{
                  transform: 'translateY(-4px)',
                  boxShadow: 'xl',
                }}
                transition="all 0.3s"
                h="full"
              >
                <VStack spacing={{ base: 3, md: 4 }} align="start" h="full">
                  <Icon as={feature.icon} boxSize={{ base: 8, md: 10 }} color={feature.color} />
                  <Heading size={{ base: "sm", md: "md" }} fontWeight="bold" color={textColor}>
                    {feature.title}
                  </Heading>
                  <Text color="gray.500" lineHeight="tall" fontSize={{ base: "sm", md: "md" }}>
                    {feature.description}
                  </Text>
                </VStack>
              </Box>
            ))}
          </Grid>
        </VStack>
      </Container>

      {/* Testimonials Section */}
      <Container maxW="95%" py={{ base: 10, md: 16 }}>
        <VStack spacing={12}>
          <VStack spacing={8} textAlign="center" maxW="3xl" mx="auto">
            <Heading size="2xl" fontWeight="black" color={textColor}>
              Ready to Transform Your Career?
            </Heading>
            <Text fontSize="xl" color="gray.500">
              Join thousands of professionals who are already using AI-powered tools to advance their careers
            </Text>
            <Button
              size="lg"
              colorScheme="blue"
              px={12}
              py={8}
              fontSize="xl"
              fontWeight="700"
              borderRadius="2xl"
              onClick={() => user ? setShowCoverLetterForm(true) : navigate('/login')}
              _hover={{
                transform: 'translateY(-3px)',
                boxShadow: '0 20px 40px rgba(66, 153, 225, 0.4)',
              }}
              transition="all 0.3s"
            >
              {user ? 'Start Your Journey' : 'Begin Your Career Transformation'}
            </Button>
          </VStack>
        </VStack>
      </Container>

      {/* Modals */}
      <LeaveATip isOpen={false} onOpen={() => {}} onClose={() => {}} credits={user?.credits || 0} />
      <LoginToBegin isOpen={false} onOpen={() => {}} onClose={() => {}} />
      <Suspense fallback={<></>}>
        <UpgradeModal isOpen={false} onClose={() => {}} userCredits={user?.credits || 0} feature="cover-letter" />
      </Suspense>
    </>
  );
};

export default BelowTheFold; 