import { V<PERSON><PERSON>ck, Spinner, Progress, Box, Text } from '@chakra-ui/react';
import UndrawIllustration, { UndrawIllustrationProps } from './UndrawIllustration';
import { motion } from 'framer-motion';

const MotionBox = motion(Box);

export interface LoadingStateProps extends Omit<UndrawIllustrationProps, 'illustration'> {
  /** Loading message */
  message?: string;
  /** Whether to show progress bar */
  showProgress?: boolean;
  /** Progress value (0-100) */
  progress?: number;
  /** Loading type */
  type?: 'spinner' | 'illustration' | 'both';
  /** Custom illustration name */
  illustration?: string;
}

/**
 * LoadingState component for displaying loading states with illustrations
 */
export default function LoadingState({
  message = 'Loading...',
  showProgress = false,
  progress,
  type = 'both',
  illustration = 'loading_re_5axr',
  size = 'md',
  animated = true,
  primaryColor = '6366f1',
  children,
  ...props
}: LoadingStateProps) {
  const spinnerVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut',
      },
    },
  };

  const progressVariants = {
    hidden: { opacity: 0, width: 0 },
    visible: {
      opacity: 1,
      width: '100%',
      transition: {
        duration: 0.8,
        delay: 0.3,
        ease: 'easeOut',
      },
    },
  };

  if (type === 'spinner') {
    return (
      <MotionBox
        py={12}
        px={6}
        textAlign="center"
        variants={animated ? spinnerVariants : undefined}
        initial={animated ? 'hidden' : undefined}
        animate={animated ? 'visible' : undefined}
        {...props}
      >
        <VStack spacing={6}>
          <Spinner
            size="xl"
            color="blue.500"
            thickness="4px"
            speed="0.65s"
          />
          <Text color="gray.600" fontSize="lg">
            {message}
          </Text>
          {showProgress && progress !== undefined && (
            <MotionBox
              width="200px"
              variants={animated ? progressVariants : undefined}
              initial={animated ? 'hidden' : undefined}
              animate={animated ? 'visible' : undefined}
            >
              <Progress
                value={progress}
                colorScheme="blue"
                borderRadius="full"
                size="sm"
              />
              <Text fontSize="sm" color="gray.500" mt={2}>
                {Math.round(progress)}%
              </Text>
            </MotionBox>
          )}
          {children}
        </VStack>
      </MotionBox>
    );
  }

  if (type === 'illustration') {
    return (
      <UndrawIllustration
        illustration={illustration}
        title={message}
        size={size}
        animated={animated}
        primaryColor={primaryColor}
        {...props}
      >
        {showProgress && progress !== undefined && (
          <MotionBox
            width="300px"
            variants={animated ? progressVariants : undefined}
            initial={animated ? 'hidden' : undefined}
            animate={animated ? 'visible' : undefined}
          >
            <Progress
              value={progress}
              colorScheme="blue"
              borderRadius="full"
              size="lg"
            />
            <Text fontSize="sm" color="gray.500" mt={2}>
              {Math.round(progress)}% complete
            </Text>
          </MotionBox>
        )}
        {children}
      </UndrawIllustration>
    );
  }

  // type === 'both'
  return (
    <MotionBox
      py={12}
      px={6}
      textAlign="center"
      variants={animated ? spinnerVariants : undefined}
      initial={animated ? 'hidden' : undefined}
      animate={animated ? 'visible' : undefined}
      {...props}
    >
      <VStack spacing={8}>
        <UndrawIllustration
          illustration={illustration}
          size={size}
          animated={animated}
          primaryColor={primaryColor}
        />
        
        <VStack spacing={4}>
          <Spinner
            size="lg"
            color="blue.500"
            thickness="3px"
            speed="0.65s"
          />
          <Text color="gray.600" fontSize="lg" fontWeight="medium">
            {message}
          </Text>
          
          {showProgress && progress !== undefined && (
            <MotionBox
              width="250px"
              variants={animated ? progressVariants : undefined}
              initial={animated ? 'hidden' : undefined}
              animate={animated ? 'visible' : undefined}
            >
              <Progress
                value={progress}
                colorScheme="blue"
                borderRadius="full"
                size="md"
              />
              <Text fontSize="sm" color="gray.500" mt={2}>
                {Math.round(progress)}% complete
              </Text>
            </MotionBox>
          )}
        </VStack>
        
        {children}
      </VStack>
    </MotionBox>
  );
}

// Convenience components for common loading scenarios
export const SimpleLoading = ({ message = 'Loading...' }: { message?: string }) => (
  <LoadingState type="spinner" message={message} />
);

export const IllustratedLoading = ({ 
  message = 'Processing...', 
  illustration = 'loading_re_5axr' 
}: { 
  message?: string; 
  illustration?: string; 
}) => (
  <LoadingState type="illustration" message={message} illustration={illustration} />
);

export const ProgressLoading = ({ 
  message = 'Processing...', 
  progress 
}: { 
  message?: string; 
  progress: number; 
}) => (
  <LoadingState 
    type="both" 
    message={message} 
    showProgress={true} 
    progress={progress} 
  />
);
