import React from 'react';
import {
  Link,
  useColorModeValue,
} from '@chakra-ui/react';

/**
 * Skip Navigation component for keyboard accessibility
 * Provides quick navigation links for screen readers and keyboard users
 * Hidden by default, appears when any skip link receives focus
 */
const SkipNavigation: React.FC = () => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const shadowColor = useColorModeValue('rgba(0, 0, 0, 0.1)', 'rgba(0, 0, 0, 0.3)');

  const skipLinkStyles = {
    position: 'absolute' as const,
    top: '-100vh', // Move completely off-screen
    left: '8px',
    transform: 'translateY(-100%)',
    opacity: 0,
    zIndex: 10000,
    bg: bgColor,
    color: 'blue.500',
    px: 4,
    py: 2,
    borderRadius: 'md',
    border: '2px solid',
    borderColor: borderColor,
    boxShadow: `0 4px 12px ${shadowColor}`,
    fontWeight: 'medium',
    fontSize: 'sm',
    textDecoration: 'none',
    whiteSpace: 'nowrap' as const,
    transition: 'all 0.2s ease-in-out',
    _focus: {
      top: '8px',
      transform: 'translateY(0)',
      opacity: 1,
      outline: '2px solid',
      outlineColor: 'blue.500',
      outlineOffset: '2px',
    },
    _hover: {
      textDecoration: 'none',
      bg: useColorModeValue('blue.50', 'gray.700'),
    }
  };

  return (
    <>
      {/* Skip links - each one is individually focusable */}
      <Link
        href="#main-content"
        sx={skipLinkStyles}
        aria-label="Skip to main content"
      >
        Skip to main content
      </Link>
      <Link
        href="#navigation"
        sx={{
          ...skipLinkStyles,
          left: '180px', // Position next to the first link when visible
        }}
        aria-label="Skip to navigation"
      >
        Skip to navigation
      </Link>
      <Link
        href="#footer"
        sx={{
          ...skipLinkStyles,
          left: '320px', // Position next to the second link when visible
        }}
        aria-label="Skip to footer"
      >
        Skip to footer
      </Link>
    </>
  );
};

export default SkipNavigation;
