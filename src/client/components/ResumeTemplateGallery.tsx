import React, { useState, useMemo } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  SimpleGrid,
  Button,
  ButtonGroup,
  useColorModeValue,
  useBreakpointValue,
  Heading,
  Flex,
  Badge,
  Icon,
  IconButton,
  Select,
  Center,
  Spacer,
  Wrap,
  WrapItem
} from '@chakra-ui/react';
import { FaArrowLeft, FaArrowRight, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { ResumeTemplate } from '../../shared/types';
import { resumeTemplates } from '../data/resumeTemplates';
import ResumeTemplateCard from './ResumeTemplateCard';
import ResumeTemplatePreview from './ResumeTemplatePreview';
import Pagination from './Pagination';

interface ResumeTemplateGalleryProps {
  selectedTemplate?: ResumeTemplate;
  onTemplateSelect: (template: ResumeTemplate) => void;
}

type TemplateCategory = 'all' | 'professional' | 'creative' | 'modern' | 'classic';

const CATEGORY_LABELS = {
  all: 'All Templates',
  professional: 'Professional',
  creative: 'Creative',
  modern: 'Modern',
  classic: 'Classic'
} as const;

const ITEMS_PER_PAGE_OPTIONS = [4, 6, 8, 12];

const ResumeTemplateGallery: React.FC<ResumeTemplateGalleryProps> = ({
  selectedTemplate,
  onTemplateSelect
}) => {
  const [selectedCategory, setSelectedCategory] = useState<TemplateCategory>('all');
  const [previewTemplate, setPreviewTemplate] = useState<ResumeTemplate | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(4);

  // Responsive values for better mobile experience
  const buttonSize = useBreakpointValue({ base: 'xs', sm: 'sm', md: 'md' });
  const filterSpacing = useBreakpointValue({ base: 1, sm: 2, md: 3 });
  const containerPadding = useBreakpointValue({ base: 2, sm: 3, md: 4 });
  const gridSpacing = useBreakpointValue({ base: 3, sm: 4, md: 6 });

  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.600', 'gray.300');

  const filteredTemplates = selectedCategory === 'all'
    ? resumeTemplates
    : resumeTemplates.filter(template => template.category === selectedCategory);

  // Pagination calculations
  const totalItems = filteredTemplates.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentTemplates = filteredTemplates.slice(startIndex, endIndex);

  // Reset page when category changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [selectedCategory, itemsPerPage]);

  const handleTemplateSelect = (template: ResumeTemplate) => {
    onTemplateSelect(template);
  };

  const handlePreview = (template: ResumeTemplate) => {
    setPreviewTemplate(template);
  };

  const closePreview = () => {
    setPreviewTemplate(null);
  };

  const getCategoryCount = (category: TemplateCategory) => {
    if (category === 'all') return resumeTemplates.length;
    return resumeTemplates.filter(template => template.category === category).length;
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (itemsPerPage: number) => {
    setItemsPerPage(itemsPerPage);
  };

  return (
    <VStack spacing={gridSpacing} align="stretch" width="100%" height="100%">
      {/* Enhanced Header with Mobile-Optimized Category Filter */}
      <Box px={containerPadding}>
        <Text
          fontSize={{ base: "xs", sm: "sm" }}
          fontWeight="medium"
          mb={{ base: 2, sm: 3 }}
          color={textColor}
        >
          Filter by Category
        </Text>

        {/* Minimalistic Filter Layout */}
        <Wrap spacing={2} align="center">
          {(Object.keys(CATEGORY_LABELS) as TemplateCategory[]).map((category) => (
            <WrapItem key={category}>
              <Button
                onClick={() => setSelectedCategory(category)}
                variant={selectedCategory === category ? 'solid' : 'ghost'}
                colorScheme={selectedCategory === category ? 'blue' : 'gray'}
                size="sm"
                fontSize="sm"
                px={3}
                py={1}
                h="auto"
                minH="32px"
                borderRadius="full"
                fontWeight={selectedCategory === category ? '600' : '400'}
                _hover={{
                  bg: selectedCategory === category ? 'blue.600' : 'gray.100',
                  _dark: {
                    bg: selectedCategory === category ? 'blue.500' : 'gray.700'
                  }
                }}
                transition="all 0.15s"
              >
                <Text display={{ base: 'none', sm: 'block' }}>
                  {CATEGORY_LABELS[category]}
                </Text>
                <Text display={{ base: 'block', sm: 'none' }}>
                  {category === 'all' ? 'All' :
                   category === 'professional' ? 'Pro' :
                   category === 'creative' ? 'Art' :
                   category === 'modern' ? 'Mod' : 'Classic'}
                </Text>
              </Button>
            </WrapItem>
          ))}
        </Wrap>
      </Box>

      {/* Enhanced Templates Grid with Scroll */}
      <Box
        flex="1"
        minHeight="400px"
        maxHeight={{ base: "500px", md: "600px", lg: "calc(100vh - 300px)" }}
        px={containerPadding}
        overflowY="auto"
        overflowX="hidden"
      >
        {currentTemplates.length > 0 ? (
          <SimpleGrid
            columns={{ base: 1, md: 2, lg: 3, xl: 4 }}
            spacing={gridSpacing}
            width="100%"
          >
            {currentTemplates.map((template) => (
              <ResumeTemplateCard
                key={template.id}
                template={template}
                isSelected={selectedTemplate?.id === template.id}
                onSelect={handleTemplateSelect}
                onPreview={handlePreview}
              />
            ))}
          </SimpleGrid>
        ) : (
          <Box
            textAlign="center"
            py={12}
            border="2px dashed"
            borderColor={borderColor}
            borderRadius="lg"
          >
            <Text color="gray.500" fontSize="lg">
              No templates found in this category
            </Text>
          </Box>
        )}
      </Box>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalItems}
        itemsPerPage={itemsPerPage}
        itemsPerPageOptions={ITEMS_PER_PAGE_OPTIONS}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
        showItemsPerPage={true}
        showSummary={true}
        size="md"
      />

      {/* Template Preview Modal */}
      {previewTemplate && (
        <ResumeTemplatePreview
          template={previewTemplate}
          isOpen={!!previewTemplate}
          onClose={closePreview}
          onSelect={() => {
            handleTemplateSelect(previewTemplate);
            closePreview();
          }}
          isSelected={selectedTemplate?.id === previewTemplate.id}
        />
      )}
    </VStack>
  );
};

export default ResumeTemplateGallery;
