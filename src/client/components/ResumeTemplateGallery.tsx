import React, { useState, useMemo } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  SimpleGrid,
  Button,
  ButtonGroup,
  useColorModeValue,
  Heading,
  Flex,
  Badge,
  Icon,
  IconButton,
  Select,
  Center,
  Spacer
} from '@chakra-ui/react';
import { FaArrowLeft, FaArrowRight, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { ResumeTemplate } from '../../shared/types';
import { resumeTemplates } from '../data/resumeTemplates';
import ResumeTemplateCard from './ResumeTemplateCard';
import ResumeTemplatePreview from './ResumeTemplatePreview';
import Pagination from './Pagination';

interface ResumeTemplateGalleryProps {
  selectedTemplate?: ResumeTemplate;
  onTemplateSelect: (template: ResumeTemplate) => void;
}

type TemplateCategory = 'all' | 'professional' | 'creative' | 'modern' | 'classic';

const CATEGORY_LABELS = {
  all: 'All Templates',
  professional: 'Professional',
  creative: 'Creative',
  modern: 'Modern',
  classic: 'Classic'
} as const;

const ITEMS_PER_PAGE_OPTIONS = [6, 9, 12, 18, 24];

const ResumeTemplateGallery: React.FC<ResumeTemplateGalleryProps> = ({
  selectedTemplate,
  onTemplateSelect
}) => {
  const [selectedCategory, setSelectedCategory] = useState<TemplateCategory>('all');
  const [previewTemplate, setPreviewTemplate] = useState<ResumeTemplate | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12);

  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.600', 'gray.300');

  const filteredTemplates = selectedCategory === 'all'
    ? resumeTemplates
    : resumeTemplates.filter(template => template.category === selectedCategory);

  // Pagination calculations
  const totalItems = filteredTemplates.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentTemplates = filteredTemplates.slice(startIndex, endIndex);

  // Reset page when category changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [selectedCategory, itemsPerPage]);

  const handleTemplateSelect = (template: ResumeTemplate) => {
    onTemplateSelect(template);
  };

  const handlePreview = (template: ResumeTemplate) => {
    setPreviewTemplate(template);
  };

  const closePreview = () => {
    setPreviewTemplate(null);
  };

  const getCategoryCount = (category: TemplateCategory) => {
    if (category === 'all') return resumeTemplates.length;
    return resumeTemplates.filter(template => template.category === category).length;
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (itemsPerPage: number) => {
    setItemsPerPage(itemsPerPage);
  };

  return (
    <VStack spacing={6} align="stretch" width="100%" height="100%">
      {/* Header with Category Filter */}
      <Box>
        <Text fontSize="sm" fontWeight="medium" mb={3} color={textColor}>
          Filter by Category
        </Text>
        <ButtonGroup size="sm" variant="outline" spacing={2} flexWrap="wrap">
          {(Object.keys(CATEGORY_LABELS) as TemplateCategory[]).map((category) => (
            <Button
              key={category}
              onClick={() => setSelectedCategory(category)}
              colorScheme={selectedCategory === category ? 'blue' : 'gray'}
              variant={selectedCategory === category ? 'solid' : 'outline'}
              rightIcon={
                <Badge
                  size="sm"
                  colorScheme={selectedCategory === category ? 'white' : 'gray'}
                  variant="subtle"
                  fontSize="xs"
                  ml={1}
                >
                  {getCategoryCount(category)}
                </Badge>
              }
            >
              {CATEGORY_LABELS[category]}
            </Button>
          ))}
        </ButtonGroup>
      </Box>

      {/* Templates Grid */}
      <Box flex="1" minHeight="400px">
        {currentTemplates.length > 0 ? (
          <SimpleGrid
            columns={{ base: 1, md: 2, lg: 3, xl: 4 }}
            spacing={6}
            width="100%"
          >
            {currentTemplates.map((template) => (
              <ResumeTemplateCard
                key={template.id}
                template={template}
                isSelected={selectedTemplate?.id === template.id}
                onSelect={handleTemplateSelect}
                onPreview={handlePreview}
              />
            ))}
          </SimpleGrid>
        ) : (
          <Box
            textAlign="center"
            py={12}
            border="2px dashed"
            borderColor={borderColor}
            borderRadius="lg"
          >
            <Text color="gray.500" fontSize="lg">
              No templates found in this category
            </Text>
          </Box>
        )}
      </Box>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalItems}
        itemsPerPage={itemsPerPage}
        itemsPerPageOptions={ITEMS_PER_PAGE_OPTIONS}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
        showItemsPerPage={true}
        showSummary={true}
        size="md"
      />

      {/* Template Preview Modal */}
      {previewTemplate && (
        <ResumeTemplatePreview
          template={previewTemplate}
          isOpen={!!previewTemplate}
          onClose={closePreview}
          onSelect={() => {
            handleTemplateSelect(previewTemplate);
            closePreview();
          }}
          isSelected={selectedTemplate?.id === previewTemplate.id}
        />
      )}
    </VStack>
  );
};

export default ResumeTemplateGallery;
