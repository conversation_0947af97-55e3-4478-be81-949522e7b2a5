import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Heading,
  Text,
  Button,
  Icon,
  useColorModeValue,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Input,
  FormControl,
  FormLabel,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import { FaTrash, FaExclamationTriangle } from 'react-icons/fa';
import { deleteUserAccount } from 'wasp/client/operations';
import { logout } from 'wasp/client/auth';

interface DangerZoneCardProps {
  userInfo: any;
}

export default function DangerZoneCard({ userInfo }: DangerZoneCardProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [confirmText, setConfirmText] = useState('');
  const { isOpen, onOpen, onClose } = useDisclosure();

  const toast = useToast();

  const handleDeleteAccount = async () => {
    if (confirmText !== 'DELETE') {
      toast({
        title: 'Confirmation required',
        description: 'Please type "DELETE" to confirm account deletion.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setIsLoading(true);

    try {
      await deleteUserAccount();

      toast({
        title: 'Account deleted',
        description: 'Your account has been permanently deleted.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      // Logout and redirect
      setTimeout(() => {
        logout();
      }, 1000);
    } catch (error: any) {
      toast({
        title: 'Deletion failed',
        description: error.message || 'Failed to delete account. Please try again.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resetModal = () => {
    setConfirmText('');
    onClose();
  };

  return (
    <>
      <Box
        bg={useColorModeValue('white', 'gray.800')}
        borderRadius="xl"
        p={4}
        borderWidth="1px"
        borderColor={useColorModeValue('red.200', 'red.700')}
      >
        <HStack justify="space-between" align="center">
          <HStack spacing={3}>
            <Icon
              as={FaTrash}
              boxSize={4}
              color={useColorModeValue('red.500', 'red.400')}
            />
            <VStack align="start" spacing={0}>
              <Text fontWeight="medium" fontSize="sm" color={useColorModeValue('red.700', 'red.300')}>
                Delete Account
              </Text>
              <Text fontSize="xs" color={useColorModeValue('red.600', 'red.400')}>
                Permanently delete all data
              </Text>
            </VStack>
          </HStack>

          <Button
            variant="outline"
            size="sm"
            borderColor={useColorModeValue('red.300', 'red.600')}
            color={useColorModeValue('red.600', 'red.400')}
            _hover={{
              bg: useColorModeValue('red.50', 'red.900'),
              borderColor: useColorModeValue('red.400', 'red.500')
            }}
            borderRadius="md"
            onClick={onOpen}
            fontSize="xs"
            px={3}
            py={1}
            h="auto"
          >
            Delete
          </Button>
        </HStack>
      </Box>

      {/* Confirmation Modal */}
      <Modal isOpen={isOpen} onClose={resetModal} isCentered>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader color={useColorModeValue('red.600', 'red.400')}>
            <Icon as={FaExclamationTriangle} mr={2} />
            Delete Account
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4} align="stretch">
              <Alert status="error" borderRadius="md">
                <AlertIcon />
                <VStack align="start" spacing={1}>
                  <Text fontWeight="medium">This action is permanent!</Text>
                  <Text fontSize="sm">
                    All your data including cover letters, jobs, resumes, and progress will be permanently deleted.
                  </Text>
                </VStack>
              </Alert>

              <Box>
                <Text mb={2}>
                  Account to be deleted: <strong>{userInfo?.email}</strong>
                </Text>
                <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')} mb={4}>
                  This will delete:
                </Text>
                <VStack align="start" spacing={1} fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
                  <Text>• All cover letters and job applications</Text>
                  <Text>• Resume data and files</Text>
                  <Text>• Learning progress and badges</Text>
                  <Text>• Interview preparation data</Text>
                  <Text>• Account settings and preferences</Text>
                </VStack>
              </Box>

              <FormControl>
                <FormLabel fontSize="sm" fontWeight="medium">
                  Type "DELETE" to confirm:
                </FormLabel>
                <Input
                  value={confirmText}
                  onChange={(e) => setConfirmText(e.target.value)}
                  placeholder="DELETE"
                  borderColor={useColorModeValue('red.300', 'red.600')}
                  _focus={{ borderColor: 'red.500', boxShadow: '0 0 0 1px red.500' }}
                />
              </FormControl>
            </VStack>
          </ModalBody>

          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={resetModal}>
              Cancel
            </Button>
            <Button
              colorScheme="red"
              onClick={handleDeleteAccount}
              isLoading={isLoading}
              loadingText="Deleting..."
              isDisabled={confirmText !== 'DELETE'}
            >
              Delete Account
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}
