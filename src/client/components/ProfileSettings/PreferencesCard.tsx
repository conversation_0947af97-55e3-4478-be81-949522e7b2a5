import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Heading,
  Text,
  Switch,
  Button,
  Icon,
  useColorModeValue,
  useToast,
  useColorMode,
} from '@chakra-ui/react';
import { FaCog, FaCheck } from 'react-icons/fa';
import { updateUser } from 'wasp/client/operations';

interface PreferencesCardProps {
  userInfo: any;
  onUpdate: () => void;
}

export default function PreferencesCard({ userInfo, onUpdate }: PreferencesCardProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [preferences, setPreferences] = useState({
    darkMode: userInfo?.darkMode ?? false,
    wordCountDisplay: userInfo?.wordCountDisplay ?? true,
    autoSave: userInfo?.autoSave ?? true,
  });

  const toast = useToast();
  const { colorMode, toggleColorMode } = useColorMode();

  useEffect(() => {
    setPreferences({
      darkMode: userInfo?.darkMode ?? false,
      wordCountDisplay: userInfo?.wordCountDisplay ?? true,
      autoSave: userInfo?.autoSave ?? true,
    });
  }, [userInfo]);

  const handlePreferenceChange = (key: string, value: boolean) => {
    setPreferences(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);

    // Handle dark mode toggle immediately
    if (key === 'darkMode') {
      if ((value && colorMode === 'light') || (!value && colorMode === 'dark')) {
        toggleColorMode();
      }
    }
  };

  const handleSave = async () => {
    setIsLoading(true);

    try {
      await updateUser({
        darkMode: preferences.darkMode,
        wordCountDisplay: preferences.wordCountDisplay,
        autoSave: preferences.autoSave,
      });

      toast({
        title: 'Preferences updated',
        description: 'Your preferences have been saved successfully.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      setHasChanges(false);
      // Small delay to ensure database update is complete before refreshing
      setTimeout(() => {
        onUpdate();
      }, 100);
    } catch (error: any) {
      toast({
        title: 'Update failed',
        description: error.message || 'Failed to update preferences.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box
      bg={useColorModeValue('white', 'gray.800')}
      borderRadius="2xl"
      p={6}
      boxShadow="xl"
      borderWidth="1px"
      borderColor={useColorModeValue('gray.100', 'gray.700')}
      transition="all 0.3s ease"
      _hover={{
        transform: 'translateY(-2px)',
        boxShadow: '2xl',
        borderColor: useColorModeValue('blue.200', 'blue.600')
      }}
    >
      <HStack spacing={4} mb={4}>
        <Box
          p={3}
          borderRadius="xl"
          bg={useColorModeValue('blue.50', 'blue.900')}
          color={useColorModeValue('blue.600', 'blue.300')}
        >
          <Icon as={FaCog} boxSize={5} />
        </Box>
        <VStack align="start" spacing={0}>
          <Heading size="md" color={useColorModeValue('gray.800', 'white')}>
            Preferences
          </Heading>
          <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
            Customize your experience
          </Text>
        </VStack>
      </HStack>

      <VStack spacing={4} align="stretch">
        {/* Dark Mode */}
        <HStack justify="space-between" p={4} borderRadius="xl" bg={useColorModeValue('gray.50', 'gray.700')}>
          <VStack align="start" spacing={1}>
            <Text fontWeight="medium">Dark Mode</Text>
            <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
              Toggle dark theme
            </Text>
          </VStack>
          <Switch
            colorScheme="blue"
            size="lg"
            isChecked={preferences.darkMode}
            onChange={(e) => handlePreferenceChange('darkMode', e.target.checked)}
          />
        </HStack>

        {/* Word Count Display */}
        <HStack justify="space-between" p={4} borderRadius="xl" bg={useColorModeValue('gray.50', 'gray.700')}>
          <VStack align="start" spacing={1}>
            <Text fontWeight="medium">Word Count Display</Text>
            <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
              Show cover letter word count
            </Text>
          </VStack>
          <Switch
            colorScheme="blue"
            size="lg"
            isChecked={preferences.wordCountDisplay}
            onChange={(e) => handlePreferenceChange('wordCountDisplay', e.target.checked)}
          />
        </HStack>

        {/* Auto-save */}
        <HStack justify="space-between" p={4} borderRadius="xl" bg={useColorModeValue('gray.50', 'gray.700')}>
          <VStack align="start" spacing={1}>
            <Text fontWeight="medium">Auto-save</Text>
            <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
              Automatically save cover letters
            </Text>
          </VStack>
          <Switch
            colorScheme="blue"
            size="lg"
            isChecked={preferences.autoSave}
            onChange={(e) => handlePreferenceChange('autoSave', e.target.checked)}
          />
        </HStack>

        {hasChanges && (
          <Button
            colorScheme="blue"
            size="sm"
            leftIcon={<Icon as={FaCheck} />}
            onClick={handleSave}
            isLoading={isLoading}
            loadingText="Saving..."
            alignSelf="flex-start"
            mt={2}
          >
            Save Preferences
          </Button>
        )}
      </VStack>
    </Box>
  );
}
