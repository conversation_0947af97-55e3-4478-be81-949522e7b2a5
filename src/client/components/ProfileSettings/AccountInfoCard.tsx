import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Heading,
  Text,
  FormControl,
  FormLabel,
  Input,
  Button,
  Icon,
  useColorModeValue,
  useToast,
  Textarea,
  FormErrorMessage,
} from '@chakra-ui/react';
import { FaUser, FaEdit, FaCheck, FaTimes } from 'react-icons/fa';
import { updateUser } from 'wasp/client/operations';

interface AccountInfoCardProps {
  userInfo: any;
  onUpdate: () => void;
}

export default function AccountInfoCard({ userInfo, onUpdate }: AccountInfoCardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    username: userInfo?.username || '',
    bio: userInfo?.bio || '',
    yearsOfExperience: userInfo?.yearsOfExperience || 0,
  });
  const [errors, setErrors] = useState<any>({});

  const toast = useToast();

  const handleSave = async () => {
    setIsLoading(true);
    setErrors({});

    try {
      // Validate username
      if (!formData.username.trim()) {
        setErrors({ username: 'Username is required' });
        setIsLoading(false);
        return;
      }

      await updateUser({
        username: formData.username.trim(),
        bio: formData.bio.trim(),
        yearsOfExperience: Number(formData.yearsOfExperience),
      });

      toast({
        title: 'Profile updated',
        description: 'Your profile information has been updated successfully.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      setIsEditing(false);
      // Small delay to ensure database update is complete before refreshing
      setTimeout(() => {
        onUpdate();
      }, 100);
    } catch (error: any) {
      toast({
        title: 'Update failed',
        description: error.message || 'Failed to update profile information.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      username: userInfo?.username || '',
      bio: userInfo?.bio || '',
      yearsOfExperience: userInfo?.yearsOfExperience || 0,
    });
    setErrors({});
    setIsEditing(false);
  };

  return (
    <Box
      bg={useColorModeValue('white', 'gray.800')}
      borderRadius="2xl"
      p={6}
      boxShadow="xl"
      borderWidth="1px"
      borderColor={useColorModeValue('gray.100', 'gray.700')}
      transition="all 0.3s ease"
      _hover={{
        transform: 'translateY(-2px)',
        boxShadow: '2xl',
        borderColor: useColorModeValue('blue.200', 'blue.600')
      }}
    >
      <HStack spacing={4} mb={4}>
        <Box
          p={3}
          borderRadius="xl"
          bg={useColorModeValue('blue.50', 'blue.900')}
          color={useColorModeValue('blue.600', 'blue.300')}
        >
          <Icon as={FaUser} boxSize={5} />
        </Box>
        <VStack align="start" spacing={0} flex={1}>
          <Heading size="md" color={useColorModeValue('gray.800', 'white')}>
            Account Information
          </Heading>
          <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
            Manage your personal details
          </Text>
        </VStack>
        <Button
          variant="outline"
          size="sm"
          leftIcon={<Icon as={isEditing ? FaTimes : FaEdit} />}
          onClick={isEditing ? handleCancel : () => setIsEditing(true)}
          borderColor={useColorModeValue('gray.300', 'gray.600')}
          color={useColorModeValue('gray.600', 'gray.400')}
          _hover={{
            bg: useColorModeValue('gray.50', 'gray.700'),
            borderColor: useColorModeValue('gray.400', 'gray.500')
          }}
          borderRadius="md"
        >
          {isEditing ? 'Cancel' : 'Edit'}
        </Button>
      </HStack>

      <VStack spacing={4} align="stretch">
        {/* Email (Read-only) */}
        <FormControl>
          <FormLabel
            fontSize="sm"
            fontWeight="semibold"
            color={useColorModeValue('gray.700', 'gray.300')}
            mb={2}
          >
            Email Address
          </FormLabel>
          <Input
            value={userInfo?.email || ''}
            isReadOnly
            bg={useColorModeValue('gray.50', 'gray.700')}
            borderRadius="xl"
            borderColor={useColorModeValue('gray.200', 'gray.600')}
          />
        </FormControl>

        {/* Username */}
        <FormControl isInvalid={!!errors.username}>
          <FormLabel
            fontSize="sm"
            fontWeight="semibold"
            color={useColorModeValue('gray.700', 'gray.300')}
            mb={2}
          >
            Display Name
          </FormLabel>
          <Input
            value={formData.username}
            onChange={(e) => setFormData({ ...formData, username: e.target.value })}
            isReadOnly={!isEditing}
            bg={isEditing ? 'transparent' : useColorModeValue('gray.50', 'gray.700')}
            borderRadius="xl"
            borderColor={useColorModeValue('gray.200', 'gray.600')}
            _focus={{ borderColor: 'blue.400', boxShadow: '0 0 0 1px blue.400' }}
          />
          <FormErrorMessage>{errors.username}</FormErrorMessage>
        </FormControl>

        {/* Bio */}
        <FormControl>
          <FormLabel
            fontSize="sm"
            fontWeight="semibold"
            color={useColorModeValue('gray.700', 'gray.300')}
            mb={2}
          >
            Bio
          </FormLabel>
          <Textarea
            value={formData.bio}
            onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
            isReadOnly={!isEditing}
            bg={isEditing ? 'transparent' : useColorModeValue('gray.50', 'gray.700')}
            borderRadius="xl"
            borderColor={useColorModeValue('gray.200', 'gray.600')}
            _focus={{ borderColor: 'blue.400', boxShadow: '0 0 0 1px blue.400' }}
            placeholder="Tell us about yourself..."
            rows={3}
          />
        </FormControl>

        {/* Years of Experience */}
        <FormControl>
          <FormLabel
            fontSize="sm"
            fontWeight="semibold"
            color={useColorModeValue('gray.700', 'gray.300')}
            mb={2}
          >
            Years of Experience
          </FormLabel>
          <Input
            type="number"
            value={formData.yearsOfExperience}
            onChange={(e) => setFormData({ ...formData, yearsOfExperience: parseInt(e.target.value) || 0 })}
            isReadOnly={!isEditing}
            bg={isEditing ? 'transparent' : useColorModeValue('gray.50', 'gray.700')}
            borderRadius="xl"
            borderColor={useColorModeValue('gray.200', 'gray.600')}
            _focus={{ borderColor: 'blue.400', boxShadow: '0 0 0 1px blue.400' }}
            min={0}
            max={50}
          />
        </FormControl>

        {isEditing && (
          <HStack spacing={2} pt={2}>
            <Button
              colorScheme="blue"
              size="sm"
              leftIcon={<Icon as={FaCheck} />}
              onClick={handleSave}
              isLoading={isLoading}
              loadingText="Saving..."
            >
              Save Changes
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCancel}
              isDisabled={isLoading}
            >
              Cancel
            </Button>
          </HStack>
        )}
      </VStack>
    </Box>
  );
}
