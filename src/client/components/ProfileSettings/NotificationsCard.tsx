import React, { useState, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Heading,
  Text,
  Switch,
  Button,
  Icon,
  useColorModeValue,
  useToast,
} from '@chakra-ui/react';
import { FaBell, FaCheck } from 'react-icons/fa';
import { updateUser } from 'wasp/client/operations';

interface NotificationsCardProps {
  userInfo: any;
  onUpdate: () => void;
}

export default function NotificationsCard({ userInfo, onUpdate }: NotificationsCardProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [notifications, setNotifications] = useState({
    jobReminders: userInfo?.jobReminders ?? true,
    featureUpdates: userInfo?.featureUpdates ?? true,
    subscriptionReminders: userInfo?.subscriptionReminders ?? true,
    marketingEmails: userInfo?.marketingEmails ?? false,
  });

  const toast = useToast();

  useEffect(() => {
    setNotifications({
      jobReminders: userInfo?.jobReminders ?? true,
      featureUpdates: userInfo?.featureUpdates ?? true,
      subscriptionReminders: userInfo?.subscriptionReminders ?? true,
      marketingEmails: userInfo?.marketingEmails ?? false,
    });
  }, [userInfo]);

  const handleNotificationChange = (key: string, value: boolean) => {
    setNotifications(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    setIsLoading(true);

    try {
      await updateUser({
        jobReminders: notifications.jobReminders,
        featureUpdates: notifications.featureUpdates,
        subscriptionReminders: notifications.subscriptionReminders,
        marketingEmails: notifications.marketingEmails,
      });

      toast({
        title: 'Notification settings updated',
        description: 'Your notification preferences have been saved successfully.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      setHasChanges(false);
      // Small delay to ensure database update is complete before refreshing
      setTimeout(() => {
        onUpdate();
      }, 100);
    } catch (error: any) {
      toast({
        title: 'Update failed',
        description: error.message || 'Failed to update notification settings.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box
      bg={useColorModeValue('white', 'gray.800')}
      borderRadius="2xl"
      p={6}
      boxShadow="xl"
      borderWidth="1px"
      borderColor={useColorModeValue('gray.100', 'gray.700')}
      transition="all 0.3s ease"
      _hover={{
        transform: 'translateY(-2px)',
        boxShadow: '2xl',
        borderColor: useColorModeValue('blue.200', 'blue.600')
      }}
    >
      <HStack spacing={4} mb={4}>
        <Box
          p={3}
          borderRadius="xl"
          bg={useColorModeValue('blue.50', 'blue.900')}
          color={useColorModeValue('blue.600', 'blue.300')}
        >
          <Icon as={FaBell} boxSize={5} />
        </Box>
        <VStack align="start" spacing={0}>
          <Heading size="md" color={useColorModeValue('gray.800', 'white')}>
            Notifications
          </Heading>
          <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
            Manage your email preferences
          </Text>
        </VStack>
      </HStack>

      <VStack spacing={4} align="stretch">
        {/* Job Application Reminders */}
        <HStack justify="space-between" p={4} borderRadius="xl" bg={useColorModeValue('gray.50', 'gray.700')}>
          <VStack align="start" spacing={1}>
            <Text fontWeight="medium">Job Application Reminders</Text>
            <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
              Get reminded about pending applications
            </Text>
          </VStack>
          <Switch
            colorScheme="blue"
            size="lg"
            isChecked={notifications.jobReminders}
            onChange={(e) => handleNotificationChange('jobReminders', e.target.checked)}
          />
        </HStack>

        {/* Feature Updates */}
        <HStack justify="space-between" p={4} borderRadius="xl" bg={useColorModeValue('gray.50', 'gray.700')}>
          <VStack align="start" spacing={1}>
            <Text fontWeight="medium">Feature Updates</Text>
            <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
              Stay informed about new features
            </Text>
          </VStack>
          <Switch
            colorScheme="blue"
            size="lg"
            isChecked={notifications.featureUpdates}
            onChange={(e) => handleNotificationChange('featureUpdates', e.target.checked)}
          />
        </HStack>

        {/* Subscription Reminders */}
        <HStack justify="space-between" p={4} borderRadius="xl" bg={useColorModeValue('gray.50', 'gray.700')}>
          <VStack align="start" spacing={1}>
            <Text fontWeight="medium">Subscription Reminders</Text>
            <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
              Renewal and billing notifications
            </Text>
          </VStack>
          <Switch
            colorScheme="blue"
            size="lg"
            isChecked={notifications.subscriptionReminders}
            onChange={(e) => handleNotificationChange('subscriptionReminders', e.target.checked)}
          />
        </HStack>

        {/* Marketing Emails */}
        <HStack justify="space-between" p={4} borderRadius="xl" bg={useColorModeValue('gray.50', 'gray.700')}>
          <VStack align="start" spacing={1}>
            <Text fontWeight="medium">Marketing Emails</Text>
            <Text fontSize="sm" color={useColorModeValue('gray.600', 'gray.400')}>
              Tips, guides, and promotional content
            </Text>
          </VStack>
          <Switch
            colorScheme="blue"
            size="lg"
            isChecked={notifications.marketingEmails}
            onChange={(e) => handleNotificationChange('marketingEmails', e.target.checked)}
          />
        </HStack>

        {hasChanges && (
          <Button
            colorScheme="blue"
            size="sm"
            leftIcon={<Icon as={FaCheck} />}
            onClick={handleSave}
            isLoading={isLoading}
            loadingText="Saving..."
            alignSelf="flex-start"
            mt={2}
          >
            Update Settings
          </Button>
        )}
      </VStack>
    </Box>
  );
}
