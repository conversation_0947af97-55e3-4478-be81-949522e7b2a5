import React, { useState, useEffect } from 'react';
import {
  Box,
  AspectRatio,
  useColorModeValue,
  Text,
  VStack,
  Icon,
  Button,
  Alert,
  AlertIcon
} from '@chakra-ui/react';
import { FaPlay, FaExternalLinkAlt, FaExclamationTriangle } from 'react-icons/fa';

interface VideoEmbedProps {
  videoUrl: string;
  videoType: 'youtube' | 'tiktok';
  title: string;
  showPreview?: boolean;
  onPlay?: () => void;
  onVideoLoad?: () => void;
  onVideoError?: () => void;
}

const VideoEmbed: React.FC<VideoEmbedProps> = ({
  videoUrl,
  videoType,
  title,
  showPreview = false,
  onPlay,
  onVideoLoad,
  onVideoError
}) => {
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [thumbnailUrl, setThumbnailUrl] = useState<string>('');
  const [videoNotFound, setVideoNotFound] = useState(false);
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const bgColor = useColorModeValue('gray.50', 'gray.800');

  // Extract video ID for thumbnail generation
  const getVideoId = (url: string, type: 'youtube' | 'tiktok'): string => {
    if (type === 'youtube') {
      const match = url.match(/embed\/([a-zA-Z0-9_-]+)/);
      return match ? match[1] : '';
    }
    return '';
  };

  const videoId = getVideoId(videoUrl, videoType);

  // YouTube thumbnail quality fallback order
  const thumbnailQualities = [
    'maxresdefault', // 1280x720
    'hqdefault',     // 480x360
    'mqdefault',     // 320x180
    'default'        // 120x90
  ];

  useEffect(() => {
    if (videoType !== 'youtube' || !videoId) {
      setIsLoading(false);
      return;
    }

    const tryThumbnail = (qualityIndex: number) => {
      if (qualityIndex >= thumbnailQualities.length) {
        // If all thumbnail qualities fail, mark as not found
        setVideoNotFound(true);
        setHasError(true);
        setIsLoading(false);
        onVideoError?.();
        return;
      }

      const quality = thumbnailQualities[qualityIndex];
      const url = `https://img.youtube.com/vi/${videoId}/${quality}.jpg`;

      const img = new Image();
      img.onload = () => {
        // Check if the image is actually valid (not a placeholder)
        if (img.naturalWidth > 120 || qualityIndex === thumbnailQualities.length - 1) {
          setThumbnailUrl(url);
          setIsLoading(false);
          setHasError(false);
          onVideoLoad?.();
        } else {
          // Try next quality
          tryThumbnail(qualityIndex + 1);
        }
      };
      img.onerror = () => {
        // Try next quality
        tryThumbnail(qualityIndex + 1);
      };
      img.src = url;
    };

    tryThumbnail(0);
  }, [videoId, videoType, onVideoLoad, onVideoError, thumbnailQualities]);



  if (showPreview) {
    return (
      <Box
        position="relative"
        borderRadius="md"
        overflow="hidden"
        bg={bgColor}
        border="1px"
        borderColor={borderColor}
        cursor="pointer"
        onClick={onPlay}
        _hover={{ transform: 'scale(1.02)', transition: 'transform 0.2s' }}
      >
        <AspectRatio ratio={16 / 9}>
          {isLoading ? (
            <VStack spacing={3} justify="center" p={4}>
              <Text fontSize="sm" color="gray.500">Loading...</Text>
            </VStack>
          ) : hasError || !thumbnailUrl ? (
            <VStack spacing={3} justify="center" p={4}>
              <Icon as={FaExclamationTriangle} boxSize={8} color={videoNotFound ? "red.500" : "orange.500"} />
              <Text fontSize="sm" textAlign="center" fontWeight="medium">
                {videoType === 'tiktok'
                  ? 'TikTok Video'
                  : videoNotFound
                    ? 'Video Not Found'
                    : 'Video thumbnail not available'
                }
              </Text>
              <Text fontSize="xs" color="gray.500" textAlign="center">
                {videoType === 'tiktok'
                  ? 'Click to play'
                  : videoNotFound
                    ? 'This video may have been removed or made private'
                    : `Click to play: ${title}`
                }
              </Text>
            </VStack>
          ) : (
            <Box
              backgroundImage={`url(${thumbnailUrl})`}
              backgroundSize="cover"
              backgroundPosition="center"
              display="flex"
              alignItems="center"
              justifyContent="center"
            >
              <Box
                bg="blackAlpha.700"
                borderRadius="full"
                p={4}
                color="white"
                _hover={{ bg: 'blackAlpha.800' }}
              >
                <Icon as={FaPlay} boxSize={6} />
              </Box>
            </Box>
          )}
        </AspectRatio>
      </Box>
    );
  }

  return (
    <Box borderRadius="md" overflow="hidden" bg={bgColor}>
      <AspectRatio ratio={videoType === 'tiktok' ? 9 / 16 : 16 / 9}>
        {videoType === 'youtube' ? (
          videoNotFound ? (
            <VStack spacing={4} justify="center" p={6}>
              <Icon as={FaExclamationTriangle} boxSize={12} color="red.500" />
              <Text fontSize="lg" textAlign="center" fontWeight="bold" color="red.500">
                Video Not Found
              </Text>
              <Text fontSize="sm" color="gray.500" textAlign="center">
                This video may have been removed, made private, or the URL is incorrect.
              </Text>
              <Alert status="error" borderRadius="md">
                <AlertIcon />
                <Text fontSize="sm">
                  Video ID: {videoId}
                </Text>
              </Alert>
            </VStack>
          ) : (
            <iframe
              src={videoUrl}
              title={title}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              style={{
                border: 'none',
                width: '100%',
                height: '100%'
              }}
            />
          )
        ) : (
          <Box
            display="flex"
            alignItems="center"
            justifyContent="center"
            flexDirection="column"
            p={4}
            textAlign="center"
          >
            <Icon as={FaExternalLinkAlt} boxSize={8} color="purple.500" mb={3} />
            <Text fontSize="sm" fontWeight="medium" mb={2}>
              TikTok Video
            </Text>
            <Text fontSize="xs" color="gray.500" mb={4}>
              Click to view on TikTok
            </Text>
            <Button
              size="sm"
              colorScheme="purple"
              leftIcon={<FaExternalLinkAlt />}
              onClick={() => window.open(videoUrl.replace('/embed/v2/', '/'), '_blank')}
            >
              Open TikTok
            </Button>
          </Box>
        )}
      </AspectRatio>
    </Box>
  );
};

export default VideoEmbed;
