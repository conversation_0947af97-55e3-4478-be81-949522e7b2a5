import { Box, Text, HStack, VStack, useColorModeValue, Image, Badge } from '@chakra-ui/react';
import { motion } from 'framer-motion';

const MotionBox = motion(Box);
const MotionPath = motion.path;

interface LogoProps {
  variant?: 'animated' | 'static' | 'icon' | 'minimized';
  size?: 'sm' | 'md' | 'lg';
}

// React SVG Icon Component with dynamic colors
function CareerDartIcon({ size = "50px", cColor, dartColor }: { size?: string; cColor: string; dartColor: string }) {
  return (
    <Box width={size} height={size} display="inline-block">
      <svg
        width={size}
        height={size}
        viewBox="0 0 500 500"
        xmlns="http://www.w3.org/2000/svg"
        style={{ display: 'block' }}
      >
        <defs>
          {/* Define a mask for the left half (C part) */}
          <mask id="leftHalf">
            <rect x="0" y="0" width="250" height="500" fill="white" />
          </mask>
          {/* Define a mask for the right half (dart part) */}
          <mask id="rightHalf">
            <rect x="250" y="0" width="250" height="500" fill="white" />
          </mask>
        </defs>
        
        {/* Original complete path with left half in Career color */}
        <path
          d="M 235.348 10.420 C 221.654 13.741, 209.945 23.229, 203.602 36.142 C 195.470 52.697, 196.585 70.321, 206.740 85.751 C 211.473 92.942, 218.028 98.457, 226.932 102.740 C 233.658 105.975, 235.475 106.394, 244.537 106.797 C 260.252 107.496, 271.635 103.270, 282.026 92.879 C 291.722 83.183, 296 72.637, 296 58.433 C 296 43.065, 291.525 32.515, 280.639 22.220 C 268.772 10.997, 251.510 6.499, 235.348 10.420 M 237.500 131.107 C 192.321 135.857, 155.629 152.217, 125.623 180.988 C 80.736 224.027, 62.278 285.218, 75.095 348.500 C 86.787 406.234, 131.691 458.712, 187 479.282 C 209.077 487.493, 228.598 489.974, 271.250 489.990 L 299 490 299 464 L 299 438 266.110 438 C 223.377 438, 215.626 436.606, 192.962 424.844 C 164.023 409.826, 140.297 382.061, 130.039 351.211 C 125.027 336.136, 123.703 327.870, 123.678 311.500 C 123.653 295.224, 125.176 284.842, 129.558 271.419 C 139.503 240.952, 160.686 215.121, 188.839 199.128 C 199.600 193.015, 213.298 187.889, 227 184.846 C 241.520 181.622, 266.542 181.627, 281.734 184.856 C 311.426 191.168, 334.643 205.349, 351.451 227.440 C 367.160 248.087, 375.057 275.600, 372.219 299.791 C 367.699 338.312, 343.661 364.265, 310.250 366.697 L 302 367.297 302 393.298 L 302 419.300 312.750 418.710 C 336.722 417.396, 361.280 407.534, 379.184 392.032 C 411.307 364.220, 428.400 320.439, 424.194 276.750 C 419.471 227.689, 394.248 185.451, 353.500 158.363 C 321.356 136.995, 278.120 126.836, 237.500 131.107 M 246 312.441 C 246 383.400, 246.338 419.091, 247.015 419.510 C 247.574 419.855, 259.274 419.815, 273.015 419.420 L 298 418.704 298 357.852 L 298 297 323.083 297 C 338.127 297, 348.033 296.628, 347.833 296.070 C 346.955 293.619, 248.678 206.068, 246.750 206.019 C 246.338 206.009, 246 253.898, 246 312.441"
          fill={cColor}
          stroke={cColor}
          strokeWidth="3"
          fillRule="evenodd"
          mask="url(#leftHalf)"
        />
        
        {/* Original complete path with right half in dart color */}
        <path
          d="M 235.348 10.420 C 221.654 13.741, 209.945 23.229, 203.602 36.142 C 195.470 52.697, 196.585 70.321, 206.740 85.751 C 211.473 92.942, 218.028 98.457, 226.932 102.740 C 233.658 105.975, 235.475 106.394, 244.537 106.797 C 260.252 107.496, 271.635 103.270, 282.026 92.879 C 291.722 83.183, 296 72.637, 296 58.433 C 296 43.065, 291.525 32.515, 280.639 22.220 C 268.772 10.997, 251.510 6.499, 235.348 10.420 M 237.500 131.107 C 192.321 135.857, 155.629 152.217, 125.623 180.988 C 80.736 224.027, 62.278 285.218, 75.095 348.500 C 86.787 406.234, 131.691 458.712, 187 479.282 C 209.077 487.493, 228.598 489.974, 271.250 489.990 L 299 490 299 464 L 299 438 266.110 438 C 223.377 438, 215.626 436.606, 192.962 424.844 C 164.023 409.826, 140.297 382.061, 130.039 351.211 C 125.027 336.136, 123.703 327.870, 123.678 311.500 C 123.653 295.224, 125.176 284.842, 129.558 271.419 C 139.503 240.952, 160.686 215.121, 188.839 199.128 C 199.600 193.015, 213.298 187.889, 227 184.846 C 241.520 181.622, 266.542 181.627, 281.734 184.856 C 311.426 191.168, 334.643 205.349, 351.451 227.440 C 367.160 248.087, 375.057 275.600, 372.219 299.791 C 367.699 338.312, 343.661 364.265, 310.250 366.697 L 302 367.297 302 393.298 L 302 419.300 312.750 418.710 C 336.722 417.396, 361.280 407.534, 379.184 392.032 C 411.307 364.220, 428.400 320.439, 424.194 276.750 C 419.471 227.689, 394.248 185.451, 353.500 158.363 C 321.356 136.995, 278.120 126.836, 237.500 131.107 M 246 312.441 C 246 383.400, 246.338 419.091, 247.015 419.510 C 247.574 419.855, 259.274 419.815, 273.015 419.420 L 298 418.704 298 357.852 L 298 297 323.083 297 C 338.127 297, 348.033 296.628, 347.833 296.070 C 346.955 293.619, 248.678 206.068, 246.750 206.019 C 246.338 206.009, 246 253.898, 246 312.441"
          fill={dartColor}
          stroke={dartColor}
          strokeWidth="3"
          fillRule="evenodd"
          mask="url(#rightHalf)"
        />
        
        {/* Vertical white line separator in the center */}
        <line
          x1="250"
          y1="0"
          x2="250"
          y2="500"
          stroke="white"
          strokeWidth="3"
          opacity="0.8"
        />
      </svg>
    </Box>
  );
}

export default function Logo({ variant = 'minimized', size = 'md' }: LogoProps) {
  const textColor = useColorModeValue('gray.800', 'white');
  const primaryColor = useColorModeValue('blue.600', 'blue.300');
  const secondaryColor = useColorModeValue('cyan.600', 'cyan.400');
  const dartGradient = useColorModeValue(
    'linear(135deg, blue.500 0%, cyan.600 100%)',
    'linear(135deg, blue.400 0%, cyan.500 100%)'
  );
  const pathColor = useColorModeValue('blue.200', 'blue.600');
  const glowColor = useColorModeValue('blue.400', 'blue.300');

  // Enhanced colors for the icon with better contrast
  const leftSideColor = useColorModeValue('gray.700', 'white'); // Left 'C' part - white in dark mode
  const rightSideColor = useColorModeValue('blue.500', 'white'); // Right dart part - white in dark mode

  const sizeConfig = {
    sm: { width: '120px', height: '60px', iconSize: '30px', fontSize: 'md' },
    md: { width: '200px', height: '120px', iconSize: '40px', fontSize: 'xl' },
    lg: { width: '300px', height: '180px', iconSize: '60px', fontSize: '2xl' }
  };

  const currentSize = sizeConfig[size];

  // Static logo variant using the new SVG
  if (variant === 'static') {
    return (
      <Box width={currentSize.width} height={currentSize.height}>
        <Image
          src="/images/careerdart_logo.svg"
          alt="CareerDart - Your Personal Career Companion"
          width={currentSize.width}
          height={currentSize.height}
          objectFit="contain"
        />
      </Box>
    );
  }

  // Icon-only variant
  if (variant === 'icon') {
    return (
      <Box width={currentSize.iconSize} height={currentSize.iconSize}>
        <CareerDartIcon 
          size={currentSize.iconSize} 
          cColor={leftSideColor} 
          dartColor={rightSideColor} 
        />
      </Box>
    );
  }

  // Minimized logo icon with text beside it
  if (variant === 'minimized') {
    return (
      <Box position="relative">
        <HStack spacing={3} alignItems="center">
          {/* Bring back the 2-colored CareerDart icon */}
          <CareerDartIcon
            size="40px"
            cColor={leftSideColor}
            dartColor={rightSideColor}
          />

          {/* Brand Text - Clean style */}
          <Text
            fontSize="24px"
            color={textColor}
            letterSpacing="-0.01em"
            lineHeight="1"
            fontFamily="Montserrat, -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif"
          >
            <Text
              as="span"
              color={useColorModeValue('gray.900', 'white')}
              fontWeight="700"
            >
              career
            </Text>
            <Text
              as="span"
              color={useColorModeValue('blue.600', 'blue.400')}
              fontWeight="400"
              ml="1px"
            >
              dart
            </Text>
          </Text>
        </HStack>

        {/* Beta Badge positioned at top-right */}
        <Badge
          position="absolute"
          top="-8px"
          right="-6px"
          colorScheme="blue"
          variant="solid"
          fontSize="2xs"
          px={1}
          py={0.5}
          borderRadius="full"
          textTransform="uppercase"
          fontWeight="500"
          boxShadow="xs"
          zIndex={10}
          minH="auto"
          lineHeight="1"
          h="14px"
          minW="24px"
        >
          Beta
        </Badge>
      </Box>
    );
  }

  // Default fallback to static
  return (
    <Box width={currentSize.width} height={currentSize.height}>
      <Image
        src="/images/careerdart_logo.svg"
        alt="CareerDart - Your Personal Career Companion"
        width={currentSize.width}
        height={currentSize.height}
        objectFit="contain"
      />
    </Box>
  );

  // Animated variant (original) - keeping for reference but not used by default
  /*
  return (
    <VStack spacing={0} alignItems="flex-start">
      <HStack spacing={3} alignItems="center">
        <MotionBox
          position="relative"
          width="40px"
          height="40px"
          initial={{ scale: 1 }}
          animate={{
            scale: [1, 1.02, 1],
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <Box
            position="absolute"
            width="40px"
            height="40px"
            borderRadius="50%"
            bgGradient={dartGradient}
            boxShadow={`0 0 20px ${glowColor}25, 0 4px 15px rgba(0,0,0,0.1)`}
            border="2px solid"
            borderColor="white"
            _dark={{ borderColor: "gray.700" }}
          />

  */
}