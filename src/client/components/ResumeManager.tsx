import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Button,
  FormControl,
  Heading,
  HStack,
  Input,
  Text,
  VStack,
  useToast,
  Spinner,
  List,
  ListItem,
  Grid,
  GridItem,
  Tooltip,
  useColorModeValue,
  IconButton,
  Select,
  Flex,
  Badge,
  useBreakpointValue,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,

} from '@chakra-ui/react';
import { DownloadIcon, CloseIcon } from '@chakra-ui/icons';
import { FaTrash } from 'react-icons/fa';
import { FaFileAlt, FaFilePdf, FaFileWord, FaFileImage, FaUpload, FaPlus, FaPrint, FaChevronLeft, FaChevronRight, FaEye } from 'react-icons/fa';
import { type Resume, type ResumeTemplate } from '../../shared/types';
import { getUserResumes, createResume, deleteResume, updateResume, parsePDFContent } from 'wasp/client/operations';
import { useQuery } from 'wasp/client/operations';
import { useAuth } from 'wasp/client/auth';
import ActionButton from '../components/ActionButton';
import ContentContainer from '../components/ContentContainer';
import * as pdfjsLib from 'pdfjs-dist';
import ResumeForm from './ResumeForm';
import { EmptyResumes } from './EmptyState';
import { generateResumePDF, printResume, saveResumeAsPDF } from '../utils/pdfGenerator';
import { SimpleLoading } from './LoadingState';
import { GeneralError } from './ErrorState';
import ResumeTemplateGallery from './ResumeTemplateGallery';
import { getTemplateById } from '../data/resumeTemplates';
import { useTemplateManager } from '../utils/templateManager';
import Pagination from './Pagination';

type CreationStep = 'template' | 'form';

interface ResumeManagerProps {
  onFileUpload?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onCreateResume?: () => void;
  onCancelCreate?: () => void;
  isCreatingResume?: boolean;
  fileInputRef?: React.RefObject<HTMLInputElement>;
}

const ResumeManager: React.FC<ResumeManagerProps> = ({
  onFileUpload: externalOnFileUpload,
  onCreateResume: externalOnCreateResume,
  onCancelCreate: externalOnCancelCreate,
  isCreatingResume: externalIsCreatingResume,
  fileInputRef: externalFileInputRef
}) => {
  const [selectedResume, setSelectedResume] = useState<Resume | null>(null);
  const [isPdfReady, setIsPdfReady] = useState<boolean>(false);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [fileType, setFileType] = useState<string | null>(null);
  const [pdfScale, setPdfScale] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [internalIsCreatingResume, setInternalIsCreatingResume] = useState(false);
  const [creationStep, setCreationStep] = useState<CreationStep>('template');
  const [selectedTemplate, setSelectedTemplate] = useState<ResumeTemplate | null>(null);
  const [resumeCurrentPage, setResumeCurrentPage] = useState(1);
  const [resumeItemsPerPage, setResumeItemsPerPage] = useState(10);
  const internalFileInputRef = useRef<HTMLInputElement>(null);

  // Modal state for resume preview
  const { isOpen: isViewModalOpen, onOpen: onViewModalOpen, onClose: onViewModalClose } = useDisclosure();

  // Use external props if provided, otherwise use internal state
  // However, if we're editing a template (selectedResume exists and no fileData), prioritize internal state
  const isCreatingResume = (selectedResume && !selectedResume.fileData && internalIsCreatingResume)
    ? internalIsCreatingResume
    : (externalIsCreatingResume !== undefined ? externalIsCreatingResume : internalIsCreatingResume);
  const fileInputRef = externalFileInputRef || internalFileInputRef;
  const toast = useToast();
  const pdfContainerRef = useRef<HTMLDivElement>(null);
  const { data: user } = useAuth();

  // Use template manager for proper template isolation
  const templateManager = useTemplateManager();

  // Fetch user's resumes only if user is authenticated
  const { data: resumes, isLoading, error, refetch } = useQuery(getUserResumes, {}, { enabled: !!user });

  // Responsive values - moved to top to avoid conditional hook calls
  const buttonSize = useBreakpointValue({ base: 'sm', md: 'md' });
  const spacing = useBreakpointValue({ base: 3, md: 4 });

  // Color mode values - moved to top to avoid conditional hook calls
  const topSectionBg = useColorModeValue('gray.50', 'gray.800');
  const topSectionBorderColor = useColorModeValue('gray.200', 'gray.600');
  const selectLabelColor = useColorModeValue('gray.700', 'gray.300');
  const selectBg = useColorModeValue('white', 'gray.700');
  const selectBorderColor = useColorModeValue('gray.300', 'gray.600');
  const containerBorderColor = useColorModeValue('gray.200', 'gray.600');
  const templateSelectedBg = useColorModeValue('purple.50', 'purple.900');
  const templateSelectedBorderColor = useColorModeValue('purple.200', 'purple.600');

  // Select the first resume by default when data is loaded
  useEffect(() => {
    if (resumes && resumes.length > 0 && !selectedResume) {
      handleSelectResume(resumes[0]);
    }
  }, [resumes, selectedResume]);

  const handleFileButtonClick = () => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to upload resumes.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const getFileIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'application/pdf':
        return <FaFilePdf />;
      case 'application/msword':
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return <FaFileWord />;
      case 'image/jpeg':
      case 'image/png':
      case 'image/gif':
        return <FaFileImage />;
      default:
        return <FaFileAlt />;
    }
  };

  const handleSelectResume = async (resume: Resume) => {
    setSelectedResume(resume);

    // Use template manager to get the correct template for this resume
    const template = templateManager.getTemplateForResume(resume.id, resume.templateId);
    setSelectedTemplate(template);

    // If the resume has file data, create a blob and URL for preview
    if (resume.fileData) {
      try {
        const fileData = resume.fileData as any;
        if (fileData.data && fileData.type) {
          // For base64 data that includes the data URL prefix
          let base64Data = fileData.data;
          if (base64Data.includes(',')) {
            base64Data = base64Data.split(',')[1];
          }

          const byteCharacters = atob(base64Data);
          const byteNumbers = new Array(byteCharacters.length);
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
          const byteArray = new Uint8Array(byteNumbers);
          const blob = new Blob([byteArray], { type: fileData.type });
          const fileUrl = URL.createObjectURL(blob);
          setPdfUrl(fileUrl);
          setFileType(fileData.type);
          setIsPdfReady(true);
          setCurrentPage(1);

          // If it's a PDF, try to get the total pages
          if (fileData.type.includes('pdf')) {
            try {
              // Ensure pdfjsLib is loaded. This might need a dynamic import or being loaded outside.
              // For now, assuming it's available via static import.
              pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
              const loadingTask = pdfjsLib.getDocument({ data: Uint8Array.from(atob(base64Data), c => c.charCodeAt(0)) });
              loadingTask.promise.then((pdf: any) => {
                setTotalPages(pdf.numPages);
              }).catch((err: any) => {
                console.error('Error loading PDF:', err);
                setTotalPages(1);
              });
            } catch (err) {
              console.error('Error with PDF.js:', err);
              setTotalPages(1);
            }
          } else {
            setTotalPages(1);
          }
        }
      } catch (error) {
        console.error('Error creating file preview:', error);
        setPdfUrl(null);
        setIsPdfReady(false);
      }
    } else {
      // Handle resumes without file data (e.g., manually created ones)
      // Try to generate a PDF preview from the resume data
      await generatePreviewForCreatedResume(resume);
    }
  };

  const generatePreviewForCreatedResume = async (resume: Resume) => {
    try {
      // Use template manager to get the correct template for this resume
      const template = templateManager.getTemplateForResume(resume.id, resume.templateId);

      // Generate PDF preview
      const pdfUrl = await generateResumePDF(resume, template || undefined);

      if (pdfUrl) {
        setPdfUrl(pdfUrl);
        setFileType('text/html'); // Since we're generating HTML-based preview
        setIsPdfReady(true);
        setCurrentPage(1);
        setTotalPages(1);
      } else {
        // Fallback to no preview
        setPdfUrl(null);
        setIsPdfReady(false);
        setFileType(null);
        setTotalPages(1);
        setCurrentPage(1);
      }
    } catch (error) {
      console.error('Error generating resume preview:', error);
      setPdfUrl(null);
      setIsPdfReady(false);
      setFileType(null);
      setTotalPages(1);
      setCurrentPage(1);
    }
  };

  const generatePreviewForCreatedResumeWithTemplate = async (resume: Resume, template: ResumeTemplate) => {
    try {
      // Clean up previous PDF URL to force refresh
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }

      // Generate PDF preview with the specific template
      const newPdfUrl = await generateResumePDF(resume, template);

      if (newPdfUrl) {
        setPdfUrl(newPdfUrl);
        setFileType('text/html'); // Since we're generating HTML-based preview
        setIsPdfReady(true);
        setCurrentPage(1);
        setTotalPages(1);
      } else {
        // Fallback to no preview
        setPdfUrl(null);
        setIsPdfReady(false);
        setFileType(null);
        setTotalPages(1);
        setCurrentPage(1);
      }
    } catch (error) {
      console.error('Error generating resume preview with template:', error);
      setPdfUrl(null);
      setIsPdfReady(false);
      setFileType(null);
      setTotalPages(1);
      setCurrentPage(1);
    }
  };

  // Action handlers for newly created resumes
  const handlePrintResume = async () => {
    if (!selectedResume) return;

    try {
      const template = templateManager.getTemplateForResume(selectedResume.id, selectedResume.templateId);
      await printResume(selectedResume, template || undefined);
      toast({
        title: 'Printing resume',
        description: 'Resume sent to printer successfully.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: 'Print failed',
        description: 'Unable to print resume. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleSaveAsPDF = async () => {
    if (!selectedResume) return;

    try {
      const template = templateManager.getTemplateForResume(selectedResume.id, selectedResume.templateId);
      await saveResumeAsPDF(selectedResume, template || undefined);
      toast({
        title: 'Resume saved',
        description: 'Resume downloaded successfully.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: 'Save failed',
        description: 'Unable to save resume. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // Helper function to extract text from PDF
  const extractPDFText = async (file: File): Promise<string> => {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);

      pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
      const loadingTask = pdfjsLib.getDocument(uint8Array);
      const pdf = await loadingTask.promise;

      let textContent = '';
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const content = await page.getTextContent();
        const text = content.items
          .map((item: any) => item.str || '')
          .join(' ');
        textContent += text + '\n';
      }

      return textContent.trim();
    } catch (error) {
      console.error('Error extracting PDF text:', error);
      throw error;
    }
  };

  const handleEditTemplate = () => {
    if (!selectedResume) return;

    // Set the resume for editing and go to template selection
    setInternalIsCreatingResume(true);
    setCreationStep('template');
    // Keep the current template selected if available
    // setSelectedTemplate(null); // Don't clear the current template
  };

  const handleTemplateChange = async (newTemplate: ResumeTemplate, showToast: boolean = true) => {
    if (!selectedResume) return;

    try {
      // Update the resume in the database with the new template
      const updatedResume = await updateResume({
        id: selectedResume.id,
        data: {
          templateId: newTemplate.id
        }
      });

      // Use template manager to set the template for this specific resume
      templateManager.setTemplateForResume(selectedResume.id, newTemplate.id);

      // Update local state
      setSelectedTemplate(newTemplate);

      // Update the selected resume with the new template ID
      setSelectedResume(prev => prev ? { ...prev, templateId: newTemplate.id } : null);

      // Regenerate preview with new template - pass the new template directly
      await generatePreviewForCreatedResumeWithTemplate(selectedResume, newTemplate);

      // Exit template selection mode
      setInternalIsCreatingResume(false);
      setCreationStep('template');

      // Refetch resumes to ensure consistency
      refetch();

      // Only show toast if requested (to avoid duplicate notifications)
      if (showToast) {
        toast({
          title: 'Template updated',
          description: 'Resume template has been changed successfully.',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (error) {
      console.error('Error updating template:', error);
      // Always show error toast
      toast({
        title: 'Update failed',
        description: 'Unable to change template. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  async function internalOnFileUpload(event: React.ChangeEvent<HTMLInputElement>) {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to upload resumes.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (event.target.files == null || event.target.files.length === 0) return;

    const file = event.target.files[0];
    setFileType(file.type);

    // Create a URL for the file preview (for PDFs and images)
    const fileUrl = URL.createObjectURL(file);
    setPdfUrl(fileUrl);
    setIsPdfReady(true);

    // Read file as base64 for storage
    const reader = new FileReader();
    reader.onload = async (e) => {
      if (!e.target || typeof e.target.result !== 'string') return;

      const base64Data = e.target.result.split(',')[1]; // Remove data URL prefix

      try {
        let newResume: Partial<Resume>;

        // If it's a PDF file, try to extract and parse the content
        if (file.type === 'application/pdf') {
          try {
            // Show parsing toast
            toast({
              title: 'Processing PDF',
              description: 'Extracting and parsing resume content...',
              status: 'info',
              duration: 2000,
              isClosable: true,
            });

            // Extract text from PDF
            const pdfText = await extractPDFText(file);

            // Parse the extracted text using AI
            const parsedData = await parsePDFContent({
              pdfText,
              fileName: file.name
            });

            // Create resume with parsed data
            newResume = {
              ...parsedData,
              fileData: {
                name: file.name,
                type: file.type,
                data: base64Data
              }
            };

            toast({
              title: 'PDF parsed successfully',
              description: 'Resume content has been extracted and structured.',
              status: 'success',
              duration: 3000,
              isClosable: true,
            });

          } catch (parseError) {
            console.error('Error parsing PDF:', parseError);

            // Fallback to basic file upload if parsing fails
            newResume = {
              title: file.name.replace(/\.[^/.]+$/, ""), // Remove file extension
              personalInfo: {
                fullName: '',
                email: '',
                phone: '',
                location: '',
              },
              summary: 'Uploaded Resume - Parsing failed, please edit manually',
              experience: [],
              education: [],
              skills: [],
              fileData: {
                name: file.name,
                type: file.type,
                data: base64Data
              }
            };

            toast({
              title: 'PDF parsing failed',
              description: 'Resume uploaded but content parsing failed. You can edit it manually.',
              status: 'warning',
              duration: 5000,
              isClosable: true,
            });
          }
        } else {
          // For non-PDF files, create basic resume entry
          newResume = {
            title: file.name.replace(/\.[^/.]+$/, ""), // Remove file extension
            personalInfo: {
              fullName: '',
              email: '',
              phone: '',
              location: '',
            },
            summary: 'Uploaded Resume',
            experience: [],
            education: [],
            skills: [],
            fileData: {
              name: file.name,
              type: file.type,
              data: base64Data
            }
          };
        }

        const createdResume = await createResume(newResume);
        toast({
          title: 'Resume uploaded',
          description: 'Your resume has been uploaded successfully.',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });

        // Select the newly created resume
        if (createdResume) {
          handleSelectResume(createdResume as Resume); // Use handleSelectResume to set up preview
          refetch(); // Refetch the list of resumes
        }
      } catch (error) {
        console.error('Error uploading resume:', error);
        toast({
          title: 'Error',
          description: 'Failed to upload resume. Please try again.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      }
    };

    reader.readAsDataURL(file);
  }

  // Clean up PDF URL when component unmounts
  useEffect(() => {
    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }
    };
  }, [pdfUrl]);



  const handleDeleteResume = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this resume?')) {
      try {
        await deleteResume({ id });

        // Clean up template manager mapping for this resume
        templateManager.removeResumeTemplate(id);

        toast({
          title: 'Resume deleted',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });

        // If the deleted resume was selected, clear selection and preview
        if (selectedResume && selectedResume.id === id) {
          setSelectedResume(null);
          setPdfUrl(null);
          setIsPdfReady(false);
          setFileType(null);
          setTotalPages(1);
          setCurrentPage(1);
        }
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to delete resume. Please try again.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      }
    }
  };

  const handleCreateResume = () => {
    if (externalOnCreateResume) {
      externalOnCreateResume();
      return;
    }

    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to create resumes.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    setSelectedResume(null); // Clear selected resume when creating a new one
    setInternalIsCreatingResume(true);
    setCreationStep('template'); // Start with template selection
    setSelectedTemplate(null); // Clear any previously selected template
  };

  const handleCancelCreate = () => {
    if (externalOnCancelCreate) {
      externalOnCancelCreate();
      return;
    }

    setInternalIsCreatingResume(false);
    setCreationStep('template');
    setSelectedTemplate(null);
    // If there are resumes, select the first one
    if (resumes && resumes.length > 0) {
      handleSelectResume(resumes[0]);
    }
  };

  const onFileUpload = externalOnFileUpload || internalOnFileUpload;

  const handleTemplateSelect = async (template: ResumeTemplate) => {
    setSelectedTemplate(template);

    // If we're editing an existing resume, apply the template immediately
    if (selectedResume && !selectedResume.fileData) {
      try {
        // Apply the template change without showing toast in handleTemplateChange
        await handleTemplateChange(template, false);

        // Show the success toast here instead
        toast({
          title: 'Template updated',
          description: 'Resume template has been changed successfully.',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      } catch (error) {
        // Error toast is already handled in handleTemplateChange
        console.error('Error changing template:', error);
      }
    }
  };

  const handleTemplateContinue = () => {
    if (selectedTemplate) {
      setCreationStep('form');
    }
  };

  const handleTemplateBack = () => {
    setCreationStep('template');
  };

  const handleSubmitResume = async (data: Partial<Resume>) => {
    try {
      // Include template information if a template was selected
      const resumeData = {
        ...data,
        templateId: selectedTemplate?.id
      };

      const createdResume = await createResume(resumeData);

      // Store template mapping for the new resume
      if (createdResume && selectedTemplate) {
        templateManager.setTemplateForResume(createdResume.id, selectedTemplate.id);
      }

      toast({
        title: 'Resume created successfully',
        description: 'Your resume has been created and is ready for preview.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });

      // Select the newly created resume and go directly to PDF preview
      if (createdResume) {
         const resume = createdResume as Resume;
         setSelectedResume(resume);
         setInternalIsCreatingResume(false);
         setCreationStep('template'); // Reset creation step for future use

         // Generate PDF preview for the newly created resume
         await generatePreviewForCreatedResume(resume);

         // Keep the selected template for preview generation
         // setSelectedTemplate(null); // Don't clear template yet
         refetch();

         // The resume is now selected and preview is generated
         // The UI will automatically show the PDF preview section
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create resume. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  if (!user) {
    return (
      <EmptyResumes
        title="Authentication Required"
        description="Please log in to access your resumes and start building your career profile."
        showActions={false}
      />
    );
  }

  if (isLoading) {
    return <SimpleLoading message="Loading your resumes..." />;
  }

  if (error) {
    return (
      <GeneralError
        error={error}
        onRetry={() => refetch()}
        showErrorDetails={false}
      />
    );
  }

  return (
    <VStack spacing={spacing} align="stretch" width="100%" height="100%">
      {/* Hidden file input for external use */}
      <Input
        type="file"
        accept="application/pdf,.pdf,image/*,.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        onChange={onFileUpload}
        display="none"
        ref={fileInputRef}
      />

      {/* Top Section: Resume Selection & Actions */}
      {!isCreatingResume && resumes && resumes.length > 0 && (
        <Flex
          direction={{ base: 'column', md: 'row' }}
          gap={4}
          align={{ base: 'stretch', md: 'center' }}
          justify="space-between"
          p={4}
          bg={topSectionBg}
          borderRadius="lg"
          border="1px"
          borderColor={topSectionBorderColor}
        >
          {/* Resume Selection Dropdown */}
          <Box flex="1" maxW={{ base: "100%", md: "400px" }}>
            <HStack justify="space-between" mb={2}>
              <Text fontSize="sm" fontWeight="medium" color={selectLabelColor}>
                Select Resume
              </Text>
              <Badge colorScheme="purple" variant="subtle" fontSize="xs">
                {resumes.length} resume{resumes.length !== 1 ? 's' : ''}
              </Badge>
            </HStack>
            <Select
              value={selectedResume?.id || ''}
              onChange={(e) => {
                const resume = resumes.find(r => r.id === e.target.value);
                if (resume) handleSelectResume(resume);
              }}
              placeholder="Choose a resume to preview"
              size={buttonSize}
              bg={selectBg}
              borderColor={selectBorderColor}
            >
              {resumes.map((resume) => (
                <option key={resume.id} value={resume.id}>
                  {resume.title}
                </option>
              ))}
            </Select>
          </Box>

          {/* Action Buttons - Minimalistic */}
          {selectedResume && (
            <HStack spacing={1} flexWrap="wrap">
              <Tooltip label="View resume" hasArrow>
                <IconButton
                  aria-label="View resume"
                  icon={<FaEye />}
                  size="sm"
                  variant="ghost"
                  onClick={onViewModalOpen}
                  isDisabled={!pdfUrl}
                />
              </Tooltip>
              <Tooltip label="Download resume" hasArrow>
                <IconButton
                  aria-label="Download resume"
                  icon={<DownloadIcon />}
                  size="sm"
                  variant="ghost"
                  onClick={() => {
                    if (selectedResume.fileData) {
                      const link = document.createElement('a');
                      link.href = pdfUrl || '';
                      link.download = selectedResume.fileData?.name || 'resume';
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    } else {
                      handleSaveAsPDF();
                    }
                  }}
                />
              </Tooltip>
              {!selectedResume.fileData && (
                <Tooltip label="Print resume" hasArrow>
                  <IconButton
                    aria-label="Print resume"
                    icon={<FaPrint />}
                    size="sm"
                    variant="ghost"
                    onClick={handlePrintResume}
                  />
                </Tooltip>
              )}
              <Tooltip label="Delete resume" hasArrow>
                <IconButton
                  aria-label="Delete resume"
                  icon={<FaTrash />}
                  size="sm"
                  variant="ghost"
                  colorScheme="red"
                  onClick={() => handleDeleteResume(selectedResume.id)}
                />
              </Tooltip>
            </HStack>
          )}
        </Flex>
      )}

      {/* Main Content Area */}
      <Box flex="1" height="100%">
        {!isCreatingResume && (!resumes || resumes.length === 0) ? (
          <ContentContainer
            height="100%"
            minHeight="400px"
            p={6}
            borderRadius="lg"
            border="1px"
            borderColor={containerBorderColor}
          >
            <EmptyResumes
              size="md"
              primaryAction={{
                label: 'Upload Resume',
                onClick: handleFileButtonClick,
                icon: <FaUpload />,
                colorScheme: 'blue',
              }}
              secondaryAction={{
                label: 'Create New',
                onClick: handleCreateResume,
                icon: <FaPlus />,
                variant: 'outline',
              }}
            />
          </ContentContainer>
        ) : isCreatingResume ? (
            <Grid
              templateColumns={{ base: "1fr", lg: "400px 1fr" }}
              gap={{ base: 0, md: 2, lg: 4 }}
              height="100%"
              minHeight="400px"
            >
              {/* Left Panel: Creation Steps */}
              <GridItem>
                <ContentContainer
                  height="100%"
                  minHeight={{ base: "300px", lg: "calc(100vh - 300px)" }}
                  maxHeight={{ base: "none", lg: "calc(100vh - 300px)" }}
                  overflowY="auto"
                  delay={0.3}
                  p={{ base: 2, md: 4 }}
                  borderRadius="lg"
                  border="1px"
                  borderColor={containerBorderColor}
                >
                  <VStack spacing={3} align="stretch" height="100%">
                    {creationStep === 'template' ? (
                      <>
                        {/* Header */}
                        <VStack spacing={2} align="flex-start">
                          <Heading size="md" color="purple.500">
                            Choose Template
                          </Heading>
                          <Text color="gray.500" fontSize="sm">
                            Select a professional template from the gallery on the right to get started.
                          </Text>
                        </VStack>

                        {/* Selected Template Display */}
                        {selectedTemplate && (
                          <Box
                            bg={templateSelectedBg}
                            border="1px solid"
                            borderColor={templateSelectedBorderColor}
                            borderRadius="md"
                            p={3}
                          >
                            <VStack align="start" spacing={1}>
                              <Text fontWeight="semibold" color="purple.700" fontSize="sm">
                                ✓ Selected: {selectedTemplate.name}
                              </Text>
                              <Text fontSize="xs" color="gray.600">
                                {selectedTemplate.description}
                              </Text>
                            </VStack>
                          </Box>
                        )}

                        {/* Spacer to push buttons to bottom */}
                        <Box flex="1" />

                        {/* Action Buttons */}
                        <Box pt={3} borderTop="1px solid" borderColor="gray.200">
                          <VStack spacing={2} align="stretch">
                            <Button
                              rightIcon={<FaPlus />}
                              colorScheme="purple"
                              onClick={handleTemplateContinue}
                              isDisabled={!selectedTemplate}
                              size="md"
                              width="100%"
                            >
                              {selectedTemplate ? `Continue with ${selectedTemplate.name}` : 'Select a Template to Continue'}
                            </Button>
                            <Button
                              leftIcon={<CloseIcon />}
                              variant="outline"
                              onClick={handleCancelCreate}
                              width="100%"
                              size="sm"
                            >
                              Cancel
                            </Button>
                          </VStack>
                        </Box>
                      </>
                    ) : (
                      <>
                        <Text fontSize="lg" fontWeight="semibold" color="purple.500">Creating New Resume</Text>
                        <Text fontSize="md" color="gray.500" textAlign="center">
                          Fill out the form on the right to create your resume with the selected template.
                        </Text>
                        {selectedTemplate && (
                          <VStack spacing={2} mt={4}>
                            <Text fontSize="sm" fontWeight="medium">Selected Template:</Text>
                            <Text fontSize="sm" color="purple.500">{selectedTemplate.name}</Text>
                          </VStack>
                        )}
                      </>
                    )}
                  </VStack>
                </ContentContainer>
              </GridItem>

              {/* Right Panel: Template Gallery or Resume Form */}
              <GridItem>
                <ContentContainer
                  height="100%"
                  minHeight={{ base: "400px", lg: "calc(100vh - 300px)" }}
                  maxHeight={{ base: "none", lg: "calc(100vh - 300px)" }}
                  overflowY="auto"
                  delay={0.4}
                  p={{ base: 2, md: 4 }}
                  borderRadius="lg"
                  border="1px"
                  borderColor={containerBorderColor}
                >
                  {creationStep === 'template' ? (
                    <VStack spacing={4} align="stretch" height="100%">
                      <Heading size="sm" color="purple.500">
                        Choose Resume Template
                      </Heading>
                      <Box flex="1" overflowY="auto">
                        <ResumeTemplateGallery
                          selectedTemplate={selectedTemplate || undefined}
                          onTemplateSelect={handleTemplateSelect}
                        />
                      </Box>
                    </VStack>
                  ) : (
                    <VStack spacing={3} align="stretch" height="100%">
                      <Heading size="sm" color="purple.500">Create New Resume</Heading>
                      <Box flex="1" overflowY="auto">
                        <ResumeForm
                          onSubmit={handleSubmitResume}
                          onCancel={handleTemplateBack}
                          initialData={selectedTemplate ? {
                            title: `${selectedTemplate.name} Resume`,
                            ...selectedTemplate.sampleData
                          } : undefined}
                        />
                      </Box>
                    </VStack>
                  )}
                </ContentContainer>
              </GridItem>
            </Grid>
          ) : (
            // Single Preview Panel for Resume Viewing
            <ContentContainer
              height="100%"
              minHeight={{ base: "400px", lg: "calc(100vh - 300px)" }}
              maxHeight={{ base: "none", lg: "calc(100vh - 300px)" }}
              overflowY="auto"
              ref={pdfContainerRef}
              delay={0.4}
              p={{ base: 2, md: 4 }}
              borderRadius="lg"
              border="1px"
              borderColor={containerBorderColor}
            >
              {!selectedResume ? (
                <VStack spacing={6} align="center" justify="center" height="100%" p={8}>
                  <Text fontSize="lg" color="gray.500" textAlign="center">
                    Select a resume from the dropdown above to preview
                  </Text>
                  <VStack spacing={3}>
                    <Button
                      leftIcon={<FaPlus />}
                      colorScheme="purple"
                      size="lg"
                      onClick={handleCreateResume}
                    >
                      Create New Resume
                    </Button>
                    <Button
                      leftIcon={<FaUpload />}
                      variant="outline"
                      size="md"
                      onClick={handleFileButtonClick}
                    >
                      Upload Resume
                    </Button>
                  </VStack>
                </VStack>
              ) : (
                <VStack spacing={8} align="center" height="100%" justify="center" p={8}>
                  {/* Enhanced Resume Header */}
                  <VStack spacing={4} align="center" w="100%" maxW="500px">
                    {/* Editable Resume Title with Tag */}
                    <VStack spacing={3} align="center">
                      <HStack spacing={3} align="center" flexWrap="wrap" justify="center">
                        <Text
                          fontSize={{ base: 'xl', md: '2xl' }}
                          fontWeight="bold"
                          color={useColorModeValue('gray.800', 'white')}
                          textAlign="center"
                          lineHeight="1.2"
                        >
                          {selectedResume.title}
                        </Text>
                        {selectedResume.fileData && (
                          <Badge
                            colorScheme="blue"
                            variant="solid"
                            px={2}
                            py={1}
                            borderRadius="md"
                            fontSize="xs"
                            fontWeight="medium"
                          >
                            Uploaded
                          </Badge>
                        )}
                      </HStack>
                    </VStack>

                    {/* Template Badge */}
                    {selectedResume.templateId && (
                      <Badge
                        colorScheme="purple"
                        variant="subtle"
                        px={3}
                        py={1}
                        borderRadius="full"
                        fontSize="xs"
                        fontWeight="medium"
                      >
                        Template Applied
                      </Badge>
                    )}
                  </VStack>

                  {/* Enhanced Resume Preview */}
                  <Box
                    width={{ base: "320px", md: "400px" }}
                    height={{ base: "420px", md: "520px" }}
                    bg={useColorModeValue('white', 'gray.800')}
                    border="1px solid"
                    borderColor={useColorModeValue('gray.200', 'gray.600')}
                    borderRadius="xl"
                    overflow="hidden"
                    position="relative"
                    boxShadow="xl"
                    transition="all 0.2s"
                    _hover={{
                      transform: "translateY(-2px)",
                      boxShadow: "2xl"
                    }}
                  >
                    {isPdfReady && pdfUrl ? (
                      fileType?.includes('image') ? (
                        <img
                          src={pdfUrl}
                          alt="Resume Preview"
                          style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'contain',
                            backgroundColor: 'white'
                          }}
                        />
                      ) : fileType?.includes('pdf') ? (
                        <VStack spacing={0} height="100%">
                          {/* PDF Controls */}
                          <HStack
                            spacing={1}
                            p={1}
                            bg={useColorModeValue('gray.50', 'gray.700')}
                            borderBottom="1px solid"
                            borderColor={useColorModeValue('gray.200', 'gray.600')}
                            width="100%"
                            justify="space-between"
                          >
                            <HStack spacing={1}>
                              <IconButton
                                aria-label="Previous page"
                                icon={<FaChevronLeft />}
                                size="xs"
                                variant="ghost"
                                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                isDisabled={currentPage <= 1}
                              />
                              <Text fontSize="xs" color="gray.600" minW="40px" textAlign="center">
                                {currentPage}/{totalPages}
                              </Text>
                              <IconButton
                                aria-label="Next page"
                                icon={<FaChevronRight />}
                                size="xs"
                                variant="ghost"
                                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                isDisabled={currentPage >= totalPages}
                              />
                            </HStack>
                            <HStack spacing={1}>
                              <IconButton
                                aria-label="Zoom out"
                                icon={<Text fontSize="xs">−</Text>}
                                size="xs"
                                variant="ghost"
                                onClick={() => setPdfScale(Math.max(0.5, pdfScale - 0.1))}
                                isDisabled={pdfScale <= 0.5}
                              />
                              <Text fontSize="xs" color="gray.600" minW="30px" textAlign="center">
                                {Math.round(pdfScale * 100)}%
                              </Text>
                              <IconButton
                                aria-label="Zoom in"
                                icon={<Text fontSize="xs">+</Text>}
                                size="xs"
                                variant="ghost"
                                onClick={() => setPdfScale(Math.min(2, pdfScale + 0.1))}
                                isDisabled={pdfScale >= 2}
                              />
                            </HStack>
                          </HStack>

                          {/* PDF Viewer */}
                          <Box flex="1" width="100%" overflow="hidden">
                            <iframe
                              key={`pdf-${currentPage}-${pdfScale}`}
                              src={`${pdfUrl}#page=${currentPage}&zoom=page-fit&view=FitH`}
                              width="100%"
                              height="100%"
                              style={{ border: 'none' }}
                              title="Resume Preview"
                            />
                          </Box>
                        </VStack>
                      ) : (
                        <iframe
                          src={pdfUrl}
                          width="100%"
                          height="100%"
                          style={{
                            border: 'none',
                            transform: 'scale(0.85)',
                            transformOrigin: 'top left',
                            width: '117.6%',
                            height: '117.6%'
                          }}
                          title="Resume Preview"
                        />
                      )
                    ) : (
                      <VStack spacing={4} align="center" justify="center" height="100%">
                        <Spinner size="lg" color="purple.500" />
                        <Text color="gray.500" fontSize="sm">Loading preview...</Text>
                      </VStack>
                    )}
                  </Box>

                  {/* Resume Details */}
                  <VStack spacing={4} align="center" maxW="500px" w="100%">
                    {selectedResume.summary && selectedResume.summary.trim() && (
                      <Box
                        textAlign="center"
                        p={4}
                        bg={useColorModeValue('gray.50', 'gray.700')}
                        borderRadius="lg"
                        w="100%"
                      >
                        <Text fontSize="sm" fontWeight="semibold" color="gray.700" mb={2}>
                          Summary
                        </Text>
                        <Text fontSize="sm" color="gray.600" lineHeight="1.5">
                          {selectedResume.summary}
                        </Text>
                      </Box>
                    )}

                    {selectedResume.personalInfo?.email && (
                      <HStack
                        spacing={6}
                        fontSize="sm"
                        color="gray.500"
                        flexWrap="wrap"
                        justify="center"
                      >
                        <HStack spacing={2}>
                          <Text fontWeight="medium">Email:</Text>
                          <Text>{selectedResume.personalInfo.email}</Text>
                        </HStack>
                        {selectedResume.personalInfo.phone && (
                          <HStack spacing={2}>
                            <Text fontWeight="medium">Phone:</Text>
                            <Text>{selectedResume.personalInfo.phone}</Text>
                          </HStack>
                        )}
                      </HStack>
                    )}
                  </VStack>
                </VStack>
              )}
            </ContentContainer>
          )}
      </Box>

      {/* Resume View Modal */}
      <Modal isOpen={isViewModalOpen} onClose={onViewModalClose} size="6xl" scrollBehavior="inside">
        <ModalOverlay backdropFilter="auto" backdropInvert="15%" backdropBlur="2px" />
        <ModalContent maxH="90vh">
          <ModalHeader>
            <VStack align="flex-start" spacing={3} w="100%">
              <HStack spacing={3} align="center" flexWrap="wrap">
                <Text fontSize="2xl" fontWeight="bold" color={useColorModeValue('gray.800', 'white')}>
                  {selectedResume?.title || 'Resume Preview'}
                </Text>
                {selectedResume?.fileData && (
                  <Badge
                    colorScheme="blue"
                    variant="solid"
                    px={2}
                    py={1}
                    borderRadius="md"
                    fontSize="xs"
                    fontWeight="medium"
                  >
                    Uploaded
                  </Badge>
                )}
              </HStack>

              {selectedResume?.templateId && (
                <Badge
                  colorScheme="purple"
                  variant="subtle"
                  px={3}
                  py={1}
                  borderRadius="full"
                  fontSize="xs"
                  fontWeight="medium"
                >
                  Template Applied
                </Badge>
              )}
            </VStack>
          </ModalHeader>
          <ModalCloseButton />

          <ModalBody>
            <VStack spacing={4} align="stretch">
              {/* Full Size Preview */}
              <Box
                width="100%"
                height="600px"
                bg={useColorModeValue('white', 'gray.800')}
                border="1px solid"
                borderColor={useColorModeValue('gray.200', 'gray.600')}
                borderRadius="md"
                overflow="hidden"
                position="relative"
              >
                {isPdfReady && pdfUrl ? (
                  fileType?.includes('image') ? (
                    <img
                      src={pdfUrl}
                      alt="Resume Preview"
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'contain',
                        backgroundColor: 'white'
                      }}
                    />
                  ) : (
                    <iframe
                      src={fileType?.includes('pdf') ? `${pdfUrl}#page=1&zoom=page-fit&view=FitH` : pdfUrl}
                      width="100%"
                      height="100%"
                      style={{ border: 'none' }}
                      title="Resume Preview"
                    />
                  )
                ) : (
                  <VStack spacing={4} align="center" justify="center" height="100%">
                    <Spinner size="lg" color="purple.500" />
                    <Text color="gray.500">Loading preview...</Text>
                  </VStack>
                )}
              </Box>

              {/* Resume Summary */}
              {selectedResume?.summary && selectedResume.summary.trim() && (
                <Box>
                  <Text fontSize="md" fontWeight="semibold" mb={2}>
                    Summary
                  </Text>
                  <Text fontSize="sm" color="gray.600">
                    {selectedResume.summary}
                  </Text>
                </Box>
              )}
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </VStack>
  );
};

export default ResumeManager;
