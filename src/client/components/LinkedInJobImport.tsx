import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { importJobFromLinkedIn } from 'wasp/client/operations';
import {
  Box,
  Button,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  VStack,
  HStack,
  Text,
  useToast,
  Collapse,
  Icon,
  Tooltip,
} from '@chakra-ui/react';
import { FaLinkedin, FaArrowRight, FaInfoCircle } from 'react-icons/fa';

// Define the job data structure
type JobData = {
  title: string;
  company: string;
  location: string;
  description: string;
};

type LinkedInJobImportProps = {
  onJobImported: (jobData: JobData) => void;
  buttonText?: string;
  isCollapsible?: boolean;
  initiallyOpen?: boolean;
  inForm?: boolean; // Whether this component is inside another form
};

export default function LinkedInJobImport({
  onJobImported,
  buttonText = 'Import from LinkedIn',
  isCollapsible = true,
  initiallyOpen = false,
  inForm = false,
}: LinkedInJobImportProps) {
  const [isOpen, setIsOpen] = useState<boolean>(initiallyOpen);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const toast = useToast();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<{ linkedinUrl: string }>();

  const onSubmit = async (data: { linkedinUrl: string }) => {
    setIsLoading(true);
    try {
      const jobData = await importJobFromLinkedIn({ url: data.linkedinUrl }) as JobData;

      // Log the data for debugging
      console.log('LinkedIn job data received:', jobData);

      toast({
        title: 'Job imported successfully',
        description: `Imported: ${jobData.title} at ${jobData.company}`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // Pass the job data to the parent component
      onJobImported(jobData);

      // Reset this form
      reset();

      if (isCollapsible) {
        setIsOpen(false);
      }
    } catch (error: any) {
      console.error('LinkedIn import error:', error);
      toast({
        title: 'Failed to import job',
        description: error.message || 'An error occurred while importing the job',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const toggleOpen = () => {
    if (isCollapsible) {
      setIsOpen(!isOpen);
    }
  };

  return (
    <Box width="100%">
      {isCollapsible ? (
        <Button
          leftIcon={<FaLinkedin />}
          onClick={toggleOpen}
          variant="outline"
          colorScheme="purple"
          size="md"
          mb={isOpen ? 4 : 0}
          width={{ base: "full", md: "auto" }}
        >
          {buttonText}
        </Button>
      ) : null}

      <Collapse in={isCollapsible ? isOpen : true} animateOpacity>
        <Box
          p={4}
          borderWidth="1px"
          borderRadius="lg"
          borderColor="gray.200"
          bg="gray.50"
          width="100%"
        >
          {inForm ? (
            <VStack spacing={4} align="stretch">
              <HStack align="center">
                <Icon as={FaInfoCircle} color="gray.500" />
                <Text fontSize="sm" color="gray.600">
                  Paste a LinkedIn job URL to automatically import job details
                </Text>
              </HStack>

              <FormControl isInvalid={!!errors.linkedinUrl}>
                <FormLabel htmlFor="linkedinUrl" fontWeight="medium">
                  LinkedIn Job URL
                  <Tooltip
                    label="URL should look like: https://www.linkedin.com/jobs/view/..."
                    placement="top"
                    hasArrow
                  >
                    <Box as="span" display="inline-block">
                      <Icon as={FaInfoCircle} ml={1} color="gray.500" />
                    </Box>
                  </Tooltip>
                </FormLabel>
                <Input
                  id="linkedinUrl"
                  placeholder="https://www.linkedin.com/jobs/view/..."
                  {...register('linkedinUrl', {
                    required: 'LinkedIn URL is required',
                    pattern: {
                      value: /linkedin\.com\/jobs\/view\//i,
                      message: 'Please enter a valid LinkedIn job URL',
                    },
                  })}
                  bg="white"
                  _hover={{ bg: 'white' }}
                  _focus={{ bg: 'white', borderColor: 'purple.500' }}
                />
                <FormErrorMessage>
                  {errors.linkedinUrl && errors.linkedinUrl.message}
                </FormErrorMessage>
              </FormControl>

              <Button
                onClick={handleSubmit(onSubmit)}
                rightIcon={<FaArrowRight />}
                colorScheme="purple"
                isLoading={isLoading}
                loadingText="Importing..."
                width={{ base: "full", md: "auto" }}
                alignSelf="flex-end"
                type="button"
              >
                Import Job
              </Button>
            </VStack>
          ) : (
            <form onSubmit={handleSubmit(onSubmit)}>
              <VStack spacing={4} align="stretch">
                <HStack align="center">
                  <Icon as={FaInfoCircle} color="gray.500" />
                  <Text fontSize="sm" color="gray.600">
                    Paste a LinkedIn job URL to automatically import job details
                  </Text>
                </HStack>

                <FormControl isInvalid={!!errors.linkedinUrl}>
                  <FormLabel htmlFor="linkedinUrl" fontWeight="medium">
                    LinkedIn Job URL
                    <Tooltip
                      label="URL should look like: https://www.linkedin.com/jobs/view/..."
                      placement="top"
                      hasArrow
                    >
                      <Box as="span" display="inline-block">
                        <Icon as={FaInfoCircle} ml={1} color="gray.500" />
                      </Box>
                    </Tooltip>
                  </FormLabel>
                  <Input
                    id="linkedinUrl"
                    placeholder="https://www.linkedin.com/jobs/view/..."
                    {...register('linkedinUrl', {
                      required: 'LinkedIn URL is required',
                      pattern: {
                        value: /linkedin\.com\/jobs\/view\//i,
                        message: 'Please enter a valid LinkedIn job URL',
                      },
                    })}
                    bg="white"
                    _hover={{ bg: 'white' }}
                    _focus={{ bg: 'white', borderColor: 'purple.500' }}
                  />
                  <FormErrorMessage>
                    {errors.linkedinUrl && errors.linkedinUrl.message}
                  </FormErrorMessage>
                </FormControl>

                <Button
                  type="submit"
                  rightIcon={<FaArrowRight />}
                  colorScheme="purple"
                  isLoading={isLoading}
                  loadingText="Importing..."
                  width={{ base: "full", md: "auto" }}
                  alignSelf="flex-end"
                >
                  Import Job
                </Button>
              </VStack>
            </form>
          )}
        </Box>
      </Collapse>
    </Box>
  );
}
