import { useState, useRef, useEffect } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Button,
  Select,
  FormControl,
  FormLabel,
  Input,
  useToast,
  Divider,
  Icon,
  Badge,
  Flex,
  Tooltip,
} from '@chakra-ui/react';
import { FaUpload, FaFileAlt, FaFilePdf, FaFileWord, FaFileImage } from 'react-icons/fa';
import { getUserResumes, createResume, useQuery } from 'wasp/client/operations';
import { useAuth } from 'wasp/client/auth';
import { type Resume } from '../../shared/types';
import * as pdfjsLib from 'pdfjs-dist';

type ResumeSelectorProps = {
  onResumeSelected: (resumeContent: string) => void;
  initialResumeId?: string;
};

export default function ResumeSelector({ onResumeSelected, initialResumeId }: ResumeSelectorProps) {
  const [selectedResumeId, setSelectedResumeId] = useState<string | null>(initialResumeId || null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const toast = useToast();
  const { data: user } = useAuth();

  // Fetch user's resumes only if user is authenticated
  const { data: resumes, isLoading, error, refetch } = useQuery(
    getUserResumes,
    undefined,
    { enabled: !!user }
  );

  // When resumes load or initialResumeId changes, set the selected resume
  useEffect(() => {
    if (resumes && resumes.length > 0) {
      // If initialResumeId is provided and exists in the resumes, use it
      if (initialResumeId && resumes.some(resume => resume.id === initialResumeId)) {
        setSelectedResumeId(initialResumeId);
      } else {
        // Otherwise, select the first resume
        setSelectedResumeId(resumes[0].id);
      }

      // Extract content from the selected resume
      const selectedResume = initialResumeId
        ? resumes.find(resume => resume.id === initialResumeId)
        : resumes[0];

      if (selectedResume) {
        extractResumeContent(selectedResume);
      }
    }
  }, [resumes, initialResumeId]);

  // When selected resume changes, extract its content
  useEffect(() => {
    if (selectedResumeId && resumes) {
      const selectedResume = resumes.find(resume => resume.id === selectedResumeId);
      if (selectedResume) {
        extractResumeContent(selectedResume);
      }
    }
  }, [selectedResumeId]);

  // Extract content from resume (either from file data or structured data)
  const extractResumeContent = async (resume: Resume) => {
    try {
      if (resume.fileData?.data) {
        // If resume has file data, extract text from it
        if (resume.fileData.type.includes('pdf')) {
          // For PDF files, use PDF.js to extract text
          const base64Data = resume.fileData.data;
          const binaryData = atob(base64Data);
          const array = new Uint8Array(binaryData.length);
          for (let i = 0; i < binaryData.length; i++) {
            array[i] = binaryData.charCodeAt(i);
          }

          pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.js`;
          const loadingTask = pdfjsLib.getDocument(array);
          const pdf = await loadingTask.promise;

          let textContent = '';
          for (let i = 1; i <= pdf.numPages; i++) {
            const page = await pdf.getPage(i);
            const content = await page.getTextContent();
            const text = content.items
              .map((item: any) => item.str || '')
              .join(' ');
            textContent += text;
          }

          onResumeSelected(textContent);
        } else {
          // For other file types, we can't extract text directly
          // Just pass a placeholder or structured data
          const structuredContent = generateStructuredContent(resume);
          onResumeSelected(structuredContent);
        }
      } else {
        // If no file data, generate content from structured data
        const structuredContent = generateStructuredContent(resume);
        onResumeSelected(structuredContent);
      }
    } catch (error) {
      console.error('Error extracting resume content:', error);
      toast({
        title: 'Error',
        description: 'Failed to extract resume content',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // Generate structured content from resume data
  const generateStructuredContent = (resume: Resume): string => {
    const { personalInfo, summary, experience, education, skills } = resume;

    let content = `${personalInfo.fullName}\n`;
    content += `${personalInfo.email} | ${personalInfo.phone} | ${personalInfo.location}\n`;
    if (personalInfo.linkedIn) content += `LinkedIn: ${personalInfo.linkedIn}\n`;
    if (personalInfo.website) content += `Website: ${personalInfo.website}\n`;

    content += `\nSUMMARY\n${summary}\n`;

    content += '\nEXPERIENCE\n';
    experience.forEach(exp => {
      content += `${exp.position} at ${exp.company} (${exp.startDate} - ${exp.current ? 'Present' : exp.endDate})\n`;
      content += `${exp.description}\n`;
      if (exp.achievements && exp.achievements.length > 0) {
        content += 'Achievements:\n';
        exp.achievements.forEach(achievement => {
          content += `- ${achievement}\n`;
        });
      }
      content += '\n';
    });

    content += 'EDUCATION\n';
    education.forEach(edu => {
      content += `${edu.degree} in ${edu.field} at ${edu.institution} (${edu.startDate} - ${edu.current ? 'Present' : edu.endDate})\n`;
      if (edu.gpa) content += `GPA: ${edu.gpa}\n`;
      content += '\n';
    });

    content += 'SKILLS\n';
    content += skills.join(', ');

    return content;
  };

  // Handle resume selection change
  const handleResumeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedResumeId(e.target.value);
  };

  // Handle file upload button click
  const handleFileButtonClick = () => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to upload resumes.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handle file upload
  const onFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to upload resumes.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (!event.target.files || event.target.files.length === 0) return;

    const file = event.target.files[0];

    // Read file as base64 for storage
    const reader = new FileReader();
    reader.onload = async (e) => {
      if (!e.target || typeof e.target.result !== 'string') return;

      const base64Data = e.target.result.split(',')[1]; // Remove data URL prefix

      // Create a new resume entry with file data
      const newResume: Partial<Resume> = {
        title: file.name.replace(/\.[^/.]+$/, ""), // Remove file extension
        personalInfo: {
          fullName: '',
          email: '',
          phone: '',
          location: '',
        },
        summary: 'Uploaded Resume',
        experience: [],
        education: [],
        skills: [],
        fileData: {
          name: file.name,
          type: file.type,
          data: base64Data
        }
      };

      try {
        const createdResume = await createResume(newResume);
        toast({
          title: 'Resume uploaded',
          description: 'Your resume has been uploaded successfully.',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });

        // Select the newly created resume
        if (createdResume) {
          setSelectedResumeId(createdResume.id);
          refetch(); // Refetch the list of resumes
        }
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to upload resume. Please try again.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      }
    };

    reader.readAsDataURL(file);
  };

  // Get file icon based on file type
  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) return <Icon as={FaFilePdf} color="red.500" />;
    if (fileType.includes('word') || fileType.includes('doc')) return <Icon as={FaFileWord} color="blue.500" />;
    if (fileType.includes('image')) return <Icon as={FaFileImage} color="green.500" />;
    return <Icon as={FaFileAlt} color="gray.500" />;
  };

  return (
    <Box width="100%">
      <FormControl>
        <FormLabel fontWeight="medium" color="gray.700">Your Resume/CV</FormLabel>

        {!user ? (
          <Text fontSize="sm" color="gray.500">Please log in to access your resumes</Text>
        ) : isLoading ? (
          <Text fontSize="sm" color="gray.500">Loading your resumes...</Text>
        ) : error ? (
          <Text fontSize="sm" color="red.500">Error loading resumes</Text>
        ) : resumes && resumes.length > 0 ? (
          <VStack spacing={4} align="stretch">
            <Select
              value={selectedResumeId || ''}
              onChange={handleResumeChange}
              bg="gray.50"
              _hover={{ bg: 'gray.100' }}
              _focus={{ bg: 'white', borderColor: 'purple.500' }}
            >
              {resumes.map((resume) => (
                <option key={resume.id} value={resume.id}>
                  {resume.title} {resume.fileData?.type ? `(${resume.fileData.type.split('/')[1]})` : ''}
                </option>
              ))}
            </Select>

            {selectedResumeId && resumes.find(r => r.id === selectedResumeId)?.fileData && (
              <Flex justify="flex-start">
                <Badge colorScheme="green" px={2} py={1} borderRadius="full">
                  Resume Selected ✓
                </Badge>
              </Flex>
            )}

            <Divider />

            <Text fontSize="sm" color="gray.500">
              Or upload a new resume:
            </Text>

            <Input
              type="file"
              accept="application/pdf,.pdf,image/*,.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
              onChange={onFileUpload}
              display="none"
              ref={fileInputRef}
            />

            <Button
              leftIcon={<FaUpload />}
              onClick={handleFileButtonClick}
              colorScheme="purple"
              variant="outline"
              size="md"
              width={{ base: "full", md: "auto" }}
            >
              Upload New Resume
            </Button>
          </VStack>
        ) : (
          <VStack spacing={4} align="stretch">
            <Text fontSize="sm" color="gray.500">
              You don't have any resumes yet. Upload one to get started:
            </Text>

            <Input
              type="file"
              accept="application/pdf,.pdf,image/*,.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
              onChange={onFileUpload}
              display="none"
              ref={fileInputRef}
            />

            <Button
              leftIcon={<FaUpload />}
              onClick={handleFileButtonClick}
              colorScheme="purple"
              variant="outline"
              size="md"
              width={{ base: "full", md: "auto" }}
            >
              Upload Resume
            </Button>
          </VStack>
        )}
      </FormControl>
    </Box>
  );
}
