import React, { useEffect, useState } from 'react';
import { Box, Text } from '@chakra-ui/react';

interface LiveRegionProps {
  message: string;
  politeness?: 'polite' | 'assertive' | 'off';
  clearAfter?: number; // Clear message after X milliseconds
}

/**
 * Live Region component for screen reader announcements
 * Used to announce dynamic content changes to assistive technologies
 */
const LiveRegion: React.FC<LiveRegionProps> = ({
  message,
  politeness = 'polite',
  clearAfter = 5000
}) => {
  const [currentMessage, setCurrentMessage] = useState('');

  useEffect(() => {
    if (message) {
      setCurrentMessage(message);
      
      if (clearAfter > 0) {
        const timer = setTimeout(() => {
          setCurrentMessage('');
        }, clearAfter);
        
        return () => clearTimeout(timer);
      }
    }
  }, [message, clearAfter]);

  return (
    <Box
      position="absolute"
      left="-10000px"
      width="1px"
      height="1px"
      overflow="hidden"
      aria-live={politeness}
      aria-atomic="true"
      role="status"
    >
      <Text>{currentMessage}</Text>
    </Box>
  );
};

export default LiveRegion;
