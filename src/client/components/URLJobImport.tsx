import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useAction, importJobFromUrl } from 'wasp/client/operations';
import {
  Box,
  Button,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  VStack,
  HStack,
  Text,
  useToast,
  Collapse,
  Icon,
  Tooltip,
  Badge,
} from '@chakra-ui/react';
import { FaLink, FaArrowRight, FaInfoCircle, FaGlobe } from 'react-icons/fa';

// Define the job data structure
type JobData = {
  title: string;
  company: string;
  location: string;
  description: string;
};

type URLJobImportProps = {
  onJobImported: (jobData: JobData) => void;
  buttonText?: string;
  isCollapsible?: boolean;
  initiallyOpen?: boolean;
  inForm?: boolean;
};

export default function URLJobImport({
  onJobImported,
  buttonText = 'Import from URL',
  isCollapsible = true,
  initiallyOpen = false,
  inForm = false,
}: URLJobImportProps) {
  const [isOpen, setIsOpen] = useState<boolean>(initiallyOpen);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const toast = useToast();

  const importJobFromUrlAction = useAction(importJobFromUrl);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<{ jobUrl: string }>();

  const onSubmit = async (data: { jobUrl: string }) => {
    setIsLoading(true);
    try {
      // Use the Wasp action
      const jobData = await importJobFromUrlAction({ url: data.jobUrl }) as JobData;

      toast({
        title: 'Job imported successfully',
        description: `Imported "${jobData.title}" at ${jobData.company}`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      onJobImported(jobData);
      reset();

      if (isCollapsible) {
        setIsOpen(false);
      }
    } catch (error: any) {
      console.error('URL import error:', error);
      toast({
        title: 'Failed to import job',
        description: error.message || 'An error occurred while importing the job. Please check the URL and try again.',
        status: 'error',
        duration: 8000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const toggleOpen = () => {
    if (isCollapsible) {
      setIsOpen(!isOpen);
    }
  };

  return (
    <Box width="100%">
      {isCollapsible ? (
        <Button
          leftIcon={<FaLink />}
          onClick={toggleOpen}
          variant="outline"
          colorScheme="blue"
          size="md"
          mb={isOpen ? 4 : 0}
          width={{ base: "full", md: "auto" }}
        >
          {buttonText}
        </Button>
      ) : null}

      <Collapse in={isCollapsible ? isOpen : true} animateOpacity>
        <Box
          p={4}
          borderWidth="1px"
          borderRadius="lg"
          borderColor="gray.200"
          bg="gray.50"
          width="100%"
        >
          {inForm ? (
            <VStack spacing={4} align="stretch">
              <HStack align="center">
                <Icon as={FaInfoCircle} color="gray.500" />
                <Text fontSize="sm" color="gray.600">
                  Paste any job posting URL to automatically extract job details
                </Text>
                <Badge colorScheme="blue" size="sm">
                  <Icon as={FaGlobe} mr={1} />
                  Universal
                </Badge>
              </HStack>

              <FormControl isInvalid={!!errors.jobUrl}>
                <FormLabel htmlFor="jobUrl" fontWeight="medium">
                  Job URL
                  <Tooltip
                    label="URL can be from LinkedIn, Indeed, Glassdoor, company websites, or any job board"
                    placement="top"
                    hasArrow
                  >
                    <Box as="span" display="inline-block">
                      <Icon as={FaInfoCircle} ml={1} color="gray.500" />
                    </Box>
                  </Tooltip>
                </FormLabel>
                <Input
                  id="jobUrl"
                  placeholder="https://jobs.example.com/software-engineer..."
                  {...register('jobUrl', {
                    required: 'Job URL is required',
                    pattern: {
                      value: /^https?:\/\/.+/i,
                      message: 'Please enter a valid URL starting with http:// or https://',
                    },
                  })}
                  bg="white"
                  _hover={{ bg: 'white' }}
                  _focus={{ bg: 'white', borderColor: 'blue.500' }}
                />
                <FormErrorMessage>
                  {errors.jobUrl && errors.jobUrl.message}
                </FormErrorMessage>
              </FormControl>

              <Button
                onClick={handleSubmit(onSubmit)}
                rightIcon={<FaArrowRight />}
                colorScheme="blue"
                isLoading={isLoading}
                loadingText="Importing..."
                width={{ base: "full", md: "auto" }}
                alignSelf="flex-end"
                type="button"
              >
                Import Job
              </Button>
            </VStack>
          ) : (
            <form onSubmit={handleSubmit(onSubmit)}>
              <VStack spacing={4} align="stretch">
                <HStack align="center">
                  <Icon as={FaInfoCircle} color="gray.500" />
                  <Text fontSize="sm" color="gray.600">
                    Paste any job posting URL to automatically extract job details
                  </Text>
                  <Badge colorScheme="blue" size="sm">
                    <Icon as={FaGlobe} mr={1} />
                    Universal
                  </Badge>
                </HStack>

                <FormControl isInvalid={!!errors.jobUrl}>
                  <FormLabel htmlFor="jobUrl" fontWeight="medium">
                    Job URL
                    <Tooltip
                      label="URL can be from LinkedIn, Indeed, Glassdoor, company websites, or any job board"
                      placement="top"
                      hasArrow
                    >
                      <Box as="span" display="inline-block">
                        <Icon as={FaInfoCircle} ml={1} color="gray.500" />
                      </Box>
                    </Tooltip>
                  </FormLabel>
                  <Input
                    id="jobUrl"
                    placeholder="https://jobs.example.com/software-engineer..."
                    {...register('jobUrl', {
                      required: 'Job URL is required',
                      pattern: {
                        value: /^https?:\/\/.+/i,
                        message: 'Please enter a valid URL starting with http:// or https://',
                      },
                    })}
                    bg="white"
                    _hover={{ bg: 'white' }}
                    _focus={{ bg: 'white', borderColor: 'blue.500' }}
                  />
                  <FormErrorMessage>
                    {errors.jobUrl && errors.jobUrl.message}
                  </FormErrorMessage>
                </FormControl>

                <Button
                  type="submit"
                  rightIcon={<FaArrowRight />}
                  colorScheme="blue"
                  isLoading={isLoading}
                  loadingText="Importing..."
                  width={{ base: "full", md: "auto" }}
                  alignSelf="flex-end"
                >
                  Import Job
                </Button>
              </VStack>
            </form>
          )}
        </Box>
      </Collapse>
    </Box>
  );
} 