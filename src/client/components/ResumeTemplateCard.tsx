import React from 'react';
import {
  Box,
  VStack,
  Text,
  Badge,
  useColorModeValue,
  AspectRatio,
  Icon,
  Flex,
  Button,
  HStack
} from '@chakra-ui/react';
import { FaEye } from 'react-icons/fa';
import { ResumeTemplate } from '../../shared/types';
import ResumeTemplateRenderer from './ResumeTemplateRenderer';

interface ResumeTemplateCardProps {
  template: ResumeTemplate;
  isSelected?: boolean;
  onSelect: (template: ResumeTemplate) => void;
  onPreview: (template: ResumeTemplate) => void;
}

const CATEGORY_COLORS = {
  professional: 'blue',
  creative: 'purple',
  modern: 'green',
  classic: 'gray'
} as const;

const ResumeTemplateCard: React.FC<ResumeTemplateCardProps> = ({
  template,
  isSelected = false,
  onSelect,
  onPreview
}) => {
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const selectedBorderColor = useColorModeValue('purple.400', 'purple.300');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');

  const handleSelect = () => {
    onSelect(template);
  };

  const handlePreview = (e: React.MouseEvent) => {
    e.stopPropagation();
    onPreview(template);
  };

  return (
    <Box
      borderRadius="lg"
      border="2px solid"
      borderColor={isSelected ? selectedBorderColor : borderColor}
      cursor="pointer"
      transition="all 0.2s"
      _hover={{
        bg: hoverBg,
        transform: 'translateY(-2px)',
        boxShadow: 'lg'
      }}
      onClick={handleSelect}
      position="relative"
      overflow="hidden"
      boxShadow={isSelected ? 'xl' : 'md'}
      transform={isSelected ? 'scale(1.02)' : 'scale(1)'}
      bg={isSelected ? useColorModeValue('purple.50', 'purple.900') : cardBg}
    >
      {/* Template Preview */}
      <AspectRatio ratio={3/4}>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          position="relative"
          bg="gray.50"
          p={2}
        >
          {/* Actual template preview */}
          <ResumeTemplateRenderer
            template={template}
            scale={0.8}
          />

          {/* Preview overlay */}
          <Box
            position="absolute"
            top={0}
            left={0}
            right={0}
            bottom={0}
            bg="blackAlpha.600"
            opacity={0}
            transition="opacity 0.2s"
            _hover={{ opacity: 1 }}
            display="flex"
            alignItems="center"
            justifyContent="center"
          >
            <Button
              leftIcon={<Icon as={FaEye} />}
              colorScheme="white"
              variant="solid"
              size="sm"
              onClick={handlePreview}
            >
              Preview
            </Button>
          </Box>
        </Box>
      </AspectRatio>

      {/* Template Info */}
      <VStack spacing={3} p={4} align="stretch">
        <Flex justify="space-between" align="flex-start">
          <VStack spacing={1} align="flex-start" flex={1}>
            <Text fontWeight="semibold" fontSize="md" lineHeight="short">
              {template.name}
            </Text>
            <Text fontSize="sm" color="gray.500" lineHeight="short">
              {template.description}
            </Text>
          </VStack>
          <Badge
            colorScheme={CATEGORY_COLORS[template.category]}
            variant="subtle"
            fontSize="xs"
            textTransform="capitalize"
            ml={2}
            flexShrink={0}
          >
            {template.category}
          </Badge>
        </Flex>

        {/* Template Features */}
        <HStack spacing={2} fontSize="xs" color="gray.500">
          <Text>{template.styles.layout.replace('-', ' ')}</Text>
          <Text>•</Text>
          <Text>{template.styles.fontFamily}</Text>
        </HStack>

        {/* Select Button */}
        <Button
          size="sm"
          colorScheme={isSelected ? 'purple' : 'gray'}
          variant={isSelected ? 'solid' : 'outline'}
          onClick={handleSelect}
          width="100%"
        >
          {isSelected ? 'Selected' : 'Select Template'}
        </Button>
      </VStack>

      {/* Selected Indicator */}
      {isSelected && (
        <Box
          position="absolute"
          top={2}
          right={2}
          bg="purple.500"
          color="white"
          borderRadius="full"
          p={1}
          fontSize="xs"
        >
          ✓
        </Box>
      )}
    </Box>
  );
};

export default ResumeTemplateCard;
