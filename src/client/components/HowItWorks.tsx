import {
  Box,
  Container,
  Heading,
  Text,
  SimpleGrid,
  Icon,
  VStack,
  useColorModeValue,
  Flex,
  Badge,
} from '@chakra-ui/react';
import { FaFileAlt, FaRobot, FaEdit, FaPaperPlane } from 'react-icons/fa';
import { motion } from 'framer-motion';

const MotionBox = motion(Box);

const steps = [
  {
    icon: FaFileAlt,
    title: 'Input Details',
    description: 'Enter job information and upload your CV/resume',
    badge: 'Step 1',
  },
  {
    icon: FaRobot,
    title: 'AI Generation',
    description: 'Our AI analyzes your profile and creates a personalized cover letter',
    badge: 'Step 2',
  },
  {
    icon: FaEdit,
    title: 'Customize',
    description: 'Review and adjust the generated content to match your style',
    badge: 'Step 3',
  },
  {
    icon: FaPaperPlane,
    title: 'Submit',
    description: 'Download your professional cover letter ready for submission',
    badge: 'Step 4',
  },
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5,
      ease: 'easeOut',
    },
  },
};

export default function HowItWorks() {
  const bgColor = useColorModeValue('whiteAlpha.900', 'gray.800');
  const borderColor = useColorModeValue('whiteAlpha.300', 'whiteAlpha.200');
  const iconColor = useColorModeValue('purple.500', 'purple.300');
  const iconBg = useColorModeValue('purple.50', 'purple.900');
  const badgeBg = useColorModeValue('purple.100', 'purple.900');
  const badgeColor = useColorModeValue('purple.700', 'purple.200');

  return (
    <Box
      position="relative"
      py={8}
      w="full"
    >
      <MotionBox
        as={SimpleGrid}
        columns={{ base: 1, sm: 2, lg: 4 }}
        spacing={6}
        w="full"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
      >
        {steps.map((step, index) => (
          <MotionBox
            key={index}
            variants={itemVariants}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <VStack
              p={6}
              bg={bgColor}
              borderRadius="xl"
              borderWidth="1px"
              borderColor={borderColor}
              spacing={5}
              h="full"
              transition="all 0.3s ease"
              _hover={{
                shadow: 'xl',
                borderColor: 'whiteAlpha.400',
                transform: 'translateY(-4px)',
              }}
              position="relative"
            >
              <Badge
                position="absolute"
                top={3}
                right={3}
                px={2}
                py={1}
                borderRadius="full"
                bg={badgeBg}
                color={badgeColor}
                fontWeight="medium"
                fontSize="xs"
              >
                {step.badge}
              </Badge>
              <Flex
                w={16}
                h={16}
                align="center"
                justify="center"
                borderRadius="xl"
                bg={iconBg}
                color={iconColor}
                transition="all 0.3s ease"
                _groupHover={{ transform: 'scale(1.1)' }}
              >
                <Icon as={step.icon} w={8} h={8} />
              </Flex>
              <VStack spacing={2}>
                <Heading size="sm" textAlign="center" color="blue.700" fontWeight="bold">
                  {step.title}
                </Heading>
                <Text
                  textAlign="center"
                  color="blue.900"
                  fontSize="sm"
                  lineHeight="tall"
                  fontWeight="medium"
                >
                  {step.description}
                </Text>
              </VStack>
            </VStack>
          </MotionBox>
        ))}
      </MotionBox>
    </Box>
  );
}