import React from 'react';
import {
  Box,
  HStack,
  VStack,
  Text,
  Button,
  IconButton,
  Select,
  Flex,
  useColorModeValue,
} from '@chakra-ui/react';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  itemsPerPageOptions?: number[];
  onPageChange: (page: number) => void;
  onItemsPerPageChange?: (itemsPerPage: number) => void;
  showItemsPerPage?: boolean;
  showSummary?: boolean;
  size?: 'sm' | 'md' | 'lg';
  isCompact?: boolean;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  itemsPerPageOptions = [6, 9, 12, 18, 24],
  onPageChange,
  onItemsPerPageChange,
  showItemsPerPage = true,
  showSummary = true,
  size = 'md',
  isCompact = false,
}) => {
  const paginationBg = useColorModeValue('gray.50', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.600', 'gray.300');

  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems);

  // Generate page numbers for pagination
  const getPageNumbers = (): (number | string)[] => {
    if (totalPages <= 7) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    const pages: (number | string)[] = [];
    pages.push(1);
    
    if (currentPage > 3) {
      pages.push('...');
    }
    
    const start = Math.max(2, currentPage - 1);
    const end = Math.min(totalPages - 1, currentPage + 1);
    
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
    
    if (currentPage < totalPages - 2) {
      pages.push('...');
    }
    
    if (totalPages > 1) {
      pages.push(totalPages);
    }
    
    return pages;
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const handleItemsPerPageChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    if (onItemsPerPageChange) {
      onItemsPerPageChange(Number(event.target.value));
    }
  };

  if (totalPages <= 1 && !showSummary) {
    return null;
  }

  return (
    <Box 
      bg={isCompact ? 'transparent' : paginationBg}
      p={isCompact ? 2 : 4}
      borderRadius={isCompact ? 'md' : 'lg'}
      border={isCompact ? 'none' : '1px'}
      borderColor={borderColor}
    >
      <Flex 
        direction={{ base: 'column', md: isCompact ? 'row' : 'row' }} 
        align="center" 
        justify="space-between"
        gap={isCompact ? 2 : 4}
      >
        {/* Summary and Items Per Page */}
        <Flex 
          direction={{ base: 'column', lg: 'row' }}
          align={{ base: 'center', lg: 'center' }}
          gap={4}
          flex={1}
        >
          {showSummary && (
            <Text fontSize={size === 'sm' ? 'xs' : 'sm'} color={textColor}>
              Showing {startIndex + 1}-{endIndex} of {totalItems} items
            </Text>
          )}

          {showItemsPerPage && onItemsPerPageChange && (
            <HStack spacing={2}>
              <Text fontSize={size === 'sm' ? 'xs' : 'sm'} color={textColor}>
                Per page:
              </Text>
              <Select 
                size={size === 'lg' ? 'md' : 'sm'}
                value={itemsPerPage} 
                onChange={handleItemsPerPageChange}
                maxW="80px"
              >
                {itemsPerPageOptions.map(option => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </Select>
            </HStack>
          )}
        </Flex>

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <HStack spacing={isCompact ? 1 : 2}>
            {/* Previous Button */}
            <IconButton
              aria-label="Previous page"
              icon={<FaChevronLeft />}
              size={size}
              variant="outline"
              onClick={handlePrevPage}
              isDisabled={currentPage === 1}
            />

            {/* Page Numbers - Desktop */}
            <HStack spacing={1} display={{ base: 'none', md: isCompact ? 'none' : 'flex', lg: 'flex' }}>
              {getPageNumbers().map((page, index) => (
                <React.Fragment key={index}>
                  {page === '...' ? (
                    <Text px={2} color={textColor} fontSize={size === 'sm' ? 'xs' : 'sm'}>
                      ...
                    </Text>
                  ) : (
                    <Button
                      size={size}
                      variant={currentPage === page ? 'solid' : 'outline'}
                      colorScheme={currentPage === page ? 'blue' : 'gray'}
                      onClick={() => onPageChange(page as number)}
                      minW={size === 'sm' ? '32px' : '40px'}
                    >
                      {page}
                    </Button>
                  )}
                </React.Fragment>
              ))}
            </HStack>

            {/* Mobile Page Selector */}
            <HStack spacing={2} display={{ base: 'flex', md: isCompact ? 'flex' : 'none', lg: isCompact ? 'flex' : 'none' }}>
              <Text fontSize={size === 'sm' ? 'xs' : 'sm'} color={textColor}>
                Page:
              </Text>
              <Select 
                size={size === 'lg' ? 'md' : 'sm'}
                value={currentPage} 
                onChange={(e) => onPageChange(Number(e.target.value))}
                maxW="80px"
              >
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                  <option key={page} value={page}>
                    {page}
                  </option>
                ))}
              </Select>
              <Text fontSize={size === 'sm' ? 'xs' : 'sm'} color={textColor}>
                of {totalPages}
              </Text>
            </HStack>

            {/* Next Button */}
            <IconButton
              aria-label="Next page"
              icon={<FaChevronRight />}
              size={size}
              variant="outline"
              onClick={handleNextPage}
              isDisabled={currentPage === totalPages}
            />
          </HStack>
        )}

        {/* Quick Jump - Large screens only */}
        {totalPages > 2 && !isCompact && (
          <HStack spacing={2} display={{ base: 'none', xl: 'flex' }}>
            <Button
              size={size}
              variant="ghost"
              onClick={() => onPageChange(1)}
              isDisabled={currentPage === 1}
            >
              First
            </Button>
            <Button
              size={size}
              variant="ghost"
              onClick={() => onPageChange(totalPages)}
              isDisabled={currentPage === totalPages}
            >
              Last
            </Button>
          </HStack>
        )}
      </Flex>
    </Box>
  );
};

export default Pagination; 