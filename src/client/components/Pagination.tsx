import React from 'react';
import {
  Box,
  HStack,
  VStack,
  Text,
  Button,
  IconButton,
  Select,
  Flex,
  useColorModeValue,
  useBreakpointValue,
} from '@chakra-ui/react';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  itemsPerPageOptions?: number[];
  onPageChange: (page: number) => void;
  onItemsPerPageChange?: (itemsPerPage: number) => void;
  showItemsPerPage?: boolean;
  showSummary?: boolean;
  size?: 'sm' | 'md' | 'lg';
  isCompact?: boolean;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  itemsPerPageOptions = [6, 9, 12, 18, 24],
  onPageChange,
  onItemsPerPageChange,
  showItemsPerPage = true,
  showSummary = true,
  size = 'md',
  isCompact = false,
}) => {
  const paginationBg = useColorModeValue('gray.50', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.600', 'gray.300');

  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems);

  // Generate page numbers for pagination
  const getPageNumbers = (): (number | string)[] => {
    if (totalPages <= 7) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    const pages: (number | string)[] = [];
    pages.push(1);
    
    if (currentPage > 3) {
      pages.push('...');
    }
    
    const start = Math.max(2, currentPage - 1);
    const end = Math.min(totalPages - 1, currentPage + 1);
    
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
    
    if (currentPage < totalPages - 2) {
      pages.push('...');
    }
    
    if (totalPages > 1) {
      pages.push(totalPages);
    }
    
    return pages;
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const handleItemsPerPageChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    if (onItemsPerPageChange) {
      onItemsPerPageChange(Number(event.target.value));
    }
  };

  if (totalPages <= 1 && !showSummary) {
    return null;
  }

  return (
    <Box
      bg="transparent"
      p={2}
      borderRadius="md"
    >
      <Flex
        direction={{ base: 'column', md: 'row' }}
        align="center"
        justify="space-between"
        gap={3}
      >
        {/* Minimalistic Summary */}
        <Flex
          direction="row"
          align="center"
          gap={3}
          flex={1}
        >
          {showSummary && (
            <Text fontSize="xs" color={textColor} fontWeight="500">
              {startIndex + 1}-{endIndex} of {totalItems}
            </Text>
          )}

          {showItemsPerPage && onItemsPerPageChange && (
            <HStack spacing={1}>
              <Select
                size="xs"
                value={itemsPerPage}
                onChange={handleItemsPerPageChange}
                maxW="60px"
                variant="ghost"
                fontSize="xs"
                border="none"
                _focus={{ border: "1px solid", borderColor: "blue.300" }}
              >
                {itemsPerPageOptions.map(option => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </Select>
            </HStack>
          )}
        </Flex>

        {/* Minimalistic Pagination Controls */}
        {totalPages > 1 && (
          <HStack spacing={1}>
            {/* Previous Button */}
            <IconButton
              aria-label="Previous page"
              icon={<FaChevronLeft />}
              size="sm"
              variant="ghost"
              onClick={handlePrevPage}
              isDisabled={currentPage === 1}
              borderRadius="full"
              _hover={{ bg: useColorModeValue('gray.100', 'gray.700') }}
            />

            {/* Simple Page Display */}
            <HStack spacing={1} display={{ base: 'none', md: 'flex' }}>
              {getPageNumbers().slice(0, 5).map((page, index) => (
                <React.Fragment key={index}>
                  {page === '...' ? (
                    <Text px={1} color={textColor} fontSize="xs">
                      ...
                    </Text>
                  ) : (
                    <Button
                      size="sm"
                      variant={currentPage === page ? 'solid' : 'ghost'}
                      colorScheme={currentPage === page ? 'blue' : 'gray'}
                      onClick={() => onPageChange(page as number)}
                      minW="32px"
                      h="32px"
                      borderRadius="full"
                      fontSize="xs"
                      fontWeight={currentPage === page ? '600' : '400'}
                    >
                      {page}
                    </Button>
                  )}
                </React.Fragment>
              ))}
            </HStack>

            {/* Mobile: Simple Page Counter */}
            <Text fontSize="xs" color={textColor} display={{ base: 'block', md: 'none' }}>
              {currentPage} / {totalPages}
            </Text>

            {/* Next Button */}
            <IconButton
              aria-label="Next page"
              icon={<FaChevronRight />}
              size="sm"
              variant="ghost"
              onClick={handleNextPage}
              isDisabled={currentPage === totalPages}
              borderRadius="full"
              _hover={{ bg: useColorModeValue('gray.100', 'gray.700') }}
            />
          </HStack>
        )}


      </Flex>
    </Box>
  );
};

export default Pagination; 