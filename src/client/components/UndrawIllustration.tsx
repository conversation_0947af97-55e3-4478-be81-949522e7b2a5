import React, { useState, useEffect } from 'react';
import { Box, Image, VStack, Text, Heading, BoxProps, Icon } from '@chakra-ui/react';
import { motion } from 'framer-motion';
import {
  FaBriefcase,
  FaFileAlt,
  FaComments,
  FaExclamationTriangle,
  FaCheckCircle,
  FaSpinner,
  FaUser,
  FaGraduationCap,
  FaSearch,
  FaChartLine
} from 'react-icons/fa';

const MotionBox = motion(Box);

// Fallback icon mapping for when illustrations fail to load
const fallbackIcons: Record<string, any> = {
  'job_hunt_re_q203': FaBriefcase,
  'newsletter_re_wrob': FaFileAlt,
  'resume_re_hkth': FaFileAlt,
  'interview_re_5jn2': FaComments,
  'searching_re_3ra9': FaSearch,
  'page_not_found_re_e9o6': FaExclamationTriangle,
  'warning_re_eoyh': FaExclamationTriangle,
  'server_down_re_8yzk': FaExclamationTriangle,
  'loading_re_5axr': FaSpinner,
  'completed_re_cisp': FaCheckCircle,
  'career_progress_re_99p2': FaChartLine,
  'mobile_login_re_9ntv': FaUser,
  'artificial_intelligence_re_enpp': FaCheckCircle,
  'online_learning_re_qw08': FaGraduationCap,
  'empty_re_opql': FaFileAlt,
};

export interface UndrawIllustrationProps extends BoxProps {
  /** The undraw.co illustration name (without .svg extension) */
  illustration: string;
  /** Title text to display below the illustration */
  title?: string;
  /** Description text to display below the title */
  description?: string;
  /** Size of the illustration */
  size?: 'sm' | 'md' | 'lg' | 'xl';
  /** Whether to show animation */
  animated?: boolean;
  /** Custom color for the illustration (hex without #) */
  primaryColor?: string;
}

const sizeMap = {
  sm: { width: '200px', height: '150px' },
  md: { width: '300px', height: '225px' },
  lg: { width: '400px', height: '300px' },
  xl: { width: '500px', height: '375px' },
};

/**
 * UndrawIllustration component for displaying undraw.co illustrations
 * with consistent styling and optional text content
 */
export default function UndrawIllustration({
  illustration,
  title,
  description,
  size = 'md',
  animated = true,
  primaryColor = '6366f1', // Default purple color
  children,
  ...props
}: UndrawIllustrationProps) {
  const { width, height } = sizeMap[size];
  const [imageError, setImageError] = useState(false);
  const [currentUrl, setCurrentUrl] = useState(0);

  // Add CSS for spin animation
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }
    `;
    document.head.appendChild(style);
    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, []);

  // Multiple URL formats to try for better compatibility
  // Try local images first, then undraw.co as fallback
  const illustrationUrls = [
    `/images/illustrations/${illustration.replace('_re_', '-').replace(/re_\w+$/, '')}.svg`,
    `/images/illustrations/${illustration}.svg`,
    `https://undraw.co/api/illustrations/${illustration}`,
    `https://undraw.co/illustrations/${illustration}.svg`,
    `https://undraw.co/api/illustrations/${illustration}.svg`,
  ].filter(Boolean);

  const handleImageError = () => {
    if (currentUrl < illustrationUrls.length - 1) {
      setCurrentUrl(currentUrl + 1);
    } else {
      setImageError(true);
    }
  };

  // Get fallback icon for this illustration
  const FallbackIcon = fallbackIcons[illustration] || FaFileAlt;

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  const imageVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: 'easeOut',
        delay: 0.2,
      },
    },
  };

  return (
    <MotionBox
      textAlign="center"
      py={8}
      variants={animated ? containerVariants : undefined}
      initial={animated ? 'hidden' : undefined}
      animate={animated ? 'visible' : undefined}
      {...props}
    >
      <VStack spacing={6} align="center">
        <MotionBox
          variants={animated ? imageVariants : undefined}
          initial={animated ? 'hidden' : undefined}
          animate={animated ? 'visible' : undefined}
        >
          {imageError ? (
            <Box
              width={width}
              height={height}
              bg={`${primaryColor}10`}
              borderRadius="xl"
              display="flex"
              alignItems="center"
              justifyContent="center"
              position="relative"
              overflow="hidden"
            >
              {/* Background decoration */}
              <Box
                position="absolute"
                top="-20%"
                right="-20%"
                width="60%"
                height="60%"
                bg={`${primaryColor}20`}
                borderRadius="full"
                opacity={0.3}
              />
              <Box
                position="absolute"
                bottom="-10%"
                left="-10%"
                width="40%"
                height="40%"
                bg={`${primaryColor}15`}
                borderRadius="full"
                opacity={0.4}
              />

              {/* Main icon */}
              <Icon
                as={FallbackIcon}
                boxSize={size === 'sm' ? '40px' : size === 'md' ? '60px' : size === 'lg' ? '80px' : '100px'}
                color={`#${primaryColor}`}
                opacity={0.8}
              />
            </Box>
          ) : (
            <Image
              src={illustrationUrls[currentUrl]}
              alt={title || 'Illustration'}
              width={width}
              height={height}
              objectFit="contain"
              loading="lazy"
              onError={handleImageError}
              fallback={
                <Box
                  width={width}
                  height={height}
                  bg={`${primaryColor}05`}
                  borderRadius="xl"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <VStack spacing={2}>
                    <Icon
                      as={FaSpinner}
                      boxSize="24px"
                      color={`#${primaryColor}`}
                      opacity={0.6}
                      style={{
                        animation: 'spin 1s linear infinite'
                      }}
                    />
                    <Text color="gray.500" fontSize="xs">
                      Loading...
                    </Text>
                  </VStack>
                </Box>
              }
            />
          )}
        </MotionBox>

        {(title || description || children) && (
          <VStack spacing={3} maxW="md">
            {title && (
              <Heading
                size="lg"
                color="gray.700"
                _dark={{ color: 'gray.200' }}
                textAlign="center"
              >
                {title}
              </Heading>
            )}

            {description && (
              <Text
                color="gray.600"
                _dark={{ color: 'gray.400' }}
                fontSize="md"
                textAlign="center"
                lineHeight="1.6"
              >
                {description}
              </Text>
            )}

            {children}
          </VStack>
        )}
      </VStack>
    </MotionBox>
  );
}

// Pre-defined illustration configurations for common use cases
export const IllustrationPresets = {
  // Empty States
  emptyJobs: {
    illustration: 'job_hunt_re_q203',
    title: 'No Jobs Yet',
    description: 'Start by adding your first job to track applications and generate cover letters.',
  },
  emptyCoverLetters: {
    illustration: 'newsletter_re_wrob',
    title: 'No Cover Letters Yet',
    description: 'Create your first cover letter by adding job details and uploading your resume.',
  },
  emptyResumes: {
    illustration: 'resume_re_hkth',
    title: 'No Resumes Found',
    description: 'Upload or create your first resume to get started with cover letter generation.',
  },
  emptyInterviewQuestions: {
    illustration: 'interview_re_5jn2',
    title: 'No Interview Questions',
    description: 'Generate practice questions based on your saved jobs to prepare for interviews.',
  },
  emptyApplications: {
    illustration: 'searching_re_3ra9',
    title: 'No Applications Tracked',
    description: 'Start tracking your job applications to stay organized in your job search.',
  },

  // Error States
  error404: {
    illustration: 'page_not_found_re_e9o6',
    title: 'Page Not Found',
    description: 'The page you are looking for might have been moved or deleted.',
  },
  errorGeneral: {
    illustration: 'warning_re_eoyh',
    title: 'Something Went Wrong',
    description: 'We encountered an unexpected error. Please try again later.',
  },
  errorNetwork: {
    illustration: 'server_down_re_8yzk',
    title: 'Connection Problem',
    description: 'Unable to connect to our servers. Please check your internet connection.',
  },

  // Loading States
  loading: {
    illustration: 'loading_re_5axr',
    title: 'Loading...',
    description: 'Please wait while we process your request.',
  },

  // Success States
  success: {
    illustration: 'completed_re_cisp',
    title: 'Success!',
    description: 'Your action has been completed successfully.',
  },

  // Onboarding/Welcome
  welcome: {
    illustration: 'career_progress_re_99p2',
    title: 'Welcome to CareerDart',
    description: 'Launch your career to new heights with rocket-powered AI tools for creating stunning resumes, cover letters, and managing job applications.',
  },
  login: {
    illustration: 'mobile_login_re_9ntv',
    title: 'Welcome Back',
    description: 'Sign in to access your personalized career tools and continue your job search journey.',
  },

  // Feature Specific
  aiGeneration: {
    illustration: 'artificial_intelligence_re_enpp',
    title: 'AI-Powered Generation',
    description: 'Our advanced AI creates personalized cover letters tailored to each job application.',
  },
  interviewPrep: {
    illustration: 'interview_re_5jn2',
    title: 'Interview Preparation',
    description: 'Practice with AI-generated questions specific to your target roles.',
  },
  learningCenter: {
    illustration: 'online_learning_re_qw08',
    title: 'Learning Center',
    description: 'Enhance your career skills with curated learning resources and tutorials.',
  },
};
