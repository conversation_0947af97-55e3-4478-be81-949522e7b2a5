import React from 'react';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  ArcElement,
  Filler,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  ChartTooltip,
  Legend,
  ArcElement,
  Filler
);

interface ChartComponentsProps {
  chartType?: 'line' | 'bar' | 'doughnut';
  data: any;
  options: any;
}

const ChartComponents: React.FC<ChartComponentsProps> = ({
  chartType = 'line',
  data,
  options,
}) => {
  const renderChart = () => {
    switch (chartType) {
      case 'bar':
        return <Bar data={data} options={options} />;
      case 'doughnut':
        return <Doughnut data={data} options={options} />;
      case 'line':
      default:
        return <Line data={data} options={options} />;
    }
  };

  return (
    <div style={{ height: '100%', width: '100%' }}>
      {renderChart()}
    </div>
  );
};

export default ChartComponents; 