import { useState, useEffect } from 'react';
import {
  VStack,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Button,
  HStack,
  IconButton,
  Box,
  useToast,
  Divider,
  Heading,
  SimpleGrid,
  useBreakpointValue,
  Checkbox,
  Text,
  useColorModeValue,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import { AddIcon, DeleteIcon, CloseIcon } from '@chakra-ui/icons';
import { FaSave } from 'react-icons/fa';
import { type Resume } from '../../shared/types';

interface ResumeFormProps {
  initialData?: Partial<Resume>;
  onSubmit: (data: Partial<Resume>) => Promise<void>;
  onCancel: () => void;
}

export default function ResumeForm({ initialData, onSubmit, onCancel }: ResumeFormProps) {
  const [formData, setFormData] = useState<Partial<Resume>>({
    title: '',
    personalInfo: {
      fullName: '',
      email: '',
      phone: '',
      location: '',
      linkedIn: '',
      website: '',
    },
    summary: '',
    experience: [],
    education: [],
    skills: [],
    certifications: [],
  });

  // Responsive layout
  const columnCount = useBreakpointValue({ base: 1, md: 2 });
  const buttonSize = useBreakpointValue({ base: "sm", md: "md" });
  const spacing = useBreakpointValue({ base: 3, md: 4 });

  const toast = useToast();

  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({
        ...prev,
        ...initialData,
        personalInfo: {
          fullName: '',
          email: '',
          phone: '',
          location: '',
          linkedIn: '',
          website: '',
          ...initialData.personalInfo
        }
      }));
    }
  }, [initialData]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await onSubmit(formData);
      // Note: Success toast is handled by the parent component (ResumeManager)
      // to avoid duplicate notifications and provide better context
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Something went wrong. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const addExperience = () => {
    setFormData(prev => ({
      ...prev,
      experience: [
        ...(prev.experience || []),
        {
          id: Date.now().toString(),
          company: '',
          position: '',
          startDate: '',
          endDate: '',
          current: false,
          description: '',
          achievements: [],
        },
      ],
    }));
  };

  const addEducation = () => {
    setFormData(prev => ({
      ...prev,
      education: [
        ...(prev.education || []),
        {
          id: Date.now().toString(),
          institution: '',
          degree: '',
          field: '',
          startDate: '',
          endDate: '',
          current: false,
        },
      ],
    }));
  };

  const addSkill = () => {
    setFormData(prev => ({
      ...prev,
      skills: [...(prev.skills || []), ''],
    }));
  };

  const addCertification = () => {
    setFormData(prev => ({
      ...prev,
      certifications: [
        ...(prev.certifications || []),
        {
          id: Date.now().toString(),
          name: '',
          issuer: '',
          date: '',
        },
      ],
    }));
  };

  const addAchievement = (experienceId: string) => {
    setFormData(prev => ({
      ...prev,
      experience: prev.experience?.map(exp =>
        exp.id === experienceId
          ? { ...exp, achievements: [...(exp.achievements || []), ''] }
          : exp
      ),
    }));
  };

  const removeExperience = (id: string) => {
    setFormData(prev => ({
      ...prev,
      experience: prev.experience?.filter(exp => exp.id !== id),
    }));
  };

  const removeEducation = (id: string) => {
    setFormData(prev => ({
      ...prev,
      education: prev.education?.filter(edu => edu.id !== id),
    }));
  };

  const removeCertification = (id: string) => {
    setFormData(prev => ({
      ...prev,
      certifications: prev.certifications?.filter(cert => cert.id !== id),
    }));
  };

  const removeSkill = (index: number) => {
    setFormData(prev => ({
      ...prev,
      skills: prev.skills?.filter((_, i) => i !== index),
    }));
  };

  const removeAchievement = (experienceId: string, achievementIndex: number) => {
    setFormData(prev => ({
      ...prev,
      experience: prev.experience?.map(exp =>
        exp.id === experienceId
          ? { ...exp, achievements: exp.achievements?.filter((_, i) => i !== achievementIndex) }
          : exp
      ),
    }));
  };

  return (
    <form onSubmit={handleSubmit}>
      <VStack spacing={4} align="stretch">
        {/* AI Disclaimer for Resume Creation */}
        <Alert status="info" borderRadius="md" fontSize="sm">
          <AlertIcon />
          <Box>
            <Text fontWeight="medium">Resume Building Notice</Text>
            <Text fontSize="xs" color="gray.600">
              If using AI-parsed content from uploaded PDFs, please review and verify all information for accuracy.
              Customize content to match your actual experience and achievements.
            </Text>
          </Box>
        </Alert>

        <FormControl isRequired>
          <FormLabel fontSize="sm" fontWeight="medium">Resume Title</FormLabel>
          <Input
            value={formData.title}
            onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
            placeholder="e.g., Software Engineer Resume"
            size="sm"
          />
        </FormControl>

        <Box as="fieldset" border="none" p={0}>
          <Heading as="legend" size="sm" mb={3} fontWeight="medium">
            Personal Information
          </Heading>
          <SimpleGrid columns={{ base: 1, md: 2 }} spacing={3} width="100%">
            <FormControl isRequired>
              <FormLabel fontSize="sm">Full Name</FormLabel>
              <Input
                value={formData.personalInfo?.fullName}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  personalInfo: { ...prev.personalInfo!, fullName: e.target.value },
                }))}
                size="sm"
              />
            </FormControl>

            <FormControl isRequired>
              <FormLabel fontSize="sm">Email</FormLabel>
              <Input
                type="email"
                value={formData.personalInfo?.email}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  personalInfo: { ...prev.personalInfo!, email: e.target.value },
                }))}
                size="sm"
              />
            </FormControl>

            <FormControl isRequired>
              <FormLabel fontSize="sm">Phone</FormLabel>
              <Input
                value={formData.personalInfo?.phone}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  personalInfo: { ...prev.personalInfo!, phone: e.target.value },
                }))}
                size="sm"
              />
            </FormControl>

            <FormControl isRequired>
              <FormLabel fontSize="sm">Location</FormLabel>
              <Input
                value={formData.personalInfo?.location}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  personalInfo: { ...prev.personalInfo!, location: e.target.value },
                }))}
                size="sm"
              />
            </FormControl>

            <FormControl>
              <FormLabel fontSize="sm">LinkedIn Profile</FormLabel>
              <Input
                value={formData.personalInfo?.linkedIn}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  personalInfo: { ...prev.personalInfo!, linkedIn: e.target.value },
                }))}
                size="sm"
              />
            </FormControl>

            <FormControl>
              <FormLabel fontSize="sm">Website</FormLabel>
              <Input
                value={formData.personalInfo?.website}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  personalInfo: { ...prev.personalInfo!, website: e.target.value },
                }))}
                size="sm"
              />
            </FormControl>
          </SimpleGrid>
        </Box>

        <FormControl isRequired>
          <FormLabel fontSize="sm">Professional Summary</FormLabel>
          <Textarea
            value={formData.summary}
            onChange={(e) => setFormData(prev => ({ ...prev, summary: e.target.value }))}
            placeholder="Write a brief summary of your professional background and career goals..."
            rows={3}
            size="sm"
          />
        </FormControl>

        <Box as="fieldset" border="none" p={0}>
          <HStack justify="space-between" mb={3}>
            <Heading as="legend" size="sm" fontWeight="medium">Experience</Heading>
            <Button
              leftIcon={<AddIcon />}
              size="xs"
              onClick={addExperience}
              aria-label="Add new work experience entry"
            >
              Add Experience
            </Button>
          </HStack>

          <VStack spacing={3} width="100%">
            {formData.experience?.map((exp, index) => (
              <Box
                key={exp.id}
                p={3}
                borderWidth={1}
                borderRadius="md"
                bg={useColorModeValue('gray.50', 'gray.700')}
                width="100%"
              >
                <HStack justify="space-between" mb={3}>
                  <Heading size="xs">Experience {index + 1}</Heading>
                  <IconButton
                    aria-label="Delete experience"
                    icon={<DeleteIcon />}
                    size="xs"
                    colorScheme="red"
                    variant="ghost"
                    onClick={() => removeExperience(exp.id)}
                  />
                </HStack>

                <VStack spacing={3}>
                  <FormControl isRequired>
                    <FormLabel>Company</FormLabel>
                    <Input
                      value={exp.company}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        experience: prev.experience?.map(ex =>
                          ex.id === exp.id ? { ...ex, company: e.target.value } : ex
                        ),
                      }))}
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel>Position</FormLabel>
                    <Input
                      value={exp.position}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        experience: prev.experience?.map(ex =>
                          ex.id === exp.id ? { ...ex, position: e.target.value } : ex
                        ),
                      }))}
                    />
                  </FormControl>

                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={spacing}>
                    <FormControl isRequired>
                      <FormLabel>Start Date</FormLabel>
                      <Input
                        type="date"
                        value={exp.startDate}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          experience: prev.experience?.map(ex =>
                            ex.id === exp.id ? { ...ex, startDate: e.target.value } : ex
                          ),
                        }))}
                      />
                    </FormControl>

                    <FormControl isRequired={!exp.current}>
                      <FormLabel>End Date</FormLabel>
                      <Input
                        type="date"
                        value={exp.endDate}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          experience: prev.experience?.map(ex =>
                            ex.id === exp.id ? { ...ex, endDate: e.target.value } : ex
                          ),
                        }))}
                        disabled={exp.current}
                        placeholder={exp.current ? "Present" : ""}
                      />
                    </FormControl>
                  </SimpleGrid>

                  <Checkbox
                    isChecked={exp.current}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      experience: prev.experience?.map(ex =>
                        ex.id === exp.id ? { ...ex, current: e.target.checked, endDate: e.target.checked ? '' : ex.endDate } : ex
                      ),
                    }))}
                  >
                    I currently work here
                  </Checkbox>

                  <FormControl>
                    <FormLabel>Job Description</FormLabel>
                    <Textarea
                      value={exp.description}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        experience: prev.experience?.map(ex =>
                          ex.id === exp.id ? { ...ex, description: e.target.value } : ex
                        ),
                      }))}
                      placeholder="Describe your role, responsibilities, and key contributions..."
                      rows={3}
                    />
                  </FormControl>

                  {/* Achievements Section */}
                  <Box>
                    <HStack justify="space-between" mb={3}>
                      <Text fontWeight="medium" fontSize="sm">Key Achievements</Text>
                      <Button
                        leftIcon={<AddIcon />}
                        size="xs"
                        variant="outline"
                        onClick={() => addAchievement(exp.id)}
                      >
                        Add Achievement
                      </Button>
                    </HStack>

                    <VStack spacing={2}>
                      {exp.achievements?.map((achievement, achievementIndex) => (
                        <HStack key={achievementIndex} width="100%">
                          <FormControl>
                            <Input
                              value={achievement}
                              onChange={(e) => setFormData(prev => ({
                                ...prev,
                                experience: prev.experience?.map(ex =>
                                  ex.id === exp.id
                                    ? {
                                        ...ex,
                                        achievements: ex.achievements?.map((ach, i) =>
                                          i === achievementIndex ? e.target.value : ach
                                        ),
                                      }
                                    : ex
                                ),
                              }))}
                              placeholder="Describe a specific achievement or accomplishment..."
                              size="sm"
                            />
                          </FormControl>
                          <IconButton
                            aria-label="Delete achievement"
                            icon={<DeleteIcon />}
                            size="xs"
                            onClick={() => removeAchievement(exp.id, achievementIndex)}
                          />
                        </HStack>
                      ))}
                      {(!exp.achievements || exp.achievements.length === 0) && (
                        <Text fontSize="xs" color="gray.500" textAlign="center" py={2}>
                          Add specific achievements to make your experience stand out
                        </Text>
                      )}
                    </VStack>
                  </Box>
                </VStack>
              </Box>
            ))}
          </VStack>
        </Box>

        <Box as="fieldset" border="none" p={0}>
          <HStack justify="space-between" mb={3}>
            <Heading as="legend" size="sm" fontWeight="medium">Education</Heading>
            <Button
              leftIcon={<AddIcon />}
              size="xs"
              onClick={addEducation}
              aria-label="Add new education entry"
            >
              Add Education
            </Button>
          </HStack>

          <VStack spacing={3} width="100%">
            {formData.education?.map((edu, index) => (
              <Box
                key={edu.id}
                p={3}
                borderWidth={1}
                borderRadius="md"
                bg={useColorModeValue('gray.50', 'gray.700')}
                width="100%"
              >
                <HStack justify="space-between" mb={3}>
                  <Heading size="xs">Education {index + 1}</Heading>
                  <IconButton
                    aria-label="Delete education"
                    icon={<DeleteIcon />}
                    size="xs"
                    colorScheme="red"
                    variant="ghost"
                    onClick={() => removeEducation(edu.id)}
                  />
                </HStack>

                <VStack spacing={4}>
                  <FormControl isRequired>
                    <FormLabel>Institution</FormLabel>
                    <Input
                      value={edu.institution}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        education: prev.education?.map(ed =>
                          ed.id === edu.id ? { ...ed, institution: e.target.value } : ed
                        ),
                      }))}
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel>Degree</FormLabel>
                    <Input
                      value={edu.degree}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        education: prev.education?.map(ed =>
                          ed.id === edu.id ? { ...ed, degree: e.target.value } : ed
                        ),
                      }))}
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel>Field of Study</FormLabel>
                    <Input
                      value={edu.field}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        education: prev.education?.map(ed =>
                          ed.id === edu.id ? { ...ed, field: e.target.value } : ed
                        ),
                      }))}
                    />
                  </FormControl>

                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={spacing}>
                    <FormControl isRequired>
                      <FormLabel>Start Date</FormLabel>
                      <Input
                        type="date"
                        value={edu.startDate}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          education: prev.education?.map(ed =>
                            ed.id === edu.id ? { ...ed, startDate: e.target.value } : ed
                          ),
                        }))}
                      />
                    </FormControl>

                    <FormControl isRequired={!edu.current}>
                      <FormLabel>End Date</FormLabel>
                      <Input
                        type="date"
                        value={edu.endDate}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          education: prev.education?.map(ed =>
                            ed.id === edu.id ? { ...ed, endDate: e.target.value } : ed
                          ),
                        }))}
                        disabled={edu.current}
                        placeholder={edu.current ? "Present" : ""}
                      />
                    </FormControl>
                  </SimpleGrid>

                  <Checkbox
                    isChecked={edu.current}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      education: prev.education?.map(ed =>
                        ed.id === edu.id ? { ...ed, current: e.target.checked, endDate: e.target.checked ? '' : ed.endDate } : ed
                      ),
                    }))}
                  >
                    I am currently studying here
                  </Checkbox>

                  <FormControl>
                    <FormLabel>GPA (Optional)</FormLabel>
                    <Input
                      value={edu.gpa || ''}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        education: prev.education?.map(ed =>
                          ed.id === edu.id ? { ...ed, gpa: e.target.value } : ed
                        ),
                      }))}
                      placeholder="e.g., 3.8/4.0"
                      maxW="200px"
                    />
                  </FormControl>
                </VStack>
              </Box>
            ))}
          </VStack>
        </Box>

        <Box as="fieldset" border="none" p={0}>
          <HStack justify="space-between" mb={4}>
            <Heading as="legend" size="sm" fontWeight="medium">Skills</Heading>
            <Button
              leftIcon={<AddIcon />}
              size="sm"
              onClick={addSkill}
              aria-label="Add new skill"
            >
              Add Skill
            </Button>
          </HStack>

          <VStack spacing={3}>
            {formData.skills?.map((skill, index) => (
              <HStack key={index} width="100%">
                <FormControl>
                  <Input
                    value={skill}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      skills: prev.skills?.map((s, i) =>
                        i === index ? e.target.value : s
                      ),
                    }))}
                    placeholder="e.g., JavaScript, Project Management, Adobe Photoshop"
                  />
                </FormControl>
                <IconButton
                  aria-label="Delete skill"
                  icon={<DeleteIcon />}
                  size="sm"
                  onClick={() => removeSkill(index)}
                />
              </HStack>
            ))}
            {(!formData.skills || formData.skills.length === 0) && (
              <Text fontSize="sm" color="gray.500" textAlign="center" py={4}>
                Add your technical and soft skills to showcase your expertise
              </Text>
            )}
          </VStack>
        </Box>

        {/* Certifications Section */}
        <Box as="fieldset" border="none" p={0}>
          <HStack justify="space-between" mb={3}>
            <Heading as="legend" size="sm" fontWeight="medium">Certifications</Heading>
            <Button
              leftIcon={<AddIcon />}
              size="xs"
              onClick={addCertification}
              aria-label="Add new certification"
            >
              Add Certification
            </Button>
          </HStack>

          <VStack spacing={3} width="100%">
            {formData.certifications?.map((cert, index) => (
              <Box
                key={cert.id}
                p={3}
                borderWidth={1}
                borderRadius="md"
                bg={useColorModeValue('gray.50', 'gray.700')}
                width="100%"
              >
                <HStack justify="space-between" mb={3}>
                  <Heading size="xs">Certification {index + 1}</Heading>
                  <IconButton
                    aria-label="Delete certification"
                    icon={<DeleteIcon />}
                    size="xs"
                    onClick={() => removeCertification(cert.id)}
                  />
                </HStack>

                <VStack spacing={4}>
                  <FormControl isRequired>
                    <FormLabel>Certification Name</FormLabel>
                    <Input
                      value={cert.name}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        certifications: prev.certifications?.map(c =>
                          c.id === cert.id ? { ...c, name: e.target.value } : c
                        ),
                      }))}
                      placeholder="e.g., AWS Certified Solutions Architect"
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel>Issuing Organization</FormLabel>
                    <Input
                      value={cert.issuer}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        certifications: prev.certifications?.map(c =>
                          c.id === cert.id ? { ...c, issuer: e.target.value } : c
                        ),
                      }))}
                      placeholder="e.g., Amazon Web Services"
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel>Date Obtained</FormLabel>
                    <Input
                      type="date"
                      value={cert.date}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        certifications: prev.certifications?.map(c =>
                          c.id === cert.id ? { ...c, date: e.target.value } : c
                        ),
                      }))}
                    />
                  </FormControl>
                </VStack>
              </Box>
            ))}
            {(!formData.certifications || formData.certifications.length === 0) && (
              <Text fontSize="sm" color="gray.500" textAlign="center" py={4}>
                Add professional certifications to demonstrate your expertise
              </Text>
            )}
          </VStack>
        </Box>

        {/* Action Buttons - Consistent with template selection */}
        <Box mt={6} pt={4} borderTop="1px solid" borderColor={useColorModeValue("gray.200", "gray.600")}>
          <VStack spacing={3} align="stretch">
            <Button
              type="submit"
              rightIcon={<FaSave />}
              colorScheme="purple"
              size="lg"
              width="100%"
            >
              {initialData ? 'Update Resume' : 'Create Resume'}
            </Button>
            <Button
              leftIcon={<CloseIcon />}
              variant="outline"
              onClick={onCancel}
              width="100%"
            >
              Cancel
            </Button>
          </VStack>
        </Box>
      </VStack>
    </form>
  );
}