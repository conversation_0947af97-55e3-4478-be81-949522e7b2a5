import { V<PERSON><PERSON><PERSON>, Button, ButtonGroup, Box } from '@chakra-ui/react';
import UndrawIllustration, { IllustrationPresets, UndrawIllustrationProps } from './UndrawIllustration';
import { motion } from 'framer-motion';

const MotionBox = motion(Box);

export interface EmptyStateProps extends Omit<UndrawIllustrationProps, 'illustration'> {
  /** Preset illustration type */
  preset?: keyof typeof IllustrationPresets;
  /** Custom illustration name if not using preset */
  illustration?: string;
  /** Primary action button */
  primaryAction?: {
    label: string;
    onClick: () => void;
    icon?: React.ReactElement;
    colorScheme?: string;
  };
  /** Secondary action button */
  secondaryAction?: {
    label: string;
    onClick: () => void;
    icon?: React.ReactElement;
    variant?: string;
  };
  /** Whether to show actions */
  showActions?: boolean;
}

/**
 * EmptyState component for displaying empty states with illustrations and actions
 */
export default function EmptyState({
  preset,
  illustration,
  title,
  description,
  primaryAction,
  secondaryAction,
  showActions = true,
  size = 'md',
  animated = true,
  primaryColor = '6366f1',
  children,
  ...props
}: EmptyStateProps) {
  // Use preset if provided, otherwise use custom illustration
  const illustrationConfig = preset ? IllustrationPresets[preset] : {
    illustration: 'empty_re_opql',
    title: 'No Items Found',
    description: 'Get started by adding your first item.'
  };
  const finalIllustration = illustration || illustrationConfig.illustration;
  const finalTitle = title || illustrationConfig.title;
  const finalDescription = description || illustrationConfig.description;

  const actionsVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        delay: 0.8,
        ease: 'easeOut',
      },
    },
  };

  return (
    <Box py={12} px={6} textAlign="center" {...props}>
      <UndrawIllustration
        illustration={finalIllustration}
        title={finalTitle}
        description={finalDescription}
        size={size}
        animated={animated}
        primaryColor={primaryColor}
      >
        {/* Custom children content */}
        {children}

        {/* Action buttons */}
        {showActions && (primaryAction || secondaryAction) && (
          <MotionBox
            variants={animated ? actionsVariants : undefined}
            initial={animated ? 'hidden' : undefined}
            animate={animated ? 'visible' : undefined}
            mt={6}
          >
            <ButtonGroup spacing={4} flexDirection={{ base: 'column', sm: 'row' }}>
              {primaryAction && (
                <Button
                  colorScheme={primaryAction.colorScheme || 'blue'}
                  size="lg"
                  leftIcon={primaryAction.icon}
                  onClick={primaryAction.onClick}
                  width={{ base: 'full', sm: 'auto' }}
                >
                  {primaryAction.label}
                </Button>
              )}

              {secondaryAction && (
                <Button
                  variant={secondaryAction.variant || 'outline'}
                  size="lg"
                  leftIcon={secondaryAction.icon}
                  onClick={secondaryAction.onClick}
                  width={{ base: 'full', sm: 'auto' }}
                >
                  {secondaryAction.label}
                </Button>
              )}
            </ButtonGroup>
          </MotionBox>
        )}
      </UndrawIllustration>
    </Box>
  );
}

// Convenience components for common empty states
export const EmptyJobs = (props: Omit<EmptyStateProps, 'preset'>) => (
  <EmptyState preset="emptyJobs" {...props} />
);

export const EmptyCoverLetters = (props: Omit<EmptyStateProps, 'preset'>) => (
  <EmptyState preset="emptyCoverLetters" {...props} />
);

export const EmptyResumes = (props: Omit<EmptyStateProps, 'preset'>) => (
  <EmptyState preset="emptyResumes" {...props} />
);

export const EmptyInterviewQuestions = (props: Omit<EmptyStateProps, 'preset'>) => (
  <EmptyState preset="emptyInterviewQuestions" {...props} />
);

export const EmptyApplications = (props: Omit<EmptyStateProps, 'preset'>) => (
  <EmptyState preset="emptyApplications" {...props} />
);
