import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  Box,
  VStack,
  Heading,
  Text,
  Button,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Code,
  Collapse,
  useDisclosure,
  HStack,
  IconButton,
  useToast,
} from '@chakra-ui/react';
import { FaRedo, FaBug, FaChevronDown, FaChevronUp, FaCopy } from 'react-icons/fa';
import { motion } from 'framer-motion';

const MotionBox = motion(Box);

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showErrorDetails?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error, errorInfo: null };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ errorInfo });
    
    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error Boundary caught an error:', error, errorInfo);
    }

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Report error to monitoring service (implement based on your monitoring solution)
    this.reportError(error, errorInfo);
  }

  reportError = async (error: Error, errorInfo: ErrorInfo) => {
    try {
      // Example: Send to error monitoring service
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     message: error.message,
      //     stack: error.stack,
      //     componentStack: errorInfo.componentStack,
      //     timestamp: new Date().toISOString(),
      //     userAgent: navigator.userAgent,
      //     url: window.location.href,
      //   }),
      // });
      console.log('Error reported:', { error, errorInfo });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  handleRetry = () => {
    this.setState({ hasError: false, error: null, errorInfo: null });
    window.location.reload();
  };

  handleReportBug = () => {
    const errorDetails = {
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      url: window.location.href,
      timestamp: new Date().toISOString(),
    };

    // Open bug report with pre-filled information
    const bugReportUrl = `mailto:<EMAIL>?subject=Bug Report&body=${encodeURIComponent(
      `Error Details:\n${JSON.stringify(errorDetails, null, 2)}`
    )}`;
    window.open(bugReportUrl);
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return <ErrorFallback 
        error={this.state.error} 
        errorInfo={this.state.errorInfo}
        onRetry={this.handleRetry}
        onReportBug={this.handleReportBug}
        showErrorDetails={this.props.showErrorDetails}
      />;
    }

    return this.props.children;
  }
}

interface ErrorFallbackProps {
  error: Error | null;
  errorInfo: ErrorInfo | null;
  onRetry: () => void;
  onReportBug: () => void;
  showErrorDetails?: boolean;
}

function ErrorFallback({ 
  error, 
  errorInfo, 
  onRetry, 
  onReportBug, 
  showErrorDetails = false 
}: ErrorFallbackProps) {
  const { isOpen, onToggle } = useDisclosure();
  const toast = useToast();

  const copyErrorDetails = () => {
    const errorDetails = {
      message: error?.message,
      stack: error?.stack,
      componentStack: errorInfo?.componentStack,
      timestamp: new Date().toISOString(),
    };

    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2));
    toast({
      title: 'Error details copied',
      status: 'success',
      duration: 2000,
      isClosable: true,
    });
  };

  return (
    <MotionBox
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      p={8}
      maxW="container.md"
      mx="auto"
      mt={8}
    >
      <VStack spacing={6} textAlign="center">
        <Box fontSize="6xl">😵</Box>
        
        <VStack spacing={2}>
          <Heading size="lg" color="red.500">
            Oops! Something went wrong
          </Heading>
          <Text color="gray.600" maxW="md">
            We encountered an unexpected error. Don't worry, our team has been notified 
            and we're working to fix it.
          </Text>
        </VStack>

        <Alert status="error" borderRadius="md" maxW="md">
          <AlertIcon />
          <Box>
            <AlertTitle>Error Details</AlertTitle>
            <AlertDescription>
              {error?.message || 'An unexpected error occurred'}
            </AlertDescription>
          </Box>
        </Alert>

        <HStack spacing={4}>
          <Button
            leftIcon={<FaRedo />}
            colorScheme="blue"
            onClick={onRetry}
            size="lg"
          >
            Try Again
          </Button>
          
          <Button
            leftIcon={<FaBug />}
            variant="outline"
            onClick={onReportBug}
            size="lg"
          >
            Report Bug
          </Button>
        </HStack>

        {(showErrorDetails || process.env.NODE_ENV === 'development') && (
          <Box w="full" maxW="md">
            <HStack justify="space-between" mb={2}>
              <Button
                leftIcon={isOpen ? <FaChevronUp /> : <FaChevronDown />}
                variant="ghost"
                size="sm"
                onClick={onToggle}
              >
                {isOpen ? 'Hide' : 'Show'} Technical Details
              </Button>
              
              {isOpen && (
                <IconButton
                  aria-label="Copy error details"
                  icon={<FaCopy />}
                  size="sm"
                  variant="ghost"
                  onClick={copyErrorDetails}
                />
              )}
            </HStack>

            <Collapse in={isOpen}>
              <Box
                bg="gray.50"
                p={4}
                borderRadius="md"
                border="1px"
                borderColor="gray.200"
                textAlign="left"
              >
                <VStack spacing={3} align="stretch">
                  <Box>
                    <Text fontWeight="bold" fontSize="sm" mb={1}>
                      Error Message:
                    </Text>
                    <Code fontSize="xs" p={2} display="block" whiteSpace="pre-wrap">
                      {error?.message}
                    </Code>
                  </Box>

                  {error?.stack && (
                    <Box>
                      <Text fontWeight="bold" fontSize="sm" mb={1}>
                        Stack Trace:
                      </Text>
                      <Code 
                        fontSize="xs" 
                        p={2} 
                        display="block" 
                        whiteSpace="pre-wrap"
                        maxH="200px"
                        overflowY="auto"
                      >
                        {error.stack}
                      </Code>
                    </Box>
                  )}

                  {errorInfo?.componentStack && (
                    <Box>
                      <Text fontWeight="bold" fontSize="sm" mb={1}>
                        Component Stack:
                      </Text>
                      <Code 
                        fontSize="xs" 
                        p={2} 
                        display="block" 
                        whiteSpace="pre-wrap"
                        maxH="200px"
                        overflowY="auto"
                      >
                        {errorInfo.componentStack}
                      </Code>
                    </Box>
                  )}
                </VStack>
              </Box>
            </Collapse>
          </Box>
        )}
      </VStack>
    </MotionBox>
  );
}

export default ErrorBoundary;

// Higher-order component for easy wrapping
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
}

// Hook for error reporting in functional components
export function useErrorHandler() {
  const toast = useToast();

  const reportError = React.useCallback((error: Error, context?: string) => {
    console.error('Error reported via useErrorHandler:', error, context);
    
    toast({
      title: 'An error occurred',
      description: context || 'Something went wrong. Please try again.',
      status: 'error',
      duration: 5000,
      isClosable: true,
    });

    // Report to monitoring service
    // Implementation depends on your monitoring solution
  }, [toast]);

  return { reportError };
}
