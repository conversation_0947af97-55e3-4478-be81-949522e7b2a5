import { Button, useColorMode, useColorModeValue, Icon } from '@chakra-ui/react';
import { FC } from 'react';
import { BsMoonStars, BsSun } from 'react-icons/bs';

const ThemeSwitch: FC = () => {
  const { colorMode, toggleColorMode } = useColorMode();

  // Theme-aware colors
  const buttonBg = useColorModeValue('white', 'gray.700');
  const buttonHoverBg = useColorModeValue('gray.100', 'gray.600');
  const iconColor = useColorModeValue('gray.600', 'yellow.300');

  return (
    <Button
      mr='3'
      border='1px'
      borderColor='border-contrast-sm'
      bg={buttonBg}
      _hover={{ bg: buttonHoverBg }}
      p='2'
      size='sm'
      onClick={toggleColorMode}
      aria-label={`Switch to ${colorMode === 'light' ? 'dark' : 'light'} mode`}
      transition="all 0.2s ease-in-out"
    >
      <Icon
        as={colorMode === 'dark' ? BsSun : BsMoonStars}
        color={iconColor}
        boxSize={4}
      />
    </Button>
  );
};

export default ThemeSwitch;
