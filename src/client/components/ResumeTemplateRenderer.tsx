import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Divider,
  SimpleGrid,
  Icon,
  Badge,
  Flex,
  useColorModeValue
} from '@chakra-ui/react';
import { FaEnvelope, FaPhone, FaMapMarkerAlt, FaLinkedin, FaGlobe } from 'react-icons/fa';
import { ResumeTemplate } from '../../shared/types';

interface ResumeTemplateRendererProps {
  template: ResumeTemplate;
  scale?: number;
}

const ResumeTemplateRenderer: React.FC<ResumeTemplateRendererProps> = ({
  template,
  scale = 1
}) => {
  const { sampleData, styles } = template;
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // Base styles
  const baseStyles = {
    width: `${300 * scale}px`,
    height: `${400 * scale}px`,
    fontSize: `${12 * scale}px`,
    fontFamily: styles.fontFamily,
    bg: cardBg,
    border: '1px solid',
    borderColor: borderColor,
    borderRadius: 'md',
    overflow: 'hidden',
    position: 'relative' as const
  };

  // Render different layouts based on template style
  const renderSingleColumn = () => (
    <VStack spacing={2} align="stretch" p={3}>
      {/* Header */}
      <VStack spacing={1} align="center" mb={2}>
        <Text 
          fontSize={`${16 * scale}px`} 
          fontWeight="bold" 
          color={styles.primaryColor}
          textAlign="center"
        >
          {sampleData.personalInfo.fullName}
        </Text>
        <HStack spacing={2} fontSize={`${10 * scale}px`} color="gray.600" flexWrap="wrap" justify="center">
          <HStack spacing={1}>
            <Icon as={FaEnvelope} boxSize={`${8 * scale}px`} />
            <Text>{sampleData.personalInfo.email}</Text>
          </HStack>
          <HStack spacing={1}>
            <Icon as={FaPhone} boxSize={`${8 * scale}px`} />
            <Text>{sampleData.personalInfo.phone}</Text>
          </HStack>
        </HStack>
      </VStack>

      <Divider borderColor={styles.primaryColor} />

      {/* Summary */}
      <Box>
        <Text fontSize={`${12 * scale}px`} fontWeight="semibold" color={styles.primaryColor} mb={1}>
          SUMMARY
        </Text>
        <Text fontSize={`${9 * scale}px`} color="gray.700" noOfLines={3}>
          {sampleData.summary}
        </Text>
      </Box>

      {/* Experience */}
      <Box>
        <Text fontSize={`${12 * scale}px`} fontWeight="semibold" color={styles.primaryColor} mb={1}>
          EXPERIENCE
        </Text>
        {sampleData.experience.slice(0, 1).map((exp) => (
          <Box key={exp.id}>
            <Text fontSize={`${10 * scale}px`} fontWeight="medium">
              {exp.position}
            </Text>
            <Text fontSize={`${9 * scale}px`} color="gray.600">
              {exp.company}
            </Text>
            <Text fontSize={`${8 * scale}px`} color="gray.500" noOfLines={2}>
              {exp.description}
            </Text>
          </Box>
        ))}
      </Box>

      {/* Skills */}
      <Box>
        <Text fontSize={`${12 * scale}px`} fontWeight="semibold" color={styles.primaryColor} mb={1}>
          SKILLS
        </Text>
        <Flex flexWrap="wrap" gap={1}>
          {sampleData.skills.slice(0, 6).map((skill, index) => (
            <Badge 
              key={index} 
              size="sm" 
              colorScheme="gray" 
              fontSize={`${7 * scale}px`}
            >
              {skill}
            </Badge>
          ))}
        </Flex>
      </Box>
    </VStack>
  );

  const renderTwoColumn = () => (
    <HStack spacing={0} align="stretch" h="full">
      {/* Left Column */}
      <Box 
        w="35%" 
        bg={styles.primaryColor} 
        color="white" 
        p={2}
      >
        <VStack spacing={2} align="stretch">
          <Text fontSize={`${14 * scale}px`} fontWeight="bold" textAlign="center">
            {sampleData.personalInfo.fullName.split(' ')[0]}
          </Text>
          <Text fontSize={`${12 * scale}px`} fontWeight="medium" textAlign="center">
            {sampleData.personalInfo.fullName.split(' ').slice(1).join(' ')}
          </Text>
          
          <Divider borderColor="whiteAlpha.400" my={1} />
          
          <Box>
            <Text fontSize={`${10 * scale}px`} fontWeight="semibold" mb={1}>
              CONTACT
            </Text>
            <VStack spacing={1} align="start" fontSize={`${8 * scale}px`}>
              <HStack spacing={1}>
                <Icon as={FaEnvelope} boxSize={`${6 * scale}px`} />
                <Text noOfLines={1}>{sampleData.personalInfo.email}</Text>
              </HStack>
              <HStack spacing={1}>
                <Icon as={FaPhone} boxSize={`${6 * scale}px`} />
                <Text>{sampleData.personalInfo.phone}</Text>
              </HStack>
            </VStack>
          </Box>

          <Box>
            <Text fontSize={`${10 * scale}px`} fontWeight="semibold" mb={1}>
              SKILLS
            </Text>
            <VStack spacing={0.5} align="start">
              {sampleData.skills.slice(0, 5).map((skill, index) => (
                <Text key={index} fontSize={`${7 * scale}px`}>
                  • {skill}
                </Text>
              ))}
            </VStack>
          </Box>
        </VStack>
      </Box>

      {/* Right Column */}
      <Box w="65%" p={2}>
        <VStack spacing={2} align="stretch">
          <Box>
            <Text fontSize={`${11 * scale}px`} fontWeight="semibold" color={styles.primaryColor} mb={1}>
              SUMMARY
            </Text>
            <Text fontSize={`${8 * scale}px`} color="gray.700" noOfLines={4}>
              {sampleData.summary}
            </Text>
          </Box>

          <Box>
            <Text fontSize={`${11 * scale}px`} fontWeight="semibold" color={styles.primaryColor} mb={1}>
              EXPERIENCE
            </Text>
            {sampleData.experience.slice(0, 1).map((exp) => (
              <Box key={exp.id}>
                <Text fontSize={`${9 * scale}px`} fontWeight="medium">
                  {exp.position}
                </Text>
                <Text fontSize={`${8 * scale}px`} color="gray.600">
                  {exp.company}
                </Text>
                <Text fontSize={`${7 * scale}px`} color="gray.500" noOfLines={3}>
                  {exp.description}
                </Text>
              </Box>
            ))}
          </Box>
        </VStack>
      </Box>
    </HStack>
  );

  const renderSidebar = () => (
    <HStack spacing={0} align="stretch" h="full">
      {/* Sidebar */}
      <Box 
        w="30%" 
        bg={`${styles.primaryColor}20`}
        borderRight="3px solid"
        borderColor={styles.primaryColor}
        p={2}
      >
        <VStack spacing={2} align="stretch">
          <Box textAlign="center">
            <Text fontSize={`${12 * scale}px`} fontWeight="bold" color={styles.primaryColor}>
              {sampleData.personalInfo.fullName.split(' ')[0]}
            </Text>
            <Text fontSize={`${10 * scale}px`} fontWeight="medium" color={styles.primaryColor}>
              {sampleData.personalInfo.fullName.split(' ').slice(1).join(' ')}
            </Text>
          </Box>
          
          <Box>
            <Text fontSize={`${9 * scale}px`} fontWeight="semibold" color={styles.primaryColor} mb={1}>
              CONTACT
            </Text>
            <VStack spacing={0.5} align="start" fontSize={`${7 * scale}px`}>
              <Text noOfLines={1}>{sampleData.personalInfo.email}</Text>
              <Text>{sampleData.personalInfo.phone}</Text>
            </VStack>
          </Box>

          <Box>
            <Text fontSize={`${9 * scale}px`} fontWeight="semibold" color={styles.primaryColor} mb={1}>
              SKILLS
            </Text>
            <VStack spacing={0.5} align="start">
              {sampleData.skills.slice(0, 4).map((skill, index) => (
                <Text key={index} fontSize={`${6 * scale}px`} color="gray.700">
                  {skill}
                </Text>
              ))}
            </VStack>
          </Box>
        </VStack>
      </Box>

      {/* Main Content */}
      <Box w="70%" p={2}>
        <VStack spacing={2} align="stretch">
          <Box>
            <Text fontSize={`${10 * scale}px`} fontWeight="semibold" color={styles.primaryColor} mb={1}>
              SUMMARY
            </Text>
            <Text fontSize={`${8 * scale}px`} color="gray.700" noOfLines={3}>
              {sampleData.summary}
            </Text>
          </Box>

          <Box>
            <Text fontSize={`${10 * scale}px`} fontWeight="semibold" color={styles.primaryColor} mb={1}>
              EXPERIENCE
            </Text>
            {sampleData.experience.slice(0, 1).map((exp) => (
              <Box key={exp.id}>
                <Text fontSize={`${9 * scale}px`} fontWeight="medium">
                  {exp.position}
                </Text>
                <Text fontSize={`${8 * scale}px`} color="gray.600">
                  {exp.company}
                </Text>
                <Text fontSize={`${7 * scale}px`} color="gray.500" noOfLines={2}>
                  {exp.description}
                </Text>
              </Box>
            ))}
          </Box>
        </VStack>
      </Box>
    </HStack>
  );

  const renderLayout = () => {
    switch (styles.layout) {
      case 'two-column':
        return renderTwoColumn();
      case 'sidebar':
        return renderSidebar();
      default:
        return renderSingleColumn();
    }
  };

  return (
    <Box {...baseStyles}>
      {renderLayout()}
    </Box>
  );
};

export default ResumeTemplateRenderer;
