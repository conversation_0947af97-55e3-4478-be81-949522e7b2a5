import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Badge,
  SimpleGrid,
  useColorModeValue,
  Tooltip,
  Icon,
  Flex
} from '@chakra-ui/react';
import { FaTrophy, FaMedal, FaStar } from 'react-icons/fa';

interface BadgeData {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  color: string;
  earnedAt?: string;
}

interface BadgeDisplayProps {
  badges: BadgeData[];
  title?: string;
  showEarnedDate?: boolean;
}

const BadgeDisplay: React.FC<BadgeDisplayProps> = ({
  badges,
  title = "Badges",
  showEarnedDate = true
}) => {
  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.600', 'gray.300');

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'learning': return FaTrophy;
      case 'experience': return FaMedal;
      case 'achievement': return FaStar;
      default: return FaTrophy;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  if (badges.length === 0) {
    return (
      <Box
        p={6}
        bg={cardBg}
        borderRadius="lg"
        border="1px"
        borderColor={borderColor}
        textAlign="center"
      >
        <Text color={textColor} fontSize="sm">
          No badges earned yet. Complete learning videos to earn your first badge!
        </Text>
      </Box>
    );
  }

  return (
    <VStack spacing={4} align="stretch">
      <Text fontSize="lg" fontWeight="semibold">
        {title} ({badges.length})
      </Text>
      
      <SimpleGrid columns={{ base: 1, sm: 2, md: 3, lg: 4 }} spacing={4}>
        {badges.map((badge) => (
          <Tooltip
            key={badge.id}
            label={badge.description}
            placement="top"
            hasArrow
          >
            <Box
              p={4}
              bg={cardBg}
              borderRadius="lg"
              border="1px"
              borderColor={borderColor}
              textAlign="center"
              cursor="pointer"
              transition="all 0.2s"
              _hover={{ transform: 'translateY(-2px)', boxShadow: 'md' }}
            >
              <VStack spacing={2}>
                <Flex
                  align="center"
                  justify="center"
                  w={12}
                  h={12}
                  borderRadius="full"
                  bg={`${badge.color}.100`}
                  color={`${badge.color}.500`}
                  fontSize="2xl"
                  position="relative"
                >
                  {badge.icon}
                  <Icon
                    as={getCategoryIcon(badge.category)}
                    position="absolute"
                    bottom="-1"
                    right="-1"
                    w={4}
                    h={4}
                    bg={cardBg}
                    borderRadius="full"
                    p={1}
                    color={`${badge.color}.400`}
                  />
                </Flex>
                
                <VStack spacing={1}>
                  <Text fontSize="sm" fontWeight="medium" noOfLines={1}>
                    {badge.name}
                  </Text>
                  
                  <Badge
                    size="sm"
                    colorScheme={badge.color}
                    variant="subtle"
                  >
                    {badge.category}
                  </Badge>
                  
                  {showEarnedDate && badge.earnedAt && (
                    <Text fontSize="xs" color={textColor}>
                      {formatDate(badge.earnedAt)}
                    </Text>
                  )}
                </VStack>
              </VStack>
            </Box>
          </Tooltip>
        ))}
      </SimpleGrid>
    </VStack>
  );
};

export default BadgeDisplay;
