import { Box, VStack, BoxProps, useBreakpointValue, useColorModeValue } from '@chakra-ui/react';
import { useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';

// Create motion components
const MotionBox = motion(Box);

interface ContentPageBoxProps extends BoxProps {
  children: React.ReactNode;
}

/**
 * ContentPageBox is a specialized container for content pages (Resume, Jobs, Cover Letter, Profile)
 * that provides consistent width and spacing across all pages
 * Enhanced with responsive design and subtle animations
 */
export default function ContentPageBox({ children, ...props }: ContentPageBoxProps) {
  const location = useLocation();

  // Background color with subtle gradient
  const bgColor = useColorModeValue('bg-overlay', 'gray.800');

  return (
    <MotionBox
      as="main"
      id="main-content"
      role="main"
      aria-label="Main content"
      width="100%"
      maxW="none" // Remove max width constraint - let parent container handle it
      mx="auto"
      mt={5}
      position="relative"
      overflowX="visible"
      zIndex="1"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      {...props}
    >
      <VStack
        bgColor={bgColor}
        gap={{ base: 3, md: 4 }}
        py={{ base: 4, md: 5 }}
        px={{ base: 4, sm: 6, md: 8 }}
        align="stretch"
        width="100%"
        borderRadius={{ base: "md", md: "lg" }}
        overflow="visible"
        boxShadow="none"
        spacing={{ base: 3, md: 4 }}
      >
        {children}
      </VStack>
    </MotionBox>
  );
}
