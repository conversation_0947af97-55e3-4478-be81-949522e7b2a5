import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, But<PERSON>, Box } from '@chakra-ui/react';
import UndrawIllustration, { IllustrationPresets, UndrawIllustrationProps } from './UndrawIllustration';
import { motion } from 'framer-motion';
import { FaCheck, FaHome, FaArrowRight } from 'react-icons/fa';

const MotionBox = motion(Box);

export interface SuccessStateProps extends Omit<UndrawIllustrationProps, 'illustration'> {
  /** Preset success type */
  preset?: 'success';
  /** Custom illustration name if not using preset */
  illustration?: string;
  /** Primary action button */
  primaryAction?: {
    label: string;
    onClick: () => void;
    icon?: React.ReactElement;
    colorScheme?: string;
  };
  /** Secondary action button */
  secondaryAction?: {
    label: string;
    onClick: () => void;
    icon?: React.ReactElement;
    variant?: string;
  };
  /** Whether to show actions */
  showActions?: boolean;
  /** Auto-hide after duration (in milliseconds) */
  autoHide?: number;
  /** Callback when auto-hide triggers */
  onAutoHide?: () => void;
}

/**
 * SuccessState component for displaying success states with illustrations and actions
 */
export default function SuccessState({
  preset = 'success',
  illustration,
  title,
  description,
  primaryAction,
  secondaryAction,
  showActions = true,
  autoHide,
  onAutoHide,
  size = 'md',
  animated = true,
  primaryColor = '10b981', // Green color for success
  children,
  ...props
}: SuccessStateProps) {
  // Use preset configuration
  const illustrationConfig = IllustrationPresets[preset];
  const finalIllustration = illustration || illustrationConfig.illustration;
  const finalTitle = title || illustrationConfig.title;
  const finalDescription = description || illustrationConfig.description;

  // Auto-hide functionality
  React.useEffect(() => {
    if (autoHide && onAutoHide) {
      const timer = setTimeout(() => {
        onAutoHide();
      }, autoHide);

      return () => clearTimeout(timer);
    }
  }, [autoHide, onAutoHide]);

  const actionsVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        delay: 0.8,
        ease: 'easeOut',
      },
    },
  };

  return (
    <Box py={12} px={6} textAlign="center" {...props}>
      <UndrawIllustration
        illustration={finalIllustration}
        title={finalTitle}
        description={finalDescription}
        size={size}
        animated={animated}
        primaryColor={primaryColor}
      >
        {/* Custom children content */}
        {children}

        {/* Action buttons */}
        {showActions && (primaryAction || secondaryAction) && (
          <MotionBox
            variants={animated ? actionsVariants : undefined}
            initial={animated ? 'hidden' : undefined}
            animate={animated ? 'visible' : undefined}
            mt={6}
          >
            <VStack spacing={4}>
              {primaryAction && (
                <Button
                  colorScheme={primaryAction.colorScheme || 'green'}
                  size="lg"
                  leftIcon={primaryAction.icon}
                  onClick={primaryAction.onClick}
                  width={{ base: 'full', sm: 'auto' }}
                  minW="200px"
                >
                  {primaryAction.label}
                </Button>
              )}

              {secondaryAction && (
                <Button
                  variant={secondaryAction.variant || 'outline'}
                  size="lg"
                  leftIcon={secondaryAction.icon}
                  onClick={secondaryAction.onClick}
                  width={{ base: 'full', sm: 'auto' }}
                  minW="200px"
                >
                  {secondaryAction.label}
                </Button>
              )}
            </VStack>
          </MotionBox>
        )}
      </UndrawIllustration>
    </Box>
  );
}

// Convenience components for common success scenarios
export const GeneralSuccess = (props: Omit<SuccessStateProps, 'preset'>) => (
  <SuccessState preset="success" {...props} />
);

export const CoverLetterSuccess = (props: Omit<SuccessStateProps, 'preset'>) => (
  <SuccessState
    preset="success"
    title="Cover Letter Generated!"
    description="Your personalized cover letter has been created successfully."
    {...props}
  />
);

export const ResumeSuccess = (props: Omit<SuccessStateProps, 'preset'>) => (
  <SuccessState
    preset="success"
    title="Resume Saved!"
    description="Your resume has been saved successfully and is ready to use."
    {...props}
  />
);

export const JobSuccess = (props: Omit<SuccessStateProps, 'preset'>) => (
  <SuccessState
    preset="success"
    title="Job Added!"
    description="Your job has been added successfully to your collection."
    {...props}
  />
);

export const InterviewSuccess = (props: Omit<SuccessStateProps, 'preset'>) => (
  <SuccessState
    preset="success"
    title="Questions Generated!"
    description="Your interview questions have been generated and are ready for practice."
    {...props}
  />
);
