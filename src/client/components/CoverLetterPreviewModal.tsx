import {
  Modal,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalBody,
  ModalCloseButton,
  Button,
  Text,
  useClipboard,
  Tooltip,
} from '@chakra-ui/react';
import { type CoverLetter } from 'wasp/entities';
import { useNavigate } from 'react-router-dom';
import { FaEdit, FaCopy } from 'react-icons/fa';
import { Icon } from '@chakra-ui/react';

type CoverLetterPreviewModalProps = {
  coverLetter: CoverLetter | null;
  isOpen: boolean;
  onClose: () => void;
};

export default function CoverLetterPreviewModal({ coverLetter, isOpen, onClose }: CoverLetterPreviewModalProps) {
  const navigate = useNavigate();
  const { hasCopied, onCopy } = useClipboard(coverLetter?.content || '');

  const handleEdit = () => {
    if (coverLetter) {
      navigate(`/cover-letters/${coverLetter.id}`);
      onClose();
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="2xl">
      <ModalOverlay backdropFilter="auto" backdropInvert="15%" backdropBlur="2px" />
      <ModalContent maxW="container.xl">
        <ModalHeader>{coverLetter?.title}</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Text whiteSpace="pre-wrap">{coverLetter?.content}</Text>
        </ModalBody>
        <ModalFooter>
          <Tooltip
            label={hasCopied ? 'Copied!' : 'Copy to Clipboard'}
            placement="top"
            hasArrow
            closeOnClick={false}
          >
            <Button
              leftIcon={<Icon as={FaCopy} />}
              variant="outline"
              mr={3}
              onClick={onCopy}
            >
              {hasCopied ? 'Copied!' : 'Copy'}
            </Button>
          </Tooltip>
          <Button
            leftIcon={<Icon as={FaEdit} />}
            colorScheme="purple"
            onClick={handleEdit}
          >
            Edit
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}