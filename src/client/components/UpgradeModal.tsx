import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Button,
  VStack,
  HStack,
  Text,
  Box,
  Icon,
  Badge,
  useColorModeValue,
  Heading,
  Divider,
  List,
  ListItem,
  ListIcon,
  Alert,
  AlertIcon,
  AlertDescription,
} from '@chakra-ui/react';
import { FaRocket, FaCheck, FaCreditCard, FaBolt, FaInfinity } from 'react-icons/fa';
import { stripePayment, stripeGpt4Payment } from 'wasp/client/operations';

interface UpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  userCredits: number;
  feature: 'cover-letter' | 'resume' | 'interview' | 'general';
}

const UpgradeModal: React.FC<UpgradeModalProps> = ({
  isOpen,
  onClose,
  userCredits,
  feature
}) => {
  const [isBasicLoading, setIsBasicLoading] = React.useState(false);
  const [isPremiumLoading, setIsPremiumLoading] = React.useState(false);

  const cardBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.800', 'white');
  const mutedColor = useColorModeValue('gray.600', 'gray.400');

  const getFeatureMessage = () => {
    switch (feature) {
      case 'cover-letter':
        return {
          title: 'Generate More Cover Letters',
          description: 'You\'ve used all your free cover letter generations. Upgrade to continue creating personalized cover letters.',
          icon: FaRocket
        };
      case 'resume':
        return {
          title: 'Create More Resumes',
          description: 'You\'ve reached your free resume limit. Upgrade to build unlimited professional resumes.',
          icon: FaRocket
        };
      case 'interview':
        return {
          title: 'Generate More Interview Questions',
          description: 'You\'ve used all your free interview question generations. Upgrade to continue preparing for interviews.',
          icon: FaRocket
        };
      default:
        return {
          title: 'Upgrade to Continue',
          description: 'You\'ve used all your free credits. Upgrade to unlock unlimited access to all CareerDart features.',
          icon: FaRocket
        };
    }
  };

  const featureInfo = getFeatureMessage();

  const handleBasicUpgrade = async () => {
    setIsBasicLoading(true);
    try {
      const response = await stripePayment();
      const url = response.sessionUrl;
      if (url) window.open(url, '_self');
    } catch (error) {
      console.error('Payment error:', error);
      alert('Something went wrong. Please try again.');
    }
    setIsBasicLoading(false);
  };

  const handlePremiumUpgrade = async () => {
    setIsPremiumLoading(true);
    try {
      const response = await stripeGpt4Payment();
      const url = response.sessionUrl;
      if (url) window.open(url, '_self');
    } catch (error) {
      console.error('Payment error:', error);
      alert('Something went wrong. Please try again.');
    }
    setIsPremiumLoading(false);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="2xl"
      isCentered
      aria-labelledby="upgrade-modal-title"
      aria-describedby="upgrade-modal-description"
      closeOnOverlayClick={false}
      trapFocus={true}
    >
      <ModalOverlay backdropFilter="blur(10px)" bg="blackAlpha.300" />
      <ModalContent
        bg={cardBg}
        borderRadius="2xl"
        boxShadow="2xl"
        border="1px"
        borderColor={borderColor}
        mx={4}
        role="dialog"
        aria-modal="true"
      >
        <ModalHeader pb={2}>
          <VStack spacing={3} align="center" textAlign="center">
            <Box
              p={4}
              borderRadius="full"
              bg={useColorModeValue('blue.50', 'blue.900')}
              color={useColorModeValue('blue.600', 'blue.300')}
              role="img"
              aria-label={`${featureInfo.title} icon`}
            >
              <Icon as={featureInfo.icon} boxSize={8} />
            </Box>
            <VStack spacing={1}>
              <Heading id="upgrade-modal-title" size="lg" color={textColor}>
                {featureInfo.title}
              </Heading>
              <Text id="upgrade-modal-description" fontSize="md" color={mutedColor} maxW="md">
                {featureInfo.description}
              </Text>
            </VStack>
          </VStack>
        </ModalHeader>
        <ModalCloseButton />

        <ModalBody py={6}>
          <VStack spacing={6}>
            {/* Usage Alert */}
            <Alert
              status="warning"
              borderRadius="lg"
              bg={useColorModeValue('orange.50', 'orange.900')}
              borderColor={useColorModeValue('orange.200', 'orange.700')}
              borderWidth="1px"
            >
              <AlertIcon />
              <AlertDescription fontSize="sm">
                <Text fontWeight="medium">
                  Free credits used: {3 - userCredits}/3
                </Text>
                <Text fontSize="xs" color={mutedColor} mt={1}>
                  Upgrade now to get unlimited access to all features
                </Text>
              </AlertDescription>
            </Alert>

            {/* Plans */}
            <HStack spacing={4} align="stretch" w="full">
              {/* Basic Plan */}
              <Box
                flex={1}
                p={6}
                borderRadius="xl"
                bg={useColorModeValue('gray.50', 'gray.700')}
                borderWidth="2px"
                borderColor="transparent"
                transition="all 0.3s ease"
                _hover={{
                  borderColor: useColorModeValue('blue.300', 'blue.500'),
                  transform: 'translateY(-2px)'
                }}
              >
                <VStack spacing={4} align="stretch">
                  <VStack spacing={2} align="center">
                    <Text fontSize="lg" fontWeight="bold" color={textColor}>
                      Basic Plan
                    </Text>
                    <HStack align="baseline">
                      <Text fontSize="3xl" fontWeight="black" color={textColor}>
                        $2.95
                      </Text>
                      <Text fontSize="sm" color={mutedColor}>
                        /month
                      </Text>
                    </HStack>
                    <Text fontSize="sm" color={mutedColor} textAlign="center">
                      GPT-4o-mini • Unlimited
                    </Text>
                  </VStack>

                  <List spacing={2}>
                    <ListItem fontSize="sm">
                      <ListIcon as={FaCheck} color="green.500" />
                      Unlimited cover letters
                    </ListItem>
                    <ListItem fontSize="sm">
                      <ListIcon as={FaCheck} color="green.500" />
                      Unlimited resumes
                    </ListItem>
                    <ListItem fontSize="sm">
                      <ListIcon as={FaCheck} color="green.500" />
                      Interview preparation
                    </ListItem>
                    <ListItem fontSize="sm">
                      <ListIcon as={FaCheck} color="green.500" />
                      Job tracking
                    </ListItem>
                  </List>

                  <Button
                    colorScheme="blue"
                    variant="outline"
                    size="md"
                    borderRadius="lg"
                    isLoading={isBasicLoading}
                    onClick={handleBasicUpgrade}
                    leftIcon={<FaCreditCard />}
                    aria-label="Subscribe to Basic Plan for $2.95 per month"
                    aria-describedby={isBasicLoading ? "basic-loading-announcement" : undefined}
                    _focus={{
                      boxShadow: "0 0 0 3px rgba(66, 153, 225, 0.6)",
                      outline: "none"
                    }}
                  >
                    Get Started
                  </Button>
                  {isBasicLoading && (
                    <Text id="basic-loading-announcement" srOnly>
                      Processing Basic Plan subscription, please wait...
                    </Text>
                  )}
                </VStack>
              </Box>

              {/* Premium Plan */}
              <Box
                flex={1}
                p={6}
                borderRadius="xl"
                bg={useColorModeValue('blue.50', 'blue.900')}
                borderWidth="2px"
                borderColor={useColorModeValue('blue.300', 'blue.500')}
                position="relative"
                transition="all 0.3s ease"
                _hover={{
                  borderColor: useColorModeValue('blue.400', 'blue.400'),
                  transform: 'translateY(-2px)'
                }}
              >
                <Badge
                  position="absolute"
                  top="-10px"
                  left="50%"
                  transform="translateX(-50%)"
                  colorScheme="blue"
                  variant="solid"
                  borderRadius="full"
                  px={3}
                  py={1}
                  fontSize="xs"
                  fontWeight="bold"
                >
                  RECOMMENDED
                </Badge>

                <VStack spacing={4} align="stretch">
                  <VStack spacing={2} align="center">
                    <Text fontSize="lg" fontWeight="bold" color={useColorModeValue('blue.800', 'blue.200')}>
                      Premium Plan
                    </Text>
                    <HStack align="baseline">
                      <Text fontSize="3xl" fontWeight="black" color={useColorModeValue('blue.800', 'blue.200')}>
                        $5.95
                      </Text>
                      <Text fontSize="sm" color={useColorModeValue('blue.600', 'blue.400')}>
                        /month
                      </Text>
                    </HStack>
                    <Text fontSize="sm" color={useColorModeValue('blue.600', 'blue.400')} textAlign="center">
                      GPT-4o • Unlimited
                    </Text>
                  </VStack>

                  <List spacing={2}>
                    <ListItem fontSize="sm">
                      <ListIcon as={FaCheck} color="green.500" />
                      Everything in Basic
                    </ListItem>
                    <ListItem fontSize="sm">
                      <ListIcon as={FaBolt} color="yellow.500" />
                      Advanced AI (GPT-4o)
                    </ListItem>
                    <ListItem fontSize="sm">
                      <ListIcon as={FaInfinity} color="purple.500" />
                      Higher quality content
                    </ListItem>
                    <ListItem fontSize="sm">
                      <ListIcon as={FaCheck} color="green.500" />
                      Priority support
                    </ListItem>
                  </List>

                  <Button
                    colorScheme="blue"
                    size="md"
                    borderRadius="lg"
                    isLoading={isPremiumLoading}
                    onClick={handlePremiumUpgrade}
                    leftIcon={<FaBolt />}
                    aria-label="Subscribe to Premium Plan for $5.95 per month with GPT-4o"
                    aria-describedby={isPremiumLoading ? "premium-loading-announcement" : undefined}
                    _focus={{
                      boxShadow: "0 0 0 3px rgba(66, 153, 225, 0.6)",
                      outline: "none"
                    }}
                  >
                    Upgrade Now
                  </Button>
                  {isPremiumLoading && (
                    <Text id="premium-loading-announcement" srOnly>
                      Processing Premium Plan subscription, please wait...
                    </Text>
                  )}
                </VStack>
              </Box>
            </HStack>
          </VStack>
        </ModalBody>

        <ModalFooter pt={2}>
          <VStack spacing={2} w="full">
            <Divider />
            <Text fontSize="xs" color={mutedColor} textAlign="center">
              Cancel anytime • Secure payment via Stripe • 30-day money-back guarantee
            </Text>
          </VStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default UpgradeModal;
