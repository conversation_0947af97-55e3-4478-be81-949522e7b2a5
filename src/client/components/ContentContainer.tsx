import React from 'react';
import {
  Box,
  BoxProps,
  useColorModeValue,
  useBreakpointValue,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';

// Create motion components
const MotionBox = motion(Box);

interface ContentContainerProps extends BoxProps {
  children: React.ReactNode;
  delay?: number;
  noPadding?: boolean;
}

/**
 * Modern content container component for page content
 * Designed with Apple-inspired aesthetics and enhanced responsiveness
 */
const ContentContainer = React.forwardRef<HTMLDivElement, ContentContainerProps>(({
  children,
  delay = 0.2,
  noPadding = false,
  ...rest
}, ref) => {
  const bgColor = useColorModeValue('white', 'gray.800');

  // Responsive border radius
  const borderRadius = useBreakpointValue({
    base: "md",
    md: "lg"
  });

  // Responsive padding
  const padding = useBreakpointValue({
    base: noPadding ? 0 : 3,
    sm: noPadding ? 0 : 3.5,
    md: noPadding ? 0 : 4,
    lg: noPadding ? 0 : 5
  });

  // Subtle shadow for depth - Apple-style subtle shadow
  const boxShadow = useColorModeValue(
    '0 1px 3px rgba(0,0,0,0.02), 0 0 1px rgba(0,0,0,0.05)',
    '0 1px 3px rgba(0,0,0,0.1), 0 0 1px rgba(0,0,0,0.06)'
  );

  return (
    <MotionBox
      ref={ref}
      bg={bgColor}
      borderRadius={borderRadius}
      boxShadow={boxShadow}
      p={padding}
      mt={{ base: 1, md: 2 }}
      mb={{ base: 2, md: 3 }}
      width="100%"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay }}
      borderWidth="0"
      overflow="hidden"
      _hover={{
        boxShadow: '0 2px 6px rgba(0,0,0,0.04), 0 0 1px rgba(0,0,0,0.06)',
        transform: 'translateY(-1px)'
      }}
      sx={{ transition: "all 0.2s ease-in-out" }}
      {...rest}
    >
      {children}
    </MotionBox>
  );
});

// Add display name for better debugging
ContentContainer.displayName = 'ContentContainer';

export default ContentContainer;
