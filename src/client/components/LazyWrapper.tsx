import React, { Suspense, lazy, ComponentType } from 'react';
import { Box, Spinner, Center } from '@chakra-ui/react';

interface LazyWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  minHeight?: string;
}

// Enhanced lazy loading with error boundary
const DefaultFallback = ({ minHeight = '200px' }: { minHeight?: string }) => (
  <Center minH={minHeight}>
    <Spinner size="lg" color="blue.500" />
  </Center>
);

// Error boundary for lazy-loaded components
class LazyErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode; fallback?: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): { hasError: boolean } {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Lazy component loading error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <Box p={4} color="red.500">
          Failed to load component. Please refresh the page.
        </Box>
      );
    }

    return this.props.children;
  }
}

export const LazyWrapper: React.FC<LazyWrapperProps> = ({ 
  children, 
  fallback, 
  minHeight = '200px' 
}) => {
  return (
    <LazyErrorBoundary fallback={fallback}>
      <Suspense fallback={fallback || <DefaultFallback minHeight={minHeight} />}>
        {children}
      </Suspense>
    </LazyErrorBoundary>
  );
};

// Higher-order component for lazy loading with optimization
export function withLazyLoading<P extends Record<string, any> = Record<string, any>>(
  importFn: () => Promise<{ default: ComponentType<P> }>,
  options: {
    fallback?: React.ReactNode;
    minHeight?: string;
    preload?: boolean;
    priority?: 'high' | 'normal' | 'low';
  } = {}
) {
  const LazyComponent = lazy(() => {
    // Add artificial delay for low priority components to improve main-thread performance
    if (options.priority === 'low') {
      return new Promise<{ default: ComponentType<P> }>((resolve) => {
        setTimeout(() => {
          importFn().then(resolve).catch(() => {
            // Handle loading errors gracefully
            const ErrorComponent = () => <div>Failed to load component</div>;
            resolve({ default: ErrorComponent as ComponentType<P> });
          });
        }, 100);
      });
    }
    return importFn();
  });

  // Preload component on hover or intersection
  if (options.preload) {
    // Preload the component eagerly for better UX
    setTimeout(() => {
      importFn().catch(() => {
        // Silently fail preloading
      });
    }, 0);
  }

  return function WrappedLazyComponent(props: P) {
    return (
      <LazyWrapper 
        fallback={options.fallback} 
        minHeight={options.minHeight}
      >
        <LazyComponent {...(props as any)} />
      </LazyWrapper>
    );
  };
}

// Intersection Observer based lazy loading for better performance
export function withIntersectionLazyLoading<P extends Record<string, any> = Record<string, any>>(
  importFn: () => Promise<{ default: ComponentType<P> }>,
  options: {
    fallback?: React.ReactNode;
    minHeight?: string;
    rootMargin?: string;
    threshold?: number;
  } = {}
) {
  return function IntersectionLazyComponent(props: P) {
    const [shouldLoad, setShouldLoad] = React.useState(false);
    const [hasLoaded, setHasLoaded] = React.useState(false);
    const ref = React.useRef<HTMLDivElement>(null);

    React.useEffect(() => {
      if (!ref.current || hasLoaded) return;

      const observer = new IntersectionObserver(
        (entries) => {
          if (entries[0]?.isIntersecting) {
            setShouldLoad(true);
            setHasLoaded(true);
            observer.disconnect();
          }
        },
        {
          rootMargin: options.rootMargin || '100px',
          threshold: options.threshold || 0.1,
        }
      );

      observer.observe(ref.current);

      return () => observer.disconnect();
    }, [hasLoaded]);

    if (!shouldLoad) {
      return (
        <Box 
          ref={ref} 
          minH={options.minHeight || '200px'} 
          display="flex" 
          alignItems="center" 
          justifyContent="center"
        >
          {options.fallback || <DefaultFallback minHeight={options.minHeight} />}
        </Box>
      );
    }

    const LazyComponent = lazy(importFn);

    return (
      <LazyWrapper 
        fallback={options.fallback} 
        minHeight={options.minHeight}
      >
        <LazyComponent {...(props as any)} />
      </LazyWrapper>
    );
  };
}

// Route-based code splitting helper
export function createLazyRoute<P extends Record<string, any> = Record<string, any>>(
  importFn: () => Promise<{ default: ComponentType<P> }>,
  options: {
    preload?: boolean;
    priority?: 'high' | 'normal' | 'low';
  } = {}
) {
  // Preload route components based on navigation patterns
  if (options.preload && typeof window !== 'undefined') {
    // Preload on requestIdleCallback for better performance
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        importFn().catch(() => {
          // Silently fail preloading
        });
      });
    }
  }

  return withLazyLoading(importFn, {
    priority: options.priority,
    minHeight: '100vh',
    fallback: (
      <Center minH="100vh">
        <Spinner size="xl" color="blue.500" />
      </Center>
    ),
  });
}

// Utility for chunk-based imports to reduce bundle size
export function createChunkedImport<T>(
  importFn: () => Promise<T>,
  chunkName?: string
) {
  return () => {
    // Add webpackChunkName for better bundle analysis
    if (chunkName) {
      return import(
        /* webpackChunkName: "[request]" */
        /* webpackMode: "lazy" */
        /* webpackPrefetch: false */
        /* webpackPreload: false */
        `${chunkName}`
      ) as Promise<T>;
    }
    return importFn();
  };
}

export default LazyWrapper; 