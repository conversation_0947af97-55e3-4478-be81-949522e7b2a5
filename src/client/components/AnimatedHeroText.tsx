import { Box, Heading, useColorModeValue, ResponsiveValue } from '@chakra-ui/react';

interface HeroTextProps {
  staticStart?: string;
  middleWord?: string;
  staticEnd?: string;
  size?: string;
  color?: string;
  fontWeight?: string;
  letterSpacing?: string;
  textAlign?: ResponsiveValue<any>;
}

export default function AnimatedHeroText({
  staticStart = "Your Personal",
  middleWord = "Career",
  staticEnd = "Companion",
  size = "2xl",
  color = "white",
  fontWeight = "extrabold",
  letterSpacing = "tight",
  textAlign = "center"
}: HeroTextProps) {

  return (
    <Heading
      as="h1"
      size={size}
      color={color}
      fontWeight={fontWeight}
      letterSpacing={letterSpacing}
      textAlign={textAlign}
      animation="fade-in 0.5s ease-in"
      lineHeight="1.2"
    >
      {staticStart}{" "}
      <Box
        as="span"
        bgGradient="linear(to-r, cyan.400, blue.500, purple.600)"
        bgClip="text"
        fontWeight="black"
        textShadow="0 0 20px rgba(59, 130, 246, 0.3)"
      >
        {middleWord}
      </Box>{" "}
      {staticEnd}
    </Heading>
  );
}
