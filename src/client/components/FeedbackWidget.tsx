import React, { useState, useRef } from 'react';
import {
  Box,
  Button,
  Modal,
  ModalO<PERSON>lay,
  ModalContent,
  Modal<PERSON>eader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Textarea,
  VStack,
  HStack,
  Text,
  useDisclosure,
  useToast,
  IconButton,
  Badge,
  Image,
  useColorModeValue,
  Tooltip,
  Select,
  FormErrorMessage,
} from '@chakra-ui/react';
import { FaComment, FaCamera, FaTimes, FaPaperPlane, FaStar } from 'react-icons/fa';
import { useAuth } from 'wasp/client/auth';
import { useAction } from 'wasp/client/operations';
import { submitFeedback } from 'wasp/client/operations';
import { useForm } from 'react-hook-form';
import html2canvas from 'html2canvas';

interface FeedbackForm {
  type: string;
  description: string;
  guestEmail?: string;
  rating?: number;
}

const FeedbackWidget: React.FC = () => {
  const { data: user } = useAuth();

  // Debug log to verify component version
  console.log('🔧 FeedbackWidget v2.1 loaded', { hasUser: !!user, userEmail: user?.email });
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [screenshot, setScreenshot] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isTakingScreenshot, setIsTakingScreenshot] = useState(false);
  const [rating, setRating] = useState<number>(0);
  const [hoveredRating, setHoveredRating] = useState<number>(0);
  const toast = useToast();
  const submitFeedbackAction = useAction(submitFeedback);



  const {
    register,
    handleSubmit,
    reset,
    watch,
    getValues,
    formState: { errors }
  } = useForm<FeedbackForm>();

  const selectedType = watch('type');

  // Color scheme
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const textColor = useColorModeValue('gray.700', 'gray.200');
  const inputBg = useColorModeValue('white', 'gray.700');
  const inputBorder = useColorModeValue('#e2e8f0', 'gray.600');
  const readOnlyBg = useColorModeValue('#f7fafc', 'gray.600');
  const readOnlyColor = useColorModeValue('#4a5568', 'gray.300');

  const takeScreenshot = async () => {
    setIsTakingScreenshot(true);
    try {
      // Hide the feedback widget temporarily
      const feedbackButton = document.getElementById('feedback-widget');
      if (feedbackButton) {
        feedbackButton.style.display = 'none';
      }

      // Take screenshot of the entire page
      const canvas = await html2canvas(document.body, {
        height: window.innerHeight,
        width: window.innerWidth,
        scrollX: 0,
        scrollY: 0,
        useCORS: true,
        allowTaint: true,
      });

      // Show the feedback widget again
      if (feedbackButton) {
        feedbackButton.style.display = 'flex';
      }

      const screenshotDataUrl = canvas.toDataURL('image/png');
      setScreenshot(screenshotDataUrl);

      toast({
        title: 'Screenshot captured!',
        description: 'Screenshot has been attached to your feedback.',
        status: 'success',
        duration: 2000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error taking screenshot:', error);
      toast({
        title: 'Screenshot failed',
        description: 'Could not capture screenshot. You can still submit feedback.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsTakingScreenshot(false);
    }
  };

  const removeScreenshot = () => {
    setScreenshot(null);
  };

  const onSubmit = async (data: FeedbackForm) => {
    console.log('🔍 Feedback submission started:', {
      user: user ? { id: user.id, email: user.email, fullUser: user } : null,
      hasUser: !!user,
      userType: typeof user,
      userKeys: user ? Object.keys(user) : [],
      data,
      selectedType,
      rating,
      environment: process.env.NODE_ENV,
      apiUrl: process.env.REACT_APP_API_URL
    });

    // Validate guest email if user is not logged in
    if (!user && !data.guestEmail) {
      console.log('❌ Guest email validation failed');
      toast({
        title: 'Email required',
        description: 'Please provide your email address so we can respond to your feedback.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // For logged-in users, skip email validation
    if (user) {
      console.log('✅ Logged-in user detected, skipping email validation');
    }

    // Validate rating if type is 'rating'
    if (selectedType === 'rating' && (!rating || rating < 1 || rating > 5)) {
      console.log('❌ Rating validation failed:', { selectedType, rating });
      toast({
        title: 'Rating required',
        description: 'Please select a star rating from 1 to 5.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    console.log('✅ Validation passed, submitting feedback...');
    setIsSubmitting(true);
    try {
      // Try to submit feedback using the backend action first
      const submissionData = {
        type: data.type,
        description: data.description,
        screenshot: screenshot,
        url: window.location.href,
        userAgent: navigator.userAgent,
        guestEmail: user ? user.email : data.guestEmail, // Send user email for logged users, form email for guests
        rating: selectedType === 'rating' ? rating : undefined,
      };

      console.log('📤 Submitting feedback with data:', submissionData);

      const result = await submitFeedbackAction(submissionData);

      console.log('✅ Feedback submission successful:', result);

      toast({
        title: 'Feedback sent!',
        description: 'Thank you for your feedback. We\'ll review it soon.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // Reset form and close modal
      reset();
      setScreenshot(null);
      setRating(0);
      setHoveredRating(0);
      onClose();
    } catch (error) {
      console.error('❌ Error submitting feedback:', error);
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
      toast({
        title: 'Error sending feedback',
        description: error.message || 'Please try again later.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    setScreenshot(null);
    setRating(0);
    setHoveredRating(0);
    onClose();
  };

  // Rating component
  const StarRating = ({ value, onChange, size = "md" }: { value: number; onChange: (rating: number) => void; size?: string }) => {
    return (
      <HStack spacing={1}>
        {[1, 2, 3, 4, 5].map((star) => (
          <IconButton
            key={star}
            aria-label={`Rate ${star} stars`}
            icon={<FaStar />}
            size={size}
            variant="ghost"
            color={star <= (hoveredRating || value) ? "yellow.400" : "gray.300"}
            onClick={() => onChange(star)}
            onMouseEnter={() => setHoveredRating(star)}
            onMouseLeave={() => setHoveredRating(0)}
            _hover={{
              color: "yellow.400",
              transform: "scale(1.1)",
            }}
            transition="all 0.2s"
          />
        ))}
      </HStack>
    );
  };

  // Show widget for all users (both authenticated and guest users)

  return (
    <>
      {/* Vertical Feedback Button - Flush to Screen Edge, Fully Visible */}
      <Button
        id="feedback-widget"
        leftIcon={<FaComment />}
        position="fixed"
        top="70%"
        left="0"
        size="md"
        colorScheme="blue"
        borderRadius="0 8px 8px 0"
        boxShadow="lg"
        zIndex={9999}
        onClick={onOpen}
        transform="translateY(-50%) rotate(90deg)"
        transformOrigin="center"
        _hover={{
          transform: 'translateY(-50%) translateX(3px) rotate(90deg)',
          boxShadow: 'xl',
        }}
        transition="all 0.2s"
        bg="blue.500"
        color="white"
        fontSize="sm"
        fontWeight="500"
        px={4}
        py={2}
        minW="auto"
        h="40px"
        border="none"
        outline="none"
        _active={{
          transform: 'translateY(-50%) translateX(1px) rotate(90deg)',
        }}
        _focus={{
          boxShadow: 'xl',
        }}
      >
        Feedback
      </Button>

      {/* Feedback Modal - Positioned near the button */}
      <Modal isOpen={isOpen} onClose={handleClose} size="lg">
        <ModalOverlay bg="blackAlpha.600" />
        <ModalContent
          bg={bgColor}
          borderColor={borderColor}
          mx={4}
          position="fixed"
          left={{ base: "20px", md: "60px" }}
          top="40%"
          transform="translateY(-50%)"
          maxW={{ base: "calc(100vw - 40px)", md: "500px" }}
          borderRadius="xl"
          boxShadow="2xl"
        >
          <ModalHeader>
            <VStack spacing={2} align="start" w="full">
              <Text fontWeight="600" fontSize="lg">Send Feedback</Text>
              <Text fontSize="sm" color="gray.500" fontWeight="normal">
                We'd love to hear from you! Your feedback helps us improve CareerDart.
              </Text>
            </VStack>
          </ModalHeader>
          <ModalCloseButton />
          
          <form onSubmit={handleSubmit(onSubmit, (errors) => {
            console.log('❌ Form validation errors:', errors);
            console.log('Current form values:', getValues());
            console.log('User context:', { hasUser: !!user, user });
          })}>
            <ModalBody>
              <VStack spacing={4} align="stretch">
                {/* Email field for all users - v2.0 */}
                <FormControl isInvalid={!!errors.guestEmail}>
                  <FormLabel fontSize="sm" fontWeight="600" color={textColor}>
                    Your Email Address {user ? "(Account Email)" : "(Required)"}
                  </FormLabel>
                  <input
                    type="email"
                    placeholder={user ? user.email : "<EMAIL>"}
                    value={user ? user.email : undefined}
                    readOnly={!!user}
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: `1px solid ${inputBorder}`,
                      borderRadius: '6px',
                      fontSize: '14px',
                      outline: 'none',
                      backgroundColor: user ? readOnlyBg : inputBg,
                      cursor: user ? 'not-allowed' : 'text',
                      color: user ? readOnlyColor : 'inherit'
                    }}
                    {...register('guestEmail', {
                      required: !user ? 'Email is required for guest feedback' : false,
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: 'Please enter a valid email address'
                      }
                    })}
                  />
                  {user && (
                    <Text fontSize="xs" color="gray.500" mt={1}>
                      ✅ Using your account email
                    </Text>
                  )}
                  <FormErrorMessage>
                    {errors.guestEmail?.message}
                  </FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!errors.type}>
                  <FormLabel fontSize="sm" fontWeight="600" color={textColor}>
                    Feedback Type
                  </FormLabel>
                  <Select
                    placeholder="Select feedback type"
                    {...register('type', { required: 'Please select a feedback type' })}
                  >
                    <option value="rating">⭐ Rate Us</option>
                    <option value="bug">🐛 Bug Report</option>
                    <option value="feature">✨ Feature Request</option>
                    <option value="improvement">🔧 Improvement Suggestion</option>
                    <option value="general">💬 General Feedback</option>
                    <option value="other">📝 Other</option>
                  </Select>
                  <FormErrorMessage>
                    {errors.type?.message}
                  </FormErrorMessage>
                </FormControl>

                {/* Rating Section - Show only for "Rate Us" type */}
                {selectedType === 'rating' && (
                  <FormControl>
                    <FormLabel fontSize="sm" fontWeight="600" color={textColor}>
                      How would you rate your experience?
                    </FormLabel>
                    <VStack spacing={2} align="start">
                      <StarRating value={rating} onChange={setRating} />
                      <Text fontSize="xs" color={textColor}>
                        {rating === 0 && "Click to rate"}
                        {rating === 1 && "⭐ Poor"}
                        {rating === 2 && "⭐⭐ Fair"}
                        {rating === 3 && "⭐⭐⭐ Good"}
                        {rating === 4 && "⭐⭐⭐⭐ Very Good"}
                        {rating === 5 && "⭐⭐⭐⭐⭐ Excellent"}
                      </Text>
                    </VStack>
                  </FormControl>
                )}

                <FormControl isInvalid={!!errors.description}>
                  <FormLabel fontSize="sm" fontWeight="600" color={textColor}>
                    Description
                  </FormLabel>
                  <Textarea
                    placeholder="Please describe your feedback in detail..."
                    rows={6}
                    resize="vertical"
                    {...register('description', { 
                      required: 'Please provide a description',
                      minLength: { value: 10, message: 'Description must be at least 10 characters' }
                    })}
                  />
                  <FormErrorMessage>
                    {errors.description?.message}
                  </FormErrorMessage>
                </FormControl>

                {/* Screenshot Section */}
                <Box>
                  <FormLabel fontSize="sm" fontWeight="600" color={textColor} mb={2}>
                    Screenshot (Optional)
                  </FormLabel>
                  {screenshot ? (
                    <Box position="relative" borderRadius="md" overflow="hidden">
                      <Image
                        src={screenshot}
                        alt="Screenshot"
                        maxH="200px"
                        w="full"
                        objectFit="cover"
                        borderRadius="md"
                      />
                      <IconButton
                        aria-label="Remove screenshot"
                        icon={<FaTimes />}
                        position="absolute"
                        top={2}
                        right={2}
                        size="sm"
                        colorScheme="red"
                        onClick={removeScreenshot}
                      />
                      <Badge
                        position="absolute"
                        bottom={2}
                        left={2}
                        colorScheme="green"
                        variant="solid"
                      >
                        Screenshot attached
                      </Badge>
                    </Box>
                  ) : (
                    <Button
                      leftIcon={<FaCamera />}
                      variant="outline"
                      onClick={takeScreenshot}
                      isLoading={isTakingScreenshot}
                      loadingText="Capturing..."
                      w="full"
                    >
                      Capture Screenshot
                    </Button>
                  )}
                </Box>
              </VStack>
            </ModalBody>

            <ModalFooter>
              <HStack spacing={3}>
                <Button variant="ghost" onClick={handleClose}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  colorScheme="blue"
                  leftIcon={<FaPaperPlane />}
                  isLoading={isSubmitting}
                  loadingText="Sending..."
                  onClick={(e) => {
                    console.log('🔘 Submit button clicked', {
                      user: !!user,
                      formData: getValues(),
                      selectedType,
                      rating,
                      isSubmitting
                    });
                  }}
                >
                  Send Feedback
                </Button>
              </HStack>
            </ModalFooter>
          </form>
        </ModalContent>
      </Modal>
    </>
  );
};

export { FeedbackWidget };
export default FeedbackWidget;
