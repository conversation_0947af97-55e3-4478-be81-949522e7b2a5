import { Button, Icon } from '@chakra-ui/react';
import { FaArrowLeft } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';

interface BackButtonProps {
  to?: string;
  label?: string;
  variant?: string;
  size?: string;
}

/**
 * BackButton component for mobile navigation
 * Only shows on small screens for better UX
 */
export default function BackButton({ 
  to, 
  label = 'Back', 
  variant = 'ghost',
  size = 'sm'
}: BackButtonProps) {
  const navigate = useNavigate();

  const handleBack = () => {
    if (to) {
      navigate(to);
    } else {
      navigate(-1); // Go back to previous page
    }
  };

  return (
    <Button
      leftIcon={<Icon as={FaArrowLeft} />}
      variant={variant}
      size={size}
      onClick={handleBack}
      mb={4}
    >
      {label}
    </Button>
  );
} 