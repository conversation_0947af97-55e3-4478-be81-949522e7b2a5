import { useAuth } from "wasp/client/auth";
import { logout } from "wasp/client/auth";
import {
  H<PERSON><PERSON>ck,
  <PERSON>ton,
  Link,
  MenuList,
  MenuItem,
  Menu,
  MenuButton,
  StackProps,
  useColorModeValue,
  Box,
  Flex,
  Container,
  useDisclosure,
  Drawer,
  DrawerBody,
  DrawerHeader,
  DrawerOverlay,
  DrawerContent,
  DrawerCloseButton,
  IconButton,
  VStack,
  Icon,
  Text,
  useBreakpointValue,
  Badge,
  Divider,
  Avatar,
  MenuDivider,
} from '@chakra-ui/react';
import { Link as RouterLink, useLocation, useNavigate } from 'react-router-dom';
import { CgProfile } from 'react-icons/cg';
import { AiOutlineMenu } from 'react-icons/ai';
import { 
  FaHome, 
  FaFileAlt, 
  FaBriefcase, 
  FaChartLine, 
  FaComments, 
  FaGraduationCap,
  FaSignOutAlt,
  FaBell,
  FaCog
} from 'react-icons/fa';
import ThemeSwitch from './ThemeSwitch';
import Logo from './Logo';
import SkipNavigation from './SkipNavigation';
import { createContext, useContext } from 'react';

// Create a context to manage sidebar visibility across components
export interface SidebarContextType {
  isSidebarOpen: boolean;
  toggleSidebar: () => void;
  closeSidebar: () => void;
  isCollapsed: boolean;
  toggleCollapse: () => void;
}

export const SidebarContext = createContext<SidebarContextType>({
  isSidebarOpen: false,
  toggleSidebar: () => {},
  closeSidebar: () => {},
  isCollapsed: false,
  toggleCollapse: () => {}
});

export const useSidebar = () => useContext(SidebarContext);

interface NavLinkProps extends StackProps {
  children: React.ReactNode;
  path: string;
}

const Links = [
  { name: 'Dashboard', path: '/dashboard', icon: FaHome },
  { name: 'Cover Letters', path: '/cover-letters', icon: FaFileAlt },
  { name: 'Jobs', path: '/jobs', icon: FaBriefcase },
  { name: 'Resume', path: '/resume', icon: FaFileAlt },
  { name: 'Interview Prep', path: '/interview', icon: FaComments },
  { name: 'Learning', path: '/learning', icon: FaGraduationCap },
  { name: 'Tracker', path: '/tracker', icon: FaChartLine },
];

const NavLink = ({ children, path, ...props }: NavLinkProps) => {
  const location = useLocation();
  const isActive = location.pathname === path;
  const activeBg = useColorModeValue('brand.50', 'brand.900');
  const activeColor = useColorModeValue('brand.700', 'brand.300');
  const hoverBg = useColorModeValue('gray.100', 'gray.700');

  return (
    <Box
      as={RouterLink}
      px={4}
      py={3}
      rounded={'lg'}
      fontFamily="Inter, system-ui, sans-serif"
      fontSize="15px"
      fontWeight={isActive ? '600' : '500'}
      color={isActive ? activeColor : 'inherit'}
      bg={isActive ? activeBg : 'transparent'}
      transition="all 0.2s cubic-bezier(0.4, 0, 0.2, 1)"
      position="relative"
      _hover={{
        textDecoration: 'none',
        bg: isActive ? activeBg : hoverBg,
        transform: 'translateY(-1px)',
        boxShadow: isActive ? 'lg' : 'md',
      }}
      _active={{
        transform: 'translateY(0)',
      }}
      to={path}
      onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
      {...props}
    >
      {isActive && (
        <Box
          position="absolute"
          left="0"
          top="0"
          bottom="0"
          width="3px"
          bg="brand.500"
          borderRadius="0 2px 2px 0"
        />
      )}
      {children}
    </Box>
  );
};

const UserMenu = ({ user }: { user: any }) => {
  const navigate = useNavigate();
  const menuBg = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  return (
    <Menu>
      <MenuButton
        as={IconButton}
        variant="ghost"
        size="sm"
        rounded="full"
        icon={
          <Avatar
            size="sm"
            name={user?.username || 'User'}
            bg="brand.500"
          />
        }
        _hover={{
          bg: 'transparent',
          transform: 'scale(1.05)',
        }}
        _active={{
          transform: 'scale(0.95)',
        }}
        transition="all 0.2s"
      />
      <MenuList
        bg={menuBg}
        borderColor={borderColor}
        boxShadow="xl"
        py={2}
        minW="200px"
      >
        <Box px={4} py={2}>
          <Text fontSize="sm" fontWeight="600" color="brand.500">
            {user?.username || 'User'}
          </Text>
          <Text fontSize="xs" color="gray.500">
            {user?.email || '<EMAIL>'}
          </Text>
        </Box>
        <MenuDivider />
        <MenuItem
          icon={<FaCog />}
          onClick={() => {
            navigate('/profile');
            window.scrollTo({ top: 0, behavior: 'smooth' });
          }}
          _hover={{ bg: 'brand.50' }}
        >
          Profile & Settings
        </MenuItem>
        <MenuItem
          icon={<FaBell />}
          _hover={{ bg: 'brand.50' }}
        >
          Notifications
        </MenuItem>
        <MenuDivider />
        <MenuItem
          icon={<FaSignOutAlt />}
          color="red.500"
          _hover={{ bg: 'red.50' }}
          onClick={() => logout()}
        >
          Sign Out
        </MenuItem>
      </MenuList>
    </Menu>
  );
};

export default function NavBar() {
  const { data: user } = useAuth();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { toggleSidebar } = useSidebar();

  // Responsive design
  const showMobileMenu = useBreakpointValue({ base: true, lg: false });
  const showDesktopMenu = useBreakpointValue({ base: false, lg: true });

  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const bgColor = useColorModeValue('rgba(255, 255, 255, 0.8)', 'rgba(26, 32, 44, 0.8)');
  const mobileMenuBg = useColorModeValue('white', 'gray.800');

  return (
    <>
      <SkipNavigation />
      <Box
        as='nav'
        id="navigation"
        role="navigation"
        aria-label="Main navigation"
        py={4}
        top={0}
        width='full'
        position='sticky'
        backdropFilter='blur(20px)'
        borderBottom='1px'
        borderColor={borderColor}
        boxShadow='sm'
        color='gray.900'
        _dark={{ color: 'white' }}
        zIndex={1000}
        bg={bgColor}
        transition="all 0.2s"
      >
        <Container maxW='95%'>
          <Flex align='center' justify='space-between' h="60px">
            {/* Logo - Far Left */}
            <Box flex="0 0 auto">
              <Link 
                as={RouterLink} 
                to='/' 
                _hover={{ textDecoration: 'none' }}
                display="flex"
                alignItems="center"
              >
                <Logo />
              </Link>
            </Box>

            {/* Desktop Nav Links - Centered */}
            <Flex
              justify='center'
              flex={1}
              display={{ base: 'none', lg: 'flex' }}
              mx={8}
            >
              <HStack spacing={2}>
                {Links.slice(0, 4).map((link) => (
                  <NavLink key={link.name} path={link.path}>
                    <HStack spacing={2}>
                      <Icon as={link.icon} boxSize={4} />
                      <Text>{link.name}</Text>
                    </HStack>
                  </NavLink>
                ))}
              </HStack>
            </Flex>

            {/* Right Section: Actions and User Menu */}
            <HStack spacing={4} alignItems='center' flex="0 0 auto">
              {/* Notifications */}
              {user && (
                <IconButton
                  aria-label="Notifications"
                  icon={<FaBell />}
                  variant="ghost"
                  size="sm"
                  position="relative"
                  _hover={{
                    bg: 'brand.50',
                    transform: 'translateY(-1px)',
                  }}
                  display={{ base: 'none', md: 'flex' }}
                >
                  <Badge
                    position="absolute"
                    top="-1"
                    right="-1"
                    bg="red.500"
                    color="white"
                    borderRadius="full"
                    boxSize="18px"
                    fontSize="10px"
                  >
                    3
                  </Badge>
                </IconButton>
              )}

              {/* Theme Switch */}
              <ThemeSwitch />
              
              {/* User Menu or Login */}
              {user ? (
                <HStack spacing={3} display={{ base: 'none', md: 'flex' }}>
                  <UserMenu user={user} />
                </HStack>
              ) : (
                <Button
                  as={RouterLink}
                  to='/login'
                  colorScheme='brand'
                  size='sm'
                  leftIcon={<CgProfile />}
                  display={{ base: 'none', md: 'flex' }}
                  _hover={{
                    transform: 'translateY(-1px)',
                    boxShadow: 'lg',
                  }}
                >
                  Sign In
                </Button>
              )}

              {/* Mobile Menu Button - Only toggles existing sidebar */}
              <IconButton
                display={{ base: 'flex', lg: 'none' }}
                onClick={toggleSidebar}
                variant='ghost'
                aria-label='Open menu'
                size='sm'
                icon={<AiOutlineMenu />}
                _hover={{
                  bg: 'brand.50',
                  transform: 'scale(1.05)',
                }}
                _active={{
                  transform: 'scale(0.95)',
                }}
              />
            </HStack>
          </Flex>
        </Container>
      </Box>
    </>
  );
}

interface NavButtonProps {
  children: React.ReactNode;
  icon: React.ReactElement;
  to: string;
}

function NavButton({ children, icon, to }: NavButtonProps) {
  const location = useLocation();
  const isActive = location.pathname === to;
  
  return (
    <Button
      as={RouterLink}
      to={to}
      variant={isActive ? 'solid' : 'ghost'}
      colorScheme="brand"
      size="sm"
      leftIcon={icon}
      _hover={{
        transform: 'translateY(-1px)',
        boxShadow: isActive ? 'lg' : 'md',
      }}
      _active={{
        transform: 'translateY(0)',
      }}
    >
      {children}
    </Button>
  );
}
