import React from 'react';
import {
  Box,
  HStack,
  Text,
  Icon,
  Badge,
  useColorModeValue,
  Tooltip,
  Progress,
} from '@chakra-ui/react';
import { FaCoins, FaInfinity } from 'react-icons/fa';

interface CreditsIndicatorProps {
  credits: number;
  hasPaid: boolean;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
}

const CreditsIndicator: React.FC<CreditsIndicatorProps> = ({
  credits,
  hasPaid,
  size = 'md',
  showLabel = true
}) => {
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.700');
  const textColor = useColorModeValue('gray.800', 'white');
  const mutedColor = useColorModeValue('gray.600', 'gray.400');

  // Size configurations
  const sizeConfig = {
    sm: {
      padding: 2,
      fontSize: 'xs',
      iconSize: 3,
      badgeSize: 'sm'
    },
    md: {
      padding: 3,
      fontSize: 'sm',
      iconSize: 4,
      badgeSize: 'md'
    },
    lg: {
      padding: 4,
      fontSize: 'md',
      iconSize: 5,
      badgeSize: 'lg'
    }
  };

  const config = sizeConfig[size];

  // Determine status and colors
  const getStatusInfo = () => {
    if (hasPaid) {
      return {
        status: 'unlimited',
        color: 'green',
        bgColor: useColorModeValue('green.50', 'green.900'),
        borderColor: useColorModeValue('green.200', 'green.700'),
        icon: FaInfinity,
        text: 'Unlimited'
      };
    }

    if (credits === 0) {
      return {
        status: 'exhausted',
        color: 'red',
        bgColor: useColorModeValue('red.50', 'red.900'),
        borderColor: useColorModeValue('red.200', 'red.700'),
        icon: FaCoins,
        text: 'No credits'
      };
    }

    if (credits === 1) {
      return {
        status: 'low',
        color: 'orange',
        bgColor: useColorModeValue('orange.50', 'orange.900'),
        borderColor: useColorModeValue('orange.200', 'orange.700'),
        icon: FaCoins,
        text: `${credits} credit left`
      };
    }

    return {
      status: 'available',
      color: 'blue',
      bgColor: useColorModeValue('blue.50', 'blue.900'),
      borderColor: useColorModeValue('blue.200', 'blue.700'),
      icon: FaCoins,
      text: `${credits} credits`
    };
  };

  const statusInfo = getStatusInfo();

  const getTooltipText = () => {
    if (hasPaid) {
      return 'You have unlimited access with your subscription';
    }
    if (credits === 0) {
      return 'You\'ve used all your free credits. Upgrade to continue using CareerDart.';
    }
    if (credits === 1) {
      return 'You have 1 free credit remaining. Consider upgrading for unlimited access.';
    }
    return `You have ${credits} free credits remaining out of 3 total.`;
  };

  return (
    <Tooltip label={getTooltipText()} placement="bottom" hasArrow>
      <Box
        bg={statusInfo.bgColor}
        borderWidth="1px"
        borderColor={statusInfo.borderColor}
        borderRadius="lg"
        p={config.padding}
        transition="all 0.2s ease"
        cursor="pointer"
        role="status"
        aria-label={getTooltipText()}
        tabIndex={0}
        _hover={{
          transform: 'translateY(-1px)',
          boxShadow: 'sm'
        }}
        _focus={{
          boxShadow: "0 0 0 3px rgba(66, 153, 225, 0.6)",
          outline: "none"
        }}
      >
        <HStack spacing={2} align="center">
          <Icon
            as={statusInfo.icon}
            boxSize={config.iconSize}
            color={`${statusInfo.color}.500`}
          />

          {showLabel && (
            <Text
              fontSize={config.fontSize}
              fontWeight="medium"
              color={`${statusInfo.color}.700`}
              _dark={{ color: `${statusInfo.color}.300` }}
            >
              {statusInfo.text}
            </Text>
          )}

          {!hasPaid && (
            <Badge
              colorScheme={statusInfo.color}
              variant="subtle"
              size={config.badgeSize}
              borderRadius="full"
            >
              {credits}/3
            </Badge>
          )}
        </HStack>

        {/* Progress bar for free users */}
        {!hasPaid && size !== 'sm' && (
          <Progress
            value={(credits / 3) * 100}
            size="xs"
            colorScheme={statusInfo.color}
            mt={2}
            borderRadius="full"
            bg={useColorModeValue('gray.100', 'gray.700')}
          />
        )}
      </Box>
    </Tooltip>
  );
};

export default CreditsIndicator;
