import {
  Box,
  VStack,
  <PERSON>ing,
  Text,
  SimpleGrid,
  Button,
  useColorModeValue,
  Badge,
  HStack,
  Icon,
  Flex,
} from '@chakra-ui/react';
import { FaExternalLinkAlt, FaEnvelopeOpenText, FaComments, FaChartLine } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';

const MotionBox = motion(Box);

const careerTools = [
  {
    id: 'cover-letter-generator',
    title: 'AI Cover Letter Generator',
    specialty: 'Personalized Applications',
    description: 'Create compelling, tailored cover letters in minutes using AI. Match your skills to job requirements and stand out from other candidates.',
    icon: 'FaEnvelopeOpenText',
    path: '/cover-letters',
    color: 'blue'
  },
  {
    id: 'interview-prep',
    title: 'Interview Preparation',
    specialty: 'Practice & Confidence Building',
    description: 'Practice with AI-generated interview questions specific to your target roles. Build confidence with mock interviews and expert tips.',
    icon: 'FaComments',
    path: '/interview',
    color: 'green'
  },
  {
    id: 'career-tracker',
    title: 'Application Tracker',
    specialty: 'Job Search Management',
    description: 'Track your job applications, interview schedules, and follow-ups in one organized dashboard. Never miss an opportunity again.',
    icon: 'FaChartLine',
    path: '/tracker',
    color: 'purple'
  },
];

export default function JobSuggestions() {
  const navigate = useNavigate();
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.100', 'gray.700');
  const hoverBg = useColorModeValue('gray.50', 'gray.700');
  const textColor = useColorModeValue('gray.600', 'gray.400');
  const accentColor = useColorModeValue('purple.500', 'purple.300');

  const handleToolClick = (tool: any) => {
    navigate(tool.path);
  };

  return (
    <Box
      as="section"
      py={8}
      px={6}
      bg={bgColor}
      borderRadius="xl"
      position="relative"
    >
      <VStack spacing={8} align="stretch">
        <Flex justify="space-between" align="center">
          <VStack align="start" spacing={1}>
            <Heading
              size="md"
              color="gray.700"
              fontWeight="medium"
            >
              Career Development Tools
            </Heading>
            <Text color="gray.500" fontSize="sm">
              AI-powered tools to accelerate your career growth
            </Text>
          </VStack>
          <Button
            variant="ghost"
            colorScheme="purple"
            size="sm"
            rightIcon={<Icon as={FaExternalLinkAlt} />}
            onClick={() => navigate('/learning')}
          >
            View All Tools
          </Button>
        </Flex>

        <SimpleGrid columns={{ base: 1, md: 3 }} spacing={6}>
          {careerTools.map((tool) => (
            <MotionBox
              key={tool.id}
              whileHover={{ y: -2 }}
              transition={{ duration: 0.2 }}
            >
              <Box
                p={5}
                borderWidth="1px"
                borderColor={borderColor}
                borderRadius="lg"
                bg={bgColor}
                transition="all 0.2s"
                _hover={{
                  borderColor: accentColor,
                  bg: hoverBg,
                }}
              >
                <VStack align="start" spacing={4}>
                  <VStack align="start" spacing={1} w="full">
                    <HStack spacing={2} align="center">
                      <Icon
                        as={tool.icon === 'FaEnvelopeOpenText' ? FaEnvelopeOpenText :
                            tool.icon === 'FaComments' ? FaComments :
                            FaChartLine}
                        color={`${tool.color}.500`}
                        boxSize={5}
                      />
                      <Heading size="sm" color={accentColor}>
                        {tool.title}
                      </Heading>
                    </HStack>
                    <Text fontSize="sm" color={textColor} fontWeight="medium">
                      {tool.specialty}
                    </Text>
                  </VStack>

                  <Text fontSize="sm" color={textColor} noOfLines={3}>
                    {tool.description}
                  </Text>

                  <Button
                    size="sm"
                    colorScheme={tool.color}
                    variant="outline"
                    w="full"
                    onClick={() => handleToolClick(tool)}
                  >
                    Get Started
                  </Button>
                </VStack>
              </Box>
            </MotionBox>
          ))}
        </SimpleGrid>
      </VStack>
    </Box>
  );
}