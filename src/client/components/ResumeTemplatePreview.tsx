import React from 'react';
import {
  <PERSON><PERSON>,
  ModalOverlay,
  Modal<PERSON>ontent,
  Modal<PERSON>eader,
  ModalBody,
  Modal<PERSON>ooter,
  ModalCloseButton,
  VStack,
  HStack,
  Text,
  Button,
  Badge,
  Box,
  Divider,
  SimpleGrid,
  Icon,
  useColorModeValue,
  Flex
} from '@chakra-ui/react';
import { FaCheck } from 'react-icons/fa';
import { ResumeTemplate } from '../../shared/types';
import ResumeTemplateRenderer from './ResumeTemplateRenderer';

interface ResumeTemplatePreviewProps {
  template: ResumeTemplate;
  isOpen: boolean;
  onClose: () => void;
  onSelect: () => void;
  isSelected: boolean;
}

const CATEGORY_COLORS = {
  professional: 'blue',
  creative: 'purple',
  modern: 'green',
  classic: 'gray'
} as const;

const ResumeTemplatePreview: React.FC<ResumeTemplatePreviewProps> = ({
  template,
  isOpen,
  onClose,
  onSelect,
  isSelected
}) => {
  const cardBg = useColorModeValue('gray.50', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="6xl" scrollBehavior="inside">
      <ModalOverlay backdropFilter="auto" backdropInvert="15%" backdropBlur="2px" />
      <ModalContent maxH="90vh">
        <ModalHeader>
          <HStack justify="space-between" align="center">
            <VStack align="flex-start" spacing={1}>
              <Text fontSize="xl" fontWeight="bold">
                {template.name}
              </Text>
              <HStack spacing={2}>
                <Badge
                  colorScheme={CATEGORY_COLORS[template.category]}
                  variant="subtle"
                  textTransform="capitalize"
                >
                  {template.category}
                </Badge>
                <Text fontSize="sm" color="gray.500">
                  {template.styles.layout.replace('-', ' ')} • {template.styles.fontFamily}
                </Text>
              </HStack>
            </VStack>
          </HStack>
        </ModalHeader>
        <ModalCloseButton />

        <ModalBody>
          <VStack spacing={6} align="stretch">
            {/* Template Description */}
            <Box>
              <Text color="gray.600" fontSize="md">
                {template.description}
              </Text>
            </Box>

            <Divider />

            {/* Template Preview */}
            <Flex justify="center" align="center" p={4}>
              <ResumeTemplateRenderer
                template={template}
                scale={1.5}
              />
            </Flex>

            <Divider />

            {/* Template Features */}
            <Box>
              <Text fontSize="md" fontWeight="semibold" mb={3}>
                Template Features
              </Text>
              <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                <Box>
                  <Text fontSize="sm" fontWeight="medium" color="gray.700">
                    Layout Style
                  </Text>
                  <Text fontSize="sm" color="gray.600" textTransform="capitalize">
                    {template.styles.layout.replace('-', ' ')}
                  </Text>
                </Box>
                <Box>
                  <Text fontSize="sm" fontWeight="medium" color="gray.700">
                    Font Family
                  </Text>
                  <Text fontSize="sm" color="gray.600">
                    {template.styles.fontFamily}
                  </Text>
                </Box>
                <Box>
                  <Text fontSize="sm" fontWeight="medium" color="gray.700">
                    Primary Color
                  </Text>
                  <HStack spacing={2}>
                    <Box
                      w={4}
                      h={4}
                      bg={template.styles.primaryColor}
                      borderRadius="sm"
                      border="1px solid"
                      borderColor="gray.300"
                    />
                    <Text fontSize="sm" color="gray.600">
                      {template.styles.primaryColor}
                    </Text>
                  </HStack>
                </Box>
                <Box>
                  <Text fontSize="sm" fontWeight="medium" color="gray.700">
                    Category
                  </Text>
                  <Badge
                    colorScheme={CATEGORY_COLORS[template.category]}
                    variant="subtle"
                    textTransform="capitalize"
                  >
                    {template.category}
                  </Badge>
                </Box>
              </SimpleGrid>
            </Box>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack spacing={3}>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button
              colorScheme="purple"
              onClick={onSelect}
              leftIcon={isSelected ? <Icon as={FaCheck} /> : undefined}
              size="lg"
              px={8}
            >
              {isSelected ? '✓ Template Selected' : 'Select This Template'}
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ResumeTemplatePreview;
