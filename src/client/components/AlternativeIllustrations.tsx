import React from 'react';
import { Box, Image, VStack, Text, Heading, BoxProps, Icon } from '@chakra-ui/react';
import { motion } from 'framer-motion';
import {
  FaBriefcase,
  FaFileAlt,
  FaComments,
  FaExclamationTriangle,
  FaCheckCircle,
  FaSpinner,
  FaUser,
  FaGraduationCap,
  FaSearch,
  FaChartLine
} from 'react-icons/fa';

const MotionBox = motion(Box);

// Alternative illustration sources
const alternativeIllustrations: Record<string, string> = {
  // Using Storyset (free illustrations)
  'job_hunt_re_q203': 'https://storyset.com/illustration/job-hunt/rafiki',
  'newsletter_re_wrob': 'https://storyset.com/illustration/newsletter/rafiki',
  'resume_re_hkth': 'https://storyset.com/illustration/resume/rafiki',
  'interview_re_5jn2': 'https://storyset.com/illustration/interview/rafiki',
  'searching_re_3ra9': 'https://storyset.com/illustration/searching/rafiki',
  'page_not_found_re_e9o6': 'https://storyset.com/illustration/404-error/rafiki',
  'warning_re_eoyh': 'https://storyset.com/illustration/warning/rafiki',
  'server_down_re_8yzk': 'https://storyset.com/illustration/server-down/rafiki',
  'loading_re_5axr': 'https://storyset.com/illustration/loading/rafiki',
  'completed_re_cisp': 'https://storyset.com/illustration/completed/rafiki',
  'career_progress_re_99p2': 'https://storyset.com/illustration/career-progress/rafiki',
  'mobile_login_re_9ntv': 'https://storyset.com/illustration/mobile-login/rafiki',
  'artificial_intelligence_re_enpp': 'https://storyset.com/illustration/artificial-intelligence/rafiki',
  'online_learning_re_qw08': 'https://storyset.com/illustration/online-learning/rafiki',
  'empty_re_opql': 'https://storyset.com/illustration/empty/rafiki',
};

// Fallback to local SVG illustrations (you can create these)
const localIllustrations: Record<string, string> = {
  'job_hunt_re_q203': '/images/illustrations/job-hunt.svg',
  'newsletter_re_wrob': '/images/illustrations/newsletter.svg',
  'resume_re_hkth': '/images/illustrations/resume.svg',
  'interview_re_5jn2': '/images/illustrations/interview.svg',
  'searching_re_3ra9': '/images/illustrations/searching.svg',
  'page_not_found_re_e9o6': '/images/illustrations/404.svg',
  'warning_re_eoyh': '/images/illustrations/warning.svg',
  'server_down_re_8yzk': '/images/illustrations/server-down.svg',
  'loading_re_5axr': '/images/illustrations/loading.svg',
  'completed_re_cisp': '/images/illustrations/success.svg',
  'career_progress_re_99p2': '/images/illustrations/career.svg',
  'mobile_login_re_9ntv': '/images/illustrations/login.svg',
  'artificial_intelligence_re_enpp': '/images/illustrations/ai.svg',
  'online_learning_re_qw08': '/images/illustrations/learning.svg',
  'empty_re_opql': '/images/illustrations/empty.svg',
};

export interface AlternativeIllustrationProps extends BoxProps {
  illustration: string;
  title?: string;
  description?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  animated?: boolean;
  primaryColor?: string;
}

const sizeMap = {
  sm: { width: '200px', height: '150px' },
  md: { width: '300px', height: '225px' },
  lg: { width: '400px', height: '300px' },
  xl: { width: '500px', height: '375px' },
};

const fallbackIcons: Record<string, any> = {
  'job_hunt_re_q203': FaBriefcase,
  'newsletter_re_wrob': FaFileAlt,
  'resume_re_hkth': FaFileAlt,
  'interview_re_5jn2': FaComments,
  'searching_re_3ra9': FaSearch,
  'page_not_found_re_e9o6': FaExclamationTriangle,
  'warning_re_eoyh': FaExclamationTriangle,
  'server_down_re_8yzk': FaExclamationTriangle,
  'loading_re_5axr': FaSpinner,
  'completed_re_cisp': FaCheckCircle,
  'career_progress_re_99p2': FaChartLine,
  'mobile_login_re_9ntv': FaUser,
  'artificial_intelligence_re_enpp': FaCheckCircle,
  'online_learning_re_qw08': FaGraduationCap,
  'empty_re_opql': FaFileAlt,
};

export default function AlternativeIllustration({
  illustration,
  title,
  description,
  size = 'md',
  animated = true,
  primaryColor = '6366f1',
  children,
  ...props
}: AlternativeIllustrationProps) {
  const { width, height } = sizeMap[size];
  const [imageError, setImageError] = React.useState(false);
  const [currentSource, setCurrentSource] = React.useState(0);

  // Try different sources in order: alternative → local → icon fallback
  const imageSources = [
    alternativeIllustrations[illustration],
    localIllustrations[illustration],
  ].filter(Boolean);

  const handleImageError = () => {
    if (currentSource < imageSources.length - 1) {
      setCurrentSource(currentSource + 1);
    } else {
      setImageError(true);
    }
  };

  const FallbackIcon = fallbackIcons[illustration] || FaFileAlt;

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  const imageVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: 'easeOut',
        delay: 0.2,
      },
    },
  };

  return (
    <MotionBox
      textAlign="center"
      py={8}
      variants={animated ? containerVariants : undefined}
      initial={animated ? 'hidden' : undefined}
      animate={animated ? 'visible' : undefined}
      {...props}
    >
      <VStack spacing={6} align="center">
        <MotionBox
          variants={animated ? imageVariants : undefined}
          initial={animated ? 'hidden' : undefined}
          animate={animated ? 'visible' : undefined}
        >
          {imageError || imageSources.length === 0 ? (
            <Box
              width={width}
              height={height}
              bg={`#${primaryColor}10`}
              borderRadius="xl"
              display="flex"
              alignItems="center"
              justifyContent="center"
              position="relative"
              overflow="hidden"
            >
              {/* Background decoration */}
              <Box
                position="absolute"
                top="-20%"
                right="-20%"
                width="60%"
                height="60%"
                bg={`#${primaryColor}20`}
                borderRadius="full"
                opacity={0.3}
              />
              <Box
                position="absolute"
                bottom="-10%"
                left="-10%"
                width="40%"
                height="40%"
                bg={`#${primaryColor}15`}
                borderRadius="full"
                opacity={0.4}
              />
              
              {/* Main icon */}
              <Icon
                as={FallbackIcon}
                boxSize={size === 'sm' ? '40px' : size === 'md' ? '60px' : size === 'lg' ? '80px' : '100px'}
                color={`#${primaryColor}`}
                opacity={0.8}
              />
            </Box>
          ) : (
            <Image
              src={imageSources[currentSource]}
              alt={title || 'Illustration'}
              width={width}
              height={height}
              objectFit="contain"
              loading="lazy"
              onError={handleImageError}
              fallback={
                <Box
                  width={width}
                  height={height}
                  bg={`#${primaryColor}05`}
                  borderRadius="xl"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  <VStack spacing={2}>
                    <Icon
                      as={FaSpinner}
                      boxSize="24px"
                      color={`#${primaryColor}`}
                      opacity={0.6}
                      style={{
                        animation: 'spin 1s linear infinite'
                      }}
                    />
                    <Text color="gray.500" fontSize="xs">
                      Loading...
                    </Text>
                  </VStack>
                </Box>
              }
            />
          )}
        </MotionBox>

        {(title || description || children) && (
          <VStack spacing={3} maxW="md">
            {title && (
              <Heading
                size="lg"
                color="gray.700"
                _dark={{ color: 'gray.200' }}
                textAlign="center"
              >
                {title}
              </Heading>
            )}
            
            {description && (
              <Text
                color="gray.600"
                _dark={{ color: 'gray.400' }}
                fontSize="md"
                textAlign="center"
                lineHeight="1.6"
              >
                {description}
              </Text>
            )}
            
            {children}
          </VStack>
        )}
      </VStack>
    </MotionBox>
  );
}
