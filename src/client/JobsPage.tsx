import { type Job, type User } from "wasp/entities";

import {
  useAction,
  type OptimisticUpdateDefinition,
  updateJob,
  createJob,
  useQuery,
  getJobs,
  getCoverLetters,
  createJobApplication,
  getJobApplications,
} from "wasp/client/operations";
import { useAuth } from "wasp/client/auth";

import { useState, useEffect, useRef } from 'react';
import {
  Heading,
  Spacer,
  VStack,
  HStack,
  Button,
  Text,
  Box,
  Container,
  Badge,
  Grid,
  GridItem,
  SimpleGrid,
  useDisclosure,
  Divider,
  Checkbox,
  Spinner,
  Tooltip,
  Input,
  Textarea,
  FormControl,
  FormLabel,
  Select,
  useToast,
  IconButton,
  useColorModeValue,
  Card,
  CardBody,
  CardHeader,
  Progress,
  Center,
  Icon,
  Circle,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Stack,
  useBreakpointValue,
  Flex,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import ModalElement from './components/Modal';
import ContentPageBox from './components/ContentPageBox';
import BackButton from './components/BackButton';
import { useNavigate } from 'react-router-dom';
import { DeleteJob } from './components/AlertDialog';
import { 
  FiDelete, 
  FiMoreVertical, 
  FiExternalLink, 
  FiClock, 
  FiMapPin, 
  FiCalendar,
  FiTrendingUp 
} from 'react-icons/fi';
import { 
  FaPlus, 
  FaBriefcase, 
  FaLinkedin, 
  FaEdit, 
  FaCopy, 
  FaEye, 
  FaSave, 
  FaTimes, 
  FaCheck,
  FaFilter,
  FaSearch,
  FaLink,
} from 'react-icons/fa';
import PageHeader from './components/PageHeader';
import ActionButton from './components/ActionButton';
import ContentContainer from './components/ContentContainer';
import LinkedInJobImport from './components/LinkedInJobImport';
import URLJobImport from './components/URLJobImport';
import JobRecommendations from './components/JobRecommendations';
import { EmptyJobs } from './components/EmptyState';
import { SimpleLoading } from './components/LoadingState';
import { GeneralError } from './components/ErrorState';

// Motion components for animations
const MotionBox = motion(Box);
const MotionCard = motion(Card);
const MotionGrid = motion(SimpleGrid);

function JobsPage({ user }: { user: User }) {
  const [jobId, setJobId] = useState<string>('');
  const [isImportingJob, setIsImportingJob] = useState<boolean>(false);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [editingJobId, setEditingJobId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [editFormData, setEditFormData] = useState({
    title: '',
    company: '',
    location: '',
    description: '',
    isCompleted: false,
    status: 'Saved'
  });

  const toast = useToast();
  const navigate = useNavigate();
  const { data: authUser } = useAuth();

  // Enhanced color scheme with responsive considerations
  const bgGradient = useColorModeValue(
    'linear(to-br, blue.50, purple.50, pink.50)',
    'linear(to-br, gray.900, blue.900, purple.900)'
  );
  const cardBg = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const textColor = useColorModeValue("gray.700", "gray.200");
  const accentColor = useColorModeValue("brand.500", "brand.400");

  // Responsive values
  const isMobile = useBreakpointValue({ base: true, lg: false });
  const containerPadding = useBreakpointValue({ base: 4, md: 8 });
  const cardSpacing = useBreakpointValue({ base: 4, md: 6 });
  const gridTemplateColumns = useBreakpointValue({ 
    base: "1fr", 
    lg: "repeat(2, 1fr)" 
  });

  // Fetch data only if user is authenticated
  const { data: jobs, isLoading, error } = useQuery(getJobs, undefined, { enabled: !!authUser });
  const { data: coverLetter } = useQuery(getCoverLetters, { id: jobId }, { enabled: jobId.length > 0 && !!authUser });
  const { data: jobApplications } = useQuery(getJobApplications, undefined, { enabled: !!authUser });

  // Filter jobs based on search and status
  const filteredJobs = jobs?.filter(job => {
    const matchesSearch = job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         job.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         job.location.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'completed' && job.isCompleted) ||
                         (statusFilter === 'pending' && !job.isCompleted);
    return matchesSearch && matchesStatus;
  }) || [];

  // Calculate stats
  const totalJobs = jobs?.length || 0;
  const completedJobs = jobs?.filter(job => job.isCompleted).length || 0;
  const pendingJobs = totalJobs - completedJobs;
  const completionRate = totalJobs > 0 ? Math.round((completedJobs / totalJobs) * 100) : 0;

  useEffect(() => {
    if (user.subscriptionStatus === 'past_due') {
      navigate('/profile');
    }
  }, [user.subscriptionStatus]);

  // Set the first job as selected when data loads
  useEffect(() => {
    if (jobs && jobs.length > 0 && !selectedJob) {
      setSelectedJob(jobs[0]);
    }
  }, [jobs]);

  const updateJobOptimistically = useAction(updateJob, {
    optimisticUpdates: [
      {
        getQuerySpecifier: () => [getJobs],
        updateQuery: ({ isCompleted, id }, oldData) => {
          return oldData && oldData.map((job) => (job.id === id ? { ...job, isCompleted } : job));
        },
      } as OptimisticUpdateDefinition<Pick<Job, 'id' | 'isCompleted'>, Job[]>,
    ],
  });

  const updateJobAction = useAction(updateJob);

  const createJobApplicationAction = useAction(createJobApplication);

  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: deleteIsOpen, onOpen: deleteOnOpen, onClose: deleteOnClose } = useDisclosure();

  const coverLetterHandler = (job: Job) => {
    if (job) {
      setJobId(job.id);
      onOpen();
    }
  };

  const checkboxHandler = async (e: any, job: Job) => {
    try {
      const isCompleted = e.target.checked;

      // Update the job
      const payload = {
        id: job.id,
        title: job.title,
        company: job.company,
        location: job.location,
        description: job.description,
        isCompleted: isCompleted,
      };
      updateJobOptimistically(payload);

      // If marking as completed (applied), create a job application
      if (isCompleted) {
        try {
          // Check if there's already an application for this job
          const existingApplication = jobApplications?.find(app => app.jobId === job.id);

          if (!existingApplication) {
            // Create a new job application
            await createJobApplicationAction({
              jobId: job.id,
              dateApplied: new Date().toISOString(),
              status: 'Applied',
              notes: 'Marked as applied from Jobs page',
            });

            toast({
              title: 'Job application created',
              description: `Job added to tracker with status: Applied`,
              status: 'success',
              duration: 3000,
              isClosable: true,
            });
          }
        } catch (error) {
          console.error('Error creating job application:', error);
          toast({
            title: 'Error',
            description: 'Failed to update job application status.',
            status: 'error',
            duration: 3000,
            isClosable: true,
          });
        }
      }
    } catch (error) {
      console.error(error);
      toast({
        title: 'Error',
        description: 'Failed to update job status.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const updateCoverLetterHandler = async (jobId: string) => {
    // Navigate to cover letter creation page with the job ID parameter
    // This allows the user to create a new cover letter for this specific job
    navigate(`/?create=new&job=${jobId}`);
  };

  const handleJobImport = async (jobData: any) => {
    try {
      console.log('Imported job data:', jobData);

      // Create the job directly from imported data
      const newJob = await createJob({
        title: jobData.title || '',
        company: jobData.company || '',
        location: jobData.location || '',
        description: jobData.description || ''
      });

      // Hide the import form
      setIsImportingJob(false);

      // Set the newly created job as selected
      setSelectedJob(newJob);

      // Show success message
      toast({
        title: 'Job imported and created',
        description: `Successfully imported and created job for ${jobData.title} at ${jobData.company}`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error handling job import:', error);
      toast({
        title: 'Error',
        description: 'Failed to import job data. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handlePreviewJob = (job: Job) => {
    setSelectedJob(job);
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString();
  };

  // Get job application status for a job
  const getJobApplicationStatus = (jobId: string) => {
    if (!jobApplications) return null;

    const application = jobApplications.find(app => app.jobId === jobId);
    return application ? application.status : null;
  };

  // Get color for job application status
  const getStatusColor = (status: string | null) => {
    if (!status) return "gray";

    switch (status) {
      case "Applied": return "blue";
      case "Interview": return "orange";
      case "Offer": return "green";
      case "Rejected": return "red";
      default: return "gray";
    }
  };

  // Start editing a job
  const handleEditJob = (job: Job) => {
    setEditingJobId(job.id);
    setEditFormData({
      title: job.title,
      company: job.company,
      location: job.location,
      description: job.description,
      isCompleted: job.isCompleted,
      status: job.isCompleted ? 'Applied' : 'Saved'
    });
  };

  // Cancel editing
  const handleCancelEdit = () => {
    setEditingJobId(null);
  };

  // Handle form field changes for editing
  const handleEditFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setEditFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setEditFormData(prev => ({
      ...prev,
      [name]: checked,
      // If marking as completed, set status to Applied
      ...(name === 'isCompleted' && checked ? { status: 'Applied' } : {})
    }));
  };

  // Save job edits
  const handleSaveJob = async (jobId: string) => {
    try {
      await updateJobAction({
        id: jobId,
        title: editFormData.title,
        company: editFormData.company,
        location: editFormData.location,
        description: editFormData.description,
        isCompleted: editFormData.isCompleted
      });

      // If the job status has changed, create or update a job application
      if (editFormData.status !== 'Saved') {
        try {
          // Check if there's already an application for this job
          const existingApplication = jobApplications?.find(app => app.jobId === jobId);

          if (!existingApplication) {
            // Create a new job application
            await createJobApplicationAction({
              jobId: jobId,
              dateApplied: new Date().toISOString(),
              status: editFormData.status,
              notes: `Status changed to ${editFormData.status}`,
            });

            toast({
              title: 'Job application created',
              description: `Job added to tracker with status: ${editFormData.status}`,
              status: 'success',
              duration: 3000,
              isClosable: true,
            });
          }
        } catch (error) {
          console.error('Error creating job application:', error);
          toast({
            title: 'Error',
            description: 'Failed to update job application status.',
            status: 'error',
            duration: 3000,
            isClosable: true,
          });
        }
      }

      setEditingJobId(null);

      // Update the selected job if it's the one being edited
      if (selectedJob && selectedJob.id === jobId) {
        setSelectedJob({
          ...selectedJob,
          title: editFormData.title,
          company: editFormData.company,
          location: editFormData.location,
          description: editFormData.description,
          isCompleted: editFormData.isCompleted
        });
      }

      toast({
        title: 'Job updated',
        description: 'The job has been updated successfully.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error updating job:', error);
      toast({
        title: 'Error',
        description: 'Failed to update job. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  return (
    <Box bg={bgGradient} minH="100vh" py={containerPadding} position="relative">
      {/* Back Button for Mobile */}
      <BackButton />
      
      <Container maxW={{ base: "100%", md: "95%" }} px={{ base: 2, md: 4, lg: 6 }}>
        <VStack spacing={cardSpacing} align="stretch">
          {/* Enhanced Page Header - Mobile Optimized */}
          <PageHeader title="Jobs" />

          {/* Tabs Interface */}
          <Tabs variant="enclosed" colorScheme="blue" size={{ base: "md", md: "lg" }}>
            <TabList bg={cardBg} borderRadius="xl" p={2} boxShadow="sm" overflowX="auto">
              <Tab
                _selected={{
                  bg: accentColor,
                  color: "white",
                  borderRadius: "lg"
                }}
                fontWeight="600"
                px={{ base: 3, md: 6 }}
                py={{ base: 2, md: 3 }}
                mr={2}
                minW="fit-content"
              >
                <HStack spacing={2}>
                  <Icon as={FaBriefcase} />
                  <VStack spacing={0} align="start" display={{ base: "none", sm: "flex" }}>
                    <Text>Your Jobs</Text>
                    <Text fontSize="xs" opacity={0.8}>Track and manage applications</Text>
                  </VStack>
                  <Text display={{ base: "block", sm: "none" }}>Jobs</Text>
                </HStack>
              </Tab>
              <Tab
                _selected={{
                  bg: accentColor,
                  color: "white",
                  borderRadius: "lg"
                }}
                fontWeight="600"
                px={{ base: 3, md: 6 }}
                py={{ base: 2, md: 3 }}
                minW="fit-content"
              >
                <HStack spacing={2}>
                  <Icon as={FaSearch} />
                  <VStack spacing={0} align="start">
                    <Text>Discover Jobs</Text>
                    <Text fontSize="xs" opacity={0.8}>Find new opportunities</Text>
                  </VStack>
                </HStack>
              </Tab>
            </TabList>

            <TabPanels>
              {/* Your Jobs Tab */}
              <TabPanel px={0} py={6}>
                <VStack spacing={6} align="stretch">
                  {/* Import Actions */}
                  <HStack spacing={3} justify="flex-end" flexWrap="wrap">
                    <ActionButton
                      icon={FaLinkedin}
                      label="Import from LinkedIn"
                      variant="outline"
                      onClick={() => setIsImportingJob(!isImportingJob)}
                      size={{ base: "sm", md: "md" }}
                    />
                    <ActionButton
                      icon={FaPlus}
                      label="Import from URL"
                      variant="outline"
                      onClick={() => setIsImportingJob(!isImportingJob)}
                      size={{ base: "sm", md: "md" }}
                    />
                  </HStack>

                  {isImportingJob && (
                    <ContentContainer delay={0.2}>
                      <VStack spacing={4} align="stretch">
                        <URLJobImport
                          onJobImported={handleJobImport}
                          isCollapsible={false}
                        />
                      </VStack>
                    </ContentContainer>
                  )}

                  {/* Jobs Content */}
                  {isLoading ? (
                    <SimpleLoading message="Loading your jobs..." />
                  ) : error ? (
                    <GeneralError
                      error={error}
                      onRetry={() => window.location.reload()}
                      showErrorDetails={false}
                    />
                  ) : !jobs || jobs.length === 0 ? (
                    <EmptyJobs
                      primaryAction={{
                        label: 'Import from URL',
                        onClick: () => setIsImportingJob(true),
                        icon: <FaLink />,
                        colorScheme: 'blue',
                      }}
                      secondaryAction={{
                        label: 'Browse Recommendations',
                        onClick: () => {
                          // Switch to the Discover Jobs tab
                          const tabList = document.querySelector('[role="tablist"]');
                          const discoverTab = tabList?.children[1] as HTMLElement;
                          discoverTab?.click();
                        },
                        icon: <FaBriefcase />,
                        variant: 'outline',
                      }}
                    />
                  ) : (
                    <Grid templateColumns={gridTemplateColumns} gap={cardSpacing}>
                      {/* Left side - List of jobs */}
                      <GridItem>
                        <ContentContainer delay={0.3} maxH="calc(100vh - 300px)" overflowY="auto">
                          <VStack spacing={2} align="stretch">
                            {filteredJobs.map((job) => (
                              <Box
                                key={job.id}
                                p={3}
                                borderRadius="md"
                                borderLeft={job.id === selectedJob?.id ? '2px solid' : 'none'}
                                borderColor={job.id === selectedJob?.id ? 'purple.400' : 'transparent'}
                                bg={job.id === selectedJob?.id ? 'gray.50' : 'transparent'}
                                cursor={editingJobId === job.id ? 'default' : 'pointer'}
                                onClick={() => editingJobId !== job.id && handlePreviewJob(job)}
                                _hover={{ bg: 'gray.50' }}
                                transition="all 0.15s"
                                boxShadow="none"
                                mb={1}
                              >
                                {editingJobId === job.id ? (
                                  // Edit mode
                                  <VStack align="stretch" spacing={3} onClick={(e) => e.stopPropagation()}>
                                    <FormControl>
                                      <FormLabel fontSize="xs">Job Title</FormLabel>
                                      <Input
                                        name="title"
                                        value={editFormData.title}
                                        onChange={handleEditFormChange}
                                        size="sm"
                                      />
                                    </FormControl>

                                    <HStack spacing={3}>
                                      <FormControl>
                                        <FormLabel fontSize="xs">Company</FormLabel>
                                        <Input
                                          name="company"
                                          value={editFormData.company}
                                          onChange={handleEditFormChange}
                                          size="sm"
                                        />
                                      </FormControl>

                                      <FormControl>
                                        <FormLabel fontSize="xs">Location</FormLabel>
                                        <Input
                                          name="location"
                                          value={editFormData.location}
                                          onChange={handleEditFormChange}
                                          size="sm"
                                        />
                                      </FormControl>
                                    </HStack>

                                    <FormControl>
                                      <FormLabel fontSize="xs">Status</FormLabel>
                                      <Select
                                        name="status"
                                        value={editFormData.status}
                                        onChange={handleEditFormChange}
                                        size="sm"
                                      >
                                        <option value="Saved">Saved</option>
                                        <option value="Applied">Applied</option>
                                        <option value="Interview">Interview</option>
                                        <option value="Offer">Offer</option>
                                        <option value="Rejected">Rejected</option>
                                      </Select>
                                    </FormControl>

                                    <HStack justify="flex-end" spacing={2} mt={2}>
                                      <IconButton
                                        aria-label="Cancel"
                                        icon={<FaTimes />}
                                        size="sm"
                                        variant="ghost"
                                        onClick={handleCancelEdit}
                                      />
                                      <IconButton
                                        aria-label="Save"
                                        icon={<FaSave />}
                                        size="sm"
                                        colorScheme="green"
                                        onClick={() => handleSaveJob(job.id)}
                                      />
                                    </HStack>
                                  </VStack>
                                ) : (
                                  // View mode
                                  <VStack align="stretch" spacing={2}>
                                    <HStack justify="space-between" align="flex-start">
                                      <Heading
                                        size="sm"
                                        fontWeight="medium"
                                        textDecoration={job.isCompleted ? 'line-through' : 'none'}
                                      >
                                        {job.title}
                                      </Heading>
                                      <HStack spacing={1}>
                                        {getJobApplicationStatus(job.id) && (
                                          <Badge colorScheme={getStatusColor(getJobApplicationStatus(job.id))} fontSize="xs" variant="subtle">
                                            {getJobApplicationStatus(job.id)}
                                          </Badge>
                                        )}
                                        <Badge colorScheme={job.isCompleted ? "green" : "purple"} fontSize="xs" variant="subtle">
                                          {job.isCompleted ? "Applied" : formatDate(job.createdAt)}
                                        </Badge>
                                      </HStack>
                                    </HStack>
                                    <Text noOfLines={1} fontSize="sm" color="gray.600">
                                      {job.company} • {job.location}
                                    </Text>
                                    <HStack spacing={{ base: 1, md: 2 }} justify="flex-end" flexWrap="wrap">
                                      <Tooltip label="Mark as Applied">
                                        <span>
                                          <Checkbox
                                            isChecked={job.isCompleted}
                                            onChange={(e) => {
                                              e.stopPropagation();
                                              checkboxHandler(e, job);
                                            }}
                                            colorScheme="green"
                                            size="sm"
                                          />
                                        </span>
                                      </Tooltip>
                                      <Tooltip label="Edit Job">
                                        <span>
                                          <ActionButton
                                            icon={FaEdit}
                                            label=""
                                            variant="ghost"
                                            size="xs"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleEditJob(job);
                                            }}
                                          />
                                        </span>
                                      </Tooltip>
                                      <Tooltip label="Create Cover Letter">
                                        <span>
                                          <ActionButton
                                            icon={FaPlus}
                                            label=""
                                            variant="ghost"
                                            size="xs"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              updateCoverLetterHandler(job.id);
                                            }}
                                          />
                                        </span>
                                      </Tooltip>
                                      <Tooltip label="Delete Job">
                                        <span>
                                          <ActionButton
                                            icon={FiDelete}
                                            label=""
                                            variant="ghost"
                                            colorScheme="red"
                                            size="xs"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              setJobId(job.id);
                                              deleteOnOpen();
                                            }}
                                          />
                                        </span>
                                      </Tooltip>
                                    </HStack>
                                  </VStack>
                                )}
                              </Box>
                            ))}
                          </VStack>
                        </ContentContainer>
                      </GridItem>

                      {/* Right side - Job details preview */}
                      <GridItem>
                        {selectedJob ? (
                          <ContentContainer
                            height="calc(100vh - 200px)"
                            overflowY="auto"
                            delay={0.4}
                          >
                            <VStack align="stretch" spacing={6}>
                              <Box>
                                {editingJobId === selectedJob.id ? (
                                  // Edit mode for job details
                                  <VStack align="stretch" spacing={4}>
                                    <HStack justify="space-between">
                                      <Heading size="md" fontWeight="medium">Edit Job</Heading>
                                      <HStack spacing={{ base: 1, md: 2 }}>
                                        <IconButton
                                          aria-label="Cancel"
                                          icon={<FaTimes />}
                                          size="sm"
                                          variant="ghost"
                                          onClick={handleCancelEdit}
                                        />
                                        <IconButton
                                          aria-label="Save"
                                          icon={<FaSave />}
                                          size="sm"
                                          colorScheme="green"
                                          onClick={() => handleSaveJob(selectedJob.id)}
                                        />
                                      </HStack>
                                    </HStack>

                                    <FormControl>
                                      <FormLabel>Job Title</FormLabel>
                                      <Input
                                        name="title"
                                        value={editFormData.title}
                                        onChange={handleEditFormChange}
                                      />
                                    </FormControl>

                                    <FormControl>
                                      <FormLabel>Company</FormLabel>
                                      <Input
                                        name="company"
                                        value={editFormData.company}
                                        onChange={handleEditFormChange}
                                      />
                                    </FormControl>

                                    <FormControl>
                                      <FormLabel>Location</FormLabel>
                                      <Input
                                        name="location"
                                        value={editFormData.location}
                                        onChange={handleEditFormChange}
                                      />
                                    </FormControl>

                                    <FormControl>
                                      <FormLabel>Status</FormLabel>
                                      <Select
                                        name="status"
                                        value={editFormData.status}
                                        onChange={handleEditFormChange}
                                      >
                                        <option value="Saved">Saved</option>
                                        <option value="Applied">Applied</option>
                                        <option value="Interview">Interview</option>
                                        <option value="Offer">Offer</option>
                                        <option value="Rejected">Rejected</option>
                                      </Select>
                                    </FormControl>

                                    <FormControl>
                                      <FormLabel>Job Description</FormLabel>
                                      <Textarea
                                        name="description"
                                        value={editFormData.description}
                                        onChange={handleEditFormChange}
                                        minH="200px"
                                      />
                                    </FormControl>

                                    <FormControl display="flex" alignItems="center">
                                      <Checkbox
                                        name="isCompleted"
                                        isChecked={editFormData.isCompleted}
                                        onChange={handleCheckboxChange}
                                        colorScheme="green"
                                        size="lg"
                                      />
                                      <FormLabel mb="0" ml={2}>
                                        Mark as Applied
                                      </FormLabel>
                                    </FormControl>
                                  </VStack>
                                ) : (
                                  // View mode for job details
                                  <>
                                    <HStack justify="space-between" mb={4}>
                                      <Heading size="md" fontWeight="medium">{selectedJob.title}</Heading>
                                      <HStack spacing={{ base: 1, md: 2 }}>
                                        <Badge colorScheme="purple" px={2} py={1}>
                                          {selectedJob.company}
                                        </Badge>
                                        <IconButton
                                          aria-label="Edit Job"
                                          icon={<FaEdit />}
                                          size="sm"
                                          variant="ghost"
                                          onClick={() => handleEditJob(selectedJob)}
                                        />
                                      </HStack>
                                    </HStack>

                                    {/* Job Details Section */}
                                    <VStack align="stretch" spacing={4} mb={6}>
                                      {/* Company and Location */}
                                      <HStack spacing={4} align="center">
                                        <Box>
                                          <Text fontSize="sm" color="gray.500" fontWeight="medium">Company</Text>
                                          <Text fontSize="md" fontWeight="semibold">{selectedJob.company}</Text>
                                        </Box>
                                        {selectedJob.location && (
                                          <Box>
                                            <Text fontSize="sm" color="gray.500" fontWeight="medium">Location</Text>
                                            <HStack spacing={1}>
                                              <Icon as={FiMapPin} boxSize={3} color="gray.400" />
                                              <Text fontSize="md">{selectedJob.location}</Text>
                                            </HStack>
                                          </Box>
                                        )}
                                      </HStack>

                                      {/* Status and Date */}
                                      <HStack spacing={4} align="center">
                                        <Box>
                                          <Text fontSize="sm" color="gray.500" fontWeight="medium">Status</Text>
                                          <HStack spacing={2} align="center">
                                            <Badge 
                                              colorScheme={selectedJob.isCompleted ? "green" : "gray"} 
                                              variant="subtle"
                                            >
                                              {selectedJob.isCompleted ? "Applied" : "Saved"}
                                            </Badge>
                                            {getJobApplicationStatus(selectedJob.id) && (
                                              <Badge 
                                                colorScheme={getStatusColor(getJobApplicationStatus(selectedJob.id))} 
                                                variant="subtle"
                                              >
                                                {getJobApplicationStatus(selectedJob.id)}
                                              </Badge>
                                            )}
                                          </HStack>
                                        </Box>
                                        <Box>
                                          <Text fontSize="sm" color="gray.500" fontWeight="medium">Date Added</Text>
                                          <HStack spacing={1}>
                                            <Icon as={FiCalendar} boxSize={3} color="gray.400" />
                                            <Text fontSize="sm">{formatDate(selectedJob.createdAt)}</Text>
                                          </HStack>
                                        </Box>
                                      </HStack>

                                      {/* Job Description */}
                                      {selectedJob.description && (
                                        <Box>
                                          <Text fontSize="sm" color="gray.500" fontWeight="medium" mb={2}>Job Description</Text>
                                          <Box 
                                            bg={useColorModeValue('gray.50', 'gray.700')} 
                                            p={4} 
                                            borderRadius="md"
                                            maxH="300px"
                                            overflowY="auto"
                                            border="1px"
                                            borderColor={useColorModeValue('gray.200', 'gray.600')}
                                          >
                                            <Text 
                                              fontSize="sm" 
                                              whiteSpace="pre-wrap" 
                                              lineHeight="1.6"
                                              color={useColorModeValue('gray.700', 'gray.300')}
                                            >
                                              {selectedJob.description}
                                            </Text>
                                          </Box>
                                        </Box>
                                      )}
                                    </VStack>

                                    <HStack spacing={{ base: 2, md: 3 }} justify="flex-end" flexWrap="wrap">
                                      <ActionButton
                                        icon={FaEye}
                                        label="View Letters"
                                        variant="outline"
                                        size={{ base: "sm", md: "md" }}
                                        onClick={() => coverLetterHandler(selectedJob)}
                                      />
                                      <ActionButton
                                        icon={FaPlus}
                                        label="Create Letter"
                                        variant="primary"
                                        size={{ base: "sm", md: "md" }}
                                        onClick={() => updateCoverLetterHandler(selectedJob.id)}
                                      />
                                    </HStack>
                                  </>
                                )}
                              </Box>
                            </VStack>
                          </ContentContainer>
                        ) : (
                          <ContentContainer
                            height="calc(100vh - 200px)"
                            display="flex"
                            alignItems="center"
                            justifyContent="center"
                            delay={0.4}
                          >
                            <Text fontSize="sm" color="gray.500">
                              Select a job from the list to view details
                            </Text>
                          </ContentContainer>
                        )}
                      </GridItem>
                    </Grid>
                  )}
                </VStack>
              </TabPanel>

              {/* Discover Jobs Tab */}
              <TabPanel px={0} py={6}>
                <VStack spacing={4} align="stretch">
                  <Box textAlign="center">
                    <Heading size="md" color={textColor} mb={2}>
                      Discover New Opportunities
                    </Heading>
                    <Text fontSize="sm" color="gray.500">
                      Browse curated job recommendations from top companies
                    </Text>
                  </Box>
                  <JobRecommendations onJobSelect={handleJobImport} />
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </VStack>
      </Container>
      {coverLetter && coverLetter.length > 0 && (
        <ModalElement coverLetterData={coverLetter} isOpen={isOpen} onOpen={onOpen} onClose={onClose} />
      )}
      <DeleteJob jobId={jobId} isOpen={deleteIsOpen} onOpen={deleteOnOpen} onClose={deleteOnClose} />
    </Box>
  );
}

export default JobsPage;
