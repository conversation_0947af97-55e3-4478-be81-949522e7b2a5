// Environment-specific configurations for CareerDart

export interface EnvironmentConfig {
  apiUrl: string;
  stripeKey: string;
  enableAnalytics: boolean;
  enableLogging: boolean;
  debugMode: boolean;
  enablePerformanceMonitoring: boolean;
  sentryDsn?: string;
  googleAnalyticsId?: string;
}

export const environments: Record<string, EnvironmentConfig> = {
  development: {
    apiUrl: process.env.REACT_APP_API_URL || 'https://careerdart-dev.fly.dev',
    stripeKey: process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY || 'pk_test_development_key',
    enableAnalytics: process.env.REACT_APP_ENABLE_ANALYTICS === 'true',
    enableLogging: true,
    debugMode: true,
    enablePerformanceMonitoring: process.env.REACT_APP_ENABLE_PERFORMANCE_MONITORING === 'true',
    sentryDsn: process.env.REACT_APP_SENTRY_DSN,
    googleAnalyticsId: process.env.REACT_APP_GA_MEASUREMENT_ID,
  },
  staging: {
    apiUrl: process.env.REACT_APP_API_URL || 'https://careerdart-staging.fly.dev',
    stripeKey: process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY || 'pk_test_staging_key',
    enableAnalytics: true,
    enableLogging: true,
    debugMode: false,
    enablePerformanceMonitoring: true,
    sentryDsn: process.env.REACT_APP_SENTRY_DSN,
    googleAnalyticsId: process.env.REACT_APP_GA_MEASUREMENT_ID,
  },
  production: {
    apiUrl: process.env.REACT_APP_API_URL || 'https://api.careerdart.com',
    stripeKey: process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY || 'pk_live_production_key',
    enableAnalytics: true,
    enableLogging: false,
    debugMode: false,
    enablePerformanceMonitoring: true,
    sentryDsn: process.env.REACT_APP_SENTRY_DSN,
    googleAnalyticsId: process.env.REACT_APP_GA_MEASUREMENT_ID,
  }
};

export const getEnvironmentConfig = (): EnvironmentConfig => {
  const env = process.env.REACT_APP_ENV || process.env.NODE_ENV || 'development';
  const config = environments[env] || environments.development;
  
  // Log current environment in development
  if (config.debugMode) {
    console.log('🌍 Environment Config:', {
      environment: env,
      apiUrl: config.apiUrl,
      enableAnalytics: config.enableAnalytics,
      debugMode: config.debugMode,
    });
  }
  
  return config;
};

// Environment-specific feature flags
export const getFeatureFlags = () => {
  const config = getEnvironmentConfig();
  
  return {
    enableAnalytics: config.enableAnalytics,
    enableLogging: config.enableLogging,
    enablePerformanceMonitoring: config.enablePerformanceMonitoring,
    enableDebugTools: config.debugMode,
    enableSourceMaps: process.env.GENERATE_SOURCEMAP === 'true',
    enableHotReload: process.env.NODE_ENV === 'development',
  };
};

// API configuration based on environment
export const getApiConfig = () => {
  const config = getEnvironmentConfig();
  
  return {
    baseURL: config.apiUrl,
    timeout: config.debugMode ? 30000 : 10000, // Longer timeout in debug mode
    retries: config.debugMode ? 1 : 3,
    headers: {
      'Content-Type': 'application/json',
      ...(config.debugMode && { 'X-Debug-Mode': 'true' }),
    },
  };
};

// Performance monitoring configuration
export const getPerformanceConfig = () => {
  const config = getEnvironmentConfig();
  
  return {
    enabled: config.enablePerformanceMonitoring,
    sampleRate: config.debugMode ? 1.0 : 0.1, // 100% in dev, 10% in prod
    tracesSampleRate: config.debugMode ? 1.0 : 0.1,
    enableVitals: true,
    enableLighthouse: config.debugMode,
  };
};

// Export current environment info
export const currentEnvironment = {
  name: process.env.REACT_APP_ENV || process.env.NODE_ENV || 'development',
  isDevelopment: (process.env.REACT_APP_ENV || process.env.NODE_ENV) === 'development',
  isStaging: process.env.REACT_APP_ENV === 'staging',
  isProduction: process.env.REACT_APP_ENV === 'production',
  config: getEnvironmentConfig(),
  featureFlags: getFeatureFlags(),
  apiConfig: getApiConfig(),
  performanceConfig: getPerformanceConfig(),
}; 