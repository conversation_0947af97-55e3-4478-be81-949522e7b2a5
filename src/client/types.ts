import { type Job } from "wasp/entities";

export type JobPayload = Pick<Job, 'title' | 'company' | 'location' | 'description'>;

export interface Resume {
  id: string;
  userId: string;
  title: string;
  personalInfo: {
    fullName: string;
    email: string;
    phone: string;
    location: string;
    linkedIn?: string;
    website?: string;
  };
  summary: string;
  experience: Array<{
    id: string;
    company: string;
    position: string;
    startDate: string;
    endDate: string;
    current: boolean;
    description: string;
    achievements: string[];
  }>;
  education: Array<{
    id: string;
    institution: string;
    degree: string;
    field: string;
    startDate: string;
    endDate: string;
    current: boolean;
    gpa?: string;
  }>;
  skills: string[];
  certifications?: Array<{
    id: string;
    name: string;
    issuer: string;
    date: string;
  }>;
  fileData?: {
    name: string;
    type: string;
    data: string;
  };
  createdAt: string;
  updatedAt: string;
}
