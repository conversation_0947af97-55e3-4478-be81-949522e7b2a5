import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { z } from 'zod';
import {
  useFormValidation,
  validationSchemas,
  jobFormSchema,
  resumeFormSchema,
  customValidators,
} from '../useFormValidation';

describe('useFormValidation', () => {
  const testSchema = z.object({
    email: validationSchemas.email,
    username: validationSchemas.username,
    password: validationSchemas.password,
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('initializes with empty errors and touched state', () => {
    const { result } = renderHook(() =>
      useFormValidation({ schema: testSchema })
    );

    expect(result.current.errors).toEqual({});
    expect(result.current.touched).toEqual({});
    expect(result.current.hasErrors).toBe(false);
    expect(result.current.isValidating).toBe(false);
  });

  it('validates form data correctly', () => {
    const { result } = renderHook(() =>
      useFormValidation({ schema: testSchema })
    );

    const validData = {
      email: '<EMAIL>',
      username: 'testuser',
      password: 'Password123',
    };

    const validResult = result.current.validateForm(validData);
    expect(validResult.isValid).toBe(true);
    expect(validResult.errors).toEqual([]);

    const invalidData = {
      email: 'invalid-email',
      username: 'ab', // too short
      password: 'weak', // doesn't meet requirements
    };

    const invalidResult = result.current.validateForm(invalidData);
    expect(invalidResult.isValid).toBe(false);
    expect(invalidResult.errors.length).toBeGreaterThan(0);
  });

  it('handles field changes with validation', async () => {
    const { result } = renderHook(() =>
      useFormValidation({
        schema: testSchema,
        validateOnChange: true,
        debounceMs: 0, // No debounce for testing
      })
    );

    const formData = {
      email: 'invalid-email',
      username: 'testuser',
      password: 'Password123',
    };

    // Mark field as touched first
    act(() => {
      result.current.handleFieldBlur('email', 'invalid-email', formData);
    });

    // Then trigger change validation
    act(() => {
      result.current.handleFieldChange('email', 'invalid-email', formData);
    });

    // Wait for debounced validation
    await new Promise(resolve => setTimeout(resolve, 50));

    expect(result.current.getFieldError('email')).toBeDefined();
    expect(result.current.isFieldInvalid('email')).toBe(true);
  });

  it('handles field blur events', () => {
    const { result } = renderHook(() =>
      useFormValidation({ schema: testSchema })
    );

    const formData = {
      email: '<EMAIL>',
      username: 'testuser',
      password: 'Password123',
    };

    act(() => {
      result.current.handleFieldBlur('email', '<EMAIL>', formData);
    });

    expect(result.current.touched.email).toBe(true);
  });

  it('clears errors correctly', () => {
    const { result } = renderHook(() =>
      useFormValidation({ schema: testSchema })
    );

    const invalidData = {
      email: 'invalid-email',
      username: 'ab',
      password: 'weak',
    };

    act(() => {
      result.current.validateForm(invalidData);
    });

    expect(result.current.hasErrors).toBe(true);

    act(() => {
      result.current.clearErrors();
    });

    expect(result.current.errors).toEqual({});
    expect(result.current.touched).toEqual({});
    expect(result.current.hasErrors).toBe(false);
  });

  it('clears specific field errors', () => {
    const { result } = renderHook(() =>
      useFormValidation({ schema: testSchema })
    );

    const invalidData = {
      email: 'invalid-email',
      username: 'ab',
      password: 'weak',
    };

    act(() => {
      result.current.validateForm(invalidData);
    });

    expect(result.current.getFieldError('email')).toBeDefined();

    act(() => {
      result.current.clearFieldError('email');
    });

    expect(result.current.getFieldError('email')).toBeUndefined();
    expect(result.current.touched.email).toBeUndefined();
  });
});

describe('validation schemas', () => {
  it('validates email correctly', () => {
    expect(() => validationSchemas.email.parse('<EMAIL>')).not.toThrow();
    expect(() => validationSchemas.email.parse('invalid-email')).toThrow();
  });

  it('validates password correctly', () => {
    expect(() => validationSchemas.password.parse('Password123')).not.toThrow();
    expect(() => validationSchemas.password.parse('weak')).toThrow();
    expect(() => validationSchemas.password.parse('password123')).toThrow(); // no uppercase
    expect(() => validationSchemas.password.parse('PASSWORD123')).toThrow(); // no lowercase
    expect(() => validationSchemas.password.parse('Password')).toThrow(); // no number
  });

  it('validates username correctly', () => {
    expect(() => validationSchemas.username.parse('testuser')).not.toThrow();
    expect(() => validationSchemas.username.parse('test_user123')).not.toThrow();
    expect(() => validationSchemas.username.parse('ab')).toThrow(); // too short
    expect(() => validationSchemas.username.parse('test-user')).toThrow(); // invalid character
  });

  it('validates LinkedIn URL correctly', () => {
    expect(() =>
      validationSchemas.linkedInUrl.parse('https://linkedin.com/jobs/view/123456')
    ).not.toThrow();
    expect(() =>
      validationSchemas.linkedInUrl.parse('https://example.com')
    ).toThrow();
  });

  it('validates phone number correctly', () => {
    expect(() => validationSchemas.phone.parse('+1234567890')).not.toThrow();
    expect(() => validationSchemas.phone.parse('1234567890')).not.toThrow();
    expect(() => validationSchemas.phone.parse('invalid-phone')).toThrow();
  });
});

describe('form schemas', () => {
  it('validates job form schema', () => {
    const validJobData = {
      title: 'Software Engineer',
      company: 'Tech Corp',
      location: 'San Francisco, CA',
      description: 'We are looking for a skilled software engineer to join our team.',
    };

    expect(() => jobFormSchema.parse(validJobData)).not.toThrow();

    const invalidJobData = {
      title: 'A', // too short
      company: '',
      location: '',
      description: 'Short', // too short
    };

    expect(() => jobFormSchema.parse(invalidJobData)).toThrow();
  });

  it('validates resume form schema', () => {
    const validResumeData = {
      title: 'Software Engineer Resume',
      personalInfo: {
        fullName: 'John Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        location: 'San Francisco, CA',
        linkedIn: 'https://linkedin.com/in/johndoe',
        website: 'https://johndoe.dev',
      },
      summary: 'Experienced software engineer with 5+ years of experience in web development.',
    };

    expect(() => resumeFormSchema.parse(validResumeData)).not.toThrow();

    const invalidResumeData = {
      title: 'AB', // too short
      personalInfo: {
        fullName: 'A', // too short
        email: 'invalid-email',
        phone: 'invalid-phone',
        location: '',
        linkedIn: 'invalid-url',
        website: 'invalid-url',
      },
      summary: 'Short', // too short
    };

    expect(() => resumeFormSchema.parse(invalidResumeData)).toThrow();
  });
});

describe('custom validators', () => {
  it('validates username availability', async () => {
    const isAvailable = await customValidators.isUsernameAvailable('newuser');
    expect(isAvailable).toBe(true);

    const isNotAvailable = await customValidators.isUsernameAvailable('admin');
    expect(isNotAvailable).toBe(false);
  });

  it('validates file upload', () => {
    const validFile = new File(['content'], 'test.pdf', { type: 'application/pdf' });
    const result = customValidators.validateFile(validFile);
    expect(result.isValid).toBe(true);
    expect(result.error).toBeNull();

    const invalidTypeFile = new File(['content'], 'test.txt', { type: 'text/plain' });
    const invalidTypeResult = customValidators.validateFile(invalidTypeFile);
    expect(invalidTypeResult.isValid).toBe(false);
    expect(invalidTypeResult.error).toBe('File type not allowed');

    // Create a large file (mock)
    const largeFile = new File(['x'.repeat(10 * 1024 * 1024)], 'large.pdf', {
      type: 'application/pdf',
    });
    const largeSizeResult = customValidators.validateFile(largeFile);
    expect(largeSizeResult.isValid).toBe(false);
    expect(largeSizeResult.error).toBe('File size too large');
  });

  it('validates creativity slider value', () => {
    expect(customValidators.validateCreativity(50)).toBe(true);
    expect(customValidators.validateCreativity(0)).toBe(true);
    expect(customValidators.validateCreativity(100)).toBe(true);
    expect(customValidators.validateCreativity(-1)).toBe(false);
    expect(customValidators.validateCreativity(101)).toBe(false);
  });
});
