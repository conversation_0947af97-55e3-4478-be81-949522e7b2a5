import { useAuth } from 'wasp/client/auth';
import { useRef, useEffect, useState, useMemo } from 'react';

// Global cache to share auth state across components
let globalAuthCache: any = null;
let globalCacheTimestamp = 0;
const CACHE_DURATION = 30000; // Cache for 30 seconds to reduce requests

// Suppress console errors for expected 401 responses
const originalConsoleError = console.error;
console.error = (...args) => {
  // Check if this is a 401 auth error that we want to suppress
  const message = args.join(' ');
  if (
    message.includes('401') &&
    (message.includes('/auth/me') || message.includes('Unauthorized'))
  ) {
    // Suppress this error as it's expected when user is not authenticated
    return;
  }
  // Allow all other console errors
  originalConsoleError.apply(console, args);
};

// Custom hook to optimize auth calls and reduce unnecessary requests
export function useOptimizedAuth() {
  const authResult = useAuth();
  const [cachedResult, setCachedResult] = useState(() => globalAuthCache || authResult);
  const lastUpdateRef = useRef<number>(globalCacheTimestamp);

  useEffect(() => {
    const now = Date.now();

    // Only update if enough time has passed or if the auth state actually changed
    if (
      now - lastUpdateRef.current > CACHE_DURATION ||
      authResult.data?.id !== cachedResult.data?.id ||
      authResult.isLoading !== cachedResult.isLoading ||
      (authResult.data && !cachedResult.data) ||
      (!authResult.data && cachedResult.data)
    ) {
      setCachedResult(authResult);
      lastUpdateRef.current = now;

      // Update global cache
      globalAuthCache = authResult;
      globalCacheTimestamp = now;
    }
  }, [authResult, cachedResult]);

  // Memoize the result to prevent unnecessary re-renders
  return useMemo(() => cachedResult, [cachedResult]);
}

// Alternative hook that only checks auth once and caches the result
export function useStaticAuth() {
  const [authState, setAuthState] = useState<any>(null);
  const [hasChecked, setHasChecked] = useState(false);
  const authResult = useAuth();

  useEffect(() => {
    if (!hasChecked && !authResult.isLoading) {
      setAuthState(authResult);
      setHasChecked(true);
    }
  }, [authResult, hasChecked]);

  return hasChecked ? authState : authResult;
}
