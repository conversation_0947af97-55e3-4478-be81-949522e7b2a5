import { useState, useEffect } from 'react';

/**
 * Hook to detect user's motion preferences
 * Respects the prefers-reduced-motion media query
 */
export const useReducedMotion = (): boolean => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    // Set initial value
    setPrefersReducedMotion(mediaQuery.matches);
    
    // Listen for changes
    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };
    
    mediaQuery.addEventListener('change', handleChange);
    
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);

  return prefersReducedMotion;
};

/**
 * Hook to get motion-safe animation variants
 * Returns reduced animations when user prefers reduced motion
 */
export const useMotionVariants = () => {
  const prefersReducedMotion = useReducedMotion();

  const variants = {
    fadeIn: {
      initial: { opacity: prefersReducedMotion ? 1 : 0 },
      animate: { opacity: 1 },
      transition: { duration: prefersReducedMotion ? 0 : 0.3 }
    },
    slideUp: {
      initial: { 
        opacity: prefersReducedMotion ? 1 : 0, 
        y: prefersReducedMotion ? 0 : 20 
      },
      animate: { opacity: 1, y: 0 },
      transition: { duration: prefersReducedMotion ? 0 : 0.5 }
    },
    scale: {
      whileHover: prefersReducedMotion ? {} : { scale: 1.05 },
      whileTap: prefersReducedMotion ? {} : { scale: 0.95 },
      transition: { duration: prefersReducedMotion ? 0 : 0.2 }
    }
  };

  return { variants, prefersReducedMotion };
};
