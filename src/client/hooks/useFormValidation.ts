import { useState, useCallback, useEffect } from 'react';
import { z } from 'zod';

// Common validation schemas
export const validationSchemas = {
  email: z.string().email('Please enter a valid email address'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number'),
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(20, 'Username must be less than 20 characters')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores'),
  jobTitle: z.string()
    .min(2, 'Job title must be at least 2 characters')
    .max(100, 'Job title must be less than 100 characters'),
  company: z.string()
    .min(1, 'Company name is required')
    .max(100, 'Company name must be less than 100 characters'),
  location: z.string()
    .min(1, 'Location is required')
    .max(100, 'Location must be less than 100 characters'),
  description: z.string()
    .min(10, 'Description must be at least 10 characters')
    .max(5000, 'Description must be less than 5000 characters'),
  url: z.string().url('Please enter a valid URL'),
  linkedInUrl: z.string()
    .url('Please enter a valid URL')
    .refine(
      (url) => url.includes('linkedin.com/jobs/view/'),
      'Please enter a valid LinkedIn job URL'
    ),
  phone: z.string()
    .regex(/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number'),
  resumeTitle: z.string()
    .min(3, 'Resume title must be at least 3 characters')
    .max(50, 'Resume title must be less than 50 characters'),
};

// Job form schema
export const jobFormSchema = z.object({
  title: validationSchemas.jobTitle,
  company: validationSchemas.company,
  location: validationSchemas.location,
  description: validationSchemas.description,
});

// Resume form schema
export const resumeFormSchema = z.object({
  title: validationSchemas.resumeTitle,
  personalInfo: z.object({
    fullName: z.string().min(2, 'Full name is required'),
    email: validationSchemas.email,
    phone: validationSchemas.phone.optional(),
    location: z.string().min(1, 'Location is required'),
    linkedIn: validationSchemas.url.optional().or(z.literal('')),
    website: validationSchemas.url.optional().or(z.literal('')),
  }),
  summary: z.string()
    .min(50, 'Summary must be at least 50 characters')
    .max(500, 'Summary must be less than 500 characters'),
});

// LinkedIn import schema
export const linkedInImportSchema = z.object({
  url: validationSchemas.linkedInUrl,
});

// User profile schema
export const userProfileSchema = z.object({
  username: validationSchemas.username,
  email: validationSchemas.email,
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),
  yearsOfExperience: z.number()
    .min(0, 'Years of experience cannot be negative')
    .max(50, 'Years of experience seems too high')
    .optional(),
});

export type ValidationError = {
  field: string;
  message: string;
};

export type ValidationResult = {
  isValid: boolean;
  errors: ValidationError[];
};

export interface UseFormValidationOptions<T> {
  schema: z.ZodSchema<T>;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  debounceMs?: number;
}

export function useFormValidation<T extends Record<string, any>>({
  schema,
  validateOnChange = true,
  validateOnBlur = true,
  debounceMs = 300,
}: UseFormValidationOptions<T>) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isValidating, setIsValidating] = useState(false);

  // Debounced validation function
  const validateField = useCallback(
    debounce((fieldName: string, value: any, formData: T) => {
      setIsValidating(true);
      
      try {
        // Validate the entire form to get proper context
        schema.parse(formData);
        
        // If validation passes, clear the error for this field
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[fieldName];
          return newErrors;
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          const fieldError = error.errors.find(err => 
            err.path.join('.') === fieldName || err.path[0] === fieldName
          );
          
          if (fieldError) {
            setErrors(prev => ({
              ...prev,
              [fieldName]: fieldError.message,
            }));
          }
        }
      } finally {
        setIsValidating(false);
      }
    }, debounceMs),
    [schema, debounceMs]
  );

  // Validate entire form
  const validateForm = useCallback((formData: T): ValidationResult => {
    try {
      schema.parse(formData);
      setErrors({});
      return { isValid: true, errors: [] };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const newErrors: Record<string, string> = {};
        const validationErrors: ValidationError[] = [];

        error.errors.forEach(err => {
          const fieldName = err.path.join('.');
          newErrors[fieldName] = err.message;
          validationErrors.push({
            field: fieldName,
            message: err.message,
          });
        });

        setErrors(newErrors);
        return { isValid: false, errors: validationErrors };
      }
    }
    
    return { isValid: false, errors: [] };
  }, [schema]);

  // Handle field change
  const handleFieldChange = useCallback((fieldName: string, value: any, formData: T) => {
    if (validateOnChange && touched[fieldName]) {
      validateField(fieldName, value, formData);
    }
  }, [validateOnChange, touched, validateField]);

  // Handle field blur
  const handleFieldBlur = useCallback((fieldName: string, value: any, formData: T) => {
    setTouched(prev => ({ ...prev, [fieldName]: true }));
    
    if (validateOnBlur) {
      validateField(fieldName, value, formData);
    }
  }, [validateOnBlur, validateField]);

  // Get field error
  const getFieldError = useCallback((fieldName: string) => {
    return touched[fieldName] ? errors[fieldName] : undefined;
  }, [errors, touched]);

  // Check if field is invalid
  const isFieldInvalid = useCallback((fieldName: string) => {
    return touched[fieldName] && !!errors[fieldName];
  }, [errors, touched]);

  // Clear all errors
  const clearErrors = useCallback(() => {
    setErrors({});
    setTouched({});
  }, []);

  // Clear specific field error
  const clearFieldError = useCallback((fieldName: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fieldName];
      return newErrors;
    });
    setTouched(prev => {
      const newTouched = { ...prev };
      delete newTouched[fieldName];
      return newTouched;
    });
  }, []);

  return {
    errors,
    touched,
    isValidating,
    validateForm,
    handleFieldChange,
    handleFieldBlur,
    getFieldError,
    isFieldInvalid,
    clearErrors,
    clearFieldError,
    hasErrors: Object.keys(errors).length > 0,
  };
}

// Debounce utility function
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// Custom validation rules
export const customValidators = {
  // Check if username is available (would need API call)
  isUsernameAvailable: async (username: string): Promise<boolean> => {
    // Implement API call to check username availability
    // This is a placeholder
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(!['admin', 'root', 'test'].includes(username.toLowerCase()));
      }, 500);
    });
  },

  // Validate file upload
  validateFile: (file: File, options: {
    maxSize?: number;
    allowedTypes?: string[];
  } = {}) => {
    const { maxSize = 5 * 1024 * 1024, allowedTypes = ['application/pdf'] } = options;
    
    if (file.size > maxSize) {
      return { isValid: false, error: 'File size too large' };
    }
    
    if (!allowedTypes.includes(file.type)) {
      return { isValid: false, error: 'File type not allowed' };
    }
    
    return { isValid: true, error: null };
  },

  // Validate creativity slider value
  validateCreativity: (value: number) => {
    return value >= 0 && value <= 100;
  },
};

// Form field component props helper
export interface FormFieldProps {
  name: string;
  value: any;
  onChange: (value: any) => void;
  onBlur?: () => void;
  error?: string;
  isInvalid?: boolean;
  isRequired?: boolean;
}

// Hook for creating form field props
export function useFormField<T extends Record<string, any>>(
  fieldName: string,
  formData: T,
  setFormData: (data: T) => void,
  validation: ReturnType<typeof useFormValidation>
): FormFieldProps {
  return {
    name: fieldName,
    value: getNestedValue(formData, fieldName),
    onChange: (value: any) => {
      const newFormData = setNestedValue(formData, fieldName, value);
      setFormData(newFormData);
      validation.handleFieldChange(fieldName, value, newFormData);
    },
    onBlur: () => {
      validation.handleFieldBlur(fieldName, getNestedValue(formData, fieldName), formData);
    },
    error: validation.getFieldError(fieldName),
    isInvalid: validation.isFieldInvalid(fieldName),
  };
}

// Utility functions for nested object access
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

function setNestedValue<T>(obj: T, path: string, value: any): T {
  const keys = path.split('.');
  const newObj = JSON.parse(JSON.stringify(obj));
  
  let current = newObj;
  for (let i = 0; i < keys.length - 1; i++) {
    if (!current[keys[i]]) {
      current[keys[i]] = {};
    }
    current = current[keys[i]];
  }
  
  current[keys[keys.length - 1]] = value;
  return newObj;
}
