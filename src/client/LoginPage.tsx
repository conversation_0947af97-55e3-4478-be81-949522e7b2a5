import { useAuth, googleSignInUrl as signInUrl, login, signup, requestPasswordReset } from 'wasp/client/auth';
import { useAction } from 'wasp/client/operations';
import { AiOutlineGoogle } from 'react-icons/ai';
import { FaShieldAlt, FaRocket, FaUsers, FaStar, FaQuoteLeft, FaCheckCircle, FaArrowRight, FaChartLine, FaBriefcase, FaGraduationCap, FaEye, FaEyeSlash, FaEnvelope, FaLock } from 'react-icons/fa';
import {
  VStack,
  Button,
  Spinner,
  Text,
  useDisclosure,
  Box,
  Heading,
  Flex,
  Card,
  CardBody,
  HStack,
  Icon,
  useColorModeValue,
  Container,
  Stack,
  Badge,
  Link,
  SimpleGrid,
  Avatar,
  Divider,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Wrap,
  WrapItem,
  Tag,
  TagLabel,
  TagLeftIcon,
  Circle,
  Grid,
  GridItem,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Input,
  FormControl,
  FormLabel,
  FormErrorMessage,
  InputGroup,
  InputRightElement,
  InputLeftElement,
  IconButton,
  Alert,
  AlertIcon,
  AlertDescription,
  useToast,
  Progress,
  Collapse
} from '@chakra-ui/react';
import { motion } from 'framer-motion';
import BorderBox from './components/BorderBox';
import { useEffect, useState } from 'react';
import UndrawIllustration, { IllustrationPresets } from './components/UndrawIllustration';
import { useNavigate, Link as RouterLink } from 'react-router-dom';

import { useForm } from 'react-hook-form';

const MotionBox = motion(Box);
const MotionCard = motion(Card);

interface LoginForm {
  email: string;
  password: string;
}

interface SignupForm {
  email: string;
  password: string;
  confirmPassword: string;
}

export default function Login() {
  const { data: user, isLoading } = useAuth();

  // Enhanced state management for better UX
  const [activeTab, setActiveTab] = useState(0);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [authError, setAuthError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resetEmailSent, setResetEmailSent] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);

  const toast = useToast();
  const navigate = useNavigate();

  // Form hooks with enhanced validation
  const loginForm = useForm<LoginForm>({
    mode: 'onBlur',
    defaultValues: { email: '', password: '' }
  });
  const signupForm = useForm<SignupForm>({
    mode: 'onChange',
    defaultValues: { email: '', password: '', confirmPassword: '' }
  });

  // Enhanced color scheme with better contrast
  const bgGradient = useColorModeValue(
    'linear(to-br, blue.50, purple.50, pink.50)',
    'linear(to-br, gray.900, blue.900, purple.900)'
  );
  const cardBg = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'gray.200');
  const accentColor = useColorModeValue('brand.500', 'brand.400');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // Check for OAuth errors in URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const error = urlParams.get('error');
    const errorDescription = urlParams.get('error_description');

    if (error) {
      console.error('OAuth Error:', error, errorDescription);
      let errorMessage = 'Authentication failed. Please try again.';

      if (error === 'access_denied') {
        errorMessage = 'Access was denied. Please try signing in again.';
      } else if (error === 'invalid_request') {
        errorMessage = 'Invalid request. Please try again.';
      } else if (error === 'server_error') {
        errorMessage = 'Server error occurred. Please try again later.';
      } else if (errorDescription) {
        errorMessage = errorDescription;
      }

      setAuthError(errorMessage);

      // Clean up URL parameters
      const cleanUrl = window.location.pathname;
      window.history.replaceState({}, document.title, cleanUrl);
    }
  }, []);

  // Redirect if already authenticated
  useEffect(() => {
    if (user) {
      navigate('/');
    }
  }, [user, navigate]);

  // Password strength calculator
  const calculatePasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength += 25;
    if (/[A-Z]/.test(password)) strength += 25;
    if (/[0-9]/.test(password)) strength += 25;
    if (/[^A-Za-z0-9]/.test(password)) strength += 25;
    return strength;
  };

  // Watch password field for strength calculation
  const watchedPassword = signupForm.watch('password');
  useEffect(() => {
    if (watchedPassword) {
      setPasswordStrength(calculatePasswordStrength(watchedPassword));
    } else {
      setPasswordStrength(0);
    }
  }, [watchedPassword]);

  // Enhanced form submission with better error handling
  const handleLogin = async (data: LoginForm) => {
    setIsSubmitting(true);
    setAuthError('');

    try {
      await login({ email: data.email, password: data.password });
      toast({
        title: 'Welcome back!',
        description: 'You have successfully signed in.',
        status: 'success',
        duration: 3000,
        isClosable: true,
        position: 'top-right'
      });
      navigate('/');
    } catch (error: any) {
      let errorMessage = 'Login failed. Please check your credentials.';

      if (error.message) {
        const msg = error.message.toLowerCase();
        if (msg.includes('unauthorized') || msg.includes('invalid credentials')) {
          errorMessage = 'Invalid email or password. If you just signed up, please check your email for a verification link.';
        } else if (msg.includes('email') && msg.includes('verified')) {
          errorMessage = 'Please verify your email address before signing in.';
        } else if (msg.includes('account') && msg.includes('not found')) {
          errorMessage = 'No account found with this email address.';
        } else {
          errorMessage = error.message;
        }
      }

      setAuthError(errorMessage);
      // Clear error after 10 seconds
      setTimeout(() => setAuthError(''), 10000);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSignup = async (data: SignupForm) => {
    setIsSubmitting(true);
    setAuthError('');

    try {
      await signup({ email: data.email, password: data.password });

      toast({
        title: 'Account created successfully!',
        description: 'Please check your email for a verification link before signing in.',
        status: 'success',
        duration: 8000,
        isClosable: true,
        position: 'top-right'
      });

      // Pre-fill login form and switch tabs
      loginForm.setValue('email', data.email);
      setActiveTab(0);
      signupForm.reset();
    } catch (error: any) {
      let errorMessage = 'Signup failed. Please try again.';

      if (error.message) {
        const msg = error.message.toLowerCase();
        if (msg.includes('email') && msg.includes('exists')) {
          errorMessage = 'An account with this email already exists. Try signing in instead.';
        } else if (msg.includes('password')) {
          errorMessage = 'Password must be at least 8 characters long.';
        } else {
          errorMessage = error.message;
        }
      }

      setAuthError(errorMessage);
      setTimeout(() => setAuthError(''), 10000);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePasswordReset = async (email: string) => {
    try {
      await requestPasswordReset({ email });
      setResetEmailSent(true);
      toast({
        title: 'Reset email sent!',
        description: 'Check your email for password reset instructions.',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to send reset email.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  return (
    <Box minH="100vh" bg={bgGradient}>
      <Container maxW={{ base: "container.sm", md: "container.xl" }} py={{ base: 2, md: 8 }}>
        <Grid
          templateColumns={{ base: "1fr", lg: "1fr 1fr" }}
          gap={{ base: 4, md: 8 }}
          alignItems="center"
          minH={{ base: "calc(100vh - 60px)", lg: "80vh" }}
        >
          {/* Left side - Auth Form */}
          <GridItem>
            <VStack spacing={{ base: 4, md: 8 }} align="stretch">
              <VStack spacing={{ base: 2, md: 4 }} textAlign="center">
                <Heading
                  size={{ base: "lg", md: "xl" }}
                  fontWeight="bold"
                  color={textColor}
                >
                  Welcome to CareerDart
                </Heading>
                <Text color={useColorModeValue("gray.600", "gray.400")} fontSize={{ base: "sm", md: "md" }}>
                  Your AI-powered career companion
                </Text>
              </VStack>

              <MotionCard
                bg={cardBg}
                p={{ base: 4, md: 8 }}
                borderRadius="2xl"
                boxShadow="xl"
                border="1px solid"
                borderColor={borderColor}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <CardBody p={0}>
                  <Tabs
                    index={activeTab}
                    onChange={(index) => {
                      setActiveTab(index);
                      setAuthError('');
                    }}
                    variant="soft-rounded"
                    colorScheme="blue"
                  >
                    <TabList
                      bg={useColorModeValue("gray.50", "gray.700")}
                      p={1}
                      borderRadius="xl"
                      mb={{ base: 3, md: 6 }}
                    >
                      <Tab
                        flex={1}
                        fontWeight="500"
                        fontSize={{ base: "sm", md: "md" }}
                        py={{ base: 2, md: 3 }}
                      >
                        Sign In
                      </Tab>
                      <Tab
                        flex={1}
                        fontWeight="500"
                        fontSize={{ base: "sm", md: "md" }}
                        py={{ base: 2, md: 3 }}
                      >
                        Sign Up
                      </Tab>
                    </TabList>

                    <TabPanels>
                      {/* Sign In Panel */}
                      <TabPanel p={0}>
                        <VStack spacing={{ base: 3, md: 6 }}>
                          <VStack spacing={2} textAlign="center">
                            <Heading size={{ base: "sm", md: "md" }} color={textColor} fontWeight="500">
                              Sign in to your account
                            </Heading>
                            <Text color={useColorModeValue("gray.600", "gray.400")} fontSize={{ base: "xs", md: "sm" }}>
                              Welcome back!
                            </Text>
                          </VStack>

                          {authError && (
                            <Alert status="error" borderRadius="lg">
                              <AlertIcon />
                              <AlertDescription fontSize={{ base: "xs", md: "sm" }}>{authError}</AlertDescription>
                            </Alert>
                          )}

                          {resetEmailSent && (
                            <Alert status="success" borderRadius="lg">
                              <AlertIcon />
                              <AlertDescription fontSize={{ base: "xs", md: "sm" }}>
                                Password reset instructions have been sent to your email.
                              </AlertDescription>
                            </Alert>
                          )}

                          {/* Google Sign-In - Priority Option */}
                          <VStack spacing={3} w="full">
                            <a href={signInUrl} style={{ width: '100%' }}>
                              <Button
                                leftIcon={<AiOutlineGoogle />}
                                size={{ base: "md", md: "lg" }}
                                width="full"
                                colorScheme="blue"
                                borderRadius="lg"
                                h={{ base: "44px", md: "48px" }}
                                fontSize={{ base: "sm", md: "md" }}
                                fontWeight="500"
                              >
                                Continue with Google
                              </Button>
                            </a>
                          </VStack>

                          <Box position="relative" w="full">
                            <Box
                              position="absolute"
                              top="50%"
                              left="0"
                              right="0"
                              h="1px"
                              bg="gray.200"
                              _dark={{ bg: 'gray.600' }}
                            />
                            <Text
                              position="relative"
                              bg={cardBg}
                              px={4}
                              color="gray.500"
                              fontSize={{ base: "xs", md: "sm" }}
                              textAlign="center"
                            >
                              or sign in with email
                            </Text>
                          </Box>

                          {/* Email/Password Login Form */}
                          <form onSubmit={loginForm.handleSubmit(handleLogin)} style={{ width: '100%' }}>
                            <VStack spacing={{ base: 3, md: 4 }} w="full">
                              <FormControl isInvalid={!!loginForm.formState.errors.email}>
                                <FormLabel fontSize="sm" fontWeight="600" color={textColor}>Email</FormLabel>
                                <InputGroup>
                                  <InputLeftElement pointerEvents="none">
                                    <Icon as={FaEnvelope} color="gray.400" />
                                  </InputLeftElement>
                                  <Input
                                    type="email"
                                    placeholder="Enter your email"
                                    size="lg"
                                    borderRadius="xl"
                                    bg={cardBg}
                                    border="2px solid"
                                    borderColor="transparent"
                                    _hover={{
                                      borderColor: accentColor,
                                    }}
                                    _focus={{
                                      borderColor: accentColor,
                                      boxShadow: `0 0 0 1px ${accentColor}`,
                                    }}
                                    pl="2.5rem"
                                    {...loginForm.register('email', {
                                      required: 'Email is required',
                                      pattern: {
                                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                                        message: 'Invalid email address'
                                      }
                                    })}
                                  />
                                </InputGroup>
                                <FormErrorMessage fontSize="sm">
                                  {loginForm.formState.errors.email?.message}
                                </FormErrorMessage>
                              </FormControl>

                              <FormControl isInvalid={!!loginForm.formState.errors.password}>
                                <FormLabel fontSize="sm" fontWeight="600" color={textColor}>Password</FormLabel>
                                <InputGroup>
                                  <InputLeftElement pointerEvents="none">
                                    <Icon as={FaLock} color="gray.400" />
                                  </InputLeftElement>
                                  <Input
                                    type={showPassword ? 'text' : 'password'}
                                    placeholder="Enter your password"
                                    size="lg"
                                    borderRadius="xl"
                                    bg={cardBg}
                                    border="2px solid"
                                    borderColor="transparent"
                                    _hover={{
                                      borderColor: accentColor,
                                    }}
                                    _focus={{
                                      borderColor: accentColor,
                                      boxShadow: `0 0 0 1px ${accentColor}`,
                                    }}
                                    pl="2.5rem"
                                    pr="2.5rem"
                                    {...loginForm.register('password', {
                                      required: 'Password is required'
                                    })}
                                  />
                                  <InputRightElement h="full">
                                    <IconButton
                                      variant="ghost"
                                      aria-label={showPassword ? 'Hide password' : 'Show password'}
                                      icon={showPassword ? <FaEyeSlash /> : <FaEye />}
                                      onClick={() => setShowPassword(!showPassword)}
                                      size="sm"
                                      color="gray.500"
                                      _hover={{ color: accentColor }}
                                    />
                                  </InputRightElement>
                                </InputGroup>
                                <FormErrorMessage fontSize="sm">
                                  {loginForm.formState.errors.password?.message}
                                </FormErrorMessage>
                              </FormControl>

                              <Button
                                type="submit"
                                size="lg"
                                width="full"
                                colorScheme="brand"
                                borderRadius="xl"
                                isLoading={isSubmitting}
                                loadingText="Signing in..."
                                minH="48px"
                                fontSize="md"
                                fontWeight="600"
                                _hover={{
                                  transform: 'translateY(-1px)',
                                  boxShadow: 'lg',
                                }}
                                transition="all 0.2s"
                              >
                                Sign In
                              </Button>
                            </VStack>
                          </form>

                          <Box textAlign="center">
                            <Link
                              color={useColorModeValue('blue.600', 'blue.300')}
                              _hover={{
                                color: useColorModeValue('blue.700', 'blue.200'),
                                textDecoration: 'underline'
                              }}
                              fontSize="sm"
                              fontWeight="500"
                              cursor="pointer"
                              onClick={() => {
                                const email = loginForm.getValues('email');
                                if (email) {
                                  handlePasswordReset(email);
                                } else {
                                  toast({
                                    title: 'Enter your email first',
                                    description: 'Please enter your email address to reset your password.',
                                    status: 'info',
                                    duration: 3000,
                                    isClosable: true,
                                    position: 'top-right'
                                  });
                                }
                              }}
                            >
                              Forgot password?
                            </Link>
                          </Box>
                        </VStack>
                      </TabPanel>

                      {/* Sign Up Panel */}
                      <TabPanel p={0}>
                        <VStack spacing={{ base: 4, md: 6 }}>
                          <VStack spacing={2} textAlign="center">
                            <Heading size="md" color={textColor} fontWeight="600">
                              Create your account
                            </Heading>
                            <Text color="gray.500" fontSize="sm">
                              Join thousands of professionals advancing their careers
                            </Text>
                          </VStack>

                          <Collapse in={!!authError} animateOpacity>
                            <Alert status="error" borderRadius="xl" bg="red.50" border="1px solid" borderColor="red.200">
                              <AlertIcon color="red.500" />
                              <AlertDescription fontSize="sm" color="red.700">{authError}</AlertDescription>
                            </Alert>
                          </Collapse>

                          {/* Google Sign-Up - Priority Option */}
                          <VStack spacing={3} w="full">
                            <a href={signInUrl} style={{ width: '100%' }}>
                              <Button
                                leftIcon={<AiOutlineGoogle />}
                                size="lg"
                                width="full"
                                colorScheme="blue"
                                borderRadius="xl"
                                minH="48px"
                                fontSize="md"
                                fontWeight="600"
                                _hover={{
                                  transform: 'translateY(-1px)',
                                  boxShadow: 'lg',
                                }}
                                transition="all 0.2s"
                              >
                                Continue with Google
                              </Button>
                            </a>
                          </VStack>

                          <Box position="relative" w="full">
                            <Box
                              position="absolute"
                              top="50%"
                              left="0"
                              right="0"
                              h="1px"
                              bg="gray.200"
                              _dark={{ bg: 'gray.600' }}
                            />
                            <Text
                              position="relative"
                              bg={cardBg}
                              px={4}
                              color="gray.500"
                              fontSize="sm"
                              textAlign="center"
                              fontWeight="500"
                            >
                              or create account with email
                            </Text>
                          </Box>

                          {/* Email/Password Signup Form */}
                          <form onSubmit={signupForm.handleSubmit(handleSignup)} style={{ width: '100%' }}>
                            <VStack spacing={{ base: 3, md: 4 }} w="full">
                              <FormControl isInvalid={!!signupForm.formState.errors.email}>
                                <FormLabel fontSize="sm" fontWeight="600" color={textColor}>Email</FormLabel>
                                <InputGroup>
                                  <InputLeftElement pointerEvents="none">
                                    <Icon as={FaEnvelope} color="gray.400" />
                                  </InputLeftElement>
                                  <Input
                                    type="email"
                                    placeholder="Enter your email"
                                    size="lg"
                                    borderRadius="xl"
                                    bg={cardBg}
                                    border="2px solid"
                                    borderColor="transparent"
                                    _hover={{
                                      borderColor: accentColor,
                                    }}
                                    _focus={{
                                      borderColor: accentColor,
                                      boxShadow: `0 0 0 1px ${accentColor}`,
                                    }}
                                    pl="2.5rem"
                                    {...signupForm.register('email', {
                                      required: 'Email is required',
                                      pattern: {
                                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                                        message: 'Invalid email address'
                                      }
                                    })}
                                  />
                                </InputGroup>
                                <FormErrorMessage fontSize="sm">
                                  {signupForm.formState.errors.email?.message}
                                </FormErrorMessage>
                              </FormControl>

                              <FormControl isInvalid={!!signupForm.formState.errors.password}>
                                <FormLabel fontSize="sm" fontWeight="600" color={textColor}>Password</FormLabel>
                                <InputGroup>
                                  <InputLeftElement pointerEvents="none">
                                    <Icon as={FaLock} color="gray.400" />
                                  </InputLeftElement>
                                  <Input
                                    type={showPassword ? 'text' : 'password'}
                                    placeholder="Create a password"
                                    size="lg"
                                    borderRadius="xl"
                                    bg={cardBg}
                                    border="2px solid"
                                    borderColor="transparent"
                                    _hover={{
                                      borderColor: accentColor,
                                    }}
                                    _focus={{
                                      borderColor: accentColor,
                                      boxShadow: `0 0 0 1px ${accentColor}`,
                                    }}
                                    pl="2.5rem"
                                    pr="2.5rem"
                                    {...signupForm.register('password', {
                                      required: 'Password is required',
                                      minLength: {
                                        value: 8,
                                        message: 'Password must be at least 8 characters'
                                      },
                                      validate: {
                                        hasNumber: (value) =>
                                          /[0-9]/.test(value) || 'Password must contain at least one number'
                                      }
                                    })}
                                  />
                                  <InputRightElement h="full">
                                    <IconButton
                                      variant="ghost"
                                      aria-label={showPassword ? 'Hide password' : 'Show password'}
                                      icon={showPassword ? <FaEyeSlash /> : <FaEye />}
                                      onClick={() => setShowPassword(!showPassword)}
                                      size="sm"
                                      color="gray.500"
                                      _hover={{ color: accentColor }}
                                    />
                                  </InputRightElement>
                                </InputGroup>
                                <FormErrorMessage fontSize="sm">
                                  {signupForm.formState.errors.password?.message}
                                </FormErrorMessage>
                                
                                {/* Enhanced Password Strength Indicator */}
                                {watchedPassword && (
                                  <Box mt={2}>
                                    <HStack justify="space-between" mb={1}>
                                      <Text fontSize="xs" color="gray.500">Password strength</Text>
                                      <Text fontSize="xs" color={
                                        passwordStrength >= 75 ? 'green.500' :
                                        passwordStrength >= 50 ? 'yellow.500' :
                                        passwordStrength >= 25 ? 'orange.500' : 'red.500'
                                      }>
                                        {passwordStrength >= 75 ? 'Strong' :
                                         passwordStrength >= 50 ? 'Good' :
                                         passwordStrength >= 25 ? 'Weak' : 'Too weak'}
                                      </Text>
                                    </HStack>
                                    <Progress
                                      value={passwordStrength}
                                      size="sm"
                                      borderRadius="full"
                                      colorScheme={
                                        passwordStrength >= 75 ? 'green' :
                                        passwordStrength >= 50 ? 'yellow' :
                                        passwordStrength >= 25 ? 'orange' : 'red'
                                      }
                                    />
                                  </Box>
                                )}
                                
                                {!signupForm.formState.errors.password && !watchedPassword && (
                                  <Text fontSize="xs" color="gray.500" mt={1}>
                                    Must be at least 8 characters with at least one number
                                  </Text>
                                )}
                              </FormControl>

                              <FormControl isInvalid={!!signupForm.formState.errors.confirmPassword}>
                                <FormLabel fontSize="sm" fontWeight="600" color={textColor}>Confirm Password</FormLabel>
                                <InputGroup>
                                  <InputLeftElement pointerEvents="none">
                                    <Icon as={FaLock} color="gray.400" />
                                  </InputLeftElement>
                                  <Input
                                    type={showConfirmPassword ? 'text' : 'password'}
                                    placeholder="Confirm your password"
                                    size="lg"
                                    borderRadius="xl"
                                    bg={cardBg}
                                    border="2px solid"
                                    borderColor="transparent"
                                    _hover={{
                                      borderColor: accentColor,
                                    }}
                                    _focus={{
                                      borderColor: accentColor,
                                      boxShadow: `0 0 0 1px ${accentColor}`,
                                    }}
                                    pl="2.5rem"
                                    pr="2.5rem"
                                    {...signupForm.register('confirmPassword', {
                                      required: 'Please confirm your password',
                                      validate: (value) =>
                                        value === signupForm.watch('password') || 'Passwords do not match'
                                    })}
                                  />
                                  <InputRightElement h="full">
                                    <IconButton
                                      variant="ghost"
                                      aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
                                      icon={showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                      size="sm"
                                      color="gray.500"
                                      _hover={{ color: accentColor }}
                                    />
                                  </InputRightElement>
                                </InputGroup>
                                <FormErrorMessage fontSize="sm">
                                  {signupForm.formState.errors.confirmPassword?.message}
                                </FormErrorMessage>
                              </FormControl>

                              <Button
                                type="submit"
                                size="lg"
                                width="full"
                                colorScheme="brand"
                                borderRadius="xl"
                                isLoading={isSubmitting}
                                loadingText="Creating account..."
                                minH="48px"
                                fontSize="md"
                                fontWeight="600"
                                _hover={{
                                  transform: 'translateY(-1px)',
                                  boxShadow: 'lg',
                                }}
                                transition="all 0.2s"
                              >
                                Create Account
                              </Button>
                            </VStack>
                          </form>
                        </VStack>
                      </TabPanel>
                    </TabPanels>

                    <Text fontSize="xs" color="gray.500" textAlign="center" mt={6}>
                      By continuing, you agree to our{' '}
                      <Link as={RouterLink} to="/tos" color="blue.500" _hover={{ color: 'blue.600', textDecoration: 'underline' }}>
                        Terms of Service
                      </Link>
                      {' '}and{' '}
                      <Link as={RouterLink} to="/privacy" color="blue.500" _hover={{ color: 'blue.600', textDecoration: 'underline' }}>
                        Privacy Policy
                      </Link>
                    </Text>
                  </Tabs>
                </CardBody>
              </MotionCard>
            </VStack>
          </GridItem>

          {/* Right side - Minimalistic Welcome Content */}
          <GridItem>
            <MotionBox
              flex="1"
              maxW={{ base: 'full', lg: '500px' }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <VStack spacing={12} align={{ base: 'center', lg: 'flex-start' }} textAlign={{ base: 'center', lg: 'left' }}>

                {/* Simplified Hero Section */}
                <VStack spacing={6} align={{ base: 'center', lg: 'flex-start' }}>
                  <Heading
                    size={{ base: '2xl', lg: '3xl' }}
                    color={textColor}
                    fontWeight="300"
                    lineHeight="shorter"
                    letterSpacing="-0.02em"
                  >
                    Your Career
                    <br />
                    <Text as="span" fontWeight="600">
                      Accelerated
                    </Text>
                  </Heading>

                  <Text
                    fontSize={{ base: 'md', lg: 'lg' }}
                    color={useColorModeValue("gray.600", "gray.400")}
                    maxW="400px"
                    lineHeight="relaxed"
                    fontWeight="400"
                  >
                    AI-powered tools for resumes, cover letters, and interview preparation.
                  </Text>
                </VStack>

                {/* Simple Feature List */}
                <VStack spacing={4} align={{ base: 'center', lg: 'flex-start' }} w="full" maxW="350px">
                  {[
                    "Generate professional resumes",
                    "Create tailored cover letters",
                    "Practice interview questions"
                  ].map((feature, index) => (
                    <HStack
                      key={feature}
                      spacing={3}
                      w="full"
                      justify={{ base: 'center', lg: 'flex-start' }}
                    >
                      <Box
                        w="6px"
                        h="6px"
                        borderRadius="full"
                        bg={useColorModeValue("gray.400", "gray.500")}
                        flexShrink={0}
                        mt={1}
                      />
                      <Text
                        fontSize="md"
                        color={useColorModeValue("gray.700", "gray.300")}
                        fontWeight="400"
                      >
                        {feature}
                      </Text>
                    </HStack>
                  ))}
                </VStack>

                {/* Minimal Social Proof */}
                <Text
                  fontSize="sm"
                  color={useColorModeValue("gray.500", "gray.400")}
                  fontWeight="400"
                >
                  Trusted by 10,000+ professionals
                </Text>

              </VStack>
            </MotionBox>
          </GridItem>
        </Grid>
      </Container>
    </Box>
  );
}
