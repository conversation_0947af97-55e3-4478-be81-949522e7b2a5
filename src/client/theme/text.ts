const FONT_SCALE_BASE = 1;
const FONT_SCALE_MULTIPLIER = 1.25;

export const fonts = {
  heading: 'Inter, system-ui, sans-serif',
  body: 'Inter, system-ui, sans-serif',
};

export const textStyles = {
  h1: {
    fontSize: ['2xl', '3xl', '4xl'],
    fontWeight: '700',
    lineHeight: '1.2',
    letterSpacing: '-0.02em',
    color: 'text-contrast-xl',
  },
  h2: {
    fontSize: ['xl', '2xl', '3xl'],
    fontWeight: '600',
    lineHeight: '1.25',
    letterSpacing: '-0.01em',
    color: 'text-contrast-xl',
  },
  h3: {
    fontSize: ['lg', 'xl', '2xl'],
    fontWeight: '600',
    lineHeight: '1.3',
    letterSpacing: '-0.005em',
    color: 'text-contrast-lg',
  },
  h4: {
    fontSize: ['md', 'lg', 'xl'],
    fontWeight: '600',
    lineHeight: '1.35',
    letterSpacing: 'normal',
    color: 'text-contrast-lg',
  },
  subtitle1: {
    fontSize: ['sm', 'md', 'lg'],
    fontWeight: '500',
    lineHeight: '1.5',
    letterSpacing: 'normal',
    color: 'text-contrast-md',
  },
  subtitle2: {
    fontSize: ['xs', 'sm', 'md'],
    fontWeight: '500',
    lineHeight: '1.5',
    letterSpacing: 'normal',
    color: 'text-contrast-md',
  },
  body1: {
    fontSize: ['15px', '16px', '17px'],
    fontWeight: '400',
    lineHeight: '1.6',
    letterSpacing: 'normal',
    color: 'text-contrast-lg',
  },
  body2: {
    fontSize: ['14px', '15px', '16px'],
    fontWeight: '400',
    lineHeight: '1.6',
    letterSpacing: 'normal',
    color: 'text-contrast-lg',
  },
  button: {
    fontSize: ['14px', '15px', '16px'],
    fontWeight: '600',
    lineHeight: '1.5',
    letterSpacing: '0.025em',
  },
  caption: {
    fontSize: ['12px', '13px', '14px'],
    fontWeight: '400',
    lineHeight: '1.5',
    letterSpacing: 'normal',
    color: 'text-contrast-sm',
  },
  overline: {
    fontSize: ['11px', '12px'],
    fontWeight: '600',
    lineHeight: '1.5',
    letterSpacing: '0.1em',
    textTransform: 'uppercase',
    color: 'text-contrast-sm',
  },
};
