export const semanticTokens = {
  colors: {
    'bg-body': {
      default: 'gray.50',
      _dark: 'gray.900',
    },
    'bg-body-inverse': {
      default: 'gray.900',
      _dark: 'gray.50',
    },
    'bg-contrast-xs': {
      default: 'white',
      _dark: 'gray.800',
    },
    'bg-contrast-sm': {
      default: 'gray.50',
      _dark: 'gray.700',
    },
    'bg-contrast-md': {
      default: 'gray.100',
      _dark: 'gray.600',
    },
    'bg-contrast-lg': {
      default: 'gray.200',
      _dark: 'gray.500',
    },
    'bg-contrast-xl': {
      default: 'gray.300',
      _dark: 'gray.400',
    },
    'bg-contrast-overlay': {
      default: 'rgba(255, 255, 255, 0.95)',
      _dark: 'rgba(0, 0, 0, 0.95)',
    },
    'bg-overlay': {
      default: 'rgba(255, 255, 255, 0.98)',
      _dark: 'rgba(0, 0, 0, 0.98)',
    },
    'bg-modal': {
      default: 'white',
      _dark: 'gray.800',
    },
    'text-contrast-xs': {
      default: 'gray.500',
      _dark: 'gray.400',
    },
    'text-contrast-sm': {
      default: 'gray.600',
      _dark: 'gray.300',
    },
    'text-contrast-md': {
      default: 'gray.700',
      _dark: 'gray.200',
    },
    'text-contrast-lg': {
      default: 'gray.800',
      _dark: 'gray.100',
    },
    'text-contrast-xl': {
      default: 'gray.900',
      _dark: 'white',
    },
    'border-contrast-xs': {
      default: 'gray.200',
      _dark: 'gray.700',
    },
    'border-contrast-sm': {
      default: 'gray.300',
      _dark: 'gray.600',
    },
    'border-contrast-md': {
      default: 'gray.400',
      _dark: 'gray.500',
    },
    'border-contrast-lg': {
      default: 'gray.500',
      _dark: 'gray.400',
    },
    'border-contrast-xl': {
      default: 'gray.600',
      _dark: 'gray.300',
    },
    active: {
      default: 'blue.500',
      _dark: 'blue.300',
    },
    primary: {
      default: 'blue.500',
      _dark: 'blue.300',
    },
    secondary: {
      default: 'cyan.600',
      _dark: 'cyan.400',
    },
    accent: {
      default: 'cyan.500',
      _dark: 'cyan.300',
    },
    success: {
      default: 'green.500',
      _dark: 'green.300',
    },
    warning: {
      default: 'orange.500',
      _dark: 'orange.300',
    },
    error: {
      default: 'red.500',
      _dark: 'red.300',
    },
    'gradient-primary': {
      default: 'linear(to-r, blue.500, blue.600)',
      _dark: 'linear(to-r, blue.400, blue.500)',
    },
    'gradient-secondary': {
      default: 'linear(to-r, cyan.500, blue.500)',
      _dark: 'linear(to-r, cyan.400, blue.400)',
    },
    'gradient-accent': {
      default: 'linear(to-r, blue.400, cyan.500)',
      _dark: 'linear(to-r, blue.300, cyan.400)',
    },
    brand: {
      primary: 'blue.500',
      secondary: 'cyan.600',
      accent: 'blue.400',
    },
  },
  borders: {
    sm: `1px solid var(--chakra-colors-border-contrast-xs)`,
    md: `2px solid var(--chakra-colors-border-contrast-xs)`,
    lg: `3px solid var(--chakra-colors-border-contrast-xs)`,
    error: `1px solid var(--chakra-colors-error)`,
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    'hover': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    'active': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  },
  transitions: {
    'default': 'all 0.2s ease-in-out',
    'smooth': 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    'bounce': 'all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },
  animations: {
    'fade-in': 'fadeIn 0.3s ease-in-out',
    'slide-up': 'slideUp 0.3s ease-in-out',
    'slide-down': 'slideDown 0.3s ease-in-out',
    'scale': 'scale 0.2s ease-in-out',
  },
  gradients: {
    primary: 'linear(to-br, blue.500, cyan.600)',
    secondary: 'linear(to-r, blue.400, cyan.500)',
    accent: 'linear(to-r, cyan.400, blue.500)',
  },
};

if (typeof window !== 'undefined') {
  const updateViewportUnits = () => {
    let vh = window.innerHeight * 0.01;
    let vw = window.innerWidth * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
    document.documentElement.style.setProperty('--vw', `${vw}px`);
  };
  updateViewportUnits();
  window.addEventListener('resize', updateViewportUnits);
}
