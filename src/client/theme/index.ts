import { extendTheme } from '@chakra-ui/react';
import { textStyles, fonts } from './text';
import { semanticTokens } from './tokens';
import {
  Button as <PERSON><PERSON><PERSON>utton,
} from '@chakra-ui/react';

// Modern color palette with better contrast and accessibility
const colors = {
  brand: {
    50: '#E6F3FF',
    100: '#B8DCFF',
    200: '#8AC5FF',
    300: '#5CAEFF',
    400: '#2E97FF',
    500: '#0080FF', // Primary brand color
    600: '#0066CC',
    700: '#004C99',
    800: '#003366',
    900: '#001933'
  },
  gray: {
    50: '#F7FAFC',
    100: '#EDF2F7',
    200: '#E2E8F0',
    300: '#CBD5E0',
    400: '#A0AEC0',
    500: '#718096',
    600: '#4A5568',
    700: '#2D3748',
    800: '#1A202C',
    900: '#171923'
  },
  success: {
    50: '#F0FFF4',
    100: '#C6F6D5',
    200: '#9AE6B4',
    300: '#68D391',
    400: '#48BB78',
    500: '#38A169',
    600: '#2F855A',
    700: '#276749',
    800: '#22543D',
    900: '#1C4532'
  },
  error: {
    50: '#FED7D7',
    100: '#FEB2B2',
    200: '#FC8181',
    300: '#F56565',
    400: '#E53E3E',
    500: '#C53030',
    600: '#9B2C2C',
    700: '#742A2A',
    800: '#63171B',
    900: '#521B1F'
  },
  warning: {
    50: '#FFFBEB',
    100: '#FEF3C7',
    200: '#FDE68A',
    300: '#FCD34D',
    400: '#FBBF24',
    500: '#F59E0B',
    600: '#D97706',
    700: '#B45309',
    800: '#92400E',
    900: '#78350F'
  }
};

const variantSolid = (props: any) => {
  const { colorScheme: c } = props;

  let bg = `${c}.500`;
  let color = 'white';
  let hoverBg = `${c}.600`;
  let activeBg = `${c}.700`;
  let disabledBg = `${c}.300`;

  if (c === 'contrast') {
    bg = 'primary';
    color = 'white';
    hoverBg = 'primary';
    activeBg = 'primary';
    disabledBg = 'gray.300';
  }

  return {
    border: 'none',
    bgColor: bg,
    color: color,
    _hover: {
      bg: hoverBg,
      transform: 'translateY(-1px)',
      boxShadow: 'md',
    },
    _focus: {
      boxShadow: 'none',
      borderColor: 'active',
    },
    _disabled: {
      bg: disabledBg,
      pointerEvents: 'none',
      opacity: 0.66,
    },
    _active: {
      bg: activeBg,
      transform: 'translateY(0)',
    },
  };
};

// Enhanced Button component with better states and animations
export const Button = {
  baseStyle: {
    fontFamily: 'Inter, system-ui, sans-serif',
    fontWeight: '600',
    borderRadius: 'lg',
    transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
    letterSpacing: '0.025em',
    cursor: 'pointer',
    _focus: {
      boxShadow: '0 0 0 3px rgba(0, 128, 255, 0.15)',
      outline: 'none',
    },
    _disabled: {
      opacity: 0.6,
      cursor: 'not-allowed',
      transform: 'none',
    },
  },
  sizes: {
    xs: {
      h: '8',
      minW: '8',
      fontSize: '12px',
      px: '3',
      py: '1.5',
    },
    sm: {
      h: '10',
      minW: '10',
      fontSize: '14px',
      px: '4',
      py: '2',
    },
    md: {
      h: '12',
      minW: '12',
      fontSize: '16px',
      px: '6',
      py: '3',
    },
    lg: {
      h: '14',
      minW: '14',
      fontSize: '18px',
      px: '8',
      py: '4',
    },
    xl: {
      h: '16',
      minW: '16',
      fontSize: '20px',
      px: '10',
      py: '5',
    },
  },
  variants: {
    solid: {
      bg: 'brand.500',
      color: 'white',
      _hover: {
        bg: 'brand.600',
        transform: 'translateY(-1px)',
        boxShadow: '0 10px 25px -5px rgba(0, 128, 255, 0.25)',
      },
      _active: {
        bg: 'brand.700',
        transform: 'translateY(0)',
      },
    },
    outline: {
      border: '2px solid',
      borderColor: 'brand.500',
      bg: 'transparent',
      color: 'brand.500',
      _hover: {
        bg: 'brand.50',
        transform: 'translateY(-1px)',
        boxShadow: '0 4px 12px rgba(0, 128, 255, 0.15)',
      },
      _active: {
        bg: 'brand.100',
        transform: 'translateY(0)',
      },
    },
    ghost: {
      bg: 'transparent',
      color: 'brand.500',
      _hover: {
        bg: 'brand.50',
        transform: 'translateY(-1px)',
      },
      _active: {
        bg: 'brand.100',
        transform: 'translateY(0)',
      },
    },
    link: {
      padding: 0,
      height: 'auto',
      lineHeight: 'normal',
      verticalAlign: 'baseline',
      color: 'brand.500',
      _hover: {
        textDecoration: 'underline',
        color: 'brand.600',
      },
    },
  },
};

ChakraButton.defaultProps = {
  ...ChakraButton.defaultProps,
  fontSize: 'md',
  variant: 'solid',
  size: 'md',
};

// Enhanced Input component with better focus states
export const Input = {
  baseStyle: {
    field: {
      fontFamily: 'Inter, system-ui, sans-serif',
      transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
    },
  },
  sizes: {
    sm: {
      field: {
        borderRadius: 'md',
        fontSize: '14px',
        h: '10',
        px: '3',
      },
    },
    md: {
      field: {
        borderRadius: 'md',
        fontSize: '16px',
        h: '12',
        px: '4',
      },
    },
    lg: {
      field: {
        borderRadius: 'md',
        fontSize: '18px',
        h: '14',
        px: '4',
      },
    },
  },
  variants: {
    outline: {
      field: {
        border: '2px solid',
        borderColor: 'gray.200',
        bg: 'white',
        color: 'gray.900',
        _dark: {
          borderColor: 'gray.600',
          bg: 'gray.800',
          color: 'white',
        },
        _hover: {
          borderColor: 'gray.300',
          _dark: {
            borderColor: 'gray.500',
          },
        },
        _focus: {
          borderColor: 'brand.500',
          boxShadow: '0 0 0 1px rgba(0, 128, 255, 0.15)',
          bg: 'white',
          _dark: {
            bg: 'gray.800',
            borderColor: 'brand.400',
          },
        },
        _invalid: {
          borderColor: 'error.500',
          boxShadow: '0 0 0 1px rgba(197, 48, 48, 0.15)',
        },
        _disabled: {
          bg: 'gray.50',
          borderColor: 'gray.200',
          opacity: 0.6,
          cursor: 'not-allowed',
          _dark: {
            bg: 'gray.700',
            borderColor: 'gray.600',
          },
        },
        _placeholder: {
          color: 'gray.500',
          _dark: {
            color: 'gray.400',
          },
        },
      },
    },
  },
};

export const Textarea = {
  baseStyle: {
    fontFamily: 'Inter, system-ui, sans-serif',
    transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
    resize: 'vertical',
  },
  sizes: {
    sm: {
      borderRadius: 'xl',
      fontSize: '14px',
      px: '3',
      py: '2',
    },
    md: {
      borderRadius: 'xl',
      fontSize: '15px',
      px: '4',
      py: '3',
    },
    lg: {
      borderRadius: 'xl',
      fontSize: '16px',
      px: '4',
      py: '3',
    },
  },
  variants: {
    outline: {
      border: '1.5px solid',
      borderColor: 'gray.200',
      bg: 'white',
      color: 'gray.900',
      _dark: {
        borderColor: 'gray.600',
        bg: 'gray.800',
        color: 'white',
      },
      _hover: {
        borderColor: 'gray.300',
        _dark: {
          borderColor: 'gray.500',
        },
      },
      _focus: {
        borderColor: 'blue.500',
        boxShadow: '0 0 0 3px rgba(66, 153, 225, 0.1)',
        bg: 'white',
        _dark: {
          bg: 'gray.800',
          borderColor: 'blue.400',
          boxShadow: '0 0 0 3px rgba(66, 153, 225, 0.15)',
        },
      },
      _invalid: {
        borderColor: 'red.500',
        boxShadow: '0 0 0 3px rgba(245, 101, 101, 0.1)',
      },
      _disabled: {
        bg: 'gray.50',
        borderColor: 'gray.200',
        opacity: 0.6,
        cursor: 'not-allowed',
        _dark: {
          bg: 'gray.700',
          borderColor: 'gray.600',
        },
      },
      _placeholder: {
        color: 'gray.500',
        _dark: {
          color: 'gray.400',
        },
      },
    },
    filled: {
      border: '1.5px solid transparent',
      bg: 'gray.50',
      color: 'gray.900',
      _dark: {
        bg: 'gray.700',
        color: 'white',
      },
      _hover: {
        bg: 'gray.100',
        _dark: {
          bg: 'gray.600',
        },
      },
      _focus: {
        bg: 'white',
        borderColor: 'blue.500',
        boxShadow: '0 0 0 3px rgba(66, 153, 225, 0.1)',
        _dark: {
          bg: 'gray.800',
          borderColor: 'blue.400',
          boxShadow: '0 0 0 3px rgba(66, 153, 225, 0.15)',
        },
      },
    },
  },
};

export const Select = {
  variants: {
    outline: {
      field: {
        border: '1px solid',
        borderColor: 'border-contrast-sm',
        bg: 'bg-contrast-xs',
        color: 'text-contrast-lg',
        borderRadius: 'md',
        fontSize: 'sm',
        height: '40px',
        transition: 'all 0.2s ease-in-out',
        _hover: {
          borderColor: 'border-contrast-md',
        },
        _focus: {
          borderColor: 'primary',
          boxShadow: '0 0 0 1px var(--chakra-colors-primary)',
        },
        _disabled: {
          bg: 'bg-contrast-xs',
          opacity: 0.7,
        },
      },
    },
  },
};

export const Checkbox = {
  baseStyle: {
    control: {
      border: '1px solid',
      borderColor: 'border-contrast-md',
      bg: 'bg-contrast-xs',
      borderRadius: 'sm',
      transition: 'all 0.2s ease-in-out',
      _hover: {
        borderColor: 'primary',
      },
      _focus: {
        boxShadow: '0 0 0 1px var(--chakra-colors-primary)',
        borderColor: 'primary',
      },
      _disabled: {
        bg: 'bg-contrast-xs',
        opacity: 0.7,
      },
      _checked: {
        bg: 'primary',
        borderColor: 'primary',
      }
    },
    label: {
      fontSize: 'sm',
      fontWeight: 'normal',
    }
  },
};

export const Radio = {
  baseStyle: {
    control: {
      border: '1px solid',
      borderColor: 'border-contrast-md',
      bg: 'bg-contrast-xs',
      transition: 'all 0.2s ease-in-out',
      _hover: {
        borderColor: 'primary',
      },
      _focus: {
        boxShadow: '0 0 0 1px var(--chakra-colors-primary)',
        borderColor: 'primary',
      },
      _disabled: {
        bg: 'bg-contrast-xs',
        opacity: 0.7,
      },
      _checked: {
        bg: 'primary',
        borderColor: 'primary',
      }
    },
    label: {
      fontSize: 'sm',
      fontWeight: 'normal',
    }
  },
};

export const Link = {
  baseStyle: {
    color: 'primary',
    fontWeight: 'medium',
    transition: 'all 0.2s ease-in-out',
    _hover: {
      textDecoration: 'none',
      color: 'blue.600',
      transform: 'translateY(-1px)',
    },
    _focus: {
      boxShadow: '0 0 0 1px var(--chakra-colors-primary)',
      outline: 'none',
    },
    _active: {
      opacity: 0.8,
      transform: 'translateY(0)',
    },
  },
};

// Enhanced Card component with better shadows and hover states
export const Card = {
  baseStyle: {
    container: {
      borderRadius: 'xl',
      bg: 'white',
      _dark: { bg: 'gray.800' },
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
      transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
      _hover: {
        boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        transform: 'translateY(-2px)',
      },
    },
    header: {
      p: 6,
      pb: 0,
    },
    body: {
      p: 6,
      pt: 4,
    },
    footer: {
      p: 6,
      pt: 0,
    },
  },
  variants: {
    elevated: {
      container: {
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        _hover: {
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
          transform: 'translateY(-4px)',
        },
      },
    },
  },
};

// Global styles
const styles = {
  global: {
    '*': {
      borderColor: 'gray.200',
      _dark: {
        borderColor: 'gray.600',
      },
    },
    '*::placeholder': {
      opacity: 1,
      color: 'gray.500',
      _dark: {
        color: 'gray.400',
      },
    },
    html: {
      scrollBehavior: 'smooth',
    },
    body: {
      fontFamily: 'Inter, system-ui, sans-serif',
      color: 'gray.900',
      bg: 'gray.50',
      _dark: {
        color: 'white',
        bg: 'gray.900',
      },
      lineHeight: 1.6,
    },
    'h1, h2, h3, h4, h5, h6': {
      fontWeight: '700',
      lineHeight: 1.2,
    },
  },
};

// Typography scale
const fontSizes = {
  xs: '0.75rem',
  sm: '0.875rem',
  md: '1rem',
  lg: '1.125rem',
  xl: '1.25rem',
  '2xl': '1.5rem',
  '3xl': '1.875rem',
  '4xl': '2.25rem',
  '5xl': '3rem',
  '6xl': '3.75rem',
  '7xl': '4.5rem',
  '8xl': '6rem',
  '9xl': '8rem',
};

// Component overrides
const components = {
  Button,
  Input,
  Card,
  // Badge component with modern styling
  Badge: {
    baseStyle: {
      px: 3,
      py: 1,
      borderRadius: 'full',
      fontSize: 'sm',
      fontWeight: '600',
      textTransform: 'none',
    },
    variants: {
      solid: {
        bg: 'brand.500',
        color: 'white',
      },
      subtle: {
        bg: 'brand.50',
        color: 'brand.700',
        _dark: {
          bg: 'brand.200',
          color: 'brand.800',
        },
      },
    },
  },
  // Textarea component
  Textarea: {
    baseStyle: {
      ...Input.baseStyle,
    },
    sizes: {
      sm: {
        borderRadius: 'md',
        fontSize: '14px',
        px: '3',
        py: '2',
      },
      md: {
        borderRadius: 'md',
        fontSize: '16px',
        px: '4',
        py: '2',
      },
      lg: {
        borderRadius: 'md',
        fontSize: '18px',
        px: '4',
        py: '3',
      },
    },
    variants: {
      outline: Input.variants.outline,
    },
  },
};

// Space scale
const space = {
  px: '1px',
  0.5: '0.125rem',
  1: '0.25rem',
  1.5: '0.375rem',
  2: '0.5rem',
  2.5: '0.625rem',
  3: '0.75rem',
  3.5: '0.875rem',
  4: '1rem',
  5: '1.25rem',
  6: '1.5rem',
  7: '1.75rem',
  8: '2rem',
  9: '2.25rem',
  10: '2.5rem',
  12: '3rem',
  14: '3.5rem',
  16: '4rem',
  20: '5rem',
  24: '6rem',
  28: '7rem',
  32: '8rem',
  36: '9rem',
  40: '10rem',
  44: '11rem',
  48: '12rem',
  52: '13rem',
  56: '14rem',
  60: '15rem',
  64: '16rem',
  72: '18rem',
  80: '20rem',
  96: '24rem',
};

// Create the theme
export const theme = extendTheme({
  colors,
  components,
  styles,
  fontSizes,
  space,
  fonts: {
    heading: 'Inter, system-ui, sans-serif',
    body: 'Inter, system-ui, sans-serif',
    mono: 'SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
  },
  textStyles,
  semanticTokens,
  config: {
    initialColorMode: 'light',
    useSystemColorMode: false,
  },
  // Enhanced border radius scale
  radii: {
    none: '0',
    sm: '0.125rem',
    base: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    '3xl': '1.5rem',
    full: '9999px',
  },
  // Enhanced shadows with better depth perception
  shadows: {
    xs: '0 0 0 1px rgba(0, 0, 0, 0.05)',
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    outline: '0 0 0 3px rgba(66, 153, 225, 0.6)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
    none: 'none',
    'dark-lg': 'rgba(0, 0, 0, 0.1) 0px 0px 0px 1px, rgba(0, 0, 0, 0.2) 0px 5px 10px, rgba(0, 0, 0, 0.4) 0px 15px 40px',
  },
  // Enhanced breakpoints for responsive design
  breakpoints: {
    base: '0em',
    sm: '30em',    // 480px
    md: '48em',    // 768px
    lg: '62em',    // 992px
    xl: '80em',    // 1280px
    '2xl': '96em', // 1536px
  },
});
