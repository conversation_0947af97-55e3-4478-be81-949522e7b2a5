import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Box } from '@chakra-ui/react';
import { useNavigate } from 'react-router-dom';
import { FaHome, FaArrowLeft } from 'react-icons/fa';
import { Error404 } from './components/ErrorState';

export default function NotFoundPage() {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/');
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <Box minH="100vh" display="flex" alignItems="center" justifyContent="center" py={12}>
      <Error404
        onGoHome={handleGoHome}
        onRetry={handleGoBack}
        size="lg"
      />
    </Box>
  );
}
