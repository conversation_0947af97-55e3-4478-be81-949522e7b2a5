import { ReactNode, useEffect, useState } from 'react';
import { Box, Flex, useColorModeValue } from '@chakra-ui/react';
import { useAuth } from 'wasp/client/auth';
import NavBar from './components/NavBar';
import OnboardingFlow from './components/OnboardingFlow';

export default function Layout({ children }: { children: ReactNode }) {
  const bgColor = useColorModeValue('gray.50', 'gray.900');
  const { data: user, isLoading } = useAuth();
  const [showOnboarding, setShowOnboarding] = useState(false);

  useEffect(() => {
    // Show onboarding for authenticated users who haven't completed it
    if (user && !isLoading) {
      // Check if user has completed onboarding (you might want to store this in user profile)
      const hasCompletedOnboarding = localStorage.getItem(`onboarding_completed_${user.id.toString()}`);
      if (!hasCompletedOnboarding) {
        setShowOnboarding(true);
      }
    }
  }, [user, isLoading]);

  const handleOnboardingComplete = (preferences: any) => {
    if (user) {
      // Save onboarding completion status
      localStorage.setItem(`onboarding_completed_${user.id.toString()}`, 'true');
      localStorage.setItem(`user_preferences_${user.id.toString()}`, JSON.stringify(preferences));
      console.log('User preferences saved:', preferences);
    }
    setShowOnboarding(false);
  };

  return (
    <Flex direction="column" minH="100vh" bg={bgColor}>
      <NavBar />
      <Box flex="1" pt={4}>
        {children}
      </Box>
      
      {/* Onboarding Modal */}
      <OnboardingFlow
        isOpen={showOnboarding}
        onClose={() => setShowOnboarding(false)}
        onComplete={handleOnboardingComplete}
      />
    </Flex>
  );
} 