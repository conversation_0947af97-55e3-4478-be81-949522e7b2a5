import { ResumeTemplate } from '../../shared/types';

export const resumeTemplates: ResumeTemplate[] = [
  {
    id: 'professional-classic',
    name: 'Professional Classic',
    description: 'A clean, traditional resume template perfect for corporate environments and established industries.',
    category: 'professional',
    preview: '/images/illustrations/resume_classic.svg',
    sampleData: {
      personalInfo: {
        fullName: '<PERSON>',
        email: '<EMAIL>',
        phone: '(*************',
        location: 'New York, NY',
        linkedIn: 'linkedin.com/in/sarah<PERSON><PERSON>son',
        website: 'sarahjohnson.dev'
      },
      summary: 'Experienced marketing professional with 8+ years of expertise in digital marketing, brand management, and strategic planning. Proven track record of increasing brand awareness by 150% and driving revenue growth through innovative marketing campaigns.',
      experience: [
        {
          id: '1',
          company: 'TechCorp Solutions',
          position: 'Senior Marketing Manager',
          startDate: '2020-03',
          endDate: '2024-01',
          current: false,
          description: 'Led comprehensive marketing strategies for B2B software products, managing a team of 5 marketing specialists.',
          achievements: [
            'Increased lead generation by 200% through targeted digital campaigns',
            'Launched 3 successful product campaigns resulting in $2M revenue growth',
            'Implemented marketing automation system reducing manual work by 60%'
          ]
        },
        {
          id: '2',
          company: 'Digital Innovations Inc.',
          position: 'Marketing Specialist',
          startDate: '2018-06',
          endDate: '2020-02',
          current: false,
          description: 'Developed and executed digital marketing campaigns across multiple channels including social media, email, and content marketing.',
          achievements: [
            'Grew social media following by 300% across all platforms',
            'Created content strategy that increased website traffic by 150%',
            'Managed email marketing campaigns with 25% open rate improvement'
          ]
        }
      ],
      education: [
        {
          id: '1',
          institution: 'University of California, Berkeley',
          degree: 'Bachelor of Science',
          field: 'Marketing',
          startDate: '2014-09',
          endDate: '2018-05',
          current: false,
          gpa: '3.8'
        }
      ],
      skills: [
        'Digital Marketing',
        'Google Analytics',
        'SEO/SEM',
        'Content Strategy',
        'Social Media Marketing',
        'Email Marketing',
        'Marketing Automation',
        'Project Management',
        'Team Leadership',
        'Data Analysis'
      ],
      certifications: [
        {
          id: '1',
          name: 'Google Analytics Certified',
          issuer: 'Google',
          date: '2023-08'
        },
        {
          id: '2',
          name: 'HubSpot Content Marketing Certification',
          issuer: 'HubSpot',
          date: '2023-06'
        }
      ]
    },
    styles: {
      // Colors
      primaryColor: '#2563eb',
      secondaryColor: '#64748b',
      textColor: '#1e293b',
      backgroundColor: '#ffffff',

      // Typography
      fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
      headerFontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
      fontSize: '11px',
      lineHeight: '1.5',
      fontWeight: '400',
      headerFontWeight: '600',

      // Layout
      layout: 'single-column',
      spacing: '16px',
      sectionSpacing: '24px',
      padding: '32px',

      // Visual Elements
      borderStyle: 'solid',
      borderWidth: '1px',
      borderRadius: '0px',

      // Section Styling
      headerStyle: 'underline',
      skillsStyle: 'tags',
      dateStyle: 'right'
    }
  },
  {
    id: 'modern-creative',
    name: 'Modern Creative',
    description: 'A contemporary design with creative elements, ideal for design, marketing, and creative industries.',
    category: 'creative',
    preview: '/images/illustrations/ai.svg',
    sampleData: {
      personalInfo: {
        fullName: 'Alex Chen',
        email: '<EMAIL>',
        phone: '(*************',
        location: 'San Francisco, CA',
        linkedIn: 'linkedin.com/in/alexchen',
        website: 'alexchen.design'
      },
      summary: 'Creative UX/UI Designer with 6+ years of experience crafting user-centered digital experiences. Passionate about creating intuitive interfaces that solve real user problems while maintaining aesthetic excellence.',
      experience: [
        {
          id: '1',
          company: 'Design Studio Pro',
          position: 'Senior UX/UI Designer',
          startDate: '2021-01',
          endDate: '2024-01',
          current: false,
          description: 'Lead designer for mobile and web applications, collaborating with cross-functional teams to deliver exceptional user experiences.',
          achievements: [
            'Redesigned mobile app resulting in 40% increase in user engagement',
            'Created design system adopted across 5 product teams',
            'Led user research initiatives improving conversion rates by 25%'
          ]
        }
      ],
      education: [
        {
          id: '1',
          institution: 'Art Center College of Design',
          degree: 'Bachelor of Fine Arts',
          field: 'Graphic Design',
          startDate: '2015-09',
          endDate: '2019-05',
          current: false
        }
      ],
      skills: [
        'UI/UX Design',
        'Figma',
        'Adobe Creative Suite',
        'Prototyping',
        'User Research',
        'Design Systems',
        'HTML/CSS',
        'JavaScript',
        'Wireframing',
        'Visual Design'
      ]
    },
    styles: {
      primaryColor: '#7c3aed',
      fontFamily: 'Poppins',
      layout: 'two-column'
    }
  },
  {
    id: 'tech-modern',
    name: 'Tech Modern',
    description: 'A sleek, minimalist template designed for technology professionals and software developers.',
    category: 'modern',
    preview: '/images/illustrations/career.svg',
    sampleData: {
      personalInfo: {
        fullName: 'Michael Rodriguez',
        email: '<EMAIL>',
        phone: '(*************',
        location: 'Austin, TX',
        linkedIn: 'linkedin.com/in/michaelrodriguez',
        website: 'github.com/mrodriguez'
      },
      summary: 'Full-stack software engineer with 7+ years of experience building scalable web applications. Expertise in React, Node.js, and cloud technologies with a passion for clean code and innovative solutions.',
      experience: [
        {
          id: '1',
          company: 'TechStart Inc.',
          position: 'Senior Software Engineer',
          startDate: '2020-08',
          endDate: '2024-01',
          current: false,
          description: 'Developed and maintained full-stack applications serving 100K+ users, leading technical architecture decisions.',
          achievements: [
            'Built microservices architecture reducing system downtime by 90%',
            'Mentored 4 junior developers improving team productivity by 30%',
            'Implemented CI/CD pipeline reducing deployment time by 75%'
          ]
        }
      ],
      education: [
        {
          id: '1',
          institution: 'University of Texas at Austin',
          degree: 'Bachelor of Science',
          field: 'Computer Science',
          startDate: '2013-09',
          endDate: '2017-05',
          current: false,
          gpa: '3.7'
        }
      ],
      skills: [
        'JavaScript',
        'React',
        'Node.js',
        'Python',
        'AWS',
        'Docker',
        'PostgreSQL',
        'MongoDB',
        'Git',
        'Agile/Scrum'
      ]
    },
    styles: {
      // Colors
      primaryColor: '#059669',
      secondaryColor: '#10b981',
      accentColor: '#34d399',
      textColor: '#111827',
      backgroundColor: '#f9fafb',

      // Typography
      fontFamily: 'JetBrains Mono, "SF Mono", Monaco, Inconsolata, "Roboto Mono", monospace',
      headerFontFamily: 'JetBrains Mono, "SF Mono", Monaco, monospace',
      fontSize: '10px',
      lineHeight: '1.6',
      fontWeight: '400',
      headerFontWeight: '700',

      // Layout
      layout: 'sidebar',
      spacing: '12px',
      sectionSpacing: '20px',
      padding: '24px',

      // Visual Elements
      borderStyle: 'solid',
      borderWidth: '2px',
      borderRadius: '8px',
      shadowStyle: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',

      // Section Styling
      headerStyle: 'background',
      skillsStyle: 'grid',
      dateStyle: 'separate-line'
    }
  },
  {
    id: 'executive-classic',
    name: 'Executive Classic',
    description: 'A sophisticated template designed for senior executives and leadership positions.',
    category: 'classic',
    preview: '/images/illustrations/success.svg',
    sampleData: {
      personalInfo: {
        fullName: 'Jennifer Williams',
        email: '<EMAIL>',
        phone: '(*************',
        location: 'Chicago, IL',
        linkedIn: 'linkedin.com/in/jenniferwilliams',
        website: 'jenniferwilliams.com'
      },
      summary: 'Accomplished executive leader with 15+ years of experience driving organizational growth and strategic transformation. Proven track record of leading cross-functional teams and delivering exceptional business results.',
      experience: [
        {
          id: '1',
          company: 'Fortune 500 Corp',
          position: 'Chief Operating Officer',
          startDate: '2019-01',
          endDate: '2024-01',
          current: false,
          description: 'Led operational excellence initiatives across multiple business units, overseeing 500+ employees and $100M+ budget.',
          achievements: [
            'Increased operational efficiency by 35% through process optimization',
            'Led successful digital transformation initiative saving $5M annually',
            'Expanded market presence to 3 new regions, increasing revenue by 40%'
          ]
        }
      ],
      education: [
        {
          id: '1',
          institution: 'Harvard Business School',
          degree: 'Master of Business Administration',
          field: 'Strategic Management',
          startDate: '2005-09',
          endDate: '2007-05',
          current: false
        }
      ],
      skills: [
        'Strategic Planning',
        'Executive Leadership',
        'Change Management',
        'Business Development',
        'Financial Analysis',
        'Team Building',
        'Operations Management',
        'Digital Transformation'
      ]
    },
    styles: {
      primaryColor: '#1f2937',
      fontFamily: 'Times New Roman',
      layout: 'single-column'
    }
  },
  {
    id: 'startup-modern',
    name: 'Startup Modern',
    description: 'A dynamic template perfect for startup environments and innovative companies.',
    category: 'modern',
    preview: '/images/illustrations/job-hunt.svg',
    sampleData: {
      personalInfo: {
        fullName: 'David Kim',
        email: '<EMAIL>',
        phone: '(*************',
        location: 'Seattle, WA',
        linkedIn: 'linkedin.com/in/davidkim',
        website: 'github.com/davidkim'
      },
      summary: 'Innovative product manager with 5+ years of experience in fast-paced startup environments. Expert in agile methodologies, user experience design, and data-driven product development.',
      experience: [
        {
          id: '1',
          company: 'TechStartup Inc.',
          position: 'Senior Product Manager',
          startDate: '2021-03',
          endDate: '2024-01',
          current: false,
          description: 'Led product strategy and development for B2B SaaS platform serving 10K+ users.',
          achievements: [
            'Launched 3 major product features increasing user engagement by 60%',
            'Reduced customer churn by 25% through data-driven product improvements',
            'Managed cross-functional team of 8 engineers and designers'
          ]
        }
      ],
      education: [
        {
          id: '1',
          institution: 'Stanford University',
          degree: 'Bachelor of Science',
          field: 'Computer Science',
          startDate: '2015-09',
          endDate: '2019-05',
          current: false,
          gpa: '3.9'
        }
      ],
      skills: [
        'Product Management',
        'Agile/Scrum',
        'User Research',
        'Data Analysis',
        'A/B Testing',
        'Wireframing',
        'SQL',
        'Python',
        'Figma',
        'Jira'
      ]
    },
    styles: {
      primaryColor: '#f59e0b',
      fontFamily: 'Roboto',
      layout: 'two-column'
    }
  },

  // PROFESSIONAL TEMPLATES
  {
    id: 'professional-executive',
    name: 'Executive Professional',
    description: 'Sophisticated template for senior executives and C-level positions with emphasis on leadership achievements.',
    category: 'professional',
    preview: '/images/illustrations/resume_executive.svg',
    sampleData: {
      personalInfo: {
        fullName: 'Michael Chen',
        email: '<EMAIL>',
        phone: '(*************',
        location: 'San Francisco, CA',
        linkedIn: 'linkedin.com/in/michaelchen',
        website: 'michaelchen.com'
      },
      summary: 'Visionary technology executive with 15+ years of experience leading digital transformation initiatives and scaling high-performance teams. Proven track record of driving revenue growth from $50M to $500M+ through strategic innovation and operational excellence.',
      experience: [
        {
          id: '1',
          company: 'TechCorp Global',
          position: 'Chief Technology Officer',
          startDate: '2020-01',
          endDate: '2024-01',
          current: false,
          description: 'Led technology strategy and digital transformation for Fortune 500 company with 10,000+ employees.',
          achievements: [
            'Increased system efficiency by 300% through cloud migration and microservices architecture',
            'Built and managed engineering teams of 200+ across 5 global offices',
            'Reduced operational costs by $25M annually through automation and process optimization'
          ]
        }
      ],
      education: [
        {
          id: '1',
          institution: 'Harvard Business School',
          degree: 'Master of Business Administration',
          field: 'Technology Management',
          startDate: '2005-09',
          endDate: '2007-05',
          current: false,
          gpa: '3.8'
        }
      ],
      skills: [
        'Strategic Leadership',
        'Digital Transformation',
        'Team Building',
        'P&L Management',
        'Cloud Architecture',
        'Agile/DevOps',
        'Stakeholder Management',
        'Innovation Strategy'
      ]
    },
    styles: {
      primaryColor: '#1e40af',
      fontFamily: 'Georgia',
      layout: 'single-column'
    }
  },

  {
    id: 'professional-consultant',
    name: 'Management Consultant',
    description: 'Analytical template designed for consultants, analysts, and strategic roles with focus on problem-solving.',
    category: 'professional',
    preview: '/images/illustrations/resume_consultant.svg',
    sampleData: {
      personalInfo: {
        fullName: 'Alexandra Rodriguez',
        email: '<EMAIL>',
        phone: '(*************',
        location: 'Chicago, IL',
        linkedIn: 'linkedin.com/in/alexandrarodriguez',
        website: 'alexrodriguez.consulting'
      },
      summary: 'Results-driven management consultant with expertise in operational strategy, process optimization, and change management. Successfully delivered $100M+ in client value through data-driven insights and strategic recommendations.',
      experience: [
        {
          id: '1',
          company: 'McKinsey & Company',
          position: 'Senior Associate',
          startDate: '2019-06',
          endDate: '2024-01',
          current: false,
          description: 'Led cross-functional teams to solve complex business challenges for Fortune 100 clients across multiple industries.',
          achievements: [
            'Generated $50M in annual savings for retail client through supply chain optimization',
            'Designed and implemented digital strategy resulting in 40% increase in customer engagement',
            'Led post-merger integration for $2B acquisition, achieving 95% retention rate'
          ]
        }
      ],
      education: [
        {
          id: '1',
          institution: 'Wharton School, University of Pennsylvania',
          degree: 'Master of Business Administration',
          field: 'Strategy & Operations',
          startDate: '2017-09',
          endDate: '2019-05',
          current: false,
          gpa: '3.9'
        }
      ],
      skills: [
        'Strategic Analysis',
        'Process Optimization',
        'Data Analytics',
        'Change Management',
        'Financial Modeling',
        'Stakeholder Engagement',
        'Project Management',
        'Presentation Skills'
      ]
    },
    styles: {
      primaryColor: '#059669',
      fontFamily: 'Inter',
      layout: 'two-column'
    }
  },

  // CREATIVE TEMPLATES
  {
    id: 'creative-designer',
    name: 'Creative Designer',
    description: 'Bold, visually striking template perfect for designers, artists, and creative professionals.',
    category: 'creative',
    preview: '/images/illustrations/resume_creative.svg',
    sampleData: {
      personalInfo: {
        fullName: 'Luna Martinez',
        email: '<EMAIL>',
        phone: '(*************',
        location: 'Los Angeles, CA',
        linkedIn: 'linkedin.com/in/lunamartinez',
        website: 'lunamartinez.design'
      },
      summary: 'Passionate visual designer with 8+ years creating compelling brand experiences and digital products. Expert in user-centered design with a portfolio spanning Fortune 500 brands to innovative startups.',
      experience: [
        {
          id: '1',
          company: 'Pixel Perfect Studios',
          position: 'Senior Visual Designer',
          startDate: '2021-03',
          endDate: '2024-01',
          current: false,
          description: 'Led design initiatives for high-profile clients, creating cohesive brand identities and digital experiences.',
          achievements: [
            'Redesigned e-commerce platform resulting in 150% increase in conversion rates',
            'Created award-winning brand identity for tech startup (Webby Award 2023)',
            'Mentored team of 5 junior designers, improving team productivity by 40%'
          ]
        }
      ],
      education: [
        {
          id: '1',
          institution: 'Art Center College of Design',
          degree: 'Bachelor of Fine Arts',
          field: 'Graphic Design',
          startDate: '2012-09',
          endDate: '2016-05',
          current: false,
          gpa: '3.7'
        }
      ],
      skills: [
        'Adobe Creative Suite',
        'Figma',
        'Sketch',
        'Prototyping',
        'Brand Identity',
        'UI/UX Design',
        'Typography',
        'Color Theory',
        'Motion Graphics',
        'Photography'
      ]
    },
    styles: {
      // Colors
      primaryColor: '#8b5cf6',
      secondaryColor: '#a78bfa',
      accentColor: '#c4b5fd',
      textColor: '#374151',
      backgroundColor: '#ffffff',

      // Typography
      fontFamily: 'Poppins, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
      headerFontFamily: 'Poppins, -apple-system, BlinkMacSystemFont, sans-serif',
      fontSize: '11px',
      lineHeight: '1.6',
      fontWeight: '300',
      headerFontWeight: '600',

      // Layout
      layout: 'sidebar',
      spacing: '18px',
      sectionSpacing: '28px',
      padding: '36px',

      // Visual Elements
      borderStyle: 'none',
      borderWidth: '0px',
      borderRadius: '12px',
      shadowStyle: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',

      // Section Styling
      headerStyle: 'minimal',
      skillsStyle: 'tags',
      dateStyle: 'inline'
    }
  },

  {
    id: 'creative-photographer',
    name: 'Visual Artist',
    description: 'Portfolio-focused template for photographers, videographers, and visual artists.',
    category: 'creative',
    preview: '/images/illustrations/resume_photographer.svg',
    sampleData: {
      personalInfo: {
        fullName: 'James Rivera',
        email: '<EMAIL>',
        phone: '(*************',
        location: 'New York, NY',
        linkedIn: 'linkedin.com/in/jamesrivera',
        website: 'jamesrivera.photography'
      },
      summary: 'Award-winning photographer specializing in commercial and editorial work. Published in Vogue, National Geographic, and Time Magazine with expertise in both digital and film photography.',
      experience: [
        {
          id: '1',
          company: 'Freelance Photography',
          position: 'Commercial Photographer',
          startDate: '2018-01',
          endDate: '2024-01',
          current: false,
          description: 'Providing high-end photography services for fashion brands, magazines, and corporate clients.',
          achievements: [
            'Featured in National Geographic "Photographer of the Year" exhibition',
            'Shot campaigns for Nike, Apple, and Mercedes-Benz',
            'Published photo series in Vogue, Harper\'s Bazaar, and GQ'
          ]
        }
      ],
      education: [
        {
          id: '1',
          institution: 'School of Visual Arts',
          degree: 'Master of Fine Arts',
          field: 'Photography',
          startDate: '2014-09',
          endDate: '2016-05',
          current: false
        }
      ],
      skills: [
        'Commercial Photography',
        'Portrait Photography',
        'Fashion Photography',
        'Adobe Lightroom',
        'Adobe Photoshop',
        'Studio Lighting',
        'Color Grading',
        'Client Relations',
        'Art Direction'
      ]
    },
    styles: {
      primaryColor: '#ec4899',
      fontFamily: 'Playfair Display',
      layout: 'sidebar'
    }
  },

  // MODERN TEMPLATES
  {
    id: 'modern-tech',
    name: 'Modern Tech',
    description: 'Clean, minimalist template perfect for software engineers, developers, and tech professionals.',
    category: 'modern',
    preview: '/images/illustrations/resume_modern.svg',
    sampleData: {
      personalInfo: {
        fullName: 'David Kim',
        email: '<EMAIL>',
        phone: '(*************',
        location: 'Seattle, WA',
        linkedIn: 'linkedin.com/in/davidkim',
        website: 'davidkim.dev'
      },
      summary: 'Full-stack software engineer with 6+ years building scalable web applications and distributed systems. Passionate about clean code, performance optimization, and emerging technologies.',
      experience: [
        {
          id: '1',
          company: 'Microsoft',
          position: 'Senior Software Engineer',
          startDate: '2021-08',
          endDate: '2024-01',
          current: false,
          description: 'Developed cloud-native applications serving millions of users with focus on performance and reliability.',
          achievements: [
            'Reduced API response time by 60% through database optimization and caching strategies',
            'Led migration of monolithic application to microservices architecture',
            'Mentored 3 junior developers and established code review best practices'
          ]
        }
      ],
      education: [
        {
          id: '1',
          institution: 'University of Washington',
          degree: 'Bachelor of Science',
          field: 'Computer Science',
          startDate: '2014-09',
          endDate: '2018-06',
          current: false,
          gpa: '3.8'
        }
      ],
      skills: [
        'JavaScript/TypeScript',
        'React/Node.js',
        'Python',
        'AWS/Azure',
        'Docker/Kubernetes',
        'PostgreSQL/MongoDB',
        'GraphQL/REST APIs',
        'Git/CI/CD',
        'Agile/Scrum'
      ]
    },
    styles: {
      primaryColor: '#06b6d4',
      fontFamily: 'Source Code Pro',
      layout: 'two-column'
    }
  },

  {
    id: 'modern-startup',
    name: 'Startup Innovator',
    description: 'Dynamic template for entrepreneurs, startup founders, and innovation-focused roles.',
    category: 'modern',
    preview: '/images/illustrations/resume_startup.svg',
    sampleData: {
      personalInfo: {
        fullName: 'Emma Thompson',
        email: '<EMAIL>',
        phone: '(*************',
        location: 'Austin, TX',
        linkedIn: 'linkedin.com/in/emmathompson',
        website: 'emmathompson.ventures'
      },
      summary: 'Serial entrepreneur and product leader with track record of building and scaling tech startups from concept to exit. Expert in product strategy, team building, and venture capital fundraising.',
      experience: [
        {
          id: '1',
          company: 'InnovateTech (Founder)',
          position: 'CEO & Co-Founder',
          startDate: '2020-01',
          endDate: '2024-01',
          current: false,
          description: 'Built AI-powered SaaS platform from idea to $10M ARR with 50+ employees.',
          achievements: [
            'Raised $15M in Series A funding from top-tier VCs',
            'Grew user base from 0 to 100,000+ active users in 3 years',
            'Successfully exited to Fortune 500 company for $75M'
          ]
        }
      ],
      education: [
        {
          id: '1',
          institution: 'Stanford University',
          degree: 'Master of Science',
          field: 'Management Science & Engineering',
          startDate: '2016-09',
          endDate: '2018-06',
          current: false
        }
      ],
      skills: [
        'Product Strategy',
        'Venture Capital',
        'Team Leadership',
        'Go-to-Market',
        'Data Analytics',
        'User Experience',
        'Agile Development',
        'Fundraising',
        'Business Development'
      ]
    },
    styles: {
      primaryColor: '#f97316',
      fontFamily: 'Inter',
      layout: 'single-column'
    }
  },

  // CLASSIC TEMPLATES
  {
    id: 'classic-academic',
    name: 'Academic Scholar',
    description: 'Traditional template ideal for academic positions, research roles, and educational institutions.',
    category: 'classic',
    preview: '/images/illustrations/resume_academic.svg',
    sampleData: {
      personalInfo: {
        fullName: 'Dr. Robert Wilson',
        email: '<EMAIL>',
        phone: '(*************',
        location: 'Boston, MA',
        linkedIn: 'linkedin.com/in/robertwilson',
        website: 'robertwilson.academia.edu'
      },
      summary: 'Distinguished professor and researcher with 15+ years in computational biology and bioinformatics. Published 50+ peer-reviewed papers with 2,000+ citations and $5M+ in research funding.',
      experience: [
        {
          id: '1',
          company: 'Harvard Medical School',
          position: 'Associate Professor',
          startDate: '2018-09',
          endDate: '2024-01',
          current: false,
          description: 'Leading research in computational genomics and teaching graduate-level bioinformatics courses.',
          achievements: [
            'Secured $2.5M NIH grant for cancer genomics research',
            'Published 25 papers in Nature, Science, and Cell journals',
            'Mentored 15 PhD students and 30+ undergraduate researchers'
          ]
        }
      ],
      education: [
        {
          id: '1',
          institution: 'MIT',
          degree: 'Doctor of Philosophy',
          field: 'Computational Biology',
          startDate: '2008-09',
          endDate: '2013-05',
          current: false
        }
      ],
      skills: [
        'Bioinformatics',
        'Machine Learning',
        'Statistical Analysis',
        'Python/R',
        'Grant Writing',
        'Research Design',
        'Data Visualization',
        'Academic Writing',
        'Peer Review'
      ]
    },
    styles: {
      primaryColor: '#374151',
      fontFamily: 'Times New Roman',
      layout: 'single-column'
    }
  },

  {
    id: 'classic-legal',
    name: 'Legal Professional',
    description: 'Formal, authoritative template designed for lawyers, legal professionals, and government positions.',
    category: 'classic',
    preview: '/images/illustrations/resume_legal.svg',
    sampleData: {
      personalInfo: {
        fullName: 'Catherine Adams',
        email: '<EMAIL>',
        phone: '(*************',
        location: 'Washington, DC',
        linkedIn: 'linkedin.com/in/catherineadams',
        website: 'catherineadams.law'
      },
      summary: 'Experienced corporate attorney with 12+ years specializing in mergers & acquisitions, securities law, and corporate governance. Successfully closed $2B+ in transactions for Fortune 500 clients.',
      experience: [
        {
          id: '1',
          company: 'Cravath, Swaine & Moore LLP',
          position: 'Senior Associate',
          startDate: '2019-01',
          endDate: '2024-01',
          current: false,
          description: 'Advised multinational corporations on complex M&A transactions and regulatory compliance matters.',
          achievements: [
            'Led legal team for $500M acquisition in healthcare sector',
            'Drafted and negotiated 100+ commercial agreements',
            'Achieved 98% success rate in regulatory approval processes'
          ]
        }
      ],
      education: [
        {
          id: '1',
          institution: 'Harvard Law School',
          degree: 'Juris Doctor',
          field: 'Corporate Law',
          startDate: '2009-09',
          endDate: '2012-05',
          current: false,
          gpa: 'Magna Cum Laude'
        }
      ],
      skills: [
        'Corporate Law',
        'M&A Transactions',
        'Securities Regulation',
        'Contract Negotiation',
        'Due Diligence',
        'Regulatory Compliance',
        'Legal Research',
        'Client Relations',
        'Litigation Support'
      ]
    },
    styles: {
      primaryColor: '#1f2937',
      fontFamily: 'Times New Roman',
      layout: 'single-column'
    }
  }
];

export const getTemplateById = (id: string): ResumeTemplate | undefined => {
  return resumeTemplates.find(template => template.id === id);
};

export const getTemplatesByCategory = (category: ResumeTemplate['category']): ResumeTemplate[] => {
  return resumeTemplates.filter(template => template.category === category);
};
