import { useState, useRef } from 'react';
import { 
  HStack, 
  FormControl, 
  Input, 
  useToast, 
  VStack, 
  Box, 
  Container, 
  useColorModeValue, 
  useBreakpointValue,
  Heading,
  Text,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Divider,
  Icon,
  Flex,
  Stack,
  Wrap,
  WrapItem
} from '@chakra-ui/react';
import { FaUpload, FaPlus, FaFolder, FaHistory, FaCog, FaLayerGroup } from 'react-icons/fa';
import { CloseIcon } from '@chakra-ui/icons';
import { useAuth } from 'wasp/client/auth';
import { createResume } from 'wasp/client/operations';
import ResumeManager from './components/ResumeManager';
import ContentPageBox from './components/ContentPageBox';
import PageHeader from './components/PageHeader';
import ContentContainer from './components/ContentContainer';
import ActionButton from './components/ActionButton';
import { type Resume } from '../shared/types';
import BackButton from './components/BackButton';
import ResumeTemplateGallery from './components/ResumeTemplateGallery';
import { motion } from 'framer-motion';
import { FiDownload, FiEye, FiEdit, FiPlus as FiPlusIcon } from 'react-icons/fi';

// Motion components for animations
const MotionBox = motion(Box);
const MotionCard = motion(Card);

export default function ResumePage() {
  const [isCreatingResume, setIsCreatingResume] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const toast = useToast();
  const { data: user } = useAuth();
  
  // Color scheme
  const bgGradient = useColorModeValue(
    'linear(to-br, blue.50, purple.50, pink.50)',
    'linear(to-br, gray.900, blue.900, purple.900)'
  );
  const cardBg = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const textColor = useColorModeValue("gray.700", "gray.200");
  const accentColor = useColorModeValue("blue.500", "blue.400");

  // Enhanced responsive values for better mobile optimization
  const containerPadding = useBreakpointValue({ base: 2, sm: 3, md: 6, lg: 8 });
  const cardSpacing = useBreakpointValue({ base: 2, sm: 3, md: 4, lg: 6 });
  const headerSize = useBreakpointValue({ base: 'md', sm: 'lg', md: 'xl' });
  const tabPadding = useBreakpointValue({ base: 2, sm: 3, md: 6, lg: 8 });
  const buttonSize = useBreakpointValue({ base: 'sm', sm: 'sm', md: 'md' });
  const stackDirection = useBreakpointValue({ base: 'column', sm: 'column', md: 'row' }) as 'column' | 'row';
  const isFittedTabs = useBreakpointValue({ base: true, sm: true, md: false });
  const containerMaxW = useBreakpointValue({ base: "100%", sm: "95%", md: "90%", lg: "85%" });

  const handleFileButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to upload resumes.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'image/jpeg',
      'image/png',
      'image/gif'
    ];

    if (!allowedTypes.includes(file.type)) {
      toast({
        title: 'Invalid file type',
        description: 'Please upload a PDF, Word document, or image file.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    // Read file as base64 for storage
    const reader = new FileReader();
    reader.onload = async (e) => {
      if (!e.target || typeof e.target.result !== 'string') return;

      const base64Data = e.target.result.split(',')[1]; // Remove data URL prefix

      // Create a new resume entry with file data
      const newResume: Partial<Resume> = {
        title: file.name.replace(/\.[^/.]+$/, ""), // Remove file extension
        personalInfo: {
          fullName: '',
          email: '',
          phone: '',
          location: '',
        },
        summary: 'Uploaded Resume',
        experience: [],
        education: [],
        skills: [],
        fileData: {
          name: file.name,
          type: file.type,
          data: base64Data
        }
      };

      try {
        await createResume(newResume);
        toast({
          title: 'Resume uploaded',
          description: 'Your resume has been uploaded successfully.',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        // Switch to My Resumes tab after upload
        setActiveTab(1);
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to upload resume. Please try again.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
      }
    };

    reader.readAsDataURL(file);
  };

  const handleCreateResume = () => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to create resumes.',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    setIsCreatingResume(true);
    setActiveTab(0); // Go to templates tab
  };

  const handleCancelCreate = () => {
    setIsCreatingResume(false);
  };

  return (
    <Box bg={bgGradient} minH="100vh" py={containerPadding} position="relative">
      {/* Back Button for Mobile */}
      <BackButton />

      <Container
        maxW={containerMaxW}
        px={{ base: 2, sm: 3, md: 6, lg: 8 }}
        mx="auto"
      >
        <VStack spacing={cardSpacing} align="stretch" w="full">
          {/* Enhanced Page Header - Mobile Optimized */}
          <MotionBox
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <PageHeader
              title="Resume Builder"
              subtitle="Create, customize, and manage your professional resumes"
            >
              {/* Enhanced mobile-first action buttons */}
              <Stack
                direction={stackDirection}
                spacing={{ base: 2, sm: 2, md: 3 }}
                w={{ base: 'full', md: 'auto' }}
                align={{ base: 'stretch', md: 'center' }}
              >
                <ActionButton
                  icon={FiPlusIcon}
                  label={useBreakpointValue({ base: "Create", sm: "Create New", md: "Create New" }) || "Create New"}
                  variant="primary"
                  size={buttonSize}
                  onClick={handleCreateResume}
                  w={{ base: 'full', md: 'auto' }}
                  minW={{ base: 'auto', md: '120px' }}
                />
                <ActionButton
                  icon={FaUpload}
                  label={useBreakpointValue({ base: "Upload", sm: "Upload", md: "Upload" }) || "Upload"}
                  variant="outline"
                  size={buttonSize}
                  onClick={handleFileButtonClick}
                  w={{ base: 'full', md: 'auto' }}
                  minW={{ base: 'auto', md: '120px' }}
                />
              </Stack>
            </PageHeader>
          </MotionBox>

          {/* Hidden file input */}
          <input
            ref={fileInputRef}
            type="file"
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
            onChange={handleFileUpload}
            style={{ display: 'none' }}
          />

          {/* Main Content with Tabs - Mobile Optimized */}
          <MotionCard
            bg={cardBg}
            border="1px"
            borderColor={borderColor}
            borderRadius={{ base: 'xl', md: '2xl' }}
            overflow="hidden"
            shadow={{ base: 'md', md: 'lg' }}
            height={{ base: "auto", lg: "calc(100vh - 180px)" }}
            minHeight={{ base: "600px", lg: "calc(100vh - 180px)" }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Tabs
              index={activeTab}
              onChange={setActiveTab}
              variant="enclosed"
              colorScheme="blue"
              isFitted={isFittedTabs}
              height="100%"
              display="flex"
              flexDirection="column"
            >
              <Box borderBottom="1px" borderColor={borderColor}>
                <TabList px={{ base: 1, sm: 2, md: 4, lg: 6 }} py={{ base: 1, sm: 2 }}>
                  <Tab
                    _selected={{
                      color: accentColor,
                      borderColor: accentColor,
                      borderBottomColor: cardBg,
                      bg: useColorModeValue('blue.50', 'blue.900')
                    }}
                    fontSize={{ base: 'xs', sm: 'sm', md: 'md' }}
                    gap={{ base: 1, sm: 1, md: 2 }}
                    px={{ base: 1, sm: 2, md: 4 }}
                    py={{ base: 2, sm: 3 }}
                    borderRadius={{ base: 'md', md: 'lg' }}
                    mb={{ base: 1, md: 0 }}
                    _hover={{
                      bg: useColorModeValue('gray.50', 'gray.700')
                    }}
                    transition="all 0.2s"
                  >
                    <Icon as={FaLayerGroup} boxSize={{ base: 3, sm: 3, md: 4 }} />
                    <Text display={{ base: 'none', sm: 'block' }}>Templates</Text>
                    <Text display={{ base: 'block', sm: 'none' }}>New</Text>
                  </Tab>
                  <Tab
                    _selected={{
                      color: accentColor,
                      borderColor: accentColor,
                      borderBottomColor: cardBg,
                      bg: useColorModeValue('blue.50', 'blue.900')
                    }}
                    fontSize={{ base: 'xs', sm: 'sm', md: 'md' }}
                    gap={{ base: 1, sm: 1, md: 2 }}
                    px={{ base: 1, sm: 2, md: 4 }}
                    py={{ base: 2, sm: 3 }}
                    borderRadius={{ base: 'md', md: 'lg' }}
                    mb={{ base: 1, md: 0 }}
                    _hover={{
                      bg: useColorModeValue('gray.50', 'gray.700')
                    }}
                    transition="all 0.2s"
                  >
                    <Icon as={FaFolder} boxSize={{ base: 3, sm: 3, md: 4 }} />
                    <Text display={{ base: 'none', sm: 'block' }}>My Resumes</Text>
                    <Text display={{ base: 'block', sm: 'none' }}>Mine</Text>
                  </Tab>
                </TabList>
              </Box>

              <TabPanels flex="1" overflow="hidden">
                {/* Templates Tab - Mobile Optimized */}
                <TabPanel p={tabPadding}>
                  <VStack spacing={{ base: 4, md: 6 }} align="stretch">
                    <Box textAlign={{ base: 'center', md: 'left' }}>
                      <Heading 
                        size={{ base: 'sm', md: 'md' }} 
                        color={textColor} 
                        mb={2}
                      >
                        Choose a Template
                      </Heading>
                      <Text 
                        color="gray.500" 
                        fontSize={{ base: 'xs', sm: 'sm', md: 'md' }}
                      >
                        Select from our professionally designed resume templates
                      </Text>
                    </Box>
                    
                    <ResumeTemplateGallery
                      onTemplateSelect={(template) => {
                        console.log('Selected template:', template);
                        // Handle template selection and switch to creation flow
                        setIsCreatingResume(true);
                      }}
                    />
                  </VStack>
                </TabPanel>

                {/* My Resumes Tab - Full Page View */}
                <TabPanel p={0} h="100%">
                  <Box h="100%" w="100%">
                    <ResumeManager
                      onFileUpload={handleFileUpload}
                      onCreateResume={handleCreateResume}
                      onCancelCreate={handleCancelCreate}
                      isCreatingResume={isCreatingResume}
                      fileInputRef={fileInputRef}
                    />
                  </Box>
                </TabPanel>
              </TabPanels>
            </Tabs>
          </MotionCard>

          {/* Clean Quick Actions Card */}
          <MotionCard
            bg={cardBg}
            border="1px"
            borderColor={borderColor}
            borderRadius={{ base: 'lg', md: 'xl' }}
            overflow="hidden"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <CardHeader pb={2}>
              <Heading size="sm" color={textColor} textAlign={{ base: 'center', md: 'left' }}>
                Quick Actions
              </Heading>
            </CardHeader>
            <CardBody pt={0}>
              <SimpleGrid
                columns={{ base: 2, md: 4 }}
                spacing={{ base: 2, md: 3 }}
              >
                <ActionButton
                  icon={FiPlusIcon}
                  label={useBreakpointValue({ base: "Create", md: "Create from Template" }) || "Create"}
                  variant="ghost"
                  size={buttonSize}
                  onClick={() => setActiveTab(0)}
                  w="full"
                  h={{ base: '40px', md: '44px' }}
                />
                <ActionButton
                  icon={FaUpload}
                  label={useBreakpointValue({ base: "Upload", md: "Upload Existing" }) || "Upload"}
                  variant="ghost"
                  size={buttonSize}
                  onClick={handleFileButtonClick}
                  w="full"
                  h={{ base: '40px', md: '44px' }}
                />
                <ActionButton
                  icon={FiDownload}
                  label={useBreakpointValue({ base: "Download", md: "Download PDF" }) || "Download"}
                  variant="ghost"
                  size={buttonSize}
                  w="full"
                  h={{ base: '40px', md: '44px' }}
                />
                <ActionButton
                  icon={FiEye}
                  label={useBreakpointValue({ base: "Preview", md: "Preview Resume" }) || "Preview"}
                  variant="ghost"
                  size={buttonSize}
                  w="full"
                  h={{ base: '40px', md: '44px' }}
                />
              </SimpleGrid>
            </CardBody>
          </MotionCard>
        </VStack>
      </Container>
    </Box>
  );
}
