import React, { useState, useEffect, createContext, lazy, Suspense } from 'react';
import { useAuth } from 'wasp/client/auth';
import {
  ChakraProvider,
  VStack,
  Box,
  Flex,
  Text,
  Button,
  HStack
} from '@chakra-ui/react';
import { theme } from './theme';
import { useLocation, Outlet, useNavigate } from 'react-router-dom';
import ErrorBoundary from './components/ErrorBoundary';

// Lazy load heavy components to reduce initial bundle
const NavBar = lazy(() => import('./components/NavBar'));
const SidebarNav = lazy(() => import('./components/SidebarNav'));
const OnboardingFlow = lazy(() => import('./components/OnboardingFlow'));
const AnimatedHeroText = lazy(() => import('./components/AnimatedHeroText'));
const Footer = lazy(() => import('./components/Footer'));
const EditPopover = lazy(() => import('./components/Popover').then(module => ({ default: module.EditPopover })));
import FeedbackWidget from './components/FeedbackWidget';

// Lazy load icons to reduce initial bundle
const FaFileAlt = lazy(() => import('react-icons/fa').then(module => ({ default: module.FaFileAlt })));
const FaEnvelopeOpenText = lazy(() => import('react-icons/fa').then(module => ({ default: module.FaEnvelopeOpenText })));
const FaBriefcase = lazy(() => import('react-icons/fa').then(module => ({ default: module.FaBriefcase })));
const FaComments = lazy(() => import('react-icons/fa').then(module => ({ default: module.FaComments })));
const FaChartLine = lazy(() => import('react-icons/fa').then(module => ({ default: module.FaChartLine })));

// Import essential utilities immediately
import { initializeCriticalCSS } from './utils/criticalCSS';

// Lazy load performance utilities to reduce initial bundle
const loadPerformanceUtils = () => Promise.all([
  import('./utils/performanceOptimizer'),
  import('./utils/performance'),
  import('./hooks/useOptimizedAuth'),
  import('./utils/fetchInterceptor')
]);

// Import SidebarContext from NavBar to avoid duplication
let SidebarContext: React.Context<any> = createContext({});

// Dynamically import SidebarContext to avoid circular dependencies
const loadSidebarContext = async () => {
  try {
    const navBarModule = await import('./components/NavBar');
    SidebarContext = navBarModule.SidebarContext;
  } catch (error) {
    console.warn('Could not load SidebarContext:', error);
  }
};

// Create Auth Context to share auth state across components
export const AuthContext = createContext<any>(null);

export const TextareaContext = createContext({
  textareaState: '',
  setTextareaState: (_: string) => {},
});

// Loading component for better UX
const LoadingFallback = ({ height = '40px' }: { height?: string }) => (
  <Box 
    height={height} 
    bg="gray.100" 
    borderRadius="md" 
    className="loading-skeleton"
    minHeight={height}
  />
);

// Performance monitor fallback
const performanceMonitor = {
  recordCustomMetric: (name: string, value: number, metadata?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`Performance metric - ${name}: ${value}`, metadata);
    }
  }
};

export default function App() {
  const [tooltip, setTooltip] = useState<{ x: string; y: string; text: string } | null>(null);
  const [currentText, setCurrentText] = useState<string | null>(null);
  const [textareaState, setTextareaState] = useState<string>('');
  const [performanceInitialized, setPerformanceInitialized] = useState(false);
  const [sidebarContextLoaded, setSidebarContextLoaded] = useState(false);

  // Onboarding state
  const [showOnboarding, setShowOnboarding] = useState(false);

  // Sidebar state - Initialize based on screen size
  const [isSidebarOpen, setIsSidebarOpen] = useState(() => {
    if (typeof window !== 'undefined') {
      return window.innerWidth >= 1024; // Default open on desktop
    }
    return false;
  });
  const [isCollapsed, setIsCollapsed] = useState(false);

  const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);
  const closeSidebar = () => setIsSidebarOpen(false);
  const toggleCollapse = () => setIsCollapsed(!isCollapsed);

  // Create sidebar context value with proper interface
  const sidebarContextValue = {
    isSidebarOpen,
    toggleSidebar,
    closeSidebar,
    isCollapsed,
    toggleCollapse
  };

  const location = useLocation();
  const navigate = useNavigate();
  const { data: user, isLoading: isAuthLoading } = useAuth();

  // Initialize critical optimizations immediately
  useEffect(() => {
    if (typeof window !== 'undefined' && !performanceInitialized) {
      // Initialize critical CSS first to reduce render-blocking
      initializeCriticalCSS();
      
      // Load sidebar context
      loadSidebarContext().then(() => {
        setSidebarContextLoaded(true);
      });
      
      // Load performance utilities asynchronously
      loadPerformanceUtils().then(([performanceOptimizer, performance]) => {
        if (performanceOptimizer.performanceOptimizer) {
          performanceOptimizer.performanceOptimizer.optimizeChakraUI();
          performanceOptimizer.performanceOptimizer.deferNonCriticalJS();
        }
        setPerformanceInitialized(true);
      }).catch(console.error);

      // Add test ID for E2E tests
      document.body.setAttribute('data-testid', 'app-loaded');
      
      // Record app initialization metric
      performanceMonitor.recordCustomMetric('APP_INIT', performance.now());
    }
  }, [performanceInitialized]);

  // Navigation and authentication logic
  useEffect(() => {
    if (!isAuthLoading) {
      if (!user) {
        // Unauthenticated user trying to access protected routes
        if (location.pathname !== '/' && location.pathname !== '/login') {
          navigate('/login');
        }
      } else {
        // Authenticated user on the home page should go to dashboard
        const hasSpecialParams = location.search.includes('create=') || location.search.includes('job=');
        if (location.pathname === '/' && !hasSpecialParams) {
          navigate('/dashboard');
        }
      }
    }
  }, [user, isAuthLoading, location.pathname, location.search, navigate]);

  // Onboarding logic - optimized to prevent unnecessary re-renders
  useEffect(() => {
    if (user && !isAuthLoading) {
      const userId = user.id.toString();
      const hasCompleted = localStorage.getItem(`onboarding_completed_${userId}`);
      const hasSkipped = localStorage.getItem(`onboarding_skipped_${userId}`);
      
      if (!hasCompleted) {
        if (!hasSkipped) {
          setShowOnboarding(true);
        } else {
          try {
            const skipData = JSON.parse(hasSkipped);
            const skipDate = new Date(skipData.skippedAt);
            const daysSinceSkip = (Date.now() - skipDate.getTime()) / (1000 * 60 * 60 * 24);
            
            if (daysSinceSkip > 7 && skipData.skipCount < 3) {
              setShowOnboarding(true);
            }
          } catch {
            setShowOnboarding(true);
          }
        }
      }
    } else if (!user) {
      setShowOnboarding(false);
    }
  }, [user, isAuthLoading]);

  const handleOnboardingComplete = (preferences: any) => {
    if (user) {
      const userId = user.id.toString();
      localStorage.setItem(`onboarding_completed_${userId}`, 'true');
      localStorage.setItem(`user_preferences_${userId}`, JSON.stringify(preferences));
      localStorage.removeItem(`onboarding_skipped_${userId}`);
    }
    setShowOnboarding(false);
  };

  const handleOnboardingSkip = () => {
    if (user) {
      const userId = user.id.toString();
      const currentSkipCount = parseInt(localStorage.getItem(`onboarding_skip_count_${userId}`) || '0');
      const skipData = {
        skippedAt: new Date().toISOString(),
        skipCount: currentSkipCount + 1
      };
      localStorage.setItem(`onboarding_skipped_${userId}`, JSON.stringify(skipData));
      localStorage.setItem(`onboarding_skip_count_${userId}`, skipData.skipCount.toString());
    }
    setShowOnboarding(false);
  };

  // Optimized event handlers with useCallback for better performance
  const handleMouseUp = React.useCallback((event: MouseEvent) => {
    const selection = window.getSelection();

    if (selection?.toString() && location.pathname.includes('cover-letter')) {
      if (selection.toString() === currentText) {
        setTooltip(null);
        return;
      }
      setCurrentText(selection.toString());
      setTooltip({
        x: event.clientX.toString(),
        y: event.clientY.toString(),
        text: selection.toString()
      });
    } else {
      setTooltip(null);
    }
  }, [location.pathname, currentText]);

  const handleMouseDown = React.useCallback(() => {
    if (location.pathname.includes('cover-letter')) {
      setTooltip(null);
    }
  }, [location.pathname]);

  // Event listeners with cleanup
  useEffect(() => {
    if (!location.pathname.includes('cover-letter')) {
      setTooltip(null);
      return;
    }

    document.addEventListener('mouseup', handleMouseUp, { passive: true });
    document.addEventListener('mousedown', handleMouseDown, { passive: true });

    return () => {
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, [location.pathname, handleMouseUp, handleMouseDown]);

  return (
    <ChakraProvider theme={theme}>
      <ErrorBoundary
        onError={(error, errorInfo) => {
          // Report error to monitoring service
          console.error('Application Error:', error, errorInfo);
          performanceMonitor.recordCustomMetric('ERROR_BOUNDARY_TRIGGERED', 1, {
            error: error.message,
            stack: error.stack,
            componentStack: errorInfo.componentStack,
          });
        }}
        showErrorDetails={process.env.NODE_ENV === 'development'}
      >
        <AuthContext.Provider value={{ data: user, isLoading: isAuthLoading }}>
          <SidebarContext.Provider value={sidebarContextValue}>
            <TextareaContext.Provider
              value={{
                textareaState,
                setTextareaState,
              }}
            >
              <Box
                top={tooltip?.y}
                left={tooltip?.x}
                display={tooltip?.text ? 'block' : 'none'}
                position='absolute'
                zIndex={100}
              >
                {!!user && (
                  <Suspense fallback={<LoadingFallback height="20px" />}>
                    <EditPopover setTooltip={setTooltip} user={user} />
                  </Suspense>
                )}
              </Box>
              
              <VStack spacing={0} align="stretch" w="full">
                <Suspense fallback={<LoadingFallback height="80px" />}>
                  <NavBar />
                </Suspense>

                <Flex w="full">
                  <Suspense fallback={<LoadingFallback height="100vh" />}>
                    <SidebarNav />
                  </Suspense>
                  
                  <Box
                    id="main-content"
                    ml={{
                      base: 0,
                      lg: location.pathname !== '/'
                        ? (isCollapsed ? '60px' : '220px')
                        : 0
                    }}
                    w="full"
                    minH="calc(100vh - 80px)"
                    bg='bg-body'
                    pl={{ base: 2, md: 4 }}
                    transition="margin-left 0.3s ease"
                    position="relative"
                    zIndex="2"
                  >
                    <VStack gap={5} minHeight='100%'>
                      <Box flexGrow={1} width="100%" maxW={{ base: "100%", md: "95%" }} mx="auto" px={{ base: 1, md: 4, lg: 6 }}>
                        <Suspense fallback={<LoadingFallback height="400px" />}>
                          <Outlet />
                        </Suspense>
                      </Box>
                      
                      <Suspense fallback={<LoadingFallback height="60px" />}>
                        <Footer />
                      </Suspense>
                    </VStack>
                  </Box>
                </Flex>
              </VStack>
              
              {/* Onboarding Modal */}
              <Suspense fallback={<div>Loading...</div>}>
                <OnboardingFlow
                  isOpen={showOnboarding}
                  onClose={() => setShowOnboarding(false)}
                  onComplete={handleOnboardingComplete}
                  onSkip={handleOnboardingSkip}
                />
              </Suspense>

              {/* Feedback Widget - Available for all users */}
              <FeedbackWidget />

            </TextareaContext.Provider>
          </SidebarContext.Provider>
        </AuthContext.Provider>
      </ErrorBoundary>
    </ChakraProvider>
  );
}
