// Shared Utility Functions
// Common helper functions used throughout the application

import { REGEX_PATTERNS, DATE_FORMATS, LIMITS } from './constants';

// String Utilities
export const stringUtils = {
  /**
   * Capitalize the first letter of a string
   */
  capitalize: (str: string): string => {
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  },

  /**
   * Convert string to title case
   */
  toTitleCase: (str: string): string => {
    return str.replace(/\w\S*/g, (txt) =>
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  },

  /**
   * Truncate string with ellipsis
   */
  truncate: (str: string, maxLength: number): string => {
    if (str.length <= maxLength) return str;
    return str.slice(0, maxLength - 3) + '...';
  },

  /**
   * Generate a random string
   */
  generateRandomString: (length: number = 8): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  /**
   * Convert string to slug format
   */
  slugify: (str: string): string => {
    return str
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  },

  /**
   * Extract initials from a name
   */
  getInitials: (name: string): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  },

  /**
   * Clean HTML tags from string
   */
  stripHtml: (html: string): string => {
    return html.replace(/<[^>]*>/g, '');
  },

  /**
   * Count words in a string
   */
  wordCount: (str: string): number => {
    return str.trim().split(/\s+/).filter(word => word.length > 0).length;
  },
};

// Number Utilities
export const numberUtils = {
  /**
   * Format number with commas
   */
  formatNumber: (num: number): string => {
    return num.toLocaleString();
  },

  /**
   * Format bytes to human readable format
   */
  formatBytes: (bytes: number, decimals: number = 2): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + ' ' + sizes[i];
  },

  /**
   * Generate random number between min and max
   */
  randomBetween: (min: number, max: number): number => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  },

  /**
   * Clamp number between min and max
   */
  clamp: (num: number, min: number, max: number): number => {
    return Math.min(Math.max(num, min), max);
  },

  /**
   * Round to specified decimal places
   */
  roundTo: (num: number, decimals: number): number => {
    return Number(Math.round(Number(num + 'e' + decimals)) + 'e-' + decimals);
  },

  /**
   * Convert percentage to decimal
   */
  percentToDecimal: (percent: number): number => {
    return percent / 100;
  },

  /**
   * Convert decimal to percentage
   */
  decimalToPercent: (decimal: number): number => {
    return decimal * 100;
  },
};

// Date Utilities
export const dateUtils = {
  /**
   * Format date to string
   */
  formatDate: (date: Date | string, format: string = DATE_FORMATS.SHORT): string => {
    const d = new Date(date);

    switch (format) {
      case DATE_FORMATS.SHORT:
        return d.toLocaleDateString();
      case DATE_FORMATS.LONG:
        return d.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      case DATE_FORMATS.ISO:
        return d.toISOString().split('T')[0];
      case DATE_FORMATS.TIME:
        return d.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        });
      case DATE_FORMATS.DATETIME:
        return d.toLocaleString();
      default:
        return d.toLocaleDateString();
    }
  },

  /**
   * Get relative time (e.g., "2 hours ago")
   */
  getRelativeTime: (date: Date | string): string => {
    const now = new Date();
    const past = new Date(date);
    const diffInSeconds = Math.floor((now.getTime() - past.getTime()) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} months ago`;
    return `${Math.floor(diffInSeconds / 31536000)} years ago`;
  },

  /**
   * Check if date is today
   */
  isToday: (date: Date | string): boolean => {
    const today = new Date();
    const checkDate = new Date(date);
    return today.toDateString() === checkDate.toDateString();
  },

  /**
   * Add days to date
   */
  addDays: (date: Date, days: number): Date => {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  },

  /**
   * Get start of day
   */
  startOfDay: (date: Date): Date => {
    const result = new Date(date);
    result.setHours(0, 0, 0, 0);
    return result;
  },

  /**
   * Get end of day
   */
  endOfDay: (date: Date): Date => {
    const result = new Date(date);
    result.setHours(23, 59, 59, 999);
    return result;
  },
};

// Validation Utilities
export const validationUtils = {
  /**
   * Validate email address
   */
  isValidEmail: (email: string): boolean => {
    return REGEX_PATTERNS.EMAIL.test(email);
  },

  /**
   * Validate phone number
   */
  isValidPhone: (phone: string): boolean => {
    return REGEX_PATTERNS.PHONE.test(phone);
  },

  /**
   * Validate URL
   */
  isValidUrl: (url: string): boolean => {
    return REGEX_PATTERNS.URL.test(url);
  },

  /**
   * Validate LinkedIn job URL
   */
  isValidLinkedInJobUrl: (url: string): boolean => {
    return REGEX_PATTERNS.LINKEDIN_JOB_URL.test(url);
  },

  /**
   * Validate username
   */
  isValidUsername: (username: string): boolean => {
    return (
      username.length >= LIMITS.USERNAME_MIN_LENGTH &&
      username.length <= LIMITS.USERNAME_MAX_LENGTH &&
      REGEX_PATTERNS.USERNAME.test(username)
    );
  },

  /**
   * Validate password strength
   */
  isStrongPassword: (password: string): boolean => {
    return (
      password.length >= LIMITS.PASSWORD_MIN_LENGTH &&
      REGEX_PATTERNS.STRONG_PASSWORD.test(password)
    );
  },

  /**
   * Validate file size
   */
  isValidFileSize: (file: File, maxSize: number = LIMITS.RESUME_MAX_SIZE): boolean => {
    return file.size <= maxSize;
  },

  /**
   * Validate file type
   */
  isValidFileType: (file: File, allowedTypes: string[]): boolean => {
    return allowedTypes.includes(file.type);
  },
};

// Array Utilities
export const arrayUtils = {
  /**
   * Remove duplicates from array
   */
  unique: <T>(array: T[]): T[] => {
    return [...new Set(array)];
  },

  /**
   * Shuffle array
   */
  shuffle: <T>(array: T[]): T[] => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  },

  /**
   * Chunk array into smaller arrays
   */
  chunk: <T>(array: T[], size: number): T[][] => {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  },

  /**
   * Group array by key
   */
  groupBy: <T>(array: T[], key: keyof T): Record<string, T[]> => {
    return array.reduce((groups, item) => {
      const group = String(item[key]);
      groups[group] = groups[group] || [];
      groups[group].push(item);
      return groups;
    }, {} as Record<string, T[]>);
  },

  /**
   * Sort array by key
   */
  sortBy: <T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] => {
    return [...array].sort((a, b) => {
      const aVal = a[key];
      const bVal = b[key];

      if (aVal < bVal) return direction === 'asc' ? -1 : 1;
      if (aVal > bVal) return direction === 'asc' ? 1 : -1;
      return 0;
    });
  },
};

// Object Utilities
export const objectUtils = {
  /**
   * Deep clone object
   */
  deepClone: <T>(obj: T): T => {
    return JSON.parse(JSON.stringify(obj));
  },

  /**
   * Check if object is empty
   */
  isEmpty: (obj: object): boolean => {
    return Object.keys(obj).length === 0;
  },

  /**
   * Pick specific keys from object
   */
  pick: <T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> => {
    const result = {} as Pick<T, K>;
    keys.forEach(key => {
      if (key in obj) {
        result[key] = obj[key];
      }
    });
    return result;
  },

  /**
   * Omit specific keys from object
   */
  omit: <T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> => {
    const result = { ...obj };
    keys.forEach(key => {
      delete result[key];
    });
    return result;
  },
};

// Browser Utilities
export const browserUtils = {
  /**
   * Copy text to clipboard
   */
  copyToClipboard: async (text: string): Promise<boolean> => {
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      return false;
    }
  },

  /**
   * Download file
   */
  downloadFile: (content: string, filename: string, contentType: string = 'text/plain'): void => {
    const blob = new Blob([content], { type: contentType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  },

  /**
   * Get device type
   */
  getDeviceType: (): 'mobile' | 'tablet' | 'desktop' => {
    const width = window.innerWidth;
    if (width < 768) return 'mobile';
    if (width < 1024) return 'tablet';
    return 'desktop';
  },

  /**
   * Check if browser supports feature
   */
  supportsFeature: (feature: string): boolean => {
    switch (feature) {
      case 'clipboard':
        return !!navigator.clipboard;
      case 'webp':
        return document.createElement('canvas').toDataURL('image/webp').indexOf('webp') > -1;
      case 'localStorage':
        try {
          localStorage.setItem('test', 'test');
          localStorage.removeItem('test');
          return true;
        } catch {
          return false;
        }
      default:
        return false;
    }
  },
};

// Export all utilities as a single object
export const utils = {
  string: stringUtils,
  number: numberUtils,
  date: dateUtils,
  validation: validationUtils,
  array: arrayUtils,
  object: objectUtils,
  browser: browserUtils,
};
