export interface UserPreferences {
  careerStage: string;
  industries: string[];
  goals: string[];
  experience: string;
}

export const getUserPreferences = (userId: string): UserPreferences | null => {
  try {
    const stored = localStorage.getItem(`user_preferences_${userId}`);
    return stored ? JSON.parse(stored) : null;
  } catch {
    return null;
  }
};

export const hasCompletedOnboarding = (userId: string): boolean => {
  return Boolean(localStorage.getItem(`onboarding_completed_${userId}`));
};

export const resetOnboarding = (userId: string): void => {
  localStorage.removeItem(`onboarding_completed_${userId}`);
  localStorage.removeItem(`user_preferences_${userId}`);
}; 