// Intercept fetch requests to handle 401 errors gracefully and reduce console noise

const originalFetch = window.fetch;

// Store original console methods
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

// Override console methods to suppress auth-related errors
console.error = (...args) => {
  const message = args.join(' ');
  if (
    (message.includes('401') || message.includes('Unauthorized')) &&
    (message.includes('/auth/me') || message.includes('fetchInterceptor') || message.includes('Failed to load resource'))
  ) {
    // Suppress auth-related 401 errors
    return;
  }
  originalConsoleError.apply(console, args);
};

console.warn = (...args) => {
  const message = args.join(' ');
  if (
    (message.includes('401') || message.includes('Unauthorized')) &&
    (message.includes('/auth/me') || message.includes('Failed to load resource'))
  ) {
    // Suppress auth-related 401 warnings
    return;
  }
  originalConsoleWarn.apply(console, args);
};

// Also override console.log to catch any other auth-related messages
const originalConsoleLog = console.log;
console.log = (...args) => {
  const message = args.join(' ');
  if (
    (message.includes('401') || message.includes('Unauthorized')) &&
    message.includes('/auth/me')
  ) {
    // Suppress auth-related 401 logs
    return;
  }
  originalConsoleLog.apply(console, args);
};

window.fetch = async (...args) => {
  try {
    const response = await originalFetch(...args);

    // If it's a 401 response to /auth/me, handle silently
    if (response.status === 401 && args[0]?.toString().includes('/auth/me')) {
      // Return the response without any console logging
      return response;
    }

    return response;
  } catch (error) {
    // Only log non-auth related errors
    if (!args[0]?.toString().includes('/auth/me')) {
      originalConsoleError('Fetch error:', error);
    }
    throw error;
  }
};

// Note: XMLHttpRequest interception removed to avoid TypeScript issues
// The main auth requests use fetch, so the fetch interceptor above should be sufficient

// Global error handler to catch any remaining auth-related errors
window.addEventListener('error', (event) => {
  if (
    event.message &&
    (event.message.includes('401') || event.message.includes('Unauthorized')) &&
    event.message.includes('/auth/me')
  ) {
    // Prevent the error from being logged to console
    event.preventDefault();
    event.stopPropagation();
    return false;
  }
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
  if (
    event.reason &&
    event.reason.toString &&
    (event.reason.toString().includes('401') || event.reason.toString().includes('Unauthorized')) &&
    event.reason.toString().includes('/auth/me')
  ) {
    // Prevent the error from being logged to console
    event.preventDefault();
    return false;
  }
});

export {}; // Make this a module
