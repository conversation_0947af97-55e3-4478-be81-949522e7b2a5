// Performance optimization utilities for better Core Web Vitals and Lighthouse scores

export interface PerformanceMetrics {
  cls?: number;
  inp?: number;
  fcp?: number;
  lcp?: number;
  ttfb?: number;
}

// Extend the Window interface for our custom properties
declare global {
  interface Window {
    __CHAKRA_TREE_SHAKE__?: {
      unused: string[];
      critical: string[];
    };
    __ROUTE_CHUNKS__?: {
      critical: string[];
      deferred: string[];
    };
    __PERFORMANCE_OBSERVER__?: PerformanceObserver;
  }
}

class PerformanceOptimizer {
  private metrics: PerformanceMetrics = {};
  private isProduction = process.env.NODE_ENV === 'production';
  private initialized = false;
  private criticalResources: string[] = [];
  private deferredResources: string[] = [];

  constructor() {
    if (typeof window !== 'undefined') {
      this.setupPerformanceObserver();
      this.initializeOptimizations();
    }
  }

  private setupPerformanceObserver() {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.entryType === 'largest-contentful-paint') {
              this.metrics.lcp = entry.startTime;
              this.reportMetric('LCP', entry.startTime);
            } else if (entry.entryType === 'first-input') {
              const inp = (entry as any).processingStart - entry.startTime;
              this.metrics.inp = inp;
              this.reportMetric('INP', inp);
            } else if (entry.entryType === 'layout-shift') {
              if (!(entry as any).hadRecentInput) {
                const currentCls = this.metrics.cls || 0;
                const newCls = currentCls + (entry as any).value;
                this.metrics.cls = newCls;
                this.reportMetric('CLS', newCls);
              }
            }
          });
        });

        observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
        window.__PERFORMANCE_OBSERVER__ = observer;
      } catch (error) {
        console.warn('Performance Observer not supported:', error);
      }
    }
  }

  private async initializeOptimizations() {
    // Critical optimizations first
    this.inlineCriticalCSS();
    this.addResourceHints();
    this.optimizeFonts();
    
    // Defer non-critical optimizations
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        this.deferNonCriticalResources();
        this.optimizeImages();
        this.setupServiceWorker();
        this.optimizeMainThread();
        this.preloadCriticalRoutes();
      });
    } else {
      setTimeout(() => {
        this.deferNonCriticalResources();
        this.optimizeImages();
        this.setupServiceWorker();
        this.optimizeMainThread();
        this.preloadCriticalRoutes();
      }, 100);
    }
  }

  private inlineCriticalCSS() {
    if (document.querySelector('[data-critical-inlined]')) return;

    const criticalCSS = `
      /* Critical path CSS for immediate rendering */
      :root {
        --chakra-colors-blue-500: #3182CE;
        --chakra-colors-gray-50: #F7FAFC;
        --chakra-colors-gray-100: #EDF2F7;
        --chakra-colors-gray-200: #E2E8F0;
        --chakra-colors-white: #FFFFFF;
        --chakra-fontSizes-md: 1rem;
        --chakra-fontWeights-medium: 500;
        --chakra-radii-md: 0.375rem;
        --chakra-space-4: 1rem;
        --chakra-space-6: 1.5rem;
      }
      
      body {
        margin: 0;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        line-height: 1.5;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        overflow-x: hidden;
      }
      
      /* Immediate layout elements */
      .chakra-container {
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
      }
      
      .chakra-flex {
        display: flex;
      }
      
      .chakra-stack {
        display: flex;
        flex-direction: column;
      }
      
      .chakra-button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        line-height: 1.2;
        border-radius: var(--chakra-radii-md);
        padding: var(--chakra-space-4) var(--chakra-space-6);
        background: var(--chakra-colors-blue-500);
        color: var(--chakra-colors-white);
        border: none;
        cursor: pointer;
        transition: all 0.2s;
      }
      
      .chakra-button:hover {
        transform: translateY(-1px);
      }
      
      /* Performance optimizations */
      *, *::before, *::after {
        box-sizing: border-box;
      }
      
      img {
        max-width: 100%;
        height: auto;
        loading: lazy;
        decoding: async;
      }
      
      /* Reduce layout shift */
      .loading-placeholder {
        background: var(--chakra-colors-gray-100);
        border-radius: var(--chakra-radii-md);
        animation: pulse 1.5s ease-in-out infinite;
      }
      
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }
      
      /* Viewport optimizations */
      @media (max-width: 768px) {
        .chakra-container {
          padding: 0 0.5rem;
        }
      }
      
      /* Reduce motion for accessibility */
      @media (prefers-reduced-motion: reduce) {
        *, *::before, *::after {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }
    `;

    const style = document.createElement('style');
    style.textContent = criticalCSS;
    style.setAttribute('data-critical-inlined', 'true');
    document.head.insertBefore(style, document.head.firstChild);
  }

  private addResourceHints() {
    const hints = [
      // DNS prefetch for external domains
      { rel: 'dns-prefetch', href: '//fonts.googleapis.com' },
      { rel: 'dns-prefetch', href: '//fonts.gstatic.com' },
      { rel: 'dns-prefetch', href: '//cdn.jsdelivr.net' },
      
      // Preconnect for critical external resources
      { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: 'anonymous' },
    ];

    hints.forEach((hint) => {
      const existing = document.querySelector(`link[rel="${hint.rel}"][href="${hint.href}"]`);
      if (!existing) {
        const link = document.createElement('link');
        link.rel = hint.rel;
        link.href = hint.href;
        if (hint.crossorigin) {
          link.crossOrigin = hint.crossorigin;
        }
        document.head.appendChild(link);
      }
    });
  }

  private optimizeFonts() {
    // Preload critical font files
    const criticalFonts = [
      {
        href: 'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2',
        weight: '400'
      }
    ];

    criticalFonts.forEach(font => {
      const existing = document.querySelector(`link[href="${font.href}"]`);
      if (!existing) {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'font';
        link.type = 'font/woff2';
        link.href = font.href;
        link.crossOrigin = 'anonymous';
        document.head.appendChild(link);
      }
    });

    // Add font-display: swap for all fonts
    const fontOptCSS = `
      @font-face {
        font-display: swap;
      }
    `;
    
    const style = document.createElement('style');
    style.textContent = fontOptCSS;
    style.setAttribute('data-font-display', 'true');
    document.head.appendChild(style);
  }

  private deferNonCriticalResources() {
    // Convert non-critical stylesheets to preload
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]:not([data-critical])');
    
    stylesheets.forEach((link) => {
      if (link instanceof HTMLLinkElement) {
        const href = link.href;
        
        // Identify non-critical CSS
        const isNonCritical = 
          href.includes('icon') ||
          href.includes('font') ||
          href.includes('animation') ||
          href.includes('highlight');
        
        if (isNonCritical && !link.dataset.deferred) {
          link.dataset.deferred = 'true';
          link.rel = 'preload';
          link.as = 'style';
          
          const loadStylesheet = () => {
            link.rel = 'stylesheet';
            link.onload = null;
          };
          
          link.onload = loadStylesheet;
          
          // Fallback timeout
          setTimeout(loadStylesheet, 3000);
        }
      }
    });

    // Defer non-critical scripts
    const scripts = document.querySelectorAll('script[data-defer="true"]');
    scripts.forEach(script => {
      if (script instanceof HTMLScriptElement && !script.dataset.loaded) {
        script.dataset.loaded = 'true';
        
        if ('requestIdleCallback' in window) {
          requestIdleCallback(() => this.loadScript(script));
        } else {
          setTimeout(() => this.loadScript(script), 2000);
        }
      }
    });
  }

  private loadScript(originalScript: HTMLScriptElement) {
    const script = document.createElement('script');
    script.src = originalScript.src;
    script.async = true;
    script.defer = true;
    document.head.appendChild(script);
  }

  private optimizeImages() {
    // Implement intersection observer for lazy loading
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            if (img.dataset.src && !img.src) {
              img.src = img.dataset.src;
              img.classList.remove('lazy');
              imageObserver.unobserve(img);
            }
          }
        });
      }, {
        rootMargin: '50px 0px', // Start loading 50px before image enters viewport
        threshold: 0.01
      });

      // Observe all lazy images
      document.querySelectorAll('img[data-src]').forEach((img) => {
        imageObserver.observe(img);
      });

      // Optimize existing images
      document.querySelectorAll('img:not([loading])').forEach((img, index) => {
        if (img instanceof HTMLImageElement) {
          // First few images load eagerly, rest lazy
          img.loading = index < 3 ? 'eager' : 'lazy';
          img.decoding = 'async';
          
          // Add explicit width/height if missing to prevent CLS
          if (!img.width && !img.height && !img.style.width && !img.style.height) {
            img.style.aspectRatio = '16/9'; // Default aspect ratio
          }
        }
      });
    }
  }

  private async setupServiceWorker() {
    if ('serviceWorker' in navigator && window.location.protocol === 'https:') {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js');
        console.log('Service Worker registered:', registration);
      } catch (error) {
        console.log('Service Worker registration failed:', error);
      }
    }
  }

  private optimizeMainThread() {
    // Break up long tasks using scheduler API or setTimeout
    const taskQueue: (() => void)[] = [];
    
    const processTaskQueue = () => {
      const start = performance.now();
      
      while (taskQueue.length > 0 && (performance.now() - start) < 5) {
        const task = taskQueue.shift();
        if (task) task();
      }
      
      if (taskQueue.length > 0) {
        if ('scheduler' in window && 'postTask' in (window as any).scheduler) {
          (window as any).scheduler.postTask(processTaskQueue, { priority: 'background' });
        } else {
          setTimeout(processTaskQueue, 0);
        }
      }
    };

    // Optimize event listeners for better performance
    const passiveEvents = ['scroll', 'wheel', 'touchstart', 'touchmove'];
    
    passiveEvents.forEach(eventType => {
      const originalAdd = EventTarget.prototype.addEventListener;
      EventTarget.prototype.addEventListener = function(type, listener, options) {
        if (passiveEvents.includes(type)) {
          if (typeof options !== 'object') {
            options = { passive: true };
          } else if (options && typeof options === 'object' && !('passive' in options)) {
            options.passive = true;
          }
        }
        return originalAdd.call(this, type, listener, options);
      };
    });

    return {
      addTask: (task: () => void) => {
        taskQueue.push(task);
        if (taskQueue.length === 1) {
          processTaskQueue();
        }
      }
    };
  }

  private preloadCriticalRoutes() {
    const criticalRoutes = [
      '/dashboard',
      '/cover-letters',
      '/jobs',
      '/resume'
    ];

    criticalRoutes.forEach((route) => {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = route;
      document.head.appendChild(link);
    });
  }

  private reportMetric(name: string, value: number, metadata?: any) {
    if (this.isProduction) {
      console.log(`Performance metric - ${name}: ${value}ms`, metadata);
      
      // Send to analytics service in production
      if (typeof window !== 'undefined' && 'gtag' in window && typeof (window as any).gtag === 'function') {
        (window as any).gtag('event', 'performance_metric', {
          metric_name: name,
          metric_value: Math.round(value),
          ...metadata
        });
      }
    }
  }

  // Public methods for external use
  public optimizeChakraUI() {
    if (this.initialized) return;
    
    // Mark frequently used Chakra components as critical
    if (typeof window !== 'undefined') {
      window.__CHAKRA_TREE_SHAKE__ = {
        unused: [
          'Accordion', 'Alert', 'AlertDialog', 'Avatar', 'Badge', 'Breadcrumb',
          'Checkbox', 'CircularProgress', 'CloseButton', 'Code', 'Divider',
          'Drawer', 'Editable', 'FormControl', 'Icon', 'IconButton',
          'Image', 'Kbd', 'Link', 'List', 'Menu', 'Modal', 'NumberInput',
          'Popover', 'Progress', 'Radio', 'RangeSlider', 'Select', 'Skeleton',
          'Slider', 'Spinner', 'Stat', 'Switch', 'Table', 'Tabs', 'Tag',
          'Textarea', 'Toast', 'Tooltip'
        ],
        critical: [
          'Box', 'Flex', 'VStack', 'HStack', 'Text', 'Heading', 'Button',
          'Input', 'Container', 'Stack', 'Grid', 'GridItem', 'Spacer'
        ]
      };
    }
    
    this.initialized = true;
  }

  public deferNonCriticalJS() {
    // This is handled in deferNonCriticalResources
    this.deferNonCriticalResources();
  }

  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  public preloadRoute(route: string) {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = route;
    document.head.appendChild(link);
  }

  public optimizeForRoute(route: string) {
    // Route-specific optimizations
    switch (route) {
      case '/dashboard':
        this.preloadRoute('/cover-letters');
        this.preloadRoute('/jobs');
        break;
      case '/cover-letters':
        this.preloadRoute('/jobs');
        break;
      case '/jobs':
        this.preloadRoute('/cover-letters');
        break;
    }
  }
}

// Create and export the performance optimizer instance
export const performanceOptimizer = new PerformanceOptimizer();

// Utility functions
export const lazyImport = async <T>(importFn: () => Promise<T>): Promise<T | null> => {
  try {
    return await importFn();
  } catch (error) {
    console.error('Failed to lazy load component:', error);
    return null;
  }
};

export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

export const optimizeResourcePriority = () => {
  // Set fetchpriority for important resources
  const criticalImages = document.querySelectorAll('img[data-priority="high"]');
  criticalImages.forEach((img) => {
    if (img instanceof HTMLImageElement) {
      img.setAttribute('fetchpriority', 'high');
      img.loading = 'eager';
    }
  });

  const nonCriticalImages = document.querySelectorAll('img[data-priority="low"]');
  nonCriticalImages.forEach((img) => {
    if (img instanceof HTMLImageElement) {
      img.setAttribute('fetchpriority', 'low');
      img.loading = 'lazy';
    }
  });
};

// React hook for performance optimization
export const usePerformanceOptimization = () => {
  return {
    preloadRoute: (route: string) => performanceOptimizer.preloadRoute(route),
    optimizeImage: (imgElement: HTMLImageElement) => {
      imgElement.loading = 'lazy';
      imgElement.decoding = 'async';
    },
    getMetrics: () => performanceOptimizer.getMetrics(),
    optimizeForRoute: (route: string) => performanceOptimizer.optimizeForRoute(route),
  };
}; 