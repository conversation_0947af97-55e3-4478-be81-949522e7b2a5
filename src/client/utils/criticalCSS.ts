// Critical CSS extraction and inlining utility
// This helps reduce render-blocking resources by inlining above-the-fold styles

export const criticalCSS = `
  /* Reset and base styles */
  *, *::before, *::after {
    box-sizing: border-box;
  }

  html {
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
  }

  body {
    margin: 0;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.5;
    color: #1A202C;
    background-color: #FFFFFF;
  }

  /* Chakra UI critical styles */
  .chakra-ui-light {
    color-scheme: light;
    --chakra-colors-chakra-body-text: #1A202C;
    --chakra-colors-chakra-body-bg: #FFFFFF;
    --chakra-colors-chakra-border-color: #E2E8F0;
  }

  .chakra-ui-dark {
    color-scheme: dark;
    --chakra-colors-chakra-body-text: #FFFFFF;
    --chakra-colors-chakra-body-bg: #1A202C;
    --chakra-colors-chakra-border-color: #4A5568;
  }

  /* Layout critical styles */
  .chakra-stack {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
  }

  .chakra-flex {
    display: flex;
  }

  .chakra-box {
    display: block;
  }

  .chakra-container {
    width: 100%;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Navigation critical styles */
  .navbar {
    position: sticky;
    top: 0;
    z-index: 1000;
    background-color: var(--chakra-colors-chakra-body-bg);
    border-bottom: 1px solid var(--chakra-colors-chakra-border-color);
  }

  /* Button critical styles */
  .chakra-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: relative;
    white-space: nowrap;
    vertical-align: middle;
    outline: transparent solid 2px;
    outline-offset: 2px;
    line-height: 1.2;
    border-radius: 0.375rem;
    font-weight: 600;
    transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
    transition-duration: 200ms;
    height: 2.5rem;
    min-width: 2.5rem;
    font-size: 1rem;
    padding-inline-start: 1rem;
    padding-inline-end: 1rem;
    background: #3182CE;
    color: white;
    border: none;
    cursor: pointer;
  }

  .chakra-button:hover {
    background: #2C5282;
  }

  /* Input critical styles */
  .chakra-input {
    width: 100%;
    min-width: 0px;
    outline: transparent solid 2px;
    outline-offset: 2px;
    position: relative;
    appearance: none;
    transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
    transition-duration: 200ms;
    font-size: 1rem;
    padding-inline-start: 1rem;
    padding-inline-end: 1rem;
    height: 2.5rem;
    border-radius: 0.375rem;
    border: 1px solid;
    border-color: inherit;
    background: inherit;
  }

  /* Card critical styles */
  .chakra-card {
    background: var(--chakra-colors-chakra-body-bg);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border-radius: 0.5rem;
    border: 1px solid var(--chakra-colors-chakra-border-color);
  }

  /* Text critical styles */
  .chakra-text {
    color: var(--chakra-colors-chakra-body-text);
  }

  .chakra-heading {
    font-weight: 700;
    line-height: 1.2;
    color: var(--chakra-colors-chakra-body-text);
  }

  /* Reduce layout shift */
  img {
    max-width: 100%;
    height: auto;
    display: block;
  }

  /* Loading states */
  .loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }

  @keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
  }

  /* Performance optimizations */
  @media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }

  /* Responsive utilities */
  @media (max-width: 768px) {
    .chakra-container {
      padding-left: 0.5rem;
      padding-right: 0.5rem;
    }
  }

  /* Focus styles for accessibility */
  .chakra-button:focus,
  .chakra-input:focus {
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.6);
  }

  /* Hide elements that are not critical */
  .non-critical {
    display: none;
  }
`;

export const inlineCriticalCSS = () => {
  // Only inline if not already done
  if (document.querySelector('[data-critical-css]')) {
    return;
  }

  const style = document.createElement('style');
  style.textContent = criticalCSS;
  style.setAttribute('data-critical-css', 'true');
  
  // Insert at the beginning of head for highest priority
  const firstChild = document.head.firstChild;
  if (firstChild) {
    document.head.insertBefore(style, firstChild);
  } else {
    document.head.appendChild(style);
  }
};

export const deferNonCriticalCSS = () => {
  // Find all stylesheets and defer non-critical ones
  const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
  
  stylesheets.forEach((link) => {
    if (link instanceof HTMLLinkElement) {
      const href = link.href;
      
      // Identify non-critical CSS (icons, fonts, animations)
      const isNonCritical = 
        href.includes('icons') ||
        href.includes('fonts') ||
        href.includes('animation') ||
        href.includes('highlight') ||
        href.includes('prism');
      
      if (isNonCritical) {
        // Convert to preload and load asynchronously
        link.rel = 'preload';
        link.as = 'style';
        link.onload = () => {
          link.rel = 'stylesheet';
          link.onload = null;
        };
      }
    }
  });
};

export const optimizeFontLoading = () => {
  // Add font-display: swap to improve font loading performance
  const fontOptimizationCSS = `
    /* Font optimization */
    @font-face {
      font-display: swap;
    }
    
    /* Fallback font stack */
    .font-fallback {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    }
  `;

  const style = document.createElement('style');
  style.textContent = fontOptimizationCSS;
  style.setAttribute('data-font-optimization', 'true');
  document.head.appendChild(style);
};

// Critical CSS optimization utility - main initialization function
export function initializeCriticalCSS() {
  // Inline critical CSS for above-the-fold content
  const enhancedCriticalCSS = `
    /* Critical CSS for initial render */
    body {
      margin: 0;
      font-family: 'Inter', system-ui, sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    /* Critical layout styles */
    .chakra-ui-light {
      color-scheme: light;
      --chakra-colors-gray-50: #F7FAFC;
      --chakra-colors-gray-100: #EDF2F7;
      --chakra-colors-gray-200: #E2E8F0;
      --chakra-colors-blue-500: #3182CE;
    }
    
    .chakra-ui-dark {
      color-scheme: dark;
      --chakra-colors-gray-50: #1A202C;
      --chakra-colors-gray-100: #2D3748;
      --chakra-colors-gray-200: #4A5568;
      --chakra-colors-blue-500: #63B3ED;
    }
    
    /* Critical navigation styles */
    .navbar-critical {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      background: white;
      border-bottom: 1px solid #E2E8F0;
      height: 80px;
    }
    
    /* Loading state styles */
    .loading-skeleton {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
    }
    
    @keyframes loading {
      0% { background-position: 200% 0; }
      100% { background-position: -200% 0; }
    }
    
    /* Reduce motion for accessibility */
    @media (prefers-reduced-motion: reduce) {
      *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    }
  `;

  // Inject critical CSS inline
  const styleElement = document.createElement('style');
  styleElement.textContent = enhancedCriticalCSS;
  styleElement.setAttribute('data-critical', 'true');
  document.head.insertBefore(styleElement, document.head.firstChild);

  // Preload fonts
  preloadCriticalFonts();

  // Defer non-critical CSS
  deferNonCriticalStylesheets();
}

function preloadCriticalFonts() {
  const fontPreloads = [
    {
      href: 'https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2',
      type: 'font/woff2',
      crossorigin: 'anonymous'
    },
    {
      href: 'https://fonts.gstatic.com/s/montserrat/v25/JTUHjIg1_i6t8kCHKm4532VJOt5-QNFgpCtr6Ew-.woff2',
      type: 'font/woff2',
      crossorigin: 'anonymous'
    }
  ];

  fontPreloads.forEach(font => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = font.href;
    link.as = 'font';
    link.type = font.type;
    link.crossOrigin = font.crossorigin;
    document.head.appendChild(link);
  });
}

function deferNonCriticalStylesheets() {
  // Find all non-critical stylesheets
  const stylesheets = document.querySelectorAll('link[rel="stylesheet"]:not([data-critical])');
  
  stylesheets.forEach(stylesheet => {
    const link = stylesheet as HTMLLinkElement;
    
    // Skip if already processed
    if (link.dataset.deferred) return;
    
    // Mark as deferred
    link.dataset.deferred = 'true';
    
    // Change to preload and switch back to stylesheet when loaded
    link.rel = 'preload';
    link.as = 'style';
    
    link.onload = () => {
      link.rel = 'stylesheet';
      link.onload = null;
    };
  });
}

// Utility to load CSS asynchronously
export function loadCSSAsync(href: string, id?: string) {
  return new Promise<void>((resolve, reject) => {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    if (id) link.id = id;
    
    link.onload = () => resolve();
    link.onerror = () => reject(new Error(`Failed to load CSS: ${href}`));
    
    document.head.appendChild(link);
  });
}

// Utility to inline small CSS files
export function inlineSmallCSS(css: string, id: string) {
  const existingStyle = document.getElementById(id);
  if (existingStyle) return;
  
  const style = document.createElement('style');
  style.id = id;
  style.textContent = css;
  document.head.appendChild(style);
}

// Critical CSS for different page types
export const criticalCSSMap = {
  dashboard: `
    /* Dashboard critical styles */
    .dashboard-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1rem;
    }
  `,
  
  coverLetter: `
    /* Cover letter critical styles */
    .editor-container {
      min-height: 400px;
      border: 1px solid #E2E8F0;
      border-radius: 8px;
    }
  `,
  
  jobs: `
    /* Jobs page critical styles */
    .job-card {
      border: 1px solid #E2E8F0;
      border-radius: 8px;
      padding: 1rem;
      margin-bottom: 1rem;
    }
  `
};

// Load page-specific critical CSS
export function loadPageCriticalCSS(pageType: keyof typeof criticalCSSMap) {
  const css = criticalCSSMap[pageType];
  if (css) {
    inlineSmallCSS(css, `critical-css-${pageType}`);
  }
} 