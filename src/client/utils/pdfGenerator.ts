import { Resume, ResumeTemplate } from '../../shared/types';
import { templateManager } from './templateManager';

/**
 * Generate single column layout (traditional resume format)
 */
const generateSingleColumnLayout = (
  headerHTML: string,
  summaryHTML: string,
  experienceHTML: string,
  educationHTML: string,
  skillsHTML: string,
  certificationsHTML: string
): string => {
  return `
    ${headerHTML}
    ${summaryHTML}
    ${experienceHTML}
    ${educationHTML}
    ${skillsHTML}
    ${certificationsHTML}
  `;
};

/**
 * Generate two column layout (sidebar left, main content right)
 * Matches template renderer: Left column (35%) = Contact + Skills, Right column (65%) = Main content
 */
const generateTwoColumnLayout = (
  headerHTML: string,
  summaryHTML: string,
  experienceHTML: string,
  educationHTML: string,
  skillsHTML: string,
  certificationsHTML: string
): string => {
  return `
    <div class="two-column-container">
      <!-- Left Column: Contact + Skills (35%) -->
      <div class="left-column">
        ${headerHTML}
        ${skillsHTML}
        ${certificationsHTML}
      </div>
      <!-- Right Column: Main Content (65%) -->
      <div class="right-column">
        ${summaryHTML}
        ${experienceHTML}
        ${educationHTML}
      </div>
    </div>
  `;
};

/**
 * Generate sidebar layout (sidebar left, main content right)
 * Matches template renderer: Sidebar (30%) = Contact + Skills, Main content (70%) = Summary + Experience + Education
 */
const generateSidebarLayout = (
  headerHTML: string,
  summaryHTML: string,
  experienceHTML: string,
  educationHTML: string,
  skillsHTML: string,
  certificationsHTML: string
): string => {
  return `
    <div class="sidebar-container">
      <!-- Sidebar: Contact + Skills (30%) -->
      <div class="sidebar">
        ${headerHTML}
        ${skillsHTML}
        ${certificationsHTML}
      </div>
      <!-- Main Content: Summary + Experience + Education (70%) -->
      <div class="main-content">
        ${summaryHTML}
        ${experienceHTML}
        ${educationHTML}
      </div>
    </div>
  `;
};

/**
 * Get the correct template for a resume with proper fallback
 */
const getResumeTemplate = (resume: Resume, providedTemplate?: ResumeTemplate): ResumeTemplate => {
  // If a template is explicitly provided, use it
  if (providedTemplate) {
    return providedTemplate;
  }

  // Try to get template from resume's templateId
  if (resume.templateId) {
    const template = templateManager.getTemplateForResume(resume.id, resume.templateId);
    if (template) {
      return template;
    }
  }

  // Fallback to default template
  return templateManager.getTemplateWithFallback();
};

/**
 * Generate comprehensive CSS styles for exact template matching
 */
const generateTemplateCSS = (template: ResumeTemplate): string => {
  const styles = template.styles;
  const {
    primaryColor,
    secondaryColor = '#64748b',
    accentColor = primaryColor,
    textColor = '#1e293b',
    backgroundColor = '#ffffff',
    fontFamily,
    headerFontFamily = fontFamily,
    fontSize = '11px',
    lineHeight = '1.5',
    fontWeight = '400',
    headerFontWeight = '600',
    layout,
    spacing = '16px',
    sectionSpacing = '24px',
    padding = '32px',
    borderStyle = 'solid',
    borderWidth = '1px',
    borderRadius = '0px',
    shadowStyle = 'none',
    headerStyle = 'underline',
    skillsStyle = 'tags',
    dateStyle = 'right',
    customCSS = ''
  } = styles;

  return `
    /* Base styles */
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: ${fontFamily};
      font-size: ${fontSize};
      line-height: ${lineHeight};
      font-weight: ${fontWeight};
      color: ${textColor};
      background: ${backgroundColor};
    }

    /* Typography */
    h1, h2, h3, h4, h5, h6 {
      font-family: ${headerFontFamily};
      color: ${primaryColor};
      font-weight: ${headerFontWeight};
      margin-bottom: ${spacing};
    }

    h1 {
      font-size: calc(${fontSize} * 2.2);
      margin-bottom: calc(${spacing} * 1.5);
    }
    h2 {
      font-size: calc(${fontSize} * 1.6);
      margin-bottom: ${spacing};
    }
    h3 {
      font-size: calc(${fontSize} * 1.3);
      margin-bottom: calc(${spacing} * 0.75);
    }
    h4 {
      font-size: calc(${fontSize} * 1.1);
      margin-bottom: calc(${spacing} * 0.5);
    }

    p, div, span {
      font-family: ${fontFamily};
      color: ${textColor};
    }

    /* Layout-specific styles */
    .resume-container {
      max-width: 8.5in;
      margin: 0 auto;
      padding: ${padding};
      background: ${backgroundColor};
      border: ${borderWidth} ${borderStyle} ${secondaryColor};
      border-radius: ${borderRadius};
      box-shadow: ${shadowStyle};
    }

    /* Section Headers - Dynamic styling based on headerStyle */
    .section-header {
      font-family: ${headerFontFamily};
      color: ${primaryColor};
      font-weight: ${headerFontWeight};
      font-size: calc(${fontSize} * 1.3);
      margin-bottom: ${spacing};
      margin-top: ${sectionSpacing};
      text-transform: uppercase;
      letter-spacing: 0.5px;
      ${headerStyle === 'underline' ? `
        border-bottom: 2px solid ${primaryColor};
        padding-bottom: calc(${spacing} * 0.5);
      ` : ''}
      ${headerStyle === 'background' ? `
        background: ${primaryColor};
        color: ${backgroundColor};
        padding: calc(${spacing} * 0.75) ${spacing};
        border-radius: calc(${borderRadius} / 2);
      ` : ''}
      ${headerStyle === 'border' ? `
        border: 2px solid ${primaryColor};
        padding: calc(${spacing} * 0.5) ${spacing};
        border-radius: calc(${borderRadius} / 2);
      ` : ''}
      ${headerStyle === 'minimal' ? `
        border-left: 4px solid ${primaryColor};
        padding-left: ${spacing};
      ` : ''}
    }

    .section-header:first-child {
      margin-top: 0;
    }

    /* Contact information */
    .contact-info {
      text-align: center;
      margin-bottom: ${sectionSpacing};
      padding: ${spacing};
      ${shadowStyle !== 'none' ? `
        background: ${backgroundColor};
        border-radius: ${borderRadius};
        box-shadow: ${shadowStyle};
      ` : ''}
    }

    .contact-info h1 {
      font-family: ${headerFontFamily};
      color: ${primaryColor};
      font-size: calc(${fontSize} * 2.5);
      font-weight: ${headerFontWeight};
      margin-bottom: ${spacing};
    }

    .contact-details {
      font-size: calc(${fontSize} * 0.9);
      color: ${secondaryColor};
      line-height: ${lineHeight};
    }

    /* Experience and Education items */
    .experience-item, .education-item {
      margin-bottom: ${spacing};
      padding: calc(${spacing} * 0.75);
      ${shadowStyle !== 'none' ? `
        background: ${backgroundColor};
        border-radius: calc(${borderRadius} / 2);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      ` : ''}
    }

    .item-header {
      display: flex;
      ${dateStyle === 'right' ? 'justify-content: space-between;' : 'flex-direction: column;'}
      align-items: ${dateStyle === 'right' ? 'baseline' : 'flex-start'};
      margin-bottom: calc(${spacing} * 0.5);
    }

    .item-title {
      font-family: ${headerFontFamily};
      font-weight: ${headerFontWeight};
      color: ${primaryColor};
      font-size: calc(${fontSize} * 1.1);
    }

    .item-company, .item-institution {
      font-weight: 600;
      color: ${secondaryColor};
      font-size: ${fontSize};
      ${dateStyle === 'separate-line' ? `margin-top: calc(${spacing} * 0.25);` : ''}
    }

    .item-date {
      font-size: calc(${fontSize} * 0.85);
      color: ${secondaryColor};
      font-style: italic;
      ${dateStyle === 'left' ? 'order: -1; margin-bottom: calc(${spacing} * 0.25);' : ''}
      ${dateStyle === 'inline' ? 'display: inline; margin-left: calc(${spacing} * 0.5);' : ''}
    }

    .item-description {
      margin-top: calc(${spacing} * 0.5);
      font-size: ${fontSize};
      line-height: ${lineHeight};
      color: ${textColor};
    }

    /* Skills - Dynamic styling based on skillsStyle */
    .skills-list {
      ${skillsStyle === 'tags' ? `
        display: flex;
        flex-wrap: wrap;
        gap: calc(${spacing} * 0.5);
      ` : ''}
      ${skillsStyle === 'grid' ? `
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: calc(${spacing} * 0.5);
      ` : ''}
      ${skillsStyle === 'list' ? `
        display: flex;
        flex-direction: column;
        gap: calc(${spacing} * 0.25);
      ` : ''}
      ${skillsStyle === 'inline' ? `
        display: flex;
        flex-wrap: wrap;
        gap: calc(${spacing} * 0.25);
      ` : ''}
    }

    .skill-item {
      ${skillsStyle === 'tags' ? `
        background: ${accentColor}20;
        color: ${primaryColor};
        padding: calc(${spacing} * 0.25) calc(${spacing} * 0.5);
        border-radius: calc(${borderRadius} / 2);
        font-size: calc(${fontSize} * 0.9);
        font-weight: 500;
        border: 1px solid ${accentColor}40;
      ` : ''}
      ${skillsStyle === 'grid' ? `
        background: ${backgroundColor};
        color: ${textColor};
        padding: calc(${spacing} * 0.5);
        border-radius: calc(${borderRadius} / 2);
        font-size: ${fontSize};
        border: 1px solid ${secondaryColor}40;
        text-align: center;
      ` : ''}
      ${skillsStyle === 'list' ? `
        color: ${textColor};
        font-size: ${fontSize};
        padding: calc(${spacing} * 0.25) 0;
        border-bottom: 1px solid ${secondaryColor}20;
      ` : ''}
      ${skillsStyle === 'inline' ? `
        color: ${primaryColor};
        font-size: ${fontSize};
        font-weight: 500;
      ` : ''}
    }

    /* Layout-specific adjustments */

    /* Two-Column Layout: Left sidebar (35%) + Right main content (65%) */
    .two-column-container {
      display: flex;
      gap: ${sectionSpacing};
      align-items: flex-start;
    }

    .two-column-container .left-column {
      width: 35%;
      background: ${primaryColor};
      color: ${backgroundColor};
      padding: ${spacing};
      border-radius: ${borderRadius};
      ${shadowStyle !== 'none' ? `box-shadow: ${shadowStyle};` : ''}
    }

    .two-column-container .right-column {
      width: 65%;
      padding-left: ${spacing};
    }

    .two-column-container .left-column .contact-info {
      text-align: center;
      margin-bottom: ${sectionSpacing};
      padding: 0;
      background: transparent;
      box-shadow: none;
    }

    .two-column-container .left-column .contact-info h1 {
      color: ${backgroundColor};
      font-size: calc(${fontSize} * 2);
    }

    .two-column-container .left-column .contact-details {
      color: ${backgroundColor};
      opacity: 0.9;
    }

    .two-column-container .left-column .section-header {
      color: ${backgroundColor};
      border-bottom: 1px solid ${backgroundColor}40;
      font-size: calc(${fontSize} * 1.1);
    }

    .two-column-container .left-column .skill-item {
      background: ${backgroundColor}20;
      color: ${backgroundColor};
      border: 1px solid ${backgroundColor}40;
    }

    /* Sidebar Layout: Left sidebar (30%) + Right main content (70%) */
    .sidebar-container {
      display: flex;
      gap: ${sectionSpacing};
      align-items: flex-start;
    }

    .sidebar-container .sidebar {
      width: 30%;
      background: ${accentColor}15;
      padding: ${spacing};
      border-radius: ${borderRadius};
      border-right: 3px solid ${primaryColor};
      ${shadowStyle !== 'none' ? `box-shadow: ${shadowStyle};` : ''}
    }

    .sidebar-container .main-content {
      width: 70%;
      padding-left: ${spacing};
    }

    .sidebar-container .sidebar .contact-info {
      text-align: center;
      margin-bottom: ${sectionSpacing};
      padding: 0;
      background: transparent;
      box-shadow: none;
    }

    .sidebar-container .sidebar .contact-info h1 {
      color: ${primaryColor};
      font-size: calc(${fontSize} * 1.8);
    }

    .sidebar-container .sidebar .contact-details {
      color: ${primaryColor};
      opacity: 0.8;
    }

    .sidebar-container .sidebar .section-header {
      color: ${primaryColor};
      font-size: calc(${fontSize} * 1.1);
      ${headerStyle === 'underline' ? `border-bottom: 1px solid ${primaryColor};` : ''}
      ${headerStyle === 'background' ? `
        background: ${primaryColor};
        color: ${backgroundColor};
        padding: calc(${spacing} * 0.5);
        border-radius: calc(${borderRadius} / 2);
      ` : ''}
    }

    .sidebar-container .sidebar .skills-list {
      ${skillsStyle === 'tags' ? `flex-direction: column;` : ''}
    }

    .sidebar-container .sidebar .skill-item {
      ${skillsStyle === 'tags' ? `
        background: ${primaryColor}20;
        color: ${primaryColor};
        border: 1px solid ${primaryColor}40;
        margin-bottom: calc(${spacing} * 0.25);
      ` : ''}
    }

    /* Summary Section */
    .summary-section {
      margin-bottom: ${sectionSpacing};
    }

    .summary-text {
      margin: 0;
      text-align: justify;
      line-height: ${lineHeight};
      color: ${textColor};
    }

    /* Experience Section */
    .experience-section {
      margin-bottom: ${sectionSpacing};
    }

    .achievements-list {
      margin: calc(${spacing} * 0.5) 0 0 calc(${spacing} * 1.25);
      padding: 0;
    }

    .achievement-item {
      margin-bottom: calc(${spacing} * 0.25);
      color: ${textColor};
      line-height: ${lineHeight};
    }

    /* Education Section */
    .education-section {
      margin-bottom: ${sectionSpacing};
    }

    .item-institution {
      font-weight: 600;
      color: ${secondaryColor};
      font-size: ${fontSize};
      ${dateStyle === 'separate-line' ? `margin-top: calc(${spacing} * 0.25);` : ''}
    }

    .item-gpa {
      font-size: calc(${fontSize} * 0.85);
      color: ${secondaryColor};
      margin-top: calc(${spacing} * 0.25);
    }

    /* Skills Section */
    .skills-section {
      margin-bottom: ${sectionSpacing};
    }

    /* Certifications Section */
    .certifications-section {
      margin-bottom: ${sectionSpacing};
    }

    .certification-item {
      margin-bottom: calc(${spacing} * 0.75);
      padding: calc(${spacing} * 0.5);
      ${shadowStyle !== 'none' ? `
        background: ${backgroundColor};
        border-radius: calc(${borderRadius} / 2);
        border-left: 3px solid ${accentColor};
      ` : ''}
    }

    .certification-name {
      font-weight: ${headerFontWeight};
      color: ${primaryColor};
      font-size: calc(${fontSize} * 1.05);
    }

    .certification-issuer {
      color: ${secondaryColor};
      font-size: calc(${fontSize} * 0.9);
      margin-top: calc(${spacing} * 0.25);
    }

    /* Print styles */
    @media print {
      body {
        margin: 0;
        padding: 0;
        background: white !important;
      }
      .resume-container {
        padding: 0.5in;
        max-width: none;
        box-shadow: none !important;
        border: none !important;
      }
      @page {
        margin: 0.5in;
        size: letter;
      }
    }

    /* Custom CSS from template */
    ${customCSS}
  `;
};

/**
 * Generate HTML content for the resume based on template
 */
const generateResumeHTML = (resume: Resume, template?: ResumeTemplate): string => {
  // Get the correct template with proper fallback
  const resolvedTemplate = getResumeTemplate(resume, template);
  const { primaryColor, layout } = resolvedTemplate.styles;

  const personalInfo = resume.personalInfo || {} as any;
  const experience = resume.experience || [];
  const education = resume.education || [];
  const skills = resume.skills || [];
  const certifications = resume.certifications || [];

  // Header section with CSS classes
  const headerHTML = `
    <div class="contact-info">
      <h1>${personalInfo.fullName || 'Resume'}</h1>
      <div class="contact-details">
        ${personalInfo.email ? `📧 ${personalInfo.email}` : ''}
        ${personalInfo.phone ? ` | 📞 ${personalInfo.phone}` : ''}
        ${personalInfo.location ? ` | 📍 ${personalInfo.location}` : ''}
        ${personalInfo.linkedIn ? ` | 🔗 ${personalInfo.linkedIn}` : ''}
        ${personalInfo.website ? ` | 🌐 ${personalInfo.website}` : ''}
      </div>
    </div>
  `;

  // Summary section with CSS classes
  const summaryHTML = resume.summary ? `
    <div class="summary-section">
      <h2 class="section-header">PROFESSIONAL SUMMARY</h2>
      <p class="summary-text">${resume.summary}</p>
    </div>
  ` : '';

  // Experience section with CSS classes
  const experienceHTML = experience.length > 0 ? `
    <div class="experience-section">
      <h2 class="section-header">EXPERIENCE</h2>
      ${experience.map(exp => `
        <div class="experience-item">
          <div class="item-header">
            <h3 class="item-title">${exp.position}</h3>
            <span class="item-date">${exp.startDate} - ${exp.current ? 'Present' : exp.endDate}</span>
          </div>
          <div class="item-company">${exp.company}</div>
          ${exp.description ? `<p class="item-description">${exp.description}</p>` : ''}
          ${exp.achievements && exp.achievements.length > 0 ? `
            <ul class="achievements-list">
              ${exp.achievements.map(achievement => `<li class="achievement-item">${achievement}</li>`).join('')}
            </ul>
          ` : ''}
        </div>
      `).join('')}
    </div>
  ` : '';

  // Education section with CSS classes
  const educationHTML = education.length > 0 ? `
    <div class="education-section">
      <h2 class="section-header">EDUCATION</h2>
      ${education.map(edu => `
        <div class="education-item">
          <div class="item-header">
            <h3 class="item-title">${edu.degree} in ${edu.field}</h3>
            <span class="item-date">${edu.startDate} - ${edu.current ? 'Present' : edu.endDate}</span>
          </div>
          <div class="item-institution">${edu.institution}</div>
          ${edu.gpa ? `<div class="item-gpa">GPA: ${edu.gpa}</div>` : ''}
        </div>
      `).join('')}
    </div>
  ` : '';

  // Skills section with CSS classes
  const skillsHTML = skills.length > 0 ? `
    <div class="skills-section">
      <h2 class="section-header">SKILLS</h2>
      <div class="skills-list">
        ${skills.map(skill => `
          <span class="skill-item">${skill}</span>
        `).join('')}
      </div>
    </div>
  ` : '';

  // Certifications section with CSS classes
  const certificationsHTML = certifications.length > 0 ? `
    <div class="certifications-section">
      <h2 class="section-header">CERTIFICATIONS</h2>
      ${certifications.map(cert => `
        <div class="certification-item">
          <div class="certification-name">${cert.name}</div>
          <div class="certification-issuer">${cert.issuer} - ${cert.date}</div>
        </div>
      `).join('')}
    </div>
  ` : '';

  // Generate layout based on template style
  switch (layout) {
    case 'two-column':
      return generateTwoColumnLayout(headerHTML, summaryHTML, experienceHTML, educationHTML, skillsHTML, certificationsHTML);
    case 'sidebar':
      return generateSidebarLayout(headerHTML, summaryHTML, experienceHTML, educationHTML, skillsHTML, certificationsHTML);
    default:
      return generateSingleColumnLayout(headerHTML, summaryHTML, experienceHTML, educationHTML, skillsHTML, certificationsHTML);
  }
};

/**
 * Generate a PDF preview of the resume
 */
export const generateResumePDF = async (
  resume: Resume,
  template?: ResumeTemplate
): Promise<string | null> => {
  try {
    // Get the correct template with proper fallback
    const resolvedTemplate = getResumeTemplate(resume, template);

    // Store template mapping for this resume
    templateManager.setTemplateForResume(resume.id, resolvedTemplate.id);

    // Create a temporary container for the resume HTML
    const container = document.createElement('div');
    const { fontFamily, primaryColor } = resolvedTemplate.styles;

    container.style.position = 'absolute';
    container.style.left = '-9999px';
    container.style.top = '-9999px';
    container.style.width = '8.5in';
    container.style.height = '11in';
    container.style.backgroundColor = 'white';
    container.style.fontFamily = fontFamily;
    container.style.fontSize = '12px';
    container.style.lineHeight = '1.4';
    container.style.padding = '0.5in';
    container.style.boxSizing = 'border-box';
    container.style.color = '#333';

    // Add comprehensive CSS for exact template matching
    const style = document.createElement('style');
    style.textContent = generateTemplateCSS(resolvedTemplate);
    container.appendChild(style);

    // Generate HTML content based on template style
    const htmlContent = generateResumeHTML(resume, resolvedTemplate);
    const contentDiv = document.createElement('div');
    contentDiv.className = 'resume-container';
    contentDiv.innerHTML = htmlContent;
    container.appendChild(contentDiv);

    // Add to document temporarily
    document.body.appendChild(container);

    // Try to use html2canvas if available
    try {
      const html2canvas = (window as any).html2canvas;
      if (html2canvas) {
        const canvas = await html2canvas(container, {
          width: 816, // 8.5 inches at 96 DPI
          height: 1056, // 11 inches at 96 DPI
          scale: 2,
          backgroundColor: '#ffffff'
        });

        return new Promise((resolve) => {
          canvas.toBlob((blob: Blob | null) => {
            if (blob) {
              const url = URL.createObjectURL(blob);
              resolve(url);
            } else {
              resolve(null);
            }
          }, 'image/png');
        });
      }
    } catch (error) {
      console.warn('html2canvas not available, using alternative method');
    }

    // Alternative: Create a data URL from the HTML content
    const htmlBlob = new Blob([`
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>${resume.title}</title>
          <style>
            ${generateTemplateCSS(resolvedTemplate)}
          </style>
        </head>
        <body>
          <div class="resume-container">
            ${htmlContent}
          </div>
        </body>
      </html>
    `], { type: 'text/html' });

    const url = URL.createObjectURL(htmlBlob);

    // Clean up
    document.body.removeChild(container);

    return url;

  } catch (error) {
    console.error('Error generating PDF:', error);
    return null;
  }
};

/**
 * Generate a simple preview image for the resume
 */
export const generateResumePreview = async (
  resume: Resume,
  template?: ResumeTemplate
): Promise<string | null> => {
  // For now, return the same as PDF generation
  // In a real implementation, this could generate a smaller preview image
  return generateResumePDF(resume, template);
};

/**
 * Print the resume directly
 */
export const printResume = async (
  resume: Resume,
  template?: ResumeTemplate
): Promise<void> => {
  try {
    // Get the correct template with proper fallback
    const resolvedTemplate = getResumeTemplate(resume, template);

    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      throw new Error('Unable to open print window');
    }

    // Generate HTML content with resolved template
    const htmlContent = generateResumeHTML(resume, resolvedTemplate);

    // Write the HTML content to the new window
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>${resume.title || 'Resume'}</title>
          <style>
            ${generateTemplateCSS(resolvedTemplate)}
            .no-print { display: none; }
          </style>
        </head>
        <body>
          <div class="resume-container">
            ${htmlContent}
          </div>
        </body>
      </html>
    `);

    printWindow.document.close();

    // Wait for content to load, then print
    printWindow.onload = () => {
      printWindow.focus();
      printWindow.print();
      printWindow.close();
    };
  } catch (error) {
    console.error('Error printing resume:', error);
    throw error;
  }
};

/**
 * Save resume as PDF file
 */
export const saveResumeAsPDF = async (
  resume: Resume,
  template?: ResumeTemplate
): Promise<void> => {
  try {
    // Get the correct template with proper fallback
    const resolvedTemplate = getResumeTemplate(resume, template);

    // Generate HTML content with resolved template
    const htmlContent = generateResumeHTML(resume, resolvedTemplate);

    // Create a blob with the HTML content
    const htmlBlob = new Blob([`
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>${resume.title || 'Resume'}</title>
          <style>
            ${generateTemplateCSS(resolvedTemplate)}
          </style>
        </head>
        <body>
          <div class="resume-container">
            ${htmlContent}
          </div>
        </body>
      </html>
    `], { type: 'text/html' });

    // Create download link
    const url = URL.createObjectURL(htmlBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${resume.title || 'resume'}.html`;

    // Trigger download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error saving resume as PDF:', error);
    throw error;
  }
};
