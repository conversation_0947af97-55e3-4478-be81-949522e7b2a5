// SEO optimization utilities for CareerDart

interface SEOMetaData {
  title: string;
  description: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
}

interface StructuredData {
  '@context': string;
  '@type': string;
  [key: string]: any;
}

class SEOOptimizer {
  private defaultMeta: SEOMetaData = {
    title: 'CareerDart - AI-Powered Career Tools',
    description: 'Transform your job search with AI-powered tools for creating professional cover letters, optimizing resumes, and managing job applications.',
    keywords: 'AI cover letter generator, resume optimization, job search tools, career development',
    image: 'https://careerdart.com/images/og-image.png',
    url: 'https://careerdart.com',
    type: 'website',
    author: 'CareerDart'
  };

  private baseUrl = 'https://careerdart.com';

  constructor() {
    this.initializeGlobalSEO();
  }

  private initializeGlobalSEO() {
    // Add canonical link if not present
    if (!document.querySelector('link[rel="canonical"]')) {
      this.setCanonicalURL();
    }

    // Set up default structured data
    this.addDefaultStructuredData();

    // Add hreflang attributes
    this.addHreflangAttributes();
  }

  public setPageSEO(route: string, customMeta?: Partial<SEOMetaData>) {
    const pageMeta = this.getPageMeta(route);
    const finalMeta = { ...this.defaultMeta, ...pageMeta, ...customMeta };
    
    this.updateMetaTags(finalMeta);
    this.updateCanonicalURL(route);
    this.addPageStructuredData(route, finalMeta);
  }

  private getPageMeta(route: string): Partial<SEOMetaData> {
    const pageMetas: Record<string, Partial<SEOMetaData>> = {
      '/': {
        title: 'CareerDart - AI-Powered Career Tools | Cover Letters, Resumes & Job Search',
        description: 'Transform your job search with AI-powered tools for creating professional cover letters, optimizing resumes, and managing job applications. Get hired faster with CareerDart.',
        keywords: 'AI cover letter generator, resume builder, job search platform, career development tools, interview preparation',
        type: 'website'
      },
      '/dashboard': {
        title: 'Dashboard - CareerDart | Your Career Command Center',
        description: 'Access your personalized career dashboard with AI-powered insights, job tracking, and career progress analytics.',
        keywords: 'career dashboard, job tracking, career analytics, professional development',
        type: 'webapp'
      },
      '/cover-letters': {
        title: 'AI Cover Letter Generator - CareerDart | Create Professional Cover Letters',
        description: 'Generate tailored, professional cover letters in minutes with our AI-powered tool. Stand out to employers with personalized cover letters.',
        keywords: 'AI cover letter generator, professional cover letters, job application letters, cover letter templates',
        type: 'webapp'
      },
      '/resume': {
        title: 'Resume Builder & Optimizer - CareerDart | Create ATS-Friendly Resumes',
        description: 'Build and optimize your resume with AI-powered suggestions. Create ATS-friendly resumes that get noticed by recruiters.',
        keywords: 'resume builder, ATS-friendly resume, resume optimization, professional resume templates',
        type: 'webapp'
      },
      '/jobs': {
        title: 'Job Search & Management - CareerDart | Find Your Dream Job',
        description: 'Search and manage job applications with intelligent tracking and AI-powered matching. Find opportunities that fit your career goals.',
        keywords: 'job search, job application tracking, career opportunities, job matching',
        type: 'webapp'
      },
      '/interview': {
        title: 'Interview Preparation - CareerDart | Practice & Excel in Interviews',
        description: 'Prepare for interviews with AI-powered practice sessions, common questions, and personalized feedback.',
        keywords: 'interview preparation, interview practice, job interview tips, interview coaching',
        type: 'webapp'
      },
      '/learning': {
        title: 'Career Learning - CareerDart | Professional Development Resources',
        description: 'Access curated learning resources, skill development courses, and career advancement guides.',
        keywords: 'career development, professional learning, skill building, career advancement',
        type: 'webapp'
      },
      '/tracker': {
        title: 'Application Tracker - CareerDart | Manage Your Job Applications',
        description: 'Track and manage all your job applications in one place with intelligent insights and follow-up reminders.',
        keywords: 'job application tracker, application management, job search organization',
        type: 'webapp'
      }
    };

    return pageMetas[route] || {};
  }

  private updateMetaTags(meta: SEOMetaData) {
    // Update title
    document.title = meta.title;
    this.updateMetaTag('name', 'description', meta.description);
    this.updateMetaTag('name', 'keywords', meta.keywords || '');
    this.updateMetaTag('name', 'author', meta.author || 'CareerDart');

    // Update Open Graph tags
    this.updateMetaTag('property', 'og:title', meta.title);
    this.updateMetaTag('property', 'og:description', meta.description);
    this.updateMetaTag('property', 'og:image', meta.image || this.defaultMeta.image!);
    this.updateMetaTag('property', 'og:url', meta.url || this.defaultMeta.url!);
    this.updateMetaTag('property', 'og:type', meta.type || 'website');

    // Update Twitter Card tags
    this.updateMetaTag('property', 'twitter:title', meta.title);
    this.updateMetaTag('property', 'twitter:description', meta.description);
    this.updateMetaTag('property', 'twitter:image', meta.image || this.defaultMeta.image!);
    this.updateMetaTag('property', 'twitter:url', meta.url || this.defaultMeta.url!);

    // Update publication times if provided
    if (meta.publishedTime) {
      this.updateMetaTag('property', 'article:published_time', meta.publishedTime);
    }
    if (meta.modifiedTime) {
      this.updateMetaTag('property', 'article:modified_time', meta.modifiedTime);
    }
  }

  private updateMetaTag(attribute: string, name: string, content: string) {
    let element = document.querySelector(`meta[${attribute}="${name}"]`) as HTMLMetaElement;
    
    if (!element) {
      element = document.createElement('meta');
      element.setAttribute(attribute, name);
      document.head.appendChild(element);
    }
    
    element.content = content;
  }

  private setCanonicalURL() {
    const currentURL = window.location.href;
    this.updateCanonicalURL(window.location.pathname);
  }

  private updateCanonicalURL(path: string) {
    const canonicalURL = `${this.baseUrl}${path}`;
    let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
    
    if (!canonical) {
      canonical = document.createElement('link');
      canonical.rel = 'canonical';
      document.head.appendChild(canonical);
    }
    
    canonical.href = canonicalURL;
  }

  private addHreflangAttributes() {
    // Add English hreflang
    let hreflang = document.querySelector('link[hreflang="en"]') as HTMLLinkElement;
    if (!hreflang) {
      hreflang = document.createElement('link');
      hreflang.rel = 'alternate';
      hreflang.hreflang = 'en';
      hreflang.href = this.baseUrl;
      document.head.appendChild(hreflang);
    }
  }

  private addDefaultStructuredData() {
    const defaultStructuredData: StructuredData = {
      '@context': 'https://schema.org',
      '@type': 'WebApplication',
      name: 'CareerDart',
      description: 'AI-powered career tools for creating professional cover letters, optimizing resumes, and managing job applications',
      url: this.baseUrl,
      applicationCategory: 'BusinessApplication',
      operatingSystem: 'Web',
      offers: {
        '@type': 'Offer',
        category: 'Career Development Tools'
      },
      publisher: {
        '@type': 'Organization',
        name: 'CareerDart',
        url: this.baseUrl,
        logo: {
          '@type': 'ImageObject',
          url: `${this.baseUrl}/images/logo.png`
        }
      },
      featureList: [
        'AI Cover Letter Generator',
        'Resume Optimization',
        'Job Search Management',
        'Interview Preparation',
        'Career Tracking',
        'Professional Development'
      ]
    };

    this.addStructuredData('default-app-data', defaultStructuredData);
  }

  private addPageStructuredData(route: string, meta: SEOMetaData) {
    const pageStructuredData = this.getPageStructuredData(route, meta);
    if (pageStructuredData) {
      this.addStructuredData(`page-data-${route.replace(/\//g, '-')}`, pageStructuredData);
    }
  }

  private getPageStructuredData(route: string, meta: SEOMetaData): StructuredData | null {
    const structuredDataMap: Record<string, StructuredData> = {
      '/cover-letters': {
        '@context': 'https://schema.org',
        '@type': 'SoftwareApplication',
        name: 'AI Cover Letter Generator',
        description: meta.description,
        url: `${this.baseUrl}${route}`,
        applicationCategory: 'BusinessApplication',
        operatingSystem: 'Web',
        offers: {
          '@type': 'Offer',
          category: 'Cover Letter Generation'
        }
      },
      '/resume': {
        '@context': 'https://schema.org',
        '@type': 'SoftwareApplication',
        name: 'Resume Builder & Optimizer',
        description: meta.description,
        url: `${this.baseUrl}${route}`,
        applicationCategory: 'BusinessApplication',
        operatingSystem: 'Web',
        offers: {
          '@type': 'Offer',
          category: 'Resume Building'
        }
      },
      '/jobs': {
        '@context': 'https://schema.org',
        '@type': 'JobPosting',
        name: 'Job Search Platform',
        description: meta.description,
        url: `${this.baseUrl}${route}`,
        hiringOrganization: {
          '@type': 'Organization',
          name: 'CareerDart'
        }
      }
    };

    return structuredDataMap[route] || null;
  }

  private addStructuredData(id: string, data: StructuredData) {
    // Remove existing structured data with same ID
    const existing = document.getElementById(id);
    if (existing) {
      existing.remove();
    }

    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.id = id;
    script.textContent = JSON.stringify(data);
    document.head.appendChild(script);
  }

  public addBreadcrumbs(breadcrumbs: Array<{ name: string; url: string }>) {
    const breadcrumbData: StructuredData = {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: breadcrumbs.map((crumb, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        name: crumb.name,
        item: `${this.baseUrl}${crumb.url}`
      }))
    };

    this.addStructuredData('breadcrumbs', breadcrumbData);
  }

  public addJobPosting(jobData: {
    title: string;
    company: string;
    location: string;
    description: string;
    salary?: string;
    datePosted: string;
  }) {
    const jobStructuredData: StructuredData = {
      '@context': 'https://schema.org',
      '@type': 'JobPosting',
      title: jobData.title,
      description: jobData.description,
      hiringOrganization: {
        '@type': 'Organization',
        name: jobData.company
      },
      jobLocation: {
        '@type': 'Place',
        address: jobData.location
      },
      datePosted: jobData.datePosted,
      ...(jobData.salary && {
        baseSalary: {
          '@type': 'MonetaryAmount',
          currency: 'USD',
          value: jobData.salary
        }
      })
    };

    this.addStructuredData('job-posting', jobStructuredData);
  }

  public generateSitemap(): string {
    const pages = [
      { url: '/', priority: '1.0', changefreq: 'weekly' },
      { url: '/dashboard', priority: '0.9', changefreq: 'weekly' },
      { url: '/cover-letters', priority: '0.9', changefreq: 'weekly' },
      { url: '/resume', priority: '0.9', changefreq: 'weekly' },
      { url: '/jobs', priority: '0.8', changefreq: 'daily' },
      { url: '/interview', priority: '0.7', changefreq: 'weekly' },
      { url: '/learning', priority: '0.7', changefreq: 'weekly' },
      { url: '/tracker', priority: '0.6', changefreq: 'weekly' }
    ];

    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${pages.map(page => `  <url>
    <loc>${this.baseUrl}${page.url}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`).join('\n')}
</urlset>`;

    return sitemap;
  }
}

// Create and export the SEO optimizer instance
export const seoOptimizer = new SEOOptimizer();

// React hook for SEO optimization
export const useSEO = () => {
  return {
    setPageSEO: (route: string, meta?: Partial<SEOMetaData>) => 
      seoOptimizer.setPageSEO(route, meta),
    addBreadcrumbs: (breadcrumbs: Array<{ name: string; url: string }>) => 
      seoOptimizer.addBreadcrumbs(breadcrumbs),
    addJobPosting: (jobData: any) => 
      seoOptimizer.addJobPosting(jobData)
  };
};

export default seoOptimizer; 