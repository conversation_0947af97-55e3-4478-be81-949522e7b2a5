/**
 * Accessibility utilities for CareerDart
 * Ensures proper touch targets, focus management, and screen reader support
 */

// Minimum touch target size (44px x 44px as per WCAG guidelines)
export const MIN_TOUCH_TARGET_SIZE = 44;

/**
 * Touch target size props for buttons and interactive elements
 * Ensures minimum 44px touch target for accessibility
 */
export const touchTargetProps = {
  minH: `${MIN_TOUCH_TARGET_SIZE}px`,
  minW: `${MIN_TOUCH_TARGET_SIZE}px`,
  // Add padding if content is smaller than minimum
  px: 3,
  py: 2,
};

/**
 * Focus ring styles for consistent focus indicators
 */
export const focusRingStyles = {
  _focus: {
    boxShadow: "0 0 0 3px rgba(66, 153, 225, 0.6)",
    outline: "none"
  },
  _focusVisible: {
    boxShadow: "0 0 0 3px rgba(66, 153, 225, 0.6)",
    outline: "none"
  }
};

/**
 * Screen reader only styles
 * Hides content visually but keeps it accessible to screen readers
 */
export const srOnlyStyles = {
  position: "absolute",
  left: "-10000px",
  width: "1px",
  height: "1px",
  overflow: "hidden",
  clip: "rect(0, 0, 0, 0)",
  whiteSpace: "nowrap",
  border: 0
};

/**
 * Skip link styles
 * Hidden by default, visible on focus
 */
export const skipLinkStyles = {
  position: "absolute",
  top: "-100px",
  left: "0",
  zIndex: 9999,
  padding: "8px 16px",
  backgroundColor: "white",
  color: "blue.500",
  textDecoration: "none",
  border: "1px solid",
  borderColor: "gray.200",
  borderRadius: "md",
  _focus: {
    top: "0",
    ...focusRingStyles._focus
  }
};

/**
 * Generates ARIA attributes for form fields with errors
 */
export const getFormFieldAria = (fieldName: string, hasError: boolean) => ({
  "aria-describedby": hasError ? `${fieldName}-error` : undefined,
  "aria-invalid": hasError ? "true" : "false"
});

/**
 * Generates ARIA attributes for loading states
 */
export const getLoadingAria = (isLoading: boolean, loadingText: string = "Loading...") => ({
  "aria-busy": isLoading ? "true" : "false",
  "aria-live": isLoading ? "polite" : undefined,
  "aria-label": isLoading ? loadingText : undefined
});

/**
 * Generates ARIA attributes for expandable content
 */
export const getExpandableAria = (isExpanded: boolean, controlsId: string) => ({
  "aria-expanded": isExpanded ? "true" : "false",
  "aria-controls": controlsId
});

/**
 * High contrast mode detection
 */
export const detectHighContrast = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  // Check for Windows High Contrast mode
  const testElement = document.createElement('div');
  testElement.style.border = '1px solid';
  testElement.style.borderColor = 'red green';
  document.body.appendChild(testElement);
  
  const computedStyle = window.getComputedStyle(testElement);
  const isHighContrast = computedStyle.borderTopColor === computedStyle.borderRightColor;
  
  document.body.removeChild(testElement);
  return isHighContrast;
};

/**
 * Announces content to screen readers
 */
export const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.style.position = 'absolute';
  announcement.style.left = '-10000px';
  announcement.style.width = '1px';
  announcement.style.height = '1px';
  announcement.style.overflow = 'hidden';
  
  document.body.appendChild(announcement);
  announcement.textContent = message;
  
  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
};

/**
 * Focus management utilities
 */
export const focusUtils = {
  /**
   * Traps focus within a container
   */
  trapFocus: (container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement.focus();
            e.preventDefault();
          }
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    return () => container.removeEventListener('keydown', handleTabKey);
  },

  /**
   * Returns focus to a previously focused element
   */
  returnFocus: (element: HTMLElement | null) => {
    if (element && typeof element.focus === 'function') {
      element.focus();
    }
  }
};
