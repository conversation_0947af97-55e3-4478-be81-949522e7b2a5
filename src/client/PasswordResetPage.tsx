import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  VStack,
  Heading,
  Text,
  Button,
  Input,
  FormControl,
  FormLabel,
  FormErrorMessage,
  useColorModeValue,
  Icon,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Card,
  CardBody,
} from '@chakra-ui/react';
import { FaLock, FaCheckCircle } from 'react-icons/fa';
import { Link as RouterLink, useSearchParams } from 'react-router-dom';
import { resetPassword } from 'wasp/client/auth';
import { useForm } from 'react-hook-form';

interface ResetPasswordForm {
  password: string;
  confirmPassword: string;
}

export default function PasswordReset() {
  const [searchParams] = useSearchParams();
  const [resetStatus, setResetStatus] = useState<'form' | 'loading' | 'success' | 'error'>('form');
  const [errorMessage, setErrorMessage] = useState('');

  const bgColor = useColorModeValue('white', 'gray.900');
  const cardBg = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'gray.200');

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting }
  } = useForm<ResetPasswordForm>();

  const password = watch('password');
  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      setResetStatus('error');
      setErrorMessage('Invalid reset link. Please request a new password reset.');
    }
  }, [token]);

  const onSubmit = async (data: ResetPasswordForm) => {
    if (!token) return;

    setResetStatus('loading');
    try {
      await resetPassword({ token, password: data.password });
      setResetStatus('success');
    } catch (error: any) {
      setResetStatus('error');
      setErrorMessage(error.message || 'Password reset failed. The link may have expired.');
    }
  };

  if (resetStatus === 'success') {
    return (
      <Box minH="100vh" bg={bgColor} py={16}>
        <Container maxW="md">
          <VStack spacing={8} textAlign="center">
            <Icon as={FaCheckCircle} boxSize={16} color="green.500" />
            <Heading size="lg" color={textColor}>
              Password Reset Successfully!
            </Heading>
            <Text color={useColorModeValue("gray.600", "gray.400")}>
              Your password has been updated. You can now sign in with your new password.
            </Text>
            <Button
              as={RouterLink}
              to="/login"
              colorScheme="blue"
              size="lg"
              borderRadius="xl"
            >
              Sign In to CareerDart
            </Button>
          </VStack>
        </Container>
      </Box>
    );
  }

  if (resetStatus === 'error') {
    return (
      <Box minH="100vh" bg={bgColor} py={16}>
        <Container maxW="md">
          <VStack spacing={8} textAlign="center">
            <Icon as={FaLock} boxSize={16} color="red.500" />
            <Heading size="lg" color={textColor}>
              Reset Link Invalid
            </Heading>
            <Alert status="error" borderRadius="xl">
              <AlertIcon />
              <Box>
                <AlertTitle>Error!</AlertTitle>
                <AlertDescription>{errorMessage}</AlertDescription>
              </Box>
            </Alert>
            <Button
              as={RouterLink}
              to="/login"
              colorScheme="blue"
              variant="outline"
              size="lg"
              borderRadius="xl"
            >
              Back to Login
            </Button>
          </VStack>
        </Container>
      </Box>
    );
  }

  return (
    <Box minH="100vh" bg={bgColor} py={16}>
      <Container maxW="md">
        <VStack spacing={8}>
          <VStack spacing={4} textAlign="center">
            <Icon as={FaLock} boxSize={12} color="blue.500" />
            <Heading size="lg" color={textColor}>
              Reset Your Password
            </Heading>
            <Text color={useColorModeValue("gray.600", "gray.400")}>
              Enter your new password below.
            </Text>
          </VStack>

          <Card w="full" bg={cardBg} shadow="sm" borderRadius="xl">
            <CardBody p={8}>
              <form onSubmit={handleSubmit(onSubmit)}>
                <VStack spacing={6}>
                  <FormControl isInvalid={!!errors.password}>
                    <FormLabel>New Password</FormLabel>
                    <Input
                      type="password"
                      placeholder="Enter your new password"
                      size="lg"
                      borderRadius="xl"
                      {...register('password', {
                        required: 'Password is required',
                        minLength: {
                          value: 8,
                          message: 'Password must be at least 8 characters'
                        }
                      })}
                    />
                    <FormErrorMessage>
                      {errors.password?.message}
                    </FormErrorMessage>
                  </FormControl>

                  <FormControl isInvalid={!!errors.confirmPassword}>
                    <FormLabel>Confirm Password</FormLabel>
                    <Input
                      type="password"
                      placeholder="Confirm your new password"
                      size="lg"
                      borderRadius="xl"
                      {...register('confirmPassword', {
                        required: 'Please confirm your password',
                        validate: (value) =>
                          value === password || 'Passwords do not match'
                      })}
                    />
                    <FormErrorMessage>
                      {errors.confirmPassword?.message}
                    </FormErrorMessage>
                  </FormControl>

                  <Button
                    type="submit"
                    colorScheme="blue"
                    size="lg"
                    width="full"
                    borderRadius="xl"
                    isLoading={isSubmitting || resetStatus === 'loading'}
                    loadingText="Resetting Password..."
                  >
                    Reset Password
                  </Button>

                  <Text fontSize="sm" textAlign="center">
                    <Text as="span" color={useColorModeValue("gray.600", "gray.400")}>
                      Remember your password?{' '}
                    </Text>
                    <Text as={RouterLink} to="/login" color="blue.500" fontWeight="medium">
                      Sign In
                    </Text>
                  </Text>
                </VStack>
              </form>
            </CardBody>
          </Card>
        </VStack>
      </Container>
    </Box>
  );
}
