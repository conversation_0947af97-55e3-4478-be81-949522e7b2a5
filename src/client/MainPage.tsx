import { type User } from "wasp/entities";
import { useAuth } from "wasp/client/auth";

import {
  generateCoverLetter,
  createJob,
  updateCoverLetter,
  importJobFromLinkedIn,
  useQuery,
  getJob,
  getCoverLetterCount,
} from "wasp/client/operations";

import {
  Box,
  HStack,
  VStack,
  Heading,
  Text,
  FormErrorMessage,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Button,
  FormHelperText,
  Code,
  Spinner,
  Slider,
  SliderTrack,
  SliderFilledTrack,
  SliderThumb,
  RadioGroup,
  Radio,
  Tooltip,
  useDisclosure,
  Container,
  Divider,
  SimpleGrid,
  Badge,
  useToast,
  Flex,
  Grid,
  GridItem,
  Card,
  CardBody,
  CardHeader,
  Stack,
  useColorModeValue,
  Avatar,
  AvatarGroup,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Progress,
  useBreakpointValue,
  Image,
  AspectRatio,
  Center,
  Wrap,
  WrapItem,
  IconButton,
  Link,
  List,
  ListItem,
  ListIcon,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Icon,
} from '@chakra-ui/react';

import { motion, useScroll, useTransform } from 'framer-motion';
import BorderBox from './components/BorderBox';
import { LeaveATip, LoginToBegin } from './components/AlertDialog';
import { convertToSliderValue, convertToSliderLabel } from './components/CreativitySlider';
import HowItWorks from './components/HowItWorks';
import JobSuggestions from './components/JobSuggestions';
import * as pdfjsLib from 'pdfjs-dist';
import { useState, useEffect, useRef, lazy, Suspense } from 'react';
import { ChangeEvent } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import {
  FaUpload,
  FaRocket,
  FaEnvelopeOpenText,
  FaComments,
  FaBriefcase,
  FaChartLine,
  FaGraduationCap,
  FaStar,
  FaUsers,
  FaCrown,
  FaGem,
  FaLightbulb,
  FaSearch,
  FaTasks,
  FaBook,
  FaFileAlt,
  FaBug,
  FaGithub,
  FaLinkedin,
  FaGoogle,
  FaApple,
  FaMicrosoft,
  FaFacebook,
  FaTwitter,
  FaInstagram,
  FaBrain,
  FaRobot,
  FaTrophy,
  FaClock,
  FaAmazon,
  FaDropbox,
  FaPaperPlane,
  FaUser,
  FaCog,
  FaHeart,
  FaTimes,
  FaArrowLeft
} from 'react-icons/fa';
import { FiInfo } from 'react-icons/fi';
import { Popover, PopoverTrigger, PopoverContent, PopoverArrow, PopoverBody } from '@chakra-ui/react';
import CreditsIndicator from './components/CreditsIndicator';
import Logo from './components/Logo';
import LinkedInJobImport from './components/LinkedInJobImport';
import URLJobImport from './components/URLJobImport';
import ResumeSelector from './components/ResumeSelector';
import BackButton from './components/BackButton';
import UndrawIllustration, { IllustrationPresets } from './components/UndrawIllustration';

// Lazy load heavy components for better performance
const UpgradeModal = lazy(() => import('./components/UpgradeModal'));

// Lazy load below-the-fold content
const BelowTheFold = lazy(() => import('./components/BelowTheFold'));

// Motion components
const MotionBox = motion(Box);
const MotionCard = motion(Card);
const MotionText = motion(Text);
const MotionHeading = motion(Heading);
const MotionButton = motion(Button);
const MotionFlex = motion(Flex);
const MotionGrid = motion(Grid);
const MotionVStack = motion(VStack);

// Define the job data structure
type JobData = {
  title: string;
  company: string;
  location: string;
  description: string;
};

// Data for the landing page
const features = [
  {
    icon: FaRocket,
    title: "AI-Powered Resume Builder",
    description: "Create professional resumes in minutes with our intelligent AI that adapts to your industry and role.",
    color: "blue.500"
  },
  {
    icon: FaEnvelopeOpenText,
    title: "Smart Cover Letters",
    description: "Generate personalized cover letters that perfectly match job descriptions and showcase your strengths.",
    color: "purple.500"
  },
  {
    icon: FaComments,
    title: "Interview Preparation",
    description: "Practice with AI-generated questions tailored to your target role and get instant feedback.",
    color: "green.500"
  },
  {
    icon: FaBriefcase,
    title: "Job Application Tracker",
    description: "Organize your job search with our comprehensive tracking system and never miss an opportunity.",
    color: "orange.500"
  },
  {
    icon: FaChartLine,
    title: "Career Analytics",
    description: "Get insights into your job search performance and optimize your strategy for better results.",
    color: "teal.500"
  },
  {
    icon: FaGraduationCap,
    title: "Learning Center",
    description: "Access curated career development resources and courses to advance your professional skills.",
    color: "pink.500"
  }
];

const testimonials = [
  {
    name: "Sarah Chen",
    role: "Software Engineer",
    company: "Google",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150",
    content: "CareerDart helped me land my dream job at Google! The AI-generated cover letters were spot-on and the interview prep was invaluable.",
    rating: 5
  },
  {
    name: "Marcus Johnson",
    role: "Product Manager",
    company: "Microsoft",
    avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150",
    content: "The resume builder is incredible. I went from 2% response rate to 40% after using CareerDart. Absolutely game-changing!",
    rating: 5
  },
  {
    name: "Emily Rodriguez",
    role: "UX Designer",
    company: "Apple",
    avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150",
    content: "I love how CareerDart tracks all my applications and provides insights. It made my job search so much more organized and effective.",
    rating: 5
  }
];

const stats = [
  { label: "Job Seekers Helped", value: "50,000+", icon: FaUsers },
  { label: "Success Rate", value: "89%", icon: FaTrophy },
  { label: "Average Time to Hire", value: "3.2 weeks", icon: FaClock },
  { label: "Cover Letters Generated", value: "250,000+", icon: FaFileAlt }
];

const companies = [
  { name: "Google", icon: FaGoogle },
  { name: "Microsoft", icon: FaMicrosoft },
  { name: "Apple", icon: FaApple },
  { name: "Amazon", icon: FaAmazon },
  { name: "Facebook", icon: FaFacebook },
  { name: "Dropbox", icon: FaDropbox }
];

function MainPage() {
  const [jobToFetch, setJobToFetch] = useState<string>('');
  const [isCoverLetterUpdate, setIsCoverLetterUpdate] = useState<boolean>(false);
  const [isCompleteCoverLetter, setIsCompleteCoverLetter] = useState<boolean>(true);
  const [sliderValue, setSliderValue] = useState(30);
  const [showTooltip, setShowTooltip] = useState(false);

  const [showLinkedInImport, setShowLinkedInImport] = useState<boolean>(false);
  const [showURLImport, setShowURLImport] = useState<boolean>(false);
  const [resumeContent, setResumeContent] = useState<string>('');
  const [showCoverLetterForm, setShowCoverLetterForm] = useState(false);

  const toast = useToast();
  const { data: user } = useAuth();

  const navigate = useNavigate();
  const urlParams = new URLSearchParams(window.location.search);
  const jobIdParam = urlParams.get('job');
  const createParam = urlParams.get('create');

  // Color mode values
  const bgGradient = useColorModeValue(
    'linear(to-br, blue.50, purple.50, pink.50)',
    'linear(to-br, gray.900, blue.900, purple.900)'
  );
  const cardBg = useColorModeValue('white', 'gray.800');
  const textColor = useColorModeValue('gray.700', 'gray.200');
  const accentColor = useColorModeValue('blue.600', 'blue.400');

  const {
    data: job,
    isLoading: isJobLoading,
    error: getJobError,
  } = useQuery(getJob, { id: jobToFetch }, { enabled: jobToFetch.length > 0 });

  const { data: coverLetterCount } = useQuery(getCoverLetterCount, undefined, { enabled: !!user });

  const {
    handleSubmit,
    register,
    setValue,
    reset,
    clearErrors,
    trigger,
    watch,
    formState: { errors: formErrors, isSubmitting },
  } = useForm({
    defaultValues: {
      title: '',
      company: '',
      location: '',
      description: '',
      pdf: ''
    },
    mode: 'onChange'
  });

  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: loginIsOpen, onOpen: loginOnOpen, onClose: loginOnClose } = useDisclosure();
  const { isOpen: upgradeIsOpen, onOpen: upgradeOnOpen, onClose: upgradeOnClose } = useDisclosure();

  let setLoadingTextTimeout: ReturnType<typeof setTimeout>;
  const loadingTextRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (jobIdParam) {
      setJobToFetch(jobIdParam);
      // Only set as update mode if the job actually has an existing cover letter
      // For newly imported jobs without cover letters, we want to create mode
      setIsCoverLetterUpdate(false);  // Default to create mode
      resetJob();
    } else {
      setIsCoverLetterUpdate(false);
      // Only reset the form if we're not currently showing the LinkedIn import
      // This prevents the form from being cleared when LinkedIn import is active
      if (!showLinkedInImport) {
        reset({
          title: '',
          company: '',
          location: '',
          description: '',
          pdf: resumeContent || ''
        });
      }
    }

    // Check if user wants to create a new cover letter
    if (createParam === 'new' && user) {
      setShowCoverLetterForm(true);
      // Clear the create parameter from URL to keep it clean
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('create');
      window.history.replaceState({}, '', newUrl.toString());
    }
  }, [jobIdParam, job, createParam, user]);

  useEffect(() => {
    resetJob();
  }, [job]);

  function resetJob() {
    if (job) {
      reset({
        title: job.title,
        company: job.company,
        location: job.location,
        description: job.description,
        pdf: resumeContent || ''
      });
    }
  }

  // Resume content is now handled by the ResumeSelector component

  function checkIfSubPastDueAndRedirect(user: Omit<User, 'password'>) {
    if (user.subscriptionStatus === 'past_due') {
      navigate('/profile')
      return true;
    } else {
      return false;
    }
  }

  async function onSubmit(values: any): Promise<void> {
    let canUserContinue = hasUserPaidOrActiveTrial();
    if (!user) {
      navigate('/login');
      return;
    }
    if (!canUserContinue) {
      upgradeOnOpen();
      return;
    }

    try {
      const isSubscriptionPastDue = checkIfSubPastDueAndRedirect(user);
      if (isSubscriptionPastDue) return;

      // If we have a job from URL parameter, use it; otherwise create a new job
      const targetJob = job || await createJob(values);

      const creativityValue = convertToSliderValue(sliderValue);

      const payload = {
        jobId: targetJob.id,
        title: targetJob.title,
        content: resumeContent || values.pdf,
        description: targetJob.description,
        isCompleteCoverLetter,

        temperature: creativityValue,
        gptModel: values.gptModel || 'gpt-4o-mini',
      };

      setLoadingText();

      const coverLetter = await generateCoverLetter(payload);

      navigate(`/cover-letters/${coverLetter.id}`);
    } catch (error: any) {
      cancelLoadingText();
      alert(`${error?.message ?? 'Something went wrong, please try again'}`);
      console.error(error);
    }
  }

  async function onUpdate(values: any): Promise<void> {
    const canUserContinue = hasUserPaidOrActiveTrial();
    if (!user) {
      navigate('/login');
      return;
    }
    if (!canUserContinue) {
      upgradeOnOpen();
      return;
    }

    try {
      const isSubscriptionPastDue = checkIfSubPastDueAndRedirect(user);
      if (isSubscriptionPastDue) return;

      if (!job) {
        throw new Error('Job not found');
      }

      const creativityValue = convertToSliderValue(sliderValue);
      const payload = {
        id: job.id,
        description: values.description,
        content: resumeContent || values.pdf,
        isCompleteCoverLetter,
        temperature: creativityValue,

        gptModel: values.gptModel || 'gpt-4o-mini',
      };

      setLoadingText();

      const coverLetterId = await updateCoverLetter(payload);

      navigate(`/cover-letters/${coverLetterId}`);
    } catch (error: any) {
      cancelLoadingText();
      alert(`${error?.message ?? 'Something went wrong, please try again'}`);
      console.error(error);
    }
  }

  // File button click is now handled by the ResumeSelector component

  function setLoadingText() {
    setLoadingTextTimeout = setTimeout(() => {
      loadingTextRef.current && (loadingTextRef.current.innerText = ' patience, my friend 🧘...');
    }, 2000);
  }

  function cancelLoadingText() {
    clearTimeout(setLoadingTextTimeout);
    loadingTextRef.current && (loadingTextRef.current.innerText = '');
  }

  async function handleLinkedInJobImport(jobData: JobData) {
    try {
      // Create a new form state with the imported data
      const formData = {
        title: jobData.title || '',
        company: jobData.company || '',
        location: jobData.location || '',
        description: jobData.description || '',
        pdf: resumeContent || '' // Keep existing resume content
      };

      // Reset the form with the new values - this should update all fields
      reset(formData);

      // Use setTimeout to ensure the reset has completed before setting individual values
      setTimeout(() => {
        // Set values individually to ensure they're updated
        setValue('title', formData.title, { shouldValidate: true, shouldDirty: true });
        setValue('company', formData.company, { shouldValidate: true, shouldDirty: true });
        setValue('location', formData.location, { shouldValidate: true, shouldDirty: true });
        setValue('description', formData.description, { shouldValidate: true, shouldDirty: true });

        // Trigger validation to update the form state
        trigger(['title', 'company', 'location', 'description']);
      }, 100);

      // Clear any form errors
      clearErrors(['title', 'company', 'location', 'description']);

      // Hide the import forms
      setShowLinkedInImport(false);
      setShowURLImport(false);

      // Show success message
      toast({
        title: 'Job data imported',
        description: `Successfully imported job details for ${jobData.title} at ${jobData.company}`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error handling job import:', error);
      toast({
        title: 'Import failed',
        description: 'Failed to import job data. Please try again.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  }

  function hasUserPaidOrActiveTrial(): Boolean {
    if (user) {
      if (!user.hasPaid && user.credits > 0) {
        if (user.credits < 3) {
          onOpen();
        }
        return user.credits > 0;
      }
      if (user.hasPaid) {
        return true;
      } else if (!user.hasPaid) {
        return false;
      }
    }
    return false;
  }

  // Job state is handled through conditional rendering in the JSX

  const handleResumeClick = () => {
    navigate('/resume');
  };

  const handleResumeSelected = (content: string) => {
    setResumeContent(content);
    setValue('pdf', content);
    console.log('Resume content set:', content.substring(0, 100) + '...');
  };

  // If user is logged in and wants to create a cover letter, show the form
  if (user && showCoverLetterForm) {
    return (
      <VStack spacing={{ base: 4, md: 6 }} align="stretch" px={{ base: 2, md: 0 }}>
        {/* Cover Letter Form */}
        <Container maxW="95%" px={{ base: 2, md: 4 }}>
          <VStack spacing={{ base: 6, md: 8 }} align="stretch">
            <BorderBox
              position="relative"
              overflow="hidden"
              bg="white"
              borderRadius="2xl"
              boxShadow="xl"
              _before={{
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '4px',
                bgGradient: 'gradient-primary',
                zIndex: 1,
              }}
            >
              <form
                onSubmit={!isCoverLetterUpdate ? handleSubmit(onSubmit) : handleSubmit(onUpdate)}
                style={{ width: '100%' }}
              >
                <VStack spacing={{ base: 4, md: 6 }} p={{ base: 4, md: 6 }}>
                  {/* Navigation Header */}
                  <Box w="full" mb={{ base: 2, md: 4 }}>
                    <HStack justify="space-between" align="center" w="full">
                      <HStack spacing={2}>
                        <IconButton
                          aria-label="Go back"
                          icon={<FaArrowLeft />}
                          variant="ghost"
                          size="sm"
                          onClick={() => navigate(-1)}
                          color="gray.600"
                          _hover={{ color: "gray.800", bg: "gray.100" }}
                        />
                        <Text fontSize="sm" color="gray.600" fontWeight="medium">
                          Create Cover Letter
                        </Text>
                      </HStack>
                      <IconButton
                        aria-label="Close"
                        icon={<FaTimes />}
                        variant="ghost"
                        size="sm"
                        onClick={() => navigate('/jobs')}
                        color="gray.600"
                        _hover={{ color: "gray.800", bg: "gray.100" }}
                      />
                    </HStack>
                  </Box>

                  <Box w="full" borderBottom="1px" borderColor="border-contrast-sm" pb={{ base: 3, md: 4 }}>
                    <VStack w="full" spacing={{ base: 3, md: 0 }} align="stretch">
                      <VStack align="start" spacing={1}>
                        <Heading
                          size={{ base: "sm", md: "md" }}
                          color="text-contrast-lg"
                          display="flex"
                          alignItems="center"
                          gap={2}
                          flexWrap="wrap"
                        >
                          Job Information
                          {isCoverLetterUpdate && (
                            <Badge
                              colorScheme="purple"
                              px={3}
                              py={1}
                              borderRadius="full"
                              fontSize="xs"
                            >
                              Editing
                            </Badge>
                          )}
                        </Heading>
                        <Text fontSize={{ base: "xs", md: "sm" }} color="text-contrast-md">
                          Fill in the details to generate your cover letter
                        </Text>
                      </VStack>
                      <VStack spacing={{ base: 2, md: 0 }} align="stretch" w="full">
                        <HStack 
                          spacing={{ base: 2, md: 3 }} 
                          flexWrap={{ base: "wrap", md: "nowrap" }}
                          justify={{ base: "flex-start", md: "flex-end" }}
                          w="full"
                          align="center"
                        >
                          {user && (
                            <CreditsIndicator
                              credits={user.credits}
                              hasPaid={user.hasPaid}
                              size="sm"
                            />
                          )}
                          <Button
                            leftIcon={<Icon as={FaLinkedin} />}
                            size={{ base: "sm", md: "sm" }}
                            variant="outline"
                            colorScheme="blue"
                            onClick={() => setShowLinkedInImport(!showLinkedInImport)}
                            isDisabled={isCoverLetterUpdate}
                            fontSize={{ base: "xs", md: "sm" }}
                            minW={{ base: "auto", md: "140px" }}
                          >
                            <Text display={{ base: "none", sm: "block" }}>Import from LinkedIn</Text>
                            <Text display={{ base: "block", sm: "none" }}>Import</Text>
                          </Button>
                          <Button
                            leftIcon={<Icon as={FaUpload} />}
                            size={{ base: "sm", md: "sm" }}
                            variant="outline"
                            colorScheme="blue"
                            onClick={() => setShowURLImport(!showURLImport)}
                            isDisabled={isCoverLetterUpdate}
                            fontSize={{ base: "xs", md: "sm" }}
                            minW={{ base: "auto", md: "140px" }}
                          >
                            <Text display={{ base: "none", sm: "block" }}>Import from URL</Text>
                            <Text display={{ base: "block", sm: "none" }}>URL</Text>
                          </Button>
                          <Badge
                            colorScheme="blue"
                            px={{ base: 2, md: 3 }}
                            py={1}
                            borderRadius="full"
                            fontSize={{ base: "xs", md: "sm" }}
                            whiteSpace="nowrap"
                          >
                            {coverLetterCount?.toLocaleString()} Generated
                          </Badge>
                        </HStack>
                      </VStack>
                    </VStack>
                  </Box>

                  {showLinkedInImport && !isCoverLetterUpdate && (
                    <Box w="full" mb={{ base: 2, md: 4 }}>
                      <LinkedInJobImport
                        onJobImported={handleLinkedInJobImport}
                        isCollapsible={false}
                        inForm={true}
                      />
                    </Box>
                  )}

                  {showURLImport && !isCoverLetterUpdate && (
                    <Box w="full" mb={{ base: 2, md: 4 }}>
                      <URLJobImport
                        onJobImported={handleLinkedInJobImport}
                        isCollapsible={false}
                        inForm={true}
                      />
                    </Box>
                  )}

                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={{ base: 3, md: 4 }} w="full">
                    <FormControl isInvalid={!!formErrors.title}>
                      <FormLabel htmlFor="title" fontWeight="medium" color="text-contrast-lg" fontSize={{ base: "sm", md: "md" }}>Job Title</FormLabel>
                      <Input
                        id='title'
                        placeholder='e.g., Senior Software Engineer'
                        aria-describedby={formErrors.title ? "title-error" : undefined}
                        {...register('title', {
                          required: 'This is required',
                          minLength: {
                            value: 2,
                            message: 'Minimum length should be 2',
                          },
                        })}
                        onFocus={(e: any) => {
                          if (user === null) {
                            loginOnOpen();
                            e.target.blur();
                          }
                        }}
                        disabled={isCoverLetterUpdate}
                        size={{ base: "md", md: "lg" }}
                      />
                      <FormErrorMessage id="title-error">
                        {!!formErrors.title && formErrors.title.message?.toString()}
                      </FormErrorMessage>
                    </FormControl>

                    <FormControl isInvalid={!!formErrors.company}>
                      <FormLabel htmlFor="company" fontWeight="medium" color="text-contrast-lg" fontSize={{ base: "sm", md: "md" }}>Company Name</FormLabel>
                      <Input
                        id='company'
                        placeholder='e.g., Tech Corp'
                        aria-describedby={formErrors.company ? "company-error" : undefined}
                        {...register('company', {
                          required: 'This is required',
                          minLength: {
                            value: 1,
                            message: 'Minimum length should be 1',
                          },
                        })}
                        disabled={isCoverLetterUpdate}
                        size={{ base: "md", md: "lg" }}
                      />
                      <FormErrorMessage id="company-error">
                        {!!formErrors.company && formErrors.company.message?.toString()}
                      </FormErrorMessage>
                    </FormControl>
                  </SimpleGrid>

                  <FormControl isInvalid={!!formErrors.location}>
                    <FormLabel fontWeight="medium" color="text-contrast-lg" fontSize={{ base: "sm", md: "md" }}>Location</FormLabel>
                    <Input
                      id='location'
                      placeholder='e.g., San Francisco, CA or Remote'
                      {...register('location', {
                        required: 'This is required',
                        minLength: {
                          value: 2,
                          message: 'Minimum length should be 2',
                        },
                      })}
                      size={{ base: "md", md: "lg" }}
                    />
                    <FormErrorMessage>{!!formErrors.location && formErrors.location.message?.toString()}</FormErrorMessage>
                  </FormControl>

                  <FormControl isInvalid={!!formErrors.description}>
                    <FormLabel fontWeight="medium" color="text-contrast-lg" fontSize={{ base: "sm", md: "md" }}>Job Description</FormLabel>
                    <Textarea
                      id='description'
                      placeholder='Paste the job description here...'
                      {...register('description', {
                        required: 'This is required',
                      })}
                      size={{ base: "md", md: "lg" }}
                      minH={{ base: "150px", md: "200px" }}
                    />
                    <FormErrorMessage>
                      {!!formErrors.description && formErrors.description.message?.toString()}
                    </FormErrorMessage>
                  </FormControl>

                  <FormControl isInvalid={!!formErrors.pdf}>
                    <ResumeSelector onResumeSelected={handleResumeSelected} />
                    <Input
                      id='pdf'
                      type='hidden'
                      {...register('pdf', {
                        required: 'Please select or upload a resume',
                        validate: () => !!resumeContent || 'Please select or upload a resume'
                      })}
                      value={resumeContent}
                    />
                    <FormErrorMessage>{!!formErrors.pdf && formErrors.pdf.message?.toString()}</FormErrorMessage>
                    <HStack mt={2} justify="flex-end" spacing={{ base: 2, md: 3 }} flexWrap="wrap">
                      <Button
                        leftIcon={<Icon as={FaFileAlt} />}
                        onClick={handleResumeClick}
                        colorScheme="blue"
                        variant="outline"
                        size={{ base: "sm", md: "sm" }}
                        minW={{ base: "auto", md: "160px" }}
                      >
                        <Text display={{ base: "none", sm: "block" }}>Manage All Resumes</Text>
                        <Text display={{ base: "block", sm: "none" }}>Resumes</Text>
                      </Button>
                    </HStack>
                  </FormControl>

                  <VStack spacing={4} w="full" bg="bg-contrast-sm" p={4} borderRadius="xl">
                    <FormControl>
                      <FormLabel fontWeight="medium" color="text-contrast-lg">Creativity Level</FormLabel>
                      <Slider
                        id='temperature'
                        defaultValue={30}
                        min={0}
                        max={68}
                        colorScheme='purple'
                        onChange={(v) => setSliderValue(v)}
                        onMouseEnter={() => setShowTooltip(true)}
                        onMouseLeave={() => setShowTooltip(false)}
                      >
                        <SliderTrack>
                          <SliderFilledTrack />
                        </SliderTrack>
                        <Tooltip
                          hasArrow
                          bg='purple.500'
                          color='white'
                          placement='top'
                          isOpen={showTooltip}
                          label={`${convertToSliderLabel(sliderValue)}`}
                        >
                          <SliderThumb />
                        </Tooltip>
                      </Slider>
                      <HStack justify="space-between" mt={2}>
                        <Text fontSize="sm" color="text-contrast-md">More Professional</Text>
                        <Text fontSize="sm" color="text-contrast-md">More Creative</Text>
                      </HStack>
                    </FormControl>
                  </VStack>

                  <Button
                    colorScheme='blue'
                    size={{ base: "md", md: "lg" }}
                    w="full"
                    isLoading={isSubmitting}
                    disabled={user === null}
                    type='submit'
                    leftIcon={<Icon as={FaPaperPlane} />}
                    minH={{ base: "44px", md: "48px" }}
                  >
                    {!isCoverLetterUpdate ? 'Generate Cover Letter' : 'Create New Cover Letter'}
                  </Button>

                  <Text fontSize="xs" color="gray.500" textAlign="center" mt={2}>
                    ⚠️ AI-generated content is for guidance only. Please review and customize before use.
                  </Text>

                  <Text fontSize="xs" color="gray.500" textAlign="center" mt={1}>
                    By using our service, you agree to our{' '}
                    <Link as={RouterLink} to="/tos" color="blue.500" _hover={{ color: 'blue.600', textDecoration: 'underline' }}>
                      Terms of Service
                    </Link>
                    {' '}and{' '}
                    <Link as={RouterLink} to="/privacy" color="blue.500" _hover={{ color: 'blue.600', textDecoration: 'underline' }}>
                      Privacy Policy
                    </Link>
                  </Text>

                  <Text ref={loadingTextRef} fontSize='sm' fontStyle='italic' color='text-contrast-md' textAlign="center">
                    {' '}
                  </Text>
                </VStack>
              </form>
            </BorderBox>

            {/* Job Suggestions Section */}
            <JobSuggestions />
          </VStack>
        </Container>

        <LeaveATip
          isOpen={isOpen}
          onOpen={onOpen}
          onClose={onClose}
          credits={user?.credits || 0}
        />
        <LoginToBegin isOpen={loginIsOpen} onOpen={loginOnOpen} onClose={loginOnClose} />
        <Suspense fallback={<></>}>
          <UpgradeModal
            isOpen={upgradeIsOpen}
            onClose={upgradeOnClose}
            userCredits={user?.credits || 0}
            feature="cover-letter"
          />
        </Suspense>
      </VStack>
    );
  }

  // Main Landing Page
  return (
    <Box minH="100vh" bg={bgGradient} position="relative" overflow="hidden">
      {/* Background Pattern */}
      <Box
        position="absolute"
        top="0"
        left="0"
        right="0"
        bottom="0"
        opacity={0.05}
        bgImage="radial-gradient(circle at 25px 25px, rgba(66, 153, 225, 0.3) 2px, transparent 0)"
        bgSize="50px 50px"
      />

      {/* Hero Section (LCP) - Remove framer-motion for instant render */}
      <Container maxW={{ base: "100%", md: "95%" }} pt={{ base: 4, md: 16 }} pb={{ base: 6, md: 20 }} px={{ base: 2, md: 6 }}>
        <VStack
          spacing={{ base: 6, md: 16 }}
          textAlign="center"
        >
          {/* Trust Badge */}
          <Badge
            colorScheme="blue"
            variant="subtle"
            px={{ base: 3, md: 4 }}
            py={{ base: 1, md: 2 }}
            borderRadius="full"
            fontSize={{ base: "xs", md: "sm" }}
            fontWeight="600"
            textTransform="none"
          >
            AI-Powered Career Intelligence
          </Badge>

          {/* Hero Headline */}
          <VStack spacing={{ base: 6, md: 8 }} maxW="6xl">
            <Heading
              size={{ base: "xl", md: "4xl" }}
              fontWeight="black"
              color={textColor}
              lineHeight="1.1"
              letterSpacing="-0.02em"
              textAlign="center"
              maxW={{ base: "95%", md: "6xl" }}
              px={{ base: 2, md: 0 }}
            >
              <Text
                as="span"
                bgGradient="linear(to-r, blue.500, purple.500, pink.500)"
                bgClip="text"
              >
                Your Personal Career Companion
              </Text>
            </Heading>

            <Text
              fontSize={{ base: "md", md: "2xl" }}
              color={textColor}
              maxW={{ base: "95%", md: "5xl" }}
              fontWeight="500"
              textAlign="center"
              px={{ base: 2, md: 0 }}
              lineHeight={{ base: "1.5", md: "1.4" }}
            >
              Experience personalized career development intelligence that adapts to your unique goals, skills, and industry—delivering custom strategies for your success.
            </Text>

            {/* Value Proposition Cards */}
            <Grid
              templateColumns={{ base: "repeat(2, 1fr)", md: "repeat(3, 1fr)" }}
              gap={{ base: 4, md: 6 }}
              w="full"
              maxW="5xl"
              mx="auto"
              mb={8}
            >
              <Box
                bg={useColorModeValue('white', 'gray.800')}
                borderRadius={{ base: "lg", md: "xl" }}
                boxShadow="md"
                p={{ base: 4, md: 6 }}
                textAlign="center"
                gridColumn={{ base: "span 2", md: "span 1" }}
              >
                <Icon as={FaUser} boxSize={{ base: 6, md: 8 }} color="blue.500" mb={2} />
                <Heading size={{ base: "sm", md: "md" }} fontWeight="bold" mb={1}>Personalized</Heading>
                <Text fontSize={{ base: "xs", md: "sm" }} color="gray.500">
                  AI learns your career goals, skills, and preferences to create tailored recommendations
                </Text>
              </Box>
              <Box
                bg={useColorModeValue('white', 'gray.800')}
                borderRadius={{ base: "lg", md: "xl" }}
                boxShadow="md"
                p={{ base: 4, md: 6 }}
                textAlign="center"
              >
                <Icon as={FaCog} boxSize={{ base: 6, md: 8 }} color="purple.500" mb={2} />
                <Heading size={{ base: "sm", md: "md" }} fontWeight="bold" mb={1}>Custom Built</Heading>
                <Text fontSize={{ base: "xs", md: "sm" }} color="gray.500">
                  Every resume, cover letter, and strategy is uniquely crafted for your target roles
                </Text>
              </Box>
              <Box
                bg={useColorModeValue('white', 'gray.800')}
                borderRadius={{ base: "lg", md: "xl" }}
                boxShadow="md"
                p={{ base: 4, md: 6 }}
                textAlign="center"
              >
                <Icon as={FaHeart} boxSize={{ base: 6, md: 8 }} color="pink.500" mb={2} />
                <Heading size={{ base: "sm", md: "md" }} fontWeight="bold" mb={1}>User-Focused</Heading>
                <Text fontSize={{ base: "xs", md: "sm" }} color="gray.500">
                  Designed around your success with intuitive tools that grow with your career
                </Text>
              </Box>
            </Grid>

            {/* CTA Button */}
            <Box px={{ base: 4, md: 0 }} w="full" maxW={{ base: "90%", md: "auto" }}>
              <Button
                size={{ base: "lg", md: "xl" }}
                colorScheme="blue"
                px={{ base: 6, md: 12 }}
                py={{ base: 4, md: 8 }}
                fontSize={{ base: "md", md: "xl" }}
                fontWeight="700"
                borderRadius={{ base: "xl", md: "2xl" }}
                h="auto"
                minH={{ base: "12", md: "16" }}
                w={{ base: "full", md: "auto" }}
                onClick={() => user ? setShowCoverLetterForm(true) : navigate('/login')}
                _hover={{
                  transform: 'translateY(-3px)',
                  boxShadow: '0 20px 40px rgba(66, 153, 225, 0.4)',
                }}
                transition="all 0.3s"
              >
                {user ? 'Start Your Journey' : 'Start for FREE'}
              </Button>
            </Box>
          </VStack>
        </VStack>
      </Container>

      {/* Below-the-fold content is lazy loaded */}
      <Suspense fallback={null}>
        <BelowTheFold user={user} setShowCoverLetterForm={setShowCoverLetterForm} navigate={navigate} />
      </Suspense>
    </Box>
  );
}

export default MainPage;