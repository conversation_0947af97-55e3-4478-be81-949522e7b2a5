import { type User } from 'wasp/entities';
import { logout } from 'wasp/client/auth';

import { stripePayment, stripeGpt4Payment, useQuery, getUserInfo, getUserBadges, getLearningProgress, getUserPreferences, updateUser } from 'wasp/client/operations';

import ContentContainer from './components/ContentContainer';
import ActionButton from './components/ActionButton';
import BadgeDisplay from './components/BadgeDisplay';
import AccountInfoCard from './components/ProfileSettings/AccountInfoCard';
import PreferencesCard from './components/ProfileSettings/PreferencesCard';
import NotificationsCard from './components/ProfileSettings/NotificationsCard';
import DangerZoneCard from './components/ProfileSettings/DangerZoneCard';
import {
  Box,
  Heading,
  Text,
  Code,
  Spinner,
  VStack,
  HStack,
  Link,
  Grid,
  Button,
  Divider,
  Avatar,
  Flex,
  Badge,
  Icon,
  useColorModeValue,
  Stack,
  Card,
  CardBody,
  Input,
  useToast,
  Container,
  useBreakpointValue,
} from '@chakra-ui/react';
import { useState } from 'react';
import { IoWarningOutline } from 'react-icons/io5';
import {
  FaSignOutAlt,
  FaShoppingCart,
  FaTrophy,
  FaUpload,
  FaEdit
} from 'react-icons/fa';
import { resetOnboarding } from './utils/userPreferences';
import BackButton from './components/BackButton';

export default function ProfilePage({ user }: { user: User }) {
  
  const [isLoading, setIsLoading] = useState(false);
  const [isGpt4loading, setIsGpt4Loading] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  const { data: userInfo, refetch: refetchUserInfo } = useQuery(getUserInfo, { id: user.id });
  const { data: userBadges, refetch: refetchUserBadges } = useQuery(getUserBadges);
  const { data: learningProgress, refetch: refetchLearningProgress } = useQuery(getLearningProgress);

  const toast = useToast();

  // All useColorModeValue hooks must be called at the top level
  const bgGradient = useColorModeValue(
    'linear(to-br, blue.50, purple.50, pink.50)',
    'linear(to-br, gray.900, blue.900, purple.900)'
  );
  const cardBg = useColorModeValue('white', 'gray.800');
  const cardBorder = useColorModeValue('gray.100', 'gray.700');
  const greenBg = useColorModeValue('green.50', 'green.900');
  const greenColor = useColorModeValue('green.600', 'green.300');
  const textPrimary = useColorModeValue('gray.800', 'white');
  const textSecondary = useColorModeValue('gray.600', 'gray.400');
  const textTertiary = useColorModeValue('gray.700', 'gray.300');
  const grayBg = useColorModeValue('gray.50', 'gray.700');
  const blueBg = useColorModeValue('blue.50', 'blue.900');
  const blueBorder = useColorModeValue('blue.300', 'blue.500');
  const blueText = useColorModeValue('blue.800', 'blue.200');
  const blueTextSecondary = useColorModeValue('blue.600', 'blue.400');
  const blueTextTertiary = useColorModeValue('blue.700', 'blue.300');
  const blueBorderHover = useColorModeValue('blue.400', 'blue.400');
  const cardBorderColor = useColorModeValue('gray.200', 'gray.700');
  const iconColor = useColorModeValue('gray.600', 'gray.400');

  // Responsive values
  const containerPadding = useBreakpointValue({ base: 4, md: 8 });
  const gridColumns = useBreakpointValue({ base: 1, lg: 2 });
  const avatarSize = useBreakpointValue({ base: 'lg', md: 'xl' });

  const handleDataRefresh = () => {
    refetchUserInfo();
    refetchUserBadges();
    refetchLearningProgress();
  };

  const userPaidOnDay = new Date(String(user.datePaid));
  const oneMonthFromDatePaid = new Date(userPaidOnDay.setMonth(userPaidOnDay.getMonth() + 1));

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: 'File too large',
          description: 'Please select an image smaller than 5MB',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast({
          title: 'Invalid file type',
          description: 'Please select an image file',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      handleImageUpload(file);
    }
  };

  const handleImageUpload = async (file: File) => {
    setIsUploadingImage(true);

    try {
      // Convert file to base64 data URL
      const reader = new FileReader();
      reader.onloadend = async () => {
        const dataUrl = reader.result as string;
        
        try {
          await updateUser({ profileImageUrl: dataUrl });
          
          toast({
            title: 'Profile image updated',
            description: 'Your profile picture has been successfully updated',
            status: 'success',
            duration: 3000,
            isClosable: true,
          });
          
          handleDataRefresh();
        } catch (error) {
          console.error('Failed to update profile image:', error);
          toast({
            title: 'Upload failed',
            description: 'Failed to update profile image. Please try again.',
            status: 'error',
            duration: 3000,
            isClosable: true,
          });
        }
      };
      
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error processing image:', error);
      toast({
        title: 'Processing failed',
        description: 'Failed to process image. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsUploadingImage(false);
    }
  };

  async function handleBuy4oMini() {
    setIsLoading(true);
    try {
      const response = await stripePayment();
      const url = response.sessionUrl;
      if (url) window.open(url, '_self');
    } catch (error) {
      alert('Something went wrong. Please try again');
    }
    setIsLoading(false);
  }

  async function handleBuy4o() {
    setIsGpt4Loading(true);
    try {
      const response = await stripeGpt4Payment();
      const url = response.sessionUrl;
      if (url) window.open(url, '_self');
    } catch (error) {
      alert('Something went wrong. Please try again');
    }
    setIsGpt4Loading(false);
  }

  return (
    <Box bg={bgGradient} minH="100vh" py={containerPadding} position="relative">
      {/* Back Button for Mobile */}
      <BackButton />
      
      <Container maxW="95%" px={{ base: 4, md: 8 }}>
        {!!userInfo ? (
          <>
            {/* Responsive Profile Header - 2-Column Layout for Small Screens */}
            <Box
              p={{ base: 3, md: 4 }}
              mb={{ base: 3, md: 6 }}
            >
              {/* Mobile 2-Column Layout */}
              <Grid
                templateColumns={{ base: "auto 1fr", sm: "auto 1fr" }}
                gap={{ base: 3, sm: 6 }}
                alignItems="start"
                display={{ base: 'grid', md: 'none' }}
              >
                {/* Left Column - Avatar */}
                <Avatar
                  size={{ base: 'md', sm: 'lg' }}
                  name={user?.username || userInfo?.email || 'User'}
                  src={userInfo?.profileImageUrl}
                  bg={useColorModeValue('blue.100', 'blue.800')}
                  color={useColorModeValue('blue.600', 'blue.200')}
                  border="3px solid"
                  borderColor={useColorModeValue('blue.200', 'blue.600')}
                  shadow="lg"
                  cursor={!userInfo?.auth?.identities?.some((identity: any) => identity.providerName === 'google') ? "pointer" : "default"}
                  onClick={!userInfo?.auth?.identities?.some((identity: any) => identity.providerName === 'google') ?
                    () => document.getElementById('profile-image-upload')?.click() : undefined}
                  _hover={!userInfo?.auth?.identities?.some((identity: any) => identity.providerName === 'google') ? {
                    transform: 'scale(1.05)',
                    borderColor: useColorModeValue('blue.300', 'blue.500')
                  } : {}}
                  transition="all 0.2s"
                />

                {/* Right Column - User Info */}
                <VStack spacing={1} align="start" minW={0}>
                  {/* Welcome message and name in same row */}
                  <Flex align="baseline" gap={1} wrap="wrap">
                    <Text fontSize="sm" color={textSecondary} fontWeight="500">
                      Welcome back,
                    </Text>
                    <Heading size="sm" color={textPrimary} fontWeight="700">
                      {user?.username || 'User'}
                    </Heading>
                  </Flex>

                  <Text color="gray.500" fontSize="xs" fontWeight="500" noOfLines={1}>
                    {userInfo?.email}
                  </Text>

                  {userInfo?.bio && (
                    <Text
                      fontSize="xs"
                      color={textTertiary}
                      noOfLines={1}
                      lineHeight="1.4"
                    >
                      {userInfo.bio}
                    </Text>
                  )}

                  {/* Badges and Logout Row - Parallel Layout */}
                  <Flex justify="space-between" align="center" w="full" mt={1}>
                    <Flex wrap="wrap" gap={1} align="center">
                      <Badge
                        colorScheme={userInfo?.hasPaid ? "green" : "gray"}
                        variant="subtle"
                        borderRadius="md"
                        px={2}
                        py={0.5}
                        fontSize="xs"
                        fontWeight="medium"
                      >
                        {userInfo?.hasPaid ? 'Premium' : 'Free'}
                      </Badge>
                      {userInfo?.yearsOfExperience !== undefined && userInfo?.yearsOfExperience > 0 && (
                        <Badge
                          colorScheme="blue"
                          variant="subtle"
                          borderRadius="md"
                          px={2}
                          py={0.5}
                          fontSize="xs"
                          fontWeight="medium"
                        >
                          {userInfo.yearsOfExperience}y exp.
                        </Badge>
                      )}
                    </Flex>

                    {/* Logout Button - Right Side */}
                    <ActionButton
                      icon={FaSignOutAlt}
                      label="Logout"
                      variant="outline"
                      onClick={() => logout()}
                      colorScheme="gray"
                      size="xs"
                      borderColor={useColorModeValue('gray.300', 'gray.600')}
                      color={useColorModeValue('gray.600', 'gray.400')}
                      _hover={{
                        bg: useColorModeValue('gray.50', 'gray.700'),
                        borderColor: useColorModeValue('gray.400', 'gray.500')
                      }}
                      flexShrink={0}
                    />
                  </Flex>
                </VStack>
              </Grid>

              {/* Desktop Layout (unchanged) */}
              <Flex
                direction="row"
                gap={6}
                align="center"
                textAlign="left"
                display={{ base: 'none', md: 'flex' }}
              >
                <Avatar
                  size={avatarSize}
                  name={user?.username || userInfo?.email || 'User'}
                  src={userInfo?.profileImageUrl}
                  bg={useColorModeValue('blue.100', 'blue.800')}
                  color={useColorModeValue('blue.600', 'blue.200')}
                  border="3px solid"
                  borderColor={useColorModeValue('blue.200', 'blue.600')}
                  shadow="lg"
                  cursor={!userInfo?.auth?.identities?.some((identity: any) => identity.providerName === 'google') ? "pointer" : "default"}
                  onClick={!userInfo?.auth?.identities?.some((identity: any) => identity.providerName === 'google') ?
                    () => document.getElementById('profile-image-upload')?.click() : undefined}
                  _hover={!userInfo?.auth?.identities?.some((identity: any) => identity.providerName === 'google') ? {
                    transform: 'scale(1.05)',
                    borderColor: useColorModeValue('blue.300', 'blue.500')
                  } : {}}
                  transition="all 0.2s"
                />
                <VStack spacing={2} align="start" flex={1}>
                  <Heading size="md" color={textPrimary} fontWeight="700">
                    {user?.username || 'User'}
                  </Heading>
                  <Text color="gray.500" fontSize="sm" fontWeight="500">
                    {userInfo?.email}
                  </Text>
                  {userInfo?.bio && (
                    <Text
                      fontSize="sm"
                      color={textTertiary}
                      noOfLines={2}
                      maxW="400px"
                      lineHeight="1.5"
                    >
                      {userInfo.bio}
                    </Text>
                  )}

                  <Flex
                    direction="row"
                    align="start"
                    justify="space-between"
                    w="full"
                    mt={2}
                    gap={4}
                  >
                    <Flex
                      wrap="wrap"
                      gap={3}
                      justify="start"
                      align="center"
                    >
                      <Badge
                        colorScheme={userInfo?.hasPaid ? "green" : "gray"}
                        variant="subtle"
                        borderRadius="lg"
                        px={3}
                        py={1}
                        fontSize="sm"
                        fontWeight="medium"
                      >
                        {userInfo?.hasPaid ? 'Premium User' : 'Free Plan'}
                      </Badge>
                      {userInfo?.yearsOfExperience !== undefined && userInfo?.yearsOfExperience > 0 && (
                        <Badge
                          colorScheme="blue"
                          variant="subtle"
                          borderRadius="lg"
                          px={3}
                          py={1}
                          fontSize="sm"
                          fontWeight="medium"
                        >
                          {userInfo.yearsOfExperience} years exp.
                        </Badge>
                      )}
                    </Flex>

                    <ActionButton
                      icon={FaSignOutAlt}
                      label="Logout"
                      variant="outline"
                      onClick={() => logout()}
                      colorScheme="gray"
                      size="sm"
                      borderColor={useColorModeValue('gray.300', 'gray.600')}
                      color={useColorModeValue('gray.600', 'gray.400')}
                      _hover={{
                        bg: useColorModeValue('gray.50', 'gray.700'),
                        borderColor: useColorModeValue('gray.400', 'gray.500')
                      }}
                      flexShrink={0}
                    />
                  </Flex>
                </VStack>
              </Flex>

              {/* Hidden file input */}
              <Input
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                display="none"
                id="profile-image-upload"
              />

              {/* Upload hint for non-Google users */}
              {!userInfo?.auth?.identities?.some((identity: any) => identity.providerName === 'google') && (
                <Box position="absolute" top="100%" left="0" mt={2}>
                  <Text fontSize="xs" color={useColorModeValue('gray.500', 'gray.400')} textAlign="center">
                    Click avatar to update photo
                  </Text>
                </Box>
              )}
            </Box>

            {/* Subscription Status */}
            <ContentContainer delay={0.3}>
              {userInfo.subscriptionStatus === 'past_due' ? (
                <VStack gap={3} py={5} alignItems='center'>
                  <Box color='blue.400'>
                    <IoWarningOutline size={30} color='inherit' />
                  </Box>
                  <Text textAlign='center' fontSize='sm' textColor='text-contrast-lg'>
                    Your subscription is past due. <br /> Please update your payment method{' '}
                    <Link textColor='blue.400' href='mailto:<EMAIL>?subject=Subscription%20Support'>
                      by contacting support
                    </Link>
                  </Text>
                </VStack>
              ) : userInfo.hasPaid ? (
                <VStack gap={3} pt={2} alignItems='flex-start'>
                  <Text textAlign='initial'>Thanks so much for your support!</Text>

                  <Text textAlign='initial'>You have unlimited access to CareerDart's premium features.</Text>

                  {userInfo.subscriptionStatus === 'canceled' && (
                    <Code alignSelf='center' fontSize='lg'>
                      {oneMonthFromDatePaid.toUTCString().slice(0, -13)}
                    </Code>
                  )}
                  <Text alignSelf='initial' fontSize='sm' fontStyle='italic' textColor='text-contrast-sm'>
                    To manage your subscription, please{' '}
                    <Link textColor='blue.600' href='mailto:<EMAIL>?subject=Subscription%20Management'>
                      contact support.
                    </Link>
                  </Text>
                </VStack>
              ) : (
                <VStack gap={{ base: 2, md: 3 }} pt={2} alignItems='center'>
                  <Flex
                    direction={{ base: 'column', sm: 'row' }}
                    align="center"
                    justify="center"
                    gap={{ base: 1, sm: 2 }}
                    wrap="wrap"
                  >
                    <Text fontSize={{ base: 'md', md: 'lg' }} fontWeight="medium" color={textPrimary}>
                      You have
                    </Text>
                    <Badge
                      colorScheme="blue"
                      variant="solid"
                      fontSize={{ base: 'sm', md: 'md' }}
                      px={3}
                      py={1}
                      borderRadius="lg"
                    >
                      {userInfo?.credits ? userInfo.credits : '0'}
                    </Badge>
                    <Text fontSize={{ base: 'md', md: 'lg' }} fontWeight="medium" color={textPrimary}>
                      credit{userInfo?.credits === 1 ? '' : 's'} remaining
                    </Text>
                  </Flex>
                  <Text
                    fontSize={{ base: 'xs', md: 'sm' }}
                    color={textSecondary}
                    textAlign="center"
                    maxW="400px"
                    px={{ base: 2, md: 0 }}
                  >
                    Each credit can be used to generate one cover letter or resume.
                    <Text as="span" fontWeight="medium" color={textPrimary}>
                      {' '}Upgrade to Premium for unlimited access.
                    </Text>
                  </Text>
                </VStack>
              )}
            </ContentContainer>

            {/* Upgrade Plans - Only for non-paid users */}
            {!userInfo.hasPaid && (
              <ContentContainer delay={0.4}>
                <Box
                  bg={cardBg}
                  borderRadius="2xl"
                  p={6}
                  boxShadow="xl"
                  borderWidth="1px"
                  borderColor={cardBorder}
                  mb={6}
                >
                  <HStack spacing={4} mb={4}>
                    <Box
                      p={3}
                      borderRadius="xl"
                      bg={greenBg}
                      color={greenColor}
                    >
                      <Icon as={FaShoppingCart} boxSize={5} />
                    </Box>
                    <VStack align="start" spacing={0}>
                      <Heading size="md" color={textPrimary}>
                        Upgrade to Premium
                      </Heading>
                      <Text fontSize="sm" color={textSecondary}>
                        Choose your plan and unlock unlimited access
                      </Text>
                    </VStack>
                  </HStack>

                  <Stack spacing={{ base: 3, md: 4 }} align="stretch" direction={{ base: "column", md: "row" }}>
                    {/* Monthly Plan */}
                    <Box
                      flex={1}
                      p={{ base: 4, md: 5 }}
                      borderRadius="xl"
                      bg={grayBg}
                      borderWidth="2px"
                      borderColor="transparent"
                      transition="all 0.3s ease"
                      _hover={{
                        borderColor: blueBorder,
                        transform: 'translateY(-2px)'
                      }}
                      w={{ base: "full", md: "auto" }}
                    >
                      <VStack spacing={{ base: 2, md: 3 }} align="stretch">
                        <HStack justify="space-between" align="center">
                          <VStack align="start" spacing={1}>
                            <HStack>
                              <Text fontSize={{ base: "xl", md: "2xl" }} fontWeight="bold" color={textPrimary}>
                                $5.95
                              </Text>
                              <Text fontSize={{ base: "xs", md: "sm" }} color={textSecondary}>
                                /month
                              </Text>
                            </HStack>
                            <Text fontSize={{ base: "xs", md: "sm" }} fontWeight="medium" color={textTertiary}>
                              CareerDart Pro - Monthly Plan
                            </Text>
                          </VStack>
                          <Text fontSize={{ base: "md", md: "lg" }}>🚀</Text>
                        </HStack>
                        <Button
                          colorScheme="blue"
                          variant="outline"
                          size={{ base: "sm", md: "sm" }}
                          borderRadius="lg"
                          isLoading={isLoading}
                          onClick={handleBuy4oMini}
                          _hover={{ bg: 'blue.50' }}
                          w="full"
                          minH={{ base: "36px", md: "40px" }}
                        >
                          Get Started
                        </Button>
                      </VStack>
                    </Box>

                    {/* Yearly Plan */}
                    <Box
                      flex={1}
                      p={{ base: 4, md: 5 }}
                      borderRadius="xl"
                      bg={blueBg}
                      borderWidth="2px"
                      borderColor={blueBorder}
                      position="relative"
                      transition="all 0.3s ease"
                      _hover={{
                        borderColor: blueBorderHover,
                        transform: 'translateY(-2px)'
                      }}
                      w={{ base: "full", md: "auto" }}
                    >
                      <Badge
                        position="absolute"
                        top="-8px"
                        left="50%"
                        transform="translateX(-50%)"
                        colorScheme="blue"
                        variant="solid"
                        borderRadius="full"
                        px={3}
                        py={1}
                        fontSize="xs"
                      >
                        POPULAR
                      </Badge>
                      <VStack spacing={{ base: 2, md: 3 }} align="stretch">
                        <HStack justify="space-between" align="center">
                          <VStack align="start" spacing={1}>
                            <HStack>
                              <Text fontSize={{ base: "xl", md: "2xl" }} fontWeight="bold" color={blueText}>
                                $59.40
                              </Text>
                              <Text fontSize={{ base: "xs", md: "sm" }} color={blueTextSecondary}>
                                /year
                              </Text>
                            </HStack>
                            <Text fontSize={{ base: "xs", md: "sm" }} fontWeight="medium" color={blueTextTertiary}>
                              CareerDart Pro - Annual Plan
                            </Text>
                          </VStack>
                          <Text fontSize={{ base: "md", md: "lg" }}>🏆</Text>
                        </HStack>
                        <Button
                          colorScheme="blue"
                          size={{ base: "sm", md: "sm" }}
                          borderRadius="lg"
                          isLoading={isGpt4loading}
                          onClick={handleBuy4o}
                          _hover={{ bg: 'blue.600' }}
                          w="full"
                          minH={{ base: "36px", md: "40px" }}
                        >
                          Upgrade Now
                        </Button>
                      </VStack>
                    </Box>
                  </Stack>
                </Box>
              </ContentContainer>
            )}

            {/* Settings Grid */}
            <ContentContainer delay={0.5}>
              <Grid templateColumns={{ base: "1fr", lg: "1fr 1fr" }} gap={4}>
                {/* Account Information Card - Modified to exclude profile image */}
                <AccountInfoCard userInfo={userInfo} onUpdate={handleDataRefresh} />

                {/* Preferences Card */}
                <PreferencesCard userInfo={userInfo} onUpdate={handleDataRefresh} />

                {/* Notifications Card */}
                <NotificationsCard userInfo={userInfo} onUpdate={handleDataRefresh} />
              </Grid>

              {/* Progress Section */}
              <Box
                bg={cardBg}
                borderRadius="xl"
                p={5}
                borderWidth="1px"
                borderColor={cardBorderColor}
                mt={4}
              >
                <HStack spacing={3} mb={4} align="center">
                  <Icon as={FaTrophy} boxSize={4} color={iconColor} />
                  <Heading size="md" color={textPrimary} fontWeight="medium">
                    Your Progress
                  </Heading>
                </HStack>

                <HStack spacing={6} justify="space-around">
                  {/* Videos */}
                  <VStack spacing={1} align="center">
                    <Text fontSize="2xl" fontWeight="bold" color={textPrimary}>
                      {learningProgress?.filter((p: any) => p.completed).length || 0}
                    </Text>
                    <Text fontSize="sm" color={textSecondary}>
                      Videos
                    </Text>
                  </VStack>

                  <Divider orientation="vertical" height="40px" />

                  {/* Minutes */}
                  <VStack spacing={1} align="center">
                    <Text fontSize="2xl" fontWeight="bold" color={textPrimary}>
                      {Math.floor((learningProgress?.reduce((sum: number, p: any) => sum + p.timeSpent, 0) || 0) / 60)}
                    </Text>
                    <Text fontSize="sm" color={textSecondary}>
                      Minutes
                    </Text>
                  </VStack>

                  <Divider orientation="vertical" height="40px" />

                  {/* Badges */}
                  <VStack spacing={1} align="center">
                    <Text fontSize="2xl" fontWeight="bold" color={textPrimary}>
                      {userBadges?.length || 0}
                    </Text>
                    <Text fontSize="sm" color={textSecondary}>
                      Badges
                    </Text>
                  </VStack>
                </HStack>

                {/* Badges Display */}
                {userBadges && userBadges.length > 0 && (
                  <>
                    <Divider my={4} />
                    <BadgeDisplay
                      badges={userBadges?.map((ub: any) => ({
                        id: ub.badge.id,
                        name: ub.badge.name,
                        description: ub.badge.description,
                        icon: ub.badge.icon,
                        category: ub.badge.category,
                        color: ub.badge.color,
                        earnedAt: ub.earnedAt
                      })) || []}
                      title=""
                    />
                  </>
                )}
              </Box>

              {/* Danger Zone */}
              <DangerZoneCard userInfo={userInfo} />

              {/* Customization Settings */}
              <Card>
                <CardBody>
                  <VStack spacing={{ base: 2, md: 3 }} align="stretch">
                    <VStack align="start" spacing={1}>
                      <Text fontWeight="medium" color={textPrimary}>
                        🎯 Customization
                      </Text>
                      <Text fontSize="sm" color={textSecondary}>
                        Personalize your CareerDart experience
                      </Text>
                    </VStack>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        if (user) {
                          resetOnboarding(user.id.toString());
                          window.location.reload(); // Refresh to trigger onboarding
                        }
                      }}
                      w="full"
                    >
                      Restart Onboarding Setup
                    </Button>
                  </VStack>
                </CardBody>
              </Card>
            </ContentContainer>
          </>
        ) : (
          <VStack py={10}>
            <Spinner size="xl" />
            <Text mt={4}>Loading profile...</Text>
          </VStack>
        )}
      </Container>
    </Box>
  );
}
