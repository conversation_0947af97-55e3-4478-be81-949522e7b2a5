import bodyParser from 'body-parser';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import cors from 'cors';
import { MiddlewareConfigFn } from 'wasp/server/middleware';
import { Request, Response } from 'express';

// Rate limiting configuration
const createRateLimit = (windowMs: number, max: number, message: string) =>
  rateLimit({
    windowMs,
    max,
    message: { error: message },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (_req: Request, res: Response) => {
      res.status(429).json({
        error: message,
        retryAfter: Math.round(windowMs / 1000),
      });
    },
  });

// Different rate limits for different endpoints
const generalRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  1000, // Increased from 100 to 1000 requests per windowMs
  'Too many requests from this IP, please try again later.'
);

const authRateLimit = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  500, // Increased to 500 auth attempts per windowMs for production stability
  'Too many authentication attempts, please try again later.'
);

const apiRateLimit = createRateLimit(
  1 * 60 * 1000, // 1 minute
  300, // Increased from 30 to 300 API requests per minute
  'API rate limit exceeded, please slow down.'
);

const coverLetterRateLimit = createRateLimit(
  1 * 60 * 1000, // 1 minute
  5, // limit each IP to 5 cover letter generations per minute
  'Cover letter generation rate limit exceeded.'
);

export const customMiddlewareConfig: MiddlewareConfigFn = (config) => {
  // Security headers with Helmet
  config.set('helmet', helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: [
          "'self'",
          "'unsafe-inline'",
          "https://fonts.googleapis.com",
          "https://cdn.jsdelivr.net"
        ],
        scriptSrc: [
          "'self'",
          "'unsafe-inline'",
          "'unsafe-eval'", // Required for React dev tools
          "https://js.stripe.com",
          "https://checkout.stripe.com"
        ],
        fontSrc: [
          "'self'",
          "https://fonts.gstatic.com",
          "data:"
        ],
        imgSrc: [
          "'self'",
          "data:",
          "https:",
          "blob:"
        ],
        connectSrc: [
          "'self'",
          "https://api.openai.com",
          "https://api.stripe.com",
          "https://checkout.stripe.com",
          "wss://localhost:*", // WebSocket for dev
          "ws://localhost:*"   // WebSocket for dev
        ],
        frameSrc: [
          "'self'",
          "https://js.stripe.com",
          "https://hooks.stripe.com"
        ],
        objectSrc: ["'none'"],
        upgradeInsecureRequests: process.env.NODE_ENV === 'production' ? [] : null,
      },
    },
    crossOriginEmbedderPolicy: false, // Disable for compatibility
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true
    }
  }));

  // Rate limiting - disabled for production debugging
  if (process.env.NODE_ENV !== 'production') {
    config.set('rateLimit', generalRateLimit);
  }

  // Specific rate limits for different routes - disabled for production debugging
  config.set('authRateLimit', (req: any, res: any, next: any) => {
    if (process.env.NODE_ENV !== 'production' && (req.path.includes('/auth/') || req.path.includes('/login'))) {
      return authRateLimit(req, res, next);
    }
    next();
  });

  config.set('apiRateLimit', (req: any, res: any, next: any) => {
    if (process.env.NODE_ENV !== 'production' && req.path.startsWith('/api/')) {
      return apiRateLimit(req, res, next);
    }
    next();
  });

  config.set('coverLetterRateLimit', (req: any, res: any, next: any) => {
    if (process.env.NODE_ENV !== 'production' && (req.path.includes('generateCoverLetter') || req.path.includes('updateCoverLetter'))) {
      return coverLetterRateLimit(req, res, next);
    }
    next();
  });

  // CORS configuration
  const corsOrigin: string[] | boolean = process.env.NODE_ENV === 'production'
    ? [
        process.env.WASP_WEB_CLIENT_URL, 
        'https://careerdart.com',
        'https://careergpt.com',
        'https://careerdart.netlify.app'
      ].filter(Boolean) as string[]
    : [
        process.env.WASP_WEB_CLIENT_URL || 'https://careerdart.netlify.app',
        'https://dev.careerdart.com',        // Development domain
        'https://api-dev.careerdart.com',    // Development API domain
        'https://careerdart-dev.netlify.app', // Development Netlify URL
        'http://localhost:3000', // Keep localhost for local development
        'http://localhost:3001',
        'http://localhost:3002'  // Add port 3002 for when client runs on different port
      ].filter(Boolean) as string[];

  config.set('cors', cors({
    origin: corsOrigin,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  }));

  // Body parser with size limits
  config.set('express.json', bodyParser.json({
    limit: '10mb', // Reduced from 50mb for security
    verify: (req: any, _res: any, buf: Buffer) => {
      // Store raw body for webhook verification
      req.rawBody = buf;
    }
  }));

  config.set('express.urlencoded', bodyParser.urlencoded({
    limit: '10mb',
    extended: true
  }));

  // Custom security middleware
  config.set('customSecurity', (req: any, res: any, next: any) => {
    // Log auth attempts for debugging
    if (req.path === '/auth/email/signup' && req.method === 'POST') {
      console.log('=== SIGNUP REQUEST INTERCEPTED ===');
      console.log('Request body:', JSON.stringify(req.body, null, 2));
    }

    if (req.path === '/auth/email/login' && req.method === 'POST') {
      console.log('=== LOGIN REQUEST INTERCEPTED ===');
      console.log('Request body:', JSON.stringify({
        email: req.body?.email,
        password: req.body?.password ? '[REDACTED]' : 'undefined',
        passwordLength: req.body?.password?.length
      }, null, 2));
    }

    // Remove server information
    res.removeHeader('X-Powered-By');

    // Add custom security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');

    // CSRF protection for state-changing operations (disabled in development)
    if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(req.method) && process.env.NODE_ENV === 'production') {
      const origin = req.get('Origin');
      const referer = req.get('Referer');
      const allowedOrigins = [
        process.env.WASP_WEB_CLIENT_URL, 
        'https://careerdart.com', 
        'https://careergpt.com', 
        'https://careerdart.netlify.app',
        'https://dev.careerdart.com',        // Development domain
        'https://api-dev.careerdart.com',    // Development API domain
        'https://careerdart-dev.netlify.app' // Development Netlify URL
      ].filter(Boolean);

      if (origin && !allowedOrigins.includes(origin)) {
        return res.status(403).json({ error: 'Forbidden: Invalid origin' });
      }

      if (referer && !allowedOrigins.some(allowed => referer.startsWith(allowed))) {
        return res.status(403).json({ error: 'Forbidden: Invalid referer' });
      }
    }

    next();
  });

  return config;
};