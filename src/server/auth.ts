import { defineUserSignupFields } from 'wasp/server/auth';

// Helper function to generate a unique username (simplified for now)
function generateUsername(baseUsername: string): string {
  // Clean the base username: remove special characters, convert to lowercase
  let cleanUsername = baseUsername
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '') // Remove non-alphanumeric characters
    .substring(0, 15); // Limit length

  // If the cleaned username is too short, add a default prefix
  if (cleanUsername.length < 3) {
    cleanUsername = 'user' + cleanUsername;
  }

  // Add a random suffix to reduce collision probability
  const randomSuffix = Math.floor(Math.random() * 10000).toString();
  return `${cleanUsername}${randomSuffix}`;
}

export const getUserFields = defineUserSignupFields({
  email: (data: any) => {
    console.log('=== EMAIL FIELD PROCESSING ===');
    console.log('Received data:', JSON.stringify(data, null, 2));

    if (data.profile) {
      console.log('Google OAuth email:', data.profile.email);
      return data.profile.email;
    } else {
      console.log('Email/password signup email:', data.email);
      return data.email;
    }
  },
  username: (data: any) => {
    console.log('=== USERNAME FIELD PROCESSING ===');
    console.log('Received data:', JSON.stringify(data, null, 2));

    try {
      if (data.profile) {
        const baseUsername = data.profile.name || data.profile.email.split('@')[0];
        const username = generateUsername(baseUsername);
        console.log('Google OAuth username generated:', username);
        return username;
      } else {
        const baseUsername = data.email.split('@')[0];
        const username = generateUsername(baseUsername);
        console.log('Email/password username generated:', username);
        return username;
      }
    } catch (error) {
      console.error('Error generating username:', error);
      // Fallback to a simple username
      const fallbackUsername = generateUsername('user');
      console.log('Using fallback username:', fallbackUsername);
      return fallbackUsername;
    }
  },
  profileImageUrl: (data: any) => {
    console.log('=== PROFILE IMAGE FIELD PROCESSING ===');
    console.log('Received data:', JSON.stringify(data, null, 2));

    try {
      if (data.profile && data.profile.picture) {
        console.log('Google OAuth profile image:', data.profile.picture);
        return data.profile.picture;
      } else {
        console.log('No profile image for manual signup');
        return null;
      }
    } catch (error) {
      console.error('Error processing profile image:', error);
      return null;
    }
  }
})

export function config() {
  return {
    scopes: ['profile', 'email'],
  };
}

export function getVerificationEmailContent({ verificationLink }: { verificationLink: string }) {
  return {
    subject: '🎯 Welcome to CareerDart - Verify Your Account',
    text: `Welcome to CareerDart! Please verify your email address by clicking this link: ${verificationLink}`,
    html: `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Welcome to CareerDart</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; background-color: #f7fafc; line-height: 1.6;">
          <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f7fafc; padding: 40px 20px;">
            <tr>
              <td align="center">
                <table width="100%" cellpadding="0" cellspacing="0" style="max-width: 600px; background-color: #ffffff; border-radius: 16px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); overflow: hidden;">
                  
                  <!-- Header with CareerDart blue gradient -->
                  <tr>
                    <td style="background: linear-gradient(135deg, #0080FF 0%, #0066CC 100%); padding: 40px 30px; text-align: center;">
                      <div style="display: inline-flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                        <h1 style="color: #ffffff; font-size: 32px; font-weight: bold; margin: 0; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">Career<span style="color: #5CAEFF;">Dart</span></h1>
                      </div>
                      <p style="color: rgba(255, 255, 255, 0.9); font-size: 18px; margin: 0; font-weight: 300;">Welcome to Your Career Journey! 🚀</p>
                    </td>
                  </tr>
                  
                  <!-- Main content -->
                  <tr>
                    <td style="padding: 50px 40px;">
                      <div style="text-align: center; margin-bottom: 40px;">
                        <h2 style="color: #1a202c; font-size: 28px; font-weight: 600; margin: 0 0 16px 0;">Verify Your Email Address ✉️</h2>
                        <p style="color: #4a5568; font-size: 16px; margin: 0; line-height: 1.6;">
                          You're one step closer to launching your career! Click the button below to verify your email and unlock the full power of CareerDart's AI-powered tools.
                        </p>
                      </div>
                      
                      <!-- Verification button -->
                      <div style="text-align: center; margin: 40px 0;">
                        <a href="${verificationLink}" 
                           style="background: linear-gradient(135deg, #0080FF 0%, #0066CC 100%); color: #ffffff; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; display: inline-block; box-shadow: 0 4px 12px rgba(0, 128, 255, 0.3); transition: all 0.2s;">
                          🚀 Verify Email Address
                        </a>
                      </div>
                      
                      <!-- Features preview -->
                      <div style="background-color: #f0f8ff; border-radius: 12px; padding: 30px; margin: 40px 0;">
                        <h3 style="color: #0080FF; font-size: 20px; font-weight: 600; margin: 0 0 20px 0; text-align: center;">What's waiting for you:</h3>
                        <div style="text-align: left;">
                          <div style="margin-bottom: 12px; display: flex; align-items: center;">
                            <span style="color: #0080FF; font-size: 16px; margin-right: 10px;">✨</span>
                            <span style="color: #2d3748; font-size: 14px;">AI-powered cover letter generation</span>
                          </div>
                          <div style="margin-bottom: 12px; display: flex; align-items: center;">
                            <span style="color: #0080FF; font-size: 16px; margin-right: 10px;">📝</span>
                            <span style="color: #2d3748; font-size: 14px;">Professional resume optimization</span>
                          </div>
                          <div style="margin-bottom: 12px; display: flex; align-items: center;">
                            <span style="color: #0080FF; font-size: 16px; margin-right: 10px;">🎯</span>
                            <span style="color: #2d3748; font-size: 14px;">Personalized job application tracking</span>
                          </div>
                          <div style="display: flex; align-items: center;">
                            <span style="color: #0080FF; font-size: 16px; margin-right: 10px;">⚡</span>
                            <span style="color: #2d3748; font-size: 14px;">Lightning-fast application creation</span>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Alternative link -->
                      <div style="background-color: #edf2f7; border-radius: 8px; padding: 20px; margin: 30px 0;">
                        <p style="color: #718096; font-size: 14px; margin: 0 0 8px 0; text-align: center;">
                          <strong>Button not working?</strong>
                        </p>
                        <p style="color: #718096; font-size: 12px; margin: 0; text-align: center; word-break: break-all;">
                          Copy and paste this link: <br>
                          <span style="color: #0080FF; font-family: monospace;">${verificationLink}</span>
                        </p>
                      </div>
                      
                      <!-- Expiration notice -->
                      <div style="text-align: center; margin: 30px 0;">
                        <p style="color: #e53e3e; font-size: 14px; margin: 0; background-color: #fed7d7; padding: 12px 20px; border-radius: 8px; display: inline-block;">
                          ⏰ This verification link expires in 24 hours
                        </p>
                      </div>
                    </td>
                  </tr>
                  
                  <!-- Footer -->
                  <tr>
                    <td style="background-color: #f7fafc; padding: 30px 40px; border-top: 1px solid #e2e8f0;">
                      <div style="text-align: center;">
                        <p style="color: #718096; font-size: 14px; margin: 0 0 12px 0;">
                          Need help? We're here for you!
                        </p>
                        <p style="color: #a0aec0; font-size: 12px; margin: 0 0 20px 0;">
                          If you didn't create an account with CareerDart, please ignore this email.
                        </p>
                        <div style="border-top: 1px solid #e2e8f0; padding-top: 20px; margin-top: 20px;">
                          <p style="color: #a0aec0; font-size: 11px; margin: 0;">
                            © 2024 CareerDart. All rights reserved.<br>
                            Rocket-powered AI tools for your career success.
                          </p>
                        </div>
                      </div>
                    </td>
                  </tr>
                  
                </table>
              </td>
            </tr>
          </table>
        </body>
      </html>
    `,
  };
}

export function getPasswordResetEmailContent({ passwordResetLink }: { passwordResetLink: string }) {
  return {
    subject: '🔐 Reset Your CareerDart Password',
    text: `Reset your CareerDart password by clicking this link: ${passwordResetLink}`,
    html: `
      <!DOCTYPE html>
      <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Reset Your Password - CareerDart</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; background-color: #f7fafc; line-height: 1.6;">
          <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f7fafc; padding: 40px 20px;">
            <tr>
              <td align="center">
                <table width="100%" cellpadding="0" cellspacing="0" style="max-width: 600px; background-color: #ffffff; border-radius: 16px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); overflow: hidden;">
                  
                  <!-- Header with CareerDart blue gradient -->
                  <tr>
                    <td style="background: linear-gradient(135deg, #0080FF 0%, #0066CC 100%); padding: 40px 30px; text-align: center;">
                      <div style="display: inline-flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                        <h1 style="color: #ffffff; font-size: 32px; font-weight: bold; margin: 0; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">Career<span style="color: #5CAEFF;">Dart</span></h1>
                      </div>
                      <p style="color: rgba(255, 255, 255, 0.9); font-size: 18px; margin: 0; font-weight: 300;">Password Reset Request</p>
                    </td>
                  </tr>
                  
                  <!-- Main content -->
                  <tr>
                    <td style="padding: 50px 40px;">
                      <div style="text-align: center; margin-bottom: 40px;">
                        <h2 style="color: #1a202c; font-size: 28px; font-weight: 600; margin: 0 0 16px 0;">Reset Your Password 🔐</h2>
                        <p style="color: #4a5568; font-size: 16px; margin: 0; line-height: 1.6;">
                          No worries! It happens to the best of us. Click the button below to reset your password and get back to advancing your career.
                        </p>
                      </div>
                      
                      <!-- Reset button -->
                      <div style="text-align: center; margin: 40px 0;">
                        <a href="${passwordResetLink}" 
                           style="background: linear-gradient(135deg, #0080FF 0%, #0066CC 100%); color: #ffffff; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; display: inline-block; box-shadow: 0 4px 12px rgba(0, 128, 255, 0.3); transition: all 0.2s;">
                          🔄 Reset Password
                        </a>
                      </div>
                      
                      <!-- Security notice -->
                      <div style="background-color: #fef5e7; border-left: 4px solid #0080FF; border-radius: 8px; padding: 20px; margin: 40px 0;">
                        <div style="display: flex; align-items: flex-start;">
                          <span style="font-size: 20px; margin-right: 12px; margin-top: 2px;">⚠️</span>
                          <div>
                            <h4 style="color: #0080FF; font-size: 16px; font-weight: 600; margin: 0 0 8px 0;">Security Notice</h4>
                            <p style="color: #4a5568; font-size: 14px; margin: 0; line-height: 1.5;">
                              If you didn't request this password reset, please ignore this email. Your account is safe and no changes have been made.
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Alternative link -->
                      <div style="background-color: #edf2f7; border-radius: 8px; padding: 20px; margin: 30px 0;">
                        <p style="color: #718096; font-size: 14px; margin: 0 0 8px 0; text-align: center;">
                          <strong>Button not working?</strong>
                        </p>
                        <p style="color: #718096; font-size: 12px; margin: 0; text-align: center; word-break: break-all;">
                          Copy and paste this link: <br>
                          <span style="color: #0080FF; font-family: monospace;">${passwordResetLink}</span>
                        </p>
                      </div>
                      
                      <!-- Expiration notice -->
                      <div style="text-align: center; margin: 30px 0;">
                        <p style="color: #e53e3e; font-size: 14px; margin: 0; background-color: #fed7d7; padding: 12px 20px; border-radius: 8px; display: inline-block;">
                          ⏰ This reset link expires in 24 hours
                        </p>
                      </div>
                    </td>
                  </tr>
                  
                  <!-- Footer -->
                  <tr>
                    <td style="background-color: #f7fafc; padding: 30px 40px; border-top: 1px solid #e2e8f0;">
                      <div style="text-align: center;">
                        <p style="color: #718096; font-size: 14px; margin: 0 0 12px 0;">
                          Need help? We're here for you!
                        </p>
                        <p style="color: #a0aec0; font-size: 12px; margin: 0 0 20px 0;">
                          If you didn't request this password reset, please ignore this email.
                        </p>
                        <div style="border-top: 1px solid #e2e8f0; padding-top: 20px; margin-top: 20px;">
                          <p style="color: #a0aec0; font-size: 11px; margin: 0;">
                            © 2024 CareerDart. All rights reserved.<br>
                            Rocket-powered AI tools for your career success.
                          </p>
                        </div>
                      </div>
                    </td>
                  </tr>
                  
                </table>
              </td>
            </tr>
          </table>
        </body>
      </html>
    `,
  };
}
