import { importJobFromUrl } from './actions.js';

// ... existing code ...

export const jobsApi = async (req: any, res: any, context: any) => {
  // Placeholder for jobs API
  res.status(200).json({ message: 'Jobs API endpoint' });
};

export const importJobFromUrlApi = async (req: any, res: any, context: any) => {
  try {
    if (req.method !== 'POST') {
      return res.status(405).json({ message: 'Method not allowed' });
    }

    const { url } = req.body;

    if (!url) {
      return res.status(400).json({ message: 'URL is required' });
    }

    // Call the importJobFromUrl action
    const jobData = await importJobFromUrl({ url }, context);

    res.status(200).json(jobData);
  } catch (error: any) {
    console.error('Error in importJobFromUrlApi:', error);
    res.status(error.statusCode || 500).json({ 
      message: error.message || 'Failed to import job from URL' 
    });
  }
}; 