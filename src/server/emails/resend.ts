import { Resend } from 'resend';

const resend = new Resend(process.env.RESEND_API_KEY);

export interface EmailOptions {
  to: string;
  subject: string;
  html?: string;
  text?: string;
  from?: string;
}

export class ResendEmailService {
  private defaultFrom = 'CareerDart <<EMAIL>>';

  async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      const { data, error } = await resend.emails.send({
        from: options.from || this.defaultFrom,
        to: [options.to],
        subject: options.subject,
        html: options.html,
        text: options.text || options.subject,
      });

      if (error) {
        console.error('Resend email error:', error);
        return false;
      }

      console.log('Email sent successfully:', data?.id);
      return true;
    } catch (error) {
      console.error('Failed to send email:', error);
      return false;
    }
  }

  async sendVerificationEmail(email: string, verificationLink: string): Promise<boolean> {
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Verify Your Email - CareerDart</title>
        </head>
        <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <div style="display: inline-flex; align-items: center; justify-content: center; margin-bottom: 10px;">
              <h1 style="color: #0080FF; margin: 0; font-size: 24px;">Career<span style="color: #5CAEFF;">Dart</span></h1>
            </div>
          </div>
          
          <div style="background: linear-gradient(135deg, #0080FF 0%, #0066CC 100%); border-radius: 8px; padding: 30px; margin-bottom: 30px; color: white;">
            <h2 style="color: white; margin-top: 0;">Verify Your Email Address</h2>
            <p style="margin-bottom: 25px; color: rgba(255, 255, 255, 0.9);">Welcome to CareerDart! Please verify your email address to complete your account setup and start creating amazing cover letters.</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${verificationLink}" 
                 style="background: white; color: #0080FF; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: 500; display: inline-block;">
                Verify Email Address
              </a>
            </div>
            
            <p style="font-size: 14px; color: rgba(255, 255, 255, 0.8); margin-bottom: 0;">
              If the button doesn't work, copy and paste this link into your browser:<br>
              <a href="${verificationLink}" style="color: #5CAEFF; word-break: break-all;">${verificationLink}</a>
            </p>
          </div>
          
          <div style="text-align: center; font-size: 12px; color: #94a3b8;">
            <p>© 2024 CareerDart. All rights reserved.</p>
          </div>
        </body>
      </html>
    `;

    return this.sendEmail({
      to: email,
      subject: 'Verify Your Email - CareerDart',
      html,
      text: `Welcome to CareerDart! Please verify your email address by clicking this link: ${verificationLink}`
    });
  }

  async sendPasswordResetEmail(email: string, resetLink: string): Promise<boolean> {
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Reset Your Password - CareerDart</title>
        </head>
        <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <div style="display: inline-flex; align-items: center; justify-content: center; margin-bottom: 10px;">
              <h1 style="color: #0080FF; margin: 0; font-size: 24px;">Career<span style="color: #5CAEFF;">Dart</span></h1>
            </div>
          </div>
          
          <div style="background: linear-gradient(135deg, #0080FF 0%, #0066CC 100%); border-radius: 8px; padding: 30px; margin-bottom: 30px; color: white;">
            <h2 style="color: white; margin-top: 0;">Reset Your Password</h2>
            <p style="margin-bottom: 25px; color: rgba(255, 255, 255, 0.9);">You requested to reset your password. Click the button below to set a new password for your CareerDart account.</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetLink}" 
                 style="background: white; color: #0080FF; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: 500; display: inline-block;">
                Reset Password
              </a>
            </div>
            
            <p style="font-size: 14px; color: rgba(255, 255, 255, 0.8); margin-bottom: 0;">
              If the button doesn't work, copy and paste this link into your browser:<br>
              <a href="${resetLink}" style="color: #5CAEFF; word-break: break-all;">${resetLink}</a>
            </p>
            
            <p style="font-size: 12px; color: rgba(255, 255, 255, 0.7); margin-top: 20px;">
              If you didn't request this password reset, you can safely ignore this email.
            </p>
          </div>
          
          <div style="text-align: center; font-size: 12px; color: #94a3b8;">
            <p>© 2024 CareerDart. All rights reserved.</p>
          </div>
        </body>
      </html>
    `;

    return this.sendEmail({
      to: email,
      subject: 'Reset Your Password - CareerDart',
      html,
      text: `You requested to reset your password. Click this link to set a new password: ${resetLink}`
    });
  }
}

export const emailService = new ResendEmailService(); 