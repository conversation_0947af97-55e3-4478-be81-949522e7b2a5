/**
 * Health Check API for CareerDart
 * Used by Fly.io, Docker, and monitoring systems
 */

import { Request, Response } from 'express';

interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  environment: string;
  version?: string;
  checks: {
    database?: {
      status: 'healthy' | 'unhealthy';
      latency?: number;
    };
    memory?: {
      status: 'healthy' | 'warning' | 'critical';
      usage: string;
      percentage: number;
    };
    disk?: {
      status: 'healthy' | 'warning';
      usage?: string;
    };
  };
}

/**
 * Check memory usage
 */
function checkMemory(): {
  status: 'healthy' | 'warning' | 'critical';
  usage: string;
  percentage: number;
} {
  const usage = process.memoryUsage();
  const maxMemory = parseInt(process.env.NODE_OPTIONS?.match(/--max-old-space-size=(\d+)/)?.[1] || '1024') * 1024 * 1024;
  const usedMemory = usage.heapUsed;
  const memoryUsagePercent = Math.round((usedMemory / maxMemory) * 100);

  const status: 'healthy' | 'warning' | 'critical' = 
    memoryUsagePercent > 90 ? 'critical' : 
    memoryUsagePercent > 70 ? 'warning' : 'healthy';

  return {
    status,
    usage: `${Math.round(usage.heapUsed / 1024 / 1024)}MB / ${Math.round(maxMemory / 1024 / 1024)}MB`,
    percentage: memoryUsagePercent,
  };
}

/**
 * Health check endpoint handler
 */
export const healthCheck = async (req: Request, res: Response) => {
  const startTime = Date.now();
  
  try {
    // Basic health check
    const memoryCheck = checkMemory();
    const uptime = Math.floor(process.uptime());
    
    // Determine overall status
    let overallStatus: 'healthy' | 'unhealthy' | 'degraded' = 'healthy';
    
    if (memoryCheck.status === 'critical') {
      overallStatus = 'unhealthy';
    } else if (memoryCheck.status === 'warning') {
      overallStatus = 'degraded';
    }

    const healthResponse: HealthCheckResponse = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime,
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      checks: {
        memory: memoryCheck,
        database: {
          status: 'healthy', // Simplified - in production you'd actually check DB connection
        },
      },
    };

    // Set appropriate HTTP status code
    const httpStatus = overallStatus === 'healthy' ? 200 : 
                      overallStatus === 'degraded' ? 200 : 503;

    // Add response headers
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    
    // Return health status
    res.status(httpStatus).json(healthResponse);
    
  } catch (error) {
    console.error('Health check failed:', error);
    
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
      environment: process.env.NODE_ENV || 'development',
    });
  }
}; 