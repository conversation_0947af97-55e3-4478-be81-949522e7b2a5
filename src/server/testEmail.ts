import { emailSender } from 'wasp/server/email';
import { getVerificationEmailContent } from './auth.js';

export async function testResendSMTP() {
  console.log('🧪 Testing RESEND SMTP Configuration...');
  
  console.log('📧 Environment Variables:');
  console.log('  - SMTP_HOST:', process.env.SMTP_HOST);
  console.log('  - SMTP_PORT:', process.env.SMTP_PORT);
  console.log('  - SMTP_USERNAME:', process.env.SMTP_USERNAME);
  console.log('  - SMTP_PASSWORD:', process.env.SMTP_PASSWORD ? '[SET]' : '[NOT SET]');
  console.log('  - SEND_EMAILS_IN_DEVELOPMENT:', process.env.SEND_EMAILS_IN_DEVELOPMENT);
  console.log('  - SKIP_EMAIL_VERIFICATION_IN_DEV:', process.env.SKIP_EMAIL_VERIFICATION_IN_DEV);
  
  try {
    // Test the email content function
    console.log('🔧 Testing email content function...');
    const emailContent = getVerificationEmailContent({ verificationLink: 'https://test.com/verify?token=test123' });
    console.log('📧 Email content generated:', emailContent.subject);
    
    // Test sending the email
    console.log('📤 Attempting to send test email...');
    const result = await emailSender.send({
      to: '<EMAIL>',
      subject: emailContent.subject,
      text: emailContent.text,
      html: emailContent.html
    });
    
    console.log('✅ Email sent successfully:', result);
    return { success: true, result };
  } catch (error) {
    console.error('❌ Email sending failed:', error);
    return { success: false, error: error.message };
  }
} 