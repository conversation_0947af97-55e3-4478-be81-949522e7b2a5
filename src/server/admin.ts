import { HttpError } from 'wasp/server';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Temporary admin check - replace with proper role-based auth when admin fields are added
const isAdmin = (user: any): boolean => {
  // For now, check if user email contains specific admin domains
  const adminEmails = ['<EMAIL>', '<EMAIL>'];
  const result = adminEmails.includes(user?.email) || user?.email?.endsWith('@admin.careerdart.com');
  
  return result;
};

// Admin Analytics Query
export const getAdminAnalytics = async (_args: any, context: any) => {
  console.log('=== getAdminAnalytics called ===');
  console.log('User:', context.user?.email);
  console.log('Available entities:', Object.keys(context.entities || {}));
  
  if (!context.user || !isAdmin(context.user)) {
    throw new HttpError(403, 'Access denied. Admin privileges required.');
  }

  try {
    // Check if entities are available
    if (!context.entities) {
      console.error('Context entities is undefined');
      throw new HttpError(500, 'Database entities not available');
    }
    
    if (!context.entities.User) {
      console.error('User entity not available. Available entities:', Object.keys(context.entities));
      throw new HttpError(500, 'User entity not available');
    }

    // Get user statistics
    const totalUsers = await context.entities.User.count();
    const activeUsers = await context.entities.User.count({
      where: {
        lastLoginAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        },
      },
    });

    const paidUsers = await context.entities.User.count({
      where: { hasPaid: true },
    });

    // Get content statistics
    const totalCoverLetters = await context.entities.CoverLetter.count();
    const totalJobs = await context.entities.Job.count();
    const totalResumes = await context.entities.Resume.count();

    // Get recent activity
    const recentUsers = await context.entities.User.findMany({
      orderBy: { createdAt: 'desc' },
      take: 10,
      select: {
        id: true,
        username: true,
        email: true,
        createdAt: true,
        hasPaid: true,
        lastLoginAt: true,
      },
    });

    // Calculate growth metrics
    const lastWeek = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const newUsersThisWeek = await context.entities.User.count({
      where: {
        createdAt: { gte: lastWeek },
      },
    });

    const coverLettersThisWeek = await context.entities.CoverLetter.count({
      where: {
        createdAt: { gte: lastWeek },
      },
    });

    return {
      overview: {
        totalUsers,
        activeUsers,
        paidUsers,
        totalCoverLetters,
        totalJobs,
        totalResumes,
        conversionRate: totalUsers > 0 ? ((paidUsers / totalUsers) * 100).toFixed(1) : '0',
      },
      growth: {
        newUsersThisWeek,
        coverLettersThisWeek,
        userGrowthRate: '12.5%', // Placeholder - calculate based on historical data
        revenueGrowthRate: '8.3%', // Placeholder
      },
      recentUsers,
      timestamp: new Date(),
    };
  } catch (error) {
    console.error('Error fetching admin analytics:', error);
    throw new HttpError(500, 'Failed to fetch analytics data');
  }
};

// System Metrics Query
export const getSystemMetrics = async (args: { timeframe?: string }, context: any) => {
  if (!context.user || !isAdmin(context.user)) {
    throw new HttpError(403, 'Access denied. Admin privileges required.');
  }

  try {
    const timeframe = args.timeframe || '24h';
    let startDate: Date;

    switch (timeframe) {
      case '1h':
        startDate = new Date(Date.now() - 60 * 60 * 1000);
        break;
      case '24h':
        startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
    }

    const metrics = await context.entities.SystemMetrics.findMany({
      where: {
        timestamp: { gte: startDate },
      },
      orderBy: { timestamp: 'desc' },
      take: 100,
    });

    // Group metrics by type for easier frontend consumption
    const groupedMetrics = metrics.reduce((acc: any, metric: any) => {
      if (!acc[metric.metricType]) {
        acc[metric.metricType] = [];
      }
      acc[metric.metricType].push(metric);
      return acc;
    }, {});

    return {
      metrics: groupedMetrics,
      timeframe,
      totalDataPoints: metrics.length,
    };
  } catch (error) {
    console.error('Error fetching system metrics:', error);
    throw new HttpError(500, 'Failed to fetch system metrics');
  }
};

// User Activity Query
export const getUserActivity = async (args: { limit?: number; userId?: number }, context: any) => {
  if (!context.user || !isAdmin(context.user)) {
    throw new HttpError(403, 'Access denied. Admin privileges required.');
  }

  try {
    const limit = args.limit || 50;
    const activities = await context.entities.UserActivity.findMany({
      where: args.userId ? { userId: args.userId } : {},
      orderBy: { timestamp: 'desc' },
      take: limit,
    });

    return activities;
  } catch (error) {
    console.error('Error fetching user activity:', error);
    throw new HttpError(500, 'Failed to fetch user activity');
  }
};

// Error Logs Query
export const getErrorLogs = async (args: { severity?: string; limit?: number }, context: any) => {
  if (!context.user || !isAdmin(context.user)) {
    throw new HttpError(403, 'Access denied. Admin privileges required.');
  }

  try {
    const limit = args.limit || 50;
    const whereClause: any = {};
    
    if (args.severity) {
      whereClause.severity = args.severity;
    }

    const errors = await context.entities.ErrorLog.findMany({
      where: whereClause,
      orderBy: { timestamp: 'desc' },
      take: limit,
    });

    return errors;
  } catch (error) {
    console.error('Error fetching error logs:', error);
    throw new HttpError(500, 'Failed to fetch error logs');
  }
};

// All Users Query
export const getAllUsers = async (args: { page?: number; limit?: number, search?: string }, context: any) => {
  if (!context.user || !isAdmin(context.user)) {
    throw new HttpError(403, 'Access denied. Admin privileges required.');
  }

  try {
    const page = args.page || 1;
    const limit = args.limit || 25;
    const skip = (page - 1) * limit;

    const whereClause: any = {};
    if (args.search) {
      whereClause.OR = [
        { username: { contains: args.search, mode: 'insensitive' } },
        { email: { contains: args.search, mode: 'insensitive' } },
      ];
    }

    const [users, totalCount] = await Promise.all([
      context.entities.User.findMany({
        where: whereClause,
        select: {
          id: true,
          username: true,
          email: true,
          hasPaid: true,
          subscriptionStatus: true,
          credits: true,
          createdAt: true,
          lastLoginAt: true,
          // role: true, // Enable when admin fields are added
          // isAdmin: true,
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      context.entities.User.count({ where: whereClause }),
    ]);

    return {
      users,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    };
  } catch (error) {
    console.error('Error fetching users:', error);
    throw new HttpError(500, 'Failed to fetch users');
  }
};

// Log User Activity Action
export const logUserActivity = async (
  args: {
    userId?: number;
    action: string;
    resource?: string;
    details?: any;
    duration?: number;
  },
  context: any
) => {
  try {
    const userAgent = context.req?.headers?.['user-agent'];
    const ipAddress = context.req?.ip || context.req?.connection?.remoteAddress;

    await context.entities.UserActivity.create({
      data: {
        userId: args.userId || context.user?.id,
        action: args.action,
        resource: args.resource,
        details: args.details,
        duration: args.duration,
        userAgent,
        ipAddress,
        timestamp: new Date(),
      },
    });

    return { success: true };
  } catch (error) {
    console.error('Error logging user activity:', error);
    // Don't throw error for logging failures to avoid breaking main functionality
    return { success: false, error: error.message };
  }
};

// Log Error Event Action
export const logErrorEvent = async (
  args: {
    errorType: string;
    severity: string;
    message: string;
    stackTrace?: string;
    endpoint?: string;
    method?: string;
    statusCode?: number;
    additionalData?: any;
  },
  context: any
) => {
  try {
    const userAgent = context.req?.headers?.['user-agent'];
    const ipAddress = context.req?.ip || context.req?.connection?.remoteAddress;

    await context.entities.ErrorLog.create({
      data: {
        errorType: args.errorType,
        severity: args.severity,
        message: args.message,
        stackTrace: args.stackTrace,
        userId: context.user?.id,
        endpoint: args.endpoint,
        method: args.method,
        statusCode: args.statusCode,
        userAgent,
        ipAddress,
        additionalData: args.additionalData,
        timestamp: new Date(),
      },
    });

    return { success: true };
  } catch (error) {
    console.error('Error logging error event:', error);
    return { success: false, error: error.message };
  }
};

// Record System Metric Action
export const recordSystemMetric = async (
  args: {
    metricType: string;
    metricName: string;
    value: number;
    unit?: string;
    metadata?: any;
  },
  context: any
) => {
  try {
    await context.entities.SystemMetrics.create({
      data: {
        metricType: args.metricType,
        metricName: args.metricName,
        value: args.value,
        unit: args.unit,
        metadata: args.metadata,
        timestamp: new Date(),
      },
    });

    return { success: true };
  } catch (error) {
    console.error('Error recording system metric:', error);
    return { success: false, error: error.message };
  }
};

// Update User Role Action (placeholder for when admin fields are enabled)
export const updateUserRole = async (
  args: {
    userId: number;
    role: string;
    isAdmin: boolean;
  },
  context: any
) => {
  if (!context.user || !isAdmin(context.user)) {
    throw new HttpError(403, 'Access denied. Admin privileges required.');
  }

  try {
    // For now, just log the attempted role change
    await logUserActivity({
      userId: context.user.id,
      action: 'admin_role_change_attempted',
      resource: `user_${args.userId}`,
      details: { targetRole: args.role, targetIsAdmin: args.isAdmin },
    }, context);

    // TODO: Implement actual role update when admin fields are enabled
    // const updatedUser = await context.entities.User.update({
    //   where: { id: args.userId },
    //   data: { role: args.role, isAdmin: args.isAdmin },
    // });

    return { 
      success: false, 
      message: 'Role update functionality will be enabled when admin fields are added to the database.' 
    };
  } catch (error) {
    console.error('Error updating user role:', error);
    throw new HttpError(500, 'Failed to update user role');
  }
};

// Lighthouse Analytics Query
export const getLighthouseMetrics = async (_args: any, context: any) => {
  console.log('=== getLighthouseMetrics called ===');
  console.log('User:', context.user?.email);
  
  if (!context.user || !isAdmin(context.user)) {
    throw new HttpError(403, 'Access denied. Admin privileges required.');
  }

  try {
    // Load Lighthouse report from file - prioritize latest reports first
    const possiblePaths = [
      path.join(process.cwd(), 'lighthouse-final.json'), // Latest comprehensive report
      path.join(process.cwd(), 'lighthouse-latest.json'), // Most recent report
      path.join(process.cwd(), 'lighthouse-current.json'), // Current version report
      path.join(process.cwd(), 'lighthouse-report.json'), // Default report
      path.join(process.cwd(), '../../../lighthouse-final.json'), // In Wasp dev mode
      path.join(process.cwd(), '../../../lighthouse-latest.json'),
      path.join(process.cwd(), '../../../lighthouse-current.json'),
      path.join(process.cwd(), '../../../lighthouse-report.json'),
      path.join(__dirname, '../../../lighthouse-final.json'),
      path.join(__dirname, '../../../lighthouse-latest.json'),
      path.join(__dirname, '../../../lighthouse-current.json'),
      path.join(__dirname, '../../../lighthouse-report.json'),
      path.join(__dirname, '../../../../lighthouse-final.json'),
      path.join(__dirname, '../../../../lighthouse-latest.json'),
      path.join(__dirname, '../../../../lighthouse-current.json'),
      path.join(__dirname, '../../../../lighthouse-report.json'),
      '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/lighthouse-final.json', // Absolute path as fallback
      '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/lighthouse-latest.json',
      '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/lighthouse-current.json',
      '/Users/<USER>/CascadeProjects/Coverletter/coverlettergpt/lighthouse-report.json'
    ];
    
    let lighthouseFilePath: string | null = null;
    let lighthouseData: any = null;
    let reportType = 'unknown';
    
    for (const testPath of possiblePaths) {
      console.log('Checking lighthouse file at:', testPath);
      if (fs.existsSync(testPath)) {
        lighthouseFilePath = testPath;
        // Determine report type from filename
        if (testPath.includes('final')) reportType = 'final';
        else if (testPath.includes('latest')) reportType = 'latest';
        else if (testPath.includes('current')) reportType = 'current';
        else reportType = 'standard';
        
        console.log(`Found lighthouse file at: ${testPath} (type: ${reportType})`);
        break;
      }
    }
    
    if (!lighthouseFilePath) {
      console.log('Lighthouse report file not found in any location. Checked:', possiblePaths);
      return {
        available: false,
        message: 'Lighthouse report not available. Run `lighthouse https://careerdart.com --output=json --output-path=./lighthouse-final.json` to generate the latest report.'
      };
    }

    try {
      lighthouseData = JSON.parse(fs.readFileSync(lighthouseFilePath, 'utf8'));
      console.log(`Successfully loaded lighthouse data from: ${lighthouseFilePath} (type: ${reportType})`);
    } catch (parseError) {
      console.error('Error parsing lighthouse file:', parseError);
      return {
        available: false,
        message: 'Lighthouse report file found but could not be parsed. Please ensure it is valid JSON.'
      };
    }
    
    // At this point, lighthouseData is guaranteed to be valid
    if (!lighthouseData) {
      return {
        available: false,
        message: 'Lighthouse data is empty or invalid.'
      };
    }
    
    // Extract key metrics
    const categories = lighthouseData.categories;
    const audits = lighthouseData.audits;
    
    console.log('Lighthouse categories found:', Object.keys(categories || {}));
    console.log('Performance score:', categories?.performance?.score);
    
    // Core Web Vitals from audits
    const coreWebVitals = {
      firstContentfulPaint: {
        score: audits['first-contentful-paint']?.score || 0,
        value: audits['first-contentful-paint']?.numericValue || 0,
        displayValue: audits['first-contentful-paint']?.displayValue || 'N/A'
      },
      largestContentfulPaint: {
        score: audits['largest-contentful-paint']?.score || 0,
        value: audits['largest-contentful-paint']?.numericValue || 0,
        displayValue: audits['largest-contentful-paint']?.displayValue || 'N/A'
      },
      totalBlockingTime: {
        score: audits['total-blocking-time']?.score || 0,
        value: audits['total-blocking-time']?.numericValue || 0,
        displayValue: audits['total-blocking-time']?.displayValue || 'N/A'
      },
      cumulativeLayoutShift: {
        score: audits['cumulative-layout-shift']?.score || 0,
        value: audits['cumulative-layout-shift']?.numericValue || 0,
        displayValue: audits['cumulative-layout-shift']?.displayValue || 'N/A'
      },
      speedIndex: {
        score: audits['speed-index']?.score || 0,
        value: audits['speed-index']?.numericValue || 0,
        displayValue: audits['speed-index']?.displayValue || 'N/A'
      },
      interactive: {
        score: audits['interactive']?.score || 0,
        value: audits['interactive']?.numericValue || 0,
        displayValue: audits['interactive']?.displayValue || 'N/A'
      }
    };

    // Performance opportunities - focus on highest impact
    const opportunities = [
      'render-blocking-resources',
      'unused-css-rules',
      'unused-javascript',
      'modern-image-formats',
      'uses-optimized-images',
      'uses-text-compression',
      'server-response-time',
      'redirects',
      'unminified-css',
      'unminified-javascript',
      'unused-css-rules',
      'legacy-javascript',
      'preload-lcp-image',
      'reduce-unused-css'
    ].map(auditId => ({
      id: auditId,
      title: audits[auditId]?.title || auditId,
      description: audits[auditId]?.description || '',
      score: audits[auditId]?.score || 0,
      displayValue: audits[auditId]?.displayValue || '',
      potentialSavings: audits[auditId]?.details?.overallSavingsMs || 0
    })).filter(opp => opp.score !== null && audits[opp.id]);

    const result = {
      available: true,
      reportType: reportType,
      filePath: lighthouseFilePath,
      testUrl: lighthouseData.finalDisplayedUrl,
      fetchTime: lighthouseData.fetchTime,
      lighthouseVersion: lighthouseData.lighthouseVersion,
      categories: {
        performance: {
          score: Math.round((categories.performance?.score || 0) * 100),
          title: categories.performance?.title || 'Performance'
        },
        accessibility: {
          score: Math.round((categories.accessibility?.score || 0) * 100),
          title: categories.accessibility?.title || 'Accessibility'
        },
        bestPractices: {
          score: Math.round((categories['best-practices']?.score || 0) * 100),
          title: categories['best-practices']?.title || 'Best Practices'
        },
        seo: {
          score: Math.round((categories.seo?.score || 0) * 100),
          title: categories.seo?.title || 'SEO'
        }
      },
      coreWebVitals,
      opportunities: opportunities.slice(0, 12), // Top 12 opportunities
      overallScore: Math.round(
        ((categories.performance?.score || 0) + 
         (categories.accessibility?.score || 0) + 
         (categories['best-practices']?.score || 0) + 
         (categories.seo?.score || 0)) / 4 * 100
      ),
      // Additional metadata for admin dashboard
      optimizationStatus: {
        seoImplemented: categories.seo?.score > 0.9,
        performanceOptimized: categories.performance?.score > 0.7,
        accessibilityCompliant: categories.accessibility?.score > 0.95,
        bestPracticesFollowed: categories['best-practices']?.score > 0.9
      }
    };
    
    console.log('Returning lighthouse data with scores:', {
      performance: result.categories.performance.score,
      accessibility: result.categories.accessibility.score,
      bestPractices: result.categories.bestPractices.score,
      seo: result.categories.seo.score,
      overall: result.overallScore,
      reportType: reportType
    });
    
    return result;
  } catch (error) {
    console.error('Error loading Lighthouse metrics:', error);
    throw new HttpError(500, 'Failed to load Lighthouse metrics');
  }
};

// Update User Details Action
export const updateUserDetails = async (
  args: {
    userId: number;
    updates: {
      username?: string;
      email?: string;
      credits?: number;
      hasPaid?: boolean;
      subscriptionStatus?: string;
      gptModel?: string;
      bio?: string;
      yearsOfExperience?: number;
    };
  },
  context: any
) => {
  if (!context.user || !isAdmin(context.user)) {
    throw new HttpError(403, 'Access denied. Admin privileges required.');
  }

  try {
    // Validate the user exists
    const existingUser = await context.entities.User.findUnique({
      where: { id: args.userId },
    });

    if (!existingUser) {
      throw new HttpError(404, 'User not found');
    }

    // Log the admin action
    await logUserActivity({
      userId: context.user.id,
      action: 'admin_user_update',
      resource: `user_${args.userId}`,
      details: { updates: args.updates, targetUser: existingUser.email },
    }, context);

    // Update the user
    const updatedUser = await context.entities.User.update({
      where: { id: args.userId },
      data: {
        ...args.updates,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        username: true,
        email: true,
        hasPaid: true,
        credits: true,
        subscriptionStatus: true,
        gptModel: true,
        bio: true,
        yearsOfExperience: true,
        createdAt: true,
        updatedAt: true,
        lastLoginAt: true,
      },
    });

    return {
      success: true,
      user: updatedUser,
      message: 'User updated successfully',
    };
  } catch (error) {
    console.error('Error updating user:', error);
    if (error instanceof HttpError) throw error;
    throw new HttpError(500, 'Failed to update user');
  }
};

// Set User Verification Status Action
export const setUserVerified = async (
  args: {
    userId: number;
    verified: boolean;
  },
  context: any
) => {
  if (!context.user || !isAdmin(context.user)) {
    throw new HttpError(403, 'Access denied. Admin privileges required.');
  }

  try {
    // For now, we'll store verification status in user preferences or bio
    // In the future, this should be a proper database field
    const existingUser = await context.entities.User.findUnique({
      where: { id: args.userId },
    });

    if (!existingUser) {
      throw new HttpError(404, 'User not found');
    }

    // Log the admin action
    await logUserActivity({
      userId: context.user.id,
      action: 'admin_user_verification',
      resource: `user_${args.userId}`,
      details: { 
        verified: args.verified, 
        targetUser: existingUser.email,
        previousStatus: existingUser.bio?.includes('[VERIFIED]') 
      },
    }, context);

    // Update bio to include/remove verification status
    let updatedBio = existingUser.bio || '';
    if (args.verified) {
      if (!updatedBio.includes('[VERIFIED]')) {
        updatedBio = `[VERIFIED] ${updatedBio}`.trim();
      }
    } else {
      updatedBio = updatedBio.replace(/\[VERIFIED\]\s*/, '').trim();
    }

    const updatedUser = await context.entities.User.update({
      where: { id: args.userId },
      data: {
        bio: updatedBio,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        username: true,
        email: true,
        bio: true,
        updatedAt: true,
      },
    });

    return {
      success: true,
      user: updatedUser,
      verified: args.verified,
      message: `User ${args.verified ? 'verified' : 'unverified'} successfully`,
    };
  } catch (error) {
    console.error('Error setting user verification:', error);
    if (error instanceof HttpError) throw error;
    throw new HttpError(500, 'Failed to update user verification status');
  }
};

// Send Email to User Action
export const sendUserEmail = async (
  args: {
    userId: number;
    subject: string;
    message: string;
    emailType?: string; // 'notification', 'warning', 'promotion', etc.
  },
  context: any
) => {
  if (!context.user || !isAdmin(context.user)) {
    throw new HttpError(403, 'Access denied. Admin privileges required.');
  }

  try {
    const targetUser = await context.entities.User.findUnique({
      where: { id: args.userId },
      select: { id: true, username: true, email: true },
    });

    if (!targetUser) {
      throw new HttpError(404, 'User not found');
    }

    // Log the admin action
    await logUserActivity({
      userId: context.user.id,
      action: 'admin_email_sent',
      resource: `user_${args.userId}`,
      details: { 
        subject: args.subject,
        emailType: args.emailType || 'general',
        targetUser: targetUser.email 
      },
    }, context);

    // Here you would integrate with your email service (Resend, SendGrid, etc.)
    // For now, we'll just simulate the email sending
    console.log(`📧 Admin Email Sent:
    From: <EMAIL>
    To: ${targetUser.email}
    Subject: ${args.subject}
    Message: ${args.message}
    Type: ${args.emailType || 'general'}`);

    // In a real implementation, you'd do:
    // await emailService.send({
    //   to: targetUser.email,
    //   subject: args.subject,
    //   text: args.message,
    //   from: '<EMAIL>'
    // });

    return {
      success: true,
      message: `Email sent to ${targetUser.email}`,
      emailDetails: {
        recipient: targetUser.email,
        subject: args.subject,
        sentAt: new Date(),
      },
    };
  } catch (error) {
    console.error('Error sending user email:', error);
    if (error instanceof HttpError) throw error;
    throw new HttpError(500, 'Failed to send email to user');
  }
};

// Delete User Account Action
export const adminDeleteUserAccount = async (
  args: {
    userId: number;
    reason?: string;
    deleteAllData?: boolean;
  },
  context: any
) => {
  if (!context.user || !isAdmin(context.user)) {
    throw new HttpError(403, 'Access denied. Admin privileges required.');
  }

  try {
    const targetUser = await context.entities.User.findUnique({
      where: { id: args.userId },
      select: { 
        id: true, 
        username: true, 
        email: true,
        createdAt: true,
        hasPaid: true,
      },
    });

    if (!targetUser) {
      throw new HttpError(404, 'User not found');
    }

    // Prevent deleting admin users (safety check)
    if (isAdmin(targetUser)) {
      throw new HttpError(403, 'Cannot delete admin users');
    }

    // Log the admin action BEFORE deletion
    await logUserActivity({
      userId: context.user.id,
      action: 'admin_user_deletion',
      resource: `user_${args.userId}`,
      details: { 
        targetUser: targetUser.email,
        reason: args.reason || 'No reason provided',
        deleteAllData: args.deleteAllData || false,
        userStats: {
          memberSince: targetUser.createdAt,
          wasPaidUser: targetUser.hasPaid,
        }
      },
    }, context);

    if (args.deleteAllData) {
      // Delete all related data (cascade should handle most of this)
      await context.entities.User.delete({
        where: { id: args.userId },
      });
    } else {
      // Soft delete - anonymize the user data
      await context.entities.User.update({
        where: { id: args.userId },
        data: {
          username: `deleted_user_${args.userId}`,
          email: `deleted_${args.userId}@deleted.com`,
          bio: '[ACCOUNT DELETED]',
          profileImageUrl: null,
        },
      });
    }

    return {
      success: true,
      message: args.deleteAllData 
        ? 'User account and all data permanently deleted'
        : 'User account deactivated and anonymized',
      deletionType: args.deleteAllData ? 'permanent' : 'soft',
      deletedAt: new Date(),
    };
  } catch (error) {
    console.error('Error deleting user account:', error);
    if (error instanceof HttpError) throw error;
    throw new HttpError(500, 'Failed to delete user account');
  }
}; 