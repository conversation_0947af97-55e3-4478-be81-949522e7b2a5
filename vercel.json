{"version": 2, "outputDirectory": ".wasp/build/web-app/build", "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": ".wasp/build/web-app/build", "framework": null, "installCommand": "npm install", "buildCommand": "npm run build"}}], "rewrites": [{"source": "/static/(.*)", "destination": "/static/$1"}, {"source": "/(.*)", "destination": "/index.html"}], "env": {"NODE_ENV": "production", "WASP_ENV": "production", "REACT_APP_API_URL": "https://careerdart-server.vercel.app"}, "build": {"env": {"NODE_ENV": "production", "WASP_ENV": "production", "CI": "true", "REACT_APP_API_URL": "https://careerdart-server.vercel.app"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}