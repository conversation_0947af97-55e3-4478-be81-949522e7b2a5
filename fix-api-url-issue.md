# Fix API URL Issue - Frontend Calling Wrong Backend

## 🔍 Problem Analysis

The frontend is calling `careerdart-prod.fly.dev/auth/me` instead of `api.careerdart.com/auth/me` because:

1. **Wasp Build Configuration**: Wasp hardcodes the API URL at build time
2. **Environment Variables**: Not properly set during the build process
3. **Netlify Configuration**: Missing correct environment variables

## 🛠️ Solution Steps

### 1. Fix Netlify Environment Variables

**Go to Netlify Dashboard → Site Settings → Environment Variables**

Set these variables:
```
REACT_APP_API_URL=https://api.careerdart.com
REACT_APP_ENV=production
NODE_ENV=production
WASP_SERVER_URL=https://api.careerdart.com
WASP_WEB_CLIENT_URL=https://careerdart.com
```

### 2. Update Build Command in Netlify

In Netlify site settings, update the build command to:
```bash
export REACT_APP_API_URL=https://api.careerdart.com && npm run build
```

### 3. Create .env.production File

Create this file in your project root:
```bash
# .env.production
REACT_APP_API_URL=https://api.careerdart.com
WASP_SERVER_URL=https://api.careerdart.com
WASP_WEB_CLIENT_URL=https://careerdart.com
NODE_ENV=production
```

### 4. Update netlify.toml

Add environment variables to netlify.toml:
```toml
[build.environment]
  NODE_ENV = "production"
  REACT_APP_API_URL = "https://api.careerdart.com"
  WASP_SERVER_URL = "https://api.careerdart.com"
  WASP_WEB_CLIENT_URL = "https://careerdart.com"
```

### 5. Rebuild and Deploy

1. **Trigger new Netlify deployment** after setting environment variables
2. **Clear browser cache** to ensure new build is loaded
3. **Test the auth flow** to verify correct API calls

## 🔧 Quick Fix Commands

### For Netlify CLI (if you have it):
```bash
# Set environment variables via CLI
netlify env:set REACT_APP_API_URL https://api.careerdart.com
netlify env:set WASP_SERVER_URL https://api.careerdart.com
netlify env:set WASP_WEB_CLIENT_URL https://careerdart.com
netlify env:set NODE_ENV production

# Trigger new deployment
netlify deploy --prod
```

### For Manual Netlify Dashboard:
1. Go to **Site Settings** → **Environment Variables**
2. Add each variable listed above
3. Go to **Deploys** → **Trigger Deploy** → **Deploy Site**

## ✅ Verification Steps

After deployment, check:

1. **Browser Network Tab**: Should show requests to `api.careerdart.com`
2. **Console Logs**: No more `careerdart-prod.fly.dev` URLs
3. **Auth Flow**: Google OAuth should work properly
4. **API Calls**: All requests should go to correct domain

## 🚨 If Still Not Working

If the issue persists:

1. **Check build logs** in Netlify for environment variable values
2. **Verify DNS** that `api.careerdart.com` resolves correctly
3. **Test API directly**: `curl https://api.careerdart.com/auth/me`
4. **Clear all caches**: Browser, CDN, and Netlify cache

## 📋 Root Cause

The issue occurs because:
- Wasp generates the frontend with hardcoded API URLs at build time
- Environment variables must be available during the build process
- Netlify needs to rebuild with correct environment variables
- Browser cache may serve old version with wrong URLs

## 🎯 Expected Result

After fix:
- ✅ Frontend calls `https://api.careerdart.com/auth/me`
- ✅ Google OAuth redirects to correct callback URL
- ✅ No more 401 errors from wrong domain
- ✅ Authentication works properly in production
