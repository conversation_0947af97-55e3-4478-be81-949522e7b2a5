-- CreateTable
CREATE TABLE "SystemMetrics" (
    "id" TEXT NOT NULL,
    "metricType" TEXT NOT NULL,
    "metricName" TEXT NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "unit" TEXT,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "metadata" JSONB,

    CONSTRAINT "SystemMetrics_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "UserActivity" (
    "id" TEXT NOT NULL,
    "userId" INTEGER,
    "sessionId" TEXT,
    "action" TEXT NOT NULL,
    "resource" TEXT,
    "details" JSONB,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "duration" INTEGER,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UserActivity_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ErrorLog" (
    "id" TEXT NOT NULL,
    "errorType" TEXT NOT NULL,
    "severity" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "stackTrace" TEXT,
    "userId" INTEGER,
    "sessionId" TEXT,
    "endpoint" TEXT,
    "method" TEXT,
    "statusCode" INTEGER,
    "userAgent" TEXT,
    "ipAddress" TEXT,
    "additionalData" JSONB,
    "resolved" BOOLEAN NOT NULL DEFAULT false,
    "resolvedAt" TIMESTAMP(3),
    "resolvedBy" TEXT,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ErrorLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FeatureUsage" (
    "id" TEXT NOT NULL,
    "featureName" TEXT NOT NULL,
    "userId" INTEGER,
    "usageCount" INTEGER NOT NULL DEFAULT 1,
    "successRate" DOUBLE PRECISION,
    "avgDuration" DOUBLE PRECISION,
    "lastUsed" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "FeatureUsage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AdminAuditLog" (
    "id" TEXT NOT NULL,
    "adminUserId" INTEGER NOT NULL,
    "action" TEXT NOT NULL,
    "targetResource" TEXT,
    "targetId" TEXT,
    "changes" JSONB,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AdminAuditLog_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "SystemMetrics_metricType_metricName_timestamp_idx" ON "SystemMetrics"("metricType", "metricName", "timestamp");

-- CreateIndex
CREATE INDEX "SystemMetrics_timestamp_idx" ON "SystemMetrics"("timestamp");

-- CreateIndex
CREATE INDEX "UserActivity_userId_timestamp_idx" ON "UserActivity"("userId", "timestamp");

-- CreateIndex
CREATE INDEX "UserActivity_action_timestamp_idx" ON "UserActivity"("action", "timestamp");

-- CreateIndex
CREATE INDEX "UserActivity_timestamp_idx" ON "UserActivity"("timestamp");

-- CreateIndex
CREATE INDEX "ErrorLog_errorType_severity_timestamp_idx" ON "ErrorLog"("errorType", "severity", "timestamp");

-- CreateIndex
CREATE INDEX "ErrorLog_userId_timestamp_idx" ON "ErrorLog"("userId", "timestamp");

-- CreateIndex
CREATE INDEX "ErrorLog_resolved_timestamp_idx" ON "ErrorLog"("resolved", "timestamp");

-- CreateIndex
CREATE INDEX "FeatureUsage_featureName_date_idx" ON "FeatureUsage"("featureName", "date");

-- CreateIndex
CREATE INDEX "FeatureUsage_userId_date_idx" ON "FeatureUsage"("userId", "date");

-- CreateIndex
CREATE UNIQUE INDEX "FeatureUsage_featureName_userId_date_key" ON "FeatureUsage"("featureName", "userId", "date");

-- CreateIndex
CREATE INDEX "AdminAuditLog_adminUserId_timestamp_idx" ON "AdminAuditLog"("adminUserId", "timestamp");

-- CreateIndex
CREATE INDEX "AdminAuditLog_action_timestamp_idx" ON "AdminAuditLog"("action", "timestamp");

-- CreateIndex
CREATE INDEX "AdminAuditLog_timestamp_idx" ON "AdminAuditLog"("timestamp");
