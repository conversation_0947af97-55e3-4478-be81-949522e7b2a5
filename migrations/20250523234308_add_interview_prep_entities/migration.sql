-- CreateTable
CREATE TABLE "InterviewQuestionSet" (
    "id" TEXT NOT NULL,
    "userId" INTEGER NOT NULL,
    "jobId" TEXT NOT NULL,
    "jobTitle" TEXT NOT NULL,
    "company" TEXT NOT NULL,
    "jobDescription" TEXT NOT NULL,
    "questions" JSONB NOT NULL,
    "generatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "InterviewQuestionSet_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SavedInterviewQuestion" (
    "id" TEXT NOT NULL,
    "userId" INTEGER NOT NULL,
    "questionType" TEXT NOT NULL,
    "questionId" TEXT,
    "questionData" JSONB NOT NULL,
    "notes" TEXT,
    "savedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SavedInterviewQuestion_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PracticeAnswer" (
    "id" TEXT NOT NULL,
    "userId" INTEGER NOT NULL,
    "questionType" TEXT NOT NULL,
    "questionData" JSONB NOT NULL,
    "userAnswer" TEXT NOT NULL,
    "notes" TEXT,
    "savedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PracticeAnswer_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "InterviewQuestionSet_userId_idx" ON "InterviewQuestionSet"("userId");

-- CreateIndex
CREATE INDEX "InterviewQuestionSet_userId_jobId_idx" ON "InterviewQuestionSet"("userId", "jobId");

-- CreateIndex
CREATE INDEX "SavedInterviewQuestion_userId_idx" ON "SavedInterviewQuestion"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "SavedInterviewQuestion_userId_questionType_questionId_key" ON "SavedInterviewQuestion"("userId", "questionType", "questionId");

-- CreateIndex
CREATE INDEX "PracticeAnswer_userId_idx" ON "PracticeAnswer"("userId");

-- AddForeignKey
ALTER TABLE "InterviewQuestionSet" ADD CONSTRAINT "InterviewQuestionSet_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SavedInterviewQuestion" ADD CONSTRAINT "SavedInterviewQuestion_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PracticeAnswer" ADD CONSTRAINT "PracticeAnswer_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
