# Google OAuth Production Fix for careerdart.com

## 🔍 Issue Analysis
Based on the console error showing `careerdart-prod.fly.dev/auth/me 401 (Unauthorized)`, the issue is likely:

1. **Frontend-Backend URL Mismatch**: Frontend calling wrong backend URL
2. **Google OAuth Redirect URI Mismatch**: OAuth configured for wrong domain
3. **Environment Variable Issues**: Missing or incorrect OAuth credentials

## 🛠️ Step-by-Step Fix

### 1. Verify Current Production URLs
- **Frontend**: https://careerdart.com (Netlify)
- **Backend**: https://api.careerdart.com (Fly.io: careerdart-prod)

### 2. Check Google Cloud Console Configuration

**Go to**: https://console.cloud.google.com/apis/credentials

**Required Settings**:
```
Application Type: Web application
Name: CareerDart Production

Authorized JavaScript origins:
- https://careerdart.com

Authorized redirect URIs:
- https://api.careerdart.com/auth/google/callback
```

### 3. Backend Environment Variables (Fly.io)

```bash
# Check current secrets
fly secrets list -a careerdart-prod

# Set correct environment variables
fly secrets set \
  WASP_WEB_CLIENT_URL=https://careerdart.com \
  WASP_SERVER_URL=https://api.careerdart.com \
  GOOGLE_CLIENT_ID=your-production-client-id.apps.googleusercontent.com \
  GOOGLE_CLIENT_SECRET=GOCSPX-your-production-client-secret \
  NODE_ENV=production \
  -a careerdart-prod
```

### 4. Frontend Environment Variables (Netlify)

**Netlify Dashboard → Site Settings → Environment Variables**:
```
REACT_APP_API_URL=https://api.careerdart.com
NODE_ENV=production
GENERATE_SOURCEMAP=false
```

### 5. Verify Domain Setup

```bash
# Check if api.careerdart.com points to Fly.io
nslookup api.careerdart.com

# Should return Fly.io IP addresses
```

### 6. Test OAuth Flow

1. Go to https://careerdart.com/login
2. Click "Continue with Google"
3. Check browser network tab for the redirect URL
4. Verify it goes to: https://api.careerdart.com/auth/google/callback

## 🚨 Common Issues & Solutions

### Issue 1: redirect_uri_mismatch
**Solution**: Update Google Cloud Console redirect URIs to match exactly:
- https://api.careerdart.com/auth/google/callback

### Issue 2: invalid_client
**Solution**: Verify GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET are correct

### Issue 3: CORS Issues
**Solution**: Ensure WASP_WEB_CLIENT_URL is set correctly in backend

### Issue 4: Domain Not Verified
**Solution**: Add domain verification in Google Cloud Console

## 🔄 Deployment Steps

1. **Update Google Cloud Console** with correct URLs
2. **Set Fly.io environment variables** with correct OAuth credentials
3. **Set Netlify environment variables** with correct API URL
4. **Deploy backend**: `fly deploy -a careerdart-prod`
5. **Deploy frontend**: Trigger Netlify rebuild
6. **Test OAuth flow** end-to-end

## ✅ Verification Checklist

- [ ] Google Cloud Console has correct redirect URIs
- [ ] Fly.io has correct GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET
- [ ] Fly.io has correct WASP_WEB_CLIENT_URL and WASP_SERVER_URL
- [ ] Netlify has correct REACT_APP_API_URL
- [ ] api.careerdart.com resolves to Fly.io
- [ ] SSL certificates are valid for both domains
- [ ] OAuth flow redirects to correct callback URL
- [ ] No CORS errors in browser console
