# CareerDart Deployment Configuration 🚀

This folder contains all the deployment-related files and configurations for the CareerDart application.

## 📁 Folder Structure

```
deployment/
├── config/                    # Configuration files
│   ├── fly.dev.toml          # Fly.io development configuration
│   └── netlify.dev.toml      # Netlify development configuration
├── docs/                      # Documentation
│   ├── DEVELOPMENT_ENVIRONMENT_SETUP.md
│   └── DEV_ENV_QUICK_REFERENCE.md
├── templates/                 # Template files
│   ├── env-development.template
│   ├── env-production.template
│   ├── deploy-dev-workflow.yml
│   └── deploy-prod-workflow.yml
└── README.md                  # This file

scripts/
└── deployment/               # Deployment scripts
    ├── deploy-dev.sh         # Deploy to development
    ├── setup-dev-env.sh      # Setup development environment
    └── switch-env.sh         # Switch between environments
```

## 🚀 Quick Start

### 1. Initial Setup
```bash
# Setup development environment (one-time)
./scripts/deployment/setup-dev-env.sh
```

### 2. Deploy to Development
```bash
# Deploy to development environment
./scripts/deployment/deploy-dev.sh
```

### 3. Switch Environments
```bash
# Switch to development environment
./scripts/deployment/switch-env.sh development

# Switch to production environment
./scripts/deployment/switch-env.sh production
```

## 📋 Configuration Files

### Fly.io Configuration
- `config/fly.dev.toml` - Development backend configuration
- `fly.toml` (project root) - Production backend configuration

### Netlify Configuration
- `config/netlify.dev.toml` - Development frontend configuration
- `netlify.toml` (project root) - Production frontend configuration

## 🔧 Environment Variables

### Development
Use `templates/env-development.template` as a reference for setting up development environment variables.

### Production
Use `templates/env-production.template` as a reference for setting up production environment variables.

## 🌍 Environment URLs

| Environment | Frontend | Backend |
|-------------|----------|---------|
| Development | https://careerdart-dev.netlify.app | https://careerdart-dev.fly.dev |
| Production  | https://careerdart.netlify.app | https://careerdart.fly.dev |

## 📖 Documentation

- **Complete Setup Guide**: `docs/DEVELOPMENT_ENVIRONMENT_SETUP.md`
- **Quick Reference**: `docs/DEV_ENV_QUICK_REFERENCE.md`

## 🔄 GitHub Actions

Use the workflow templates in `templates/` to set up automated deployments:

1. Copy `deploy-dev-workflow.yml` to `.github/workflows/deploy-dev.yml`
2. Copy `deploy-prod-workflow.yml` to `.github/workflows/deploy-prod.yml`

## 🛠️ Scripts

All deployment scripts are located in `scripts/deployment/`:

- **setup-dev-env.sh** - One-time setup for development environment
- **deploy-dev.sh** - Deploy to development environment
- **switch-env.sh** - Switch between environment configurations

## 📞 Support

For deployment issues, refer to:
- Fly.io Dashboard: https://fly.io/dashboard
- Netlify Dashboard: https://app.netlify.com
- GitHub Actions: Repository → Actions tab

## 🔐 Security Notes

- Never commit real API keys to version control
- Use different secrets for development and production
- Regularly rotate API keys and tokens
- Use environment-specific database credentials 