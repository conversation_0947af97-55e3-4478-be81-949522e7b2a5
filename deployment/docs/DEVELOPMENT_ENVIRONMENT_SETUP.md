# Development Environment Setup Guide

This guide walks you through setting up separate development/staging environments on Netlify and Fly.io that mirror your production setup.

## Overview

We'll create:
- **Development Backend**: Fly.io app with separate database
- **Development Frontend**: Netlify site with development environment variables
- **Staging Pipeline**: Automated deployments from `develop` branch

## 🚀 Backend Development Environment (Fly.io)

### 1. Create Development Fly.io App

```bash
# Create new Fly.io app for development
fly apps create careerdart-dev

# Generate development fly.toml
cp fly.toml fly.dev.toml
```

### 2. Development fly.toml Configuration

```toml
# fly.dev.toml
app = "careerdart-dev"
primary_region = "sjc"

[build]
  dockerfile = "Dockerfile"

[env]
  NODE_ENV = "development"
  WASP_ENV = "development"
  DATABASE_URL = "postgresql://username:<EMAIL>:5432/careerdart_dev"

[[services]]
  protocol = "tcp"
  internal_port = 3001
  processes = ["app"]

  [[services.ports]]
    port = 80
    handlers = ["http"]
    force_https = true

  [[services.ports]]
    port = 443
    handlers = ["tls", "http"]

[http_service]
  internal_port = 3001
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ["app"]

[[vm]]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 512
```

### 3. Create Development Database

```bash
# Create development Postgres database
fly postgres create careerdart-dev-db --region sjc --vm-size shared-cpu-1x --volume-size 10

# Get database connection details
fly postgres connect -a careerdart-dev-db

# Note the connection URL for environment variables
```

### 4. Set Development Environment Variables

```bash
# Set environment variables for development app
fly secrets set -a careerdart-dev \
  DATABASE_URL="postgresql://username:<EMAIL>:5432/careerdart_dev" \
  JWT_SECRET="dev-jwt-secret-key-different-from-prod" \
  GOOGLE_CLIENT_ID="your-google-client-id" \
  GOOGLE_CLIENT_SECRET="your-google-client-secret" \
  STRIPE_SECRET_KEY="sk_test_your_stripe_test_key" \
  STRIPE_WEBHOOK_SECRET="whsec_your_dev_webhook_secret" \
  EMAIL_FROM="<EMAIL>" \
  RESEND_API_KEY="re_dev_your_resend_api_key" \
  OPENAI_API_KEY="your-openai-api-key" \
  NODE_ENV="development" \
  WASP_ENV="development"
```

### 5. Deploy Development Backend

```bash
# Deploy to development environment
fly deploy -a careerdart-dev -c fly.dev.toml

# Check deployment status
fly status -a careerdart-dev

# View logs
fly logs -a careerdart-dev
```

## 🌐 Frontend Development Environment (Netlify)

### 1. Create Development Site on Netlify

```bash
# Install Netlify CLI if not already installed
npm install -g netlify-cli

# Login to Netlify
netlify login

# Create new site for development
netlify sites:create --name careerdart-dev
```

### 2. Development Netlify Configuration

Create `netlify.dev.toml`:

```toml
# netlify.dev.toml
[build]
  command = "wasp build && cd .wasp/build/web-app && npm install && npm run build"
  publish = ".wasp/build/web-app/build"

[build.environment]
  NODE_VERSION = "18"
  NODE_ENV = "development"
  REACT_APP_ENV = "development"

[context.development]
  command = "wasp build && cd .wasp/build/web-app && npm install && npm run build"
  
[context.development.environment]
  REACT_APP_API_URL = "https://careerdart-dev.fly.dev"
  REACT_APP_STRIPE_PUBLISHABLE_KEY = "pk_test_your_stripe_test_publishable_key"
  REACT_APP_GOOGLE_CLIENT_ID = "your-google-client-id"
  REACT_APP_GA_MEASUREMENT_ID = "G-DEV123456"
  REACT_APP_ENABLE_ANALYTICS = "false"
  REACT_APP_ENABLE_PERFORMANCE_MONITORING = "true"
  REACT_APP_SENTRY_DSN = "your-sentry-dev-dsn"
  GENERATE_SOURCEMAP = "true"
  NODE_ENV = "development"

# Redirects for SPA routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = false

# API redirects to development backend
[[redirects]]
  from = "/api/*"
  to = "https://careerdart-dev.fly.dev/api/:splat"
  status = 200
  force = false

[[redirects]]
  from = "/auth/*"
  to = "https://careerdart-dev.fly.dev/auth/:splat"
  status = 200
  force = false

[[redirects]]
  from = "/operations/*"
  to = "https://careerdart-dev.fly.dev/operations/:splat"
  status = 200
  force = false
```

### 3. Set Development Environment Variables in Netlify

Via Netlify Dashboard:
1. Go to Site Settings → Environment Variables
2. Add development-specific variables:

```bash
# Development Environment Variables
REACT_APP_API_URL=https://careerdart-dev.fly.dev
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_test_key
REACT_APP_GOOGLE_CLIENT_ID=your-google-client-id
REACT_APP_ENV=development
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_ENABLE_DEBUG=true
NODE_ENV=development
GENERATE_SOURCEMAP=true
```

## 🔄 Git Branch Strategy

### 1. Create Development Branch

```bash
# Create and switch to development branch
git checkout -b develop

# Push development branch
git push -u origin develop
```

### 2. Configure Branch-Based Deployments

**Fly.io GitHub Actions** (`.github/workflows/deploy-dev.yml`):

```yaml
name: Deploy to Development
on:
  push:
    branches: [develop]
  pull_request:
    branches: [develop]

jobs:
  deploy-backend:
    name: Deploy Backend to Fly.io Dev
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Fly.io CLI
        uses: superfly/flyctl-actions/setup-flyctl@master
        
      - name: Deploy to Fly.io Development
        run: flyctl deploy -a careerdart-dev -c fly.dev.toml
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

  deploy-frontend:
    name: Deploy Frontend to Netlify Dev
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Setup Wasp
        run: curl -sSL https://get.wasp-lang.dev/installer.sh | sh
        
      - name: Build and Deploy to Netlify
        run: |
          wasp build
          cd .wasp/build/web-app
          npm install
          npm run build
          npx netlify deploy --prod --dir=build --site=${{ secrets.NETLIFY_DEV_SITE_ID }}
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
```

**Netlify Branch Deploys**:
1. Go to Site Settings → Build & Deploy → Branch Deploys
2. Set branch deploys to: `develop`
3. Configure context-specific builds

## 🧪 Testing Environment Setup

### 1. Environment-Specific Configurations

Create `config/environments.ts`:

```typescript
// config/environments.ts
export const environments = {
  development: {
    apiUrl: 'https://careerdart-dev.fly.dev',
    stripeKey: 'pk_test_development_key',
    enableAnalytics: false,
    enableLogging: true,
    debugMode: true,
  },
  staging: {
    apiUrl: 'https://careerdart-staging.fly.dev',
    stripeKey: 'pk_test_staging_key',
    enableAnalytics: true,
    enableLogging: true,
    debugMode: false,
  },
  production: {
    apiUrl: 'https://careerdart.fly.dev',
    stripeKey: 'pk_live_production_key',
    enableAnalytics: true,
    enableLogging: false,
    debugMode: false,
  }
};

export const getEnvironmentConfig = () => {
  const env = process.env.REACT_APP_ENV || 'development';
  return environments[env as keyof typeof environments];
};
```

### 2. Database Seeding for Development

Create `scripts/seed-dev.ts`:

```typescript
// scripts/seed-dev.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedDevelopmentData() {
  // Create test admin user
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'dev-admin',
      isAdmin: true,
      credits: 1000,
      bio: '[VERIFIED] Development Admin Account',
      yearsOfExperience: 5,
      gptModel: 'gpt-4',
    },
  });

  // Create test regular user
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'dev-user',
      isAdmin: false,
      credits: 50,
      bio: 'Development Test User',
      yearsOfExperience: 3,
      gptModel: 'gpt-3.5-turbo',
    },
  });

  console.log('Development database seeded successfully!');
}

seedDevelopmentData()
  .catch((e) => {
    console.error('Error seeding development data:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
```

## 🔧 Development Deployment Commands

### Quick Deploy Scripts

Create `scripts/deploy-dev.sh`:

```bash
#!/bin/bash

echo "🚀 Deploying to Development Environment..."

# Deploy backend to Fly.io development
echo "📦 Deploying backend to Fly.io..."
fly deploy -a careerdart-dev -c fly.dev.toml

# Build and deploy frontend to Netlify
echo "🌐 Building and deploying frontend..."
wasp build
cd .wasp/build/web-app
npm install
npm run build
netlify deploy --prod --dir=build --site=careerdart-dev

echo "✅ Development deployment complete!"
echo "🔗 Frontend: https://careerdart-dev.netlify.app"
echo "🔗 Backend: https://careerdart-dev.fly.dev"
```

### Environment Switching

Create `scripts/switch-env.sh`:

```bash
#!/bin/bash

ENV=${1:-development}

case $ENV in
  "development")
    export REACT_APP_API_URL="https://careerdart-dev.fly.dev"
    export REACT_APP_ENV="development"
    ;;
  "staging")
    export REACT_APP_API_URL="https://careerdart-staging.fly.dev"
    export REACT_APP_ENV="staging"
    ;;
  "production")
    export REACT_APP_API_URL="https://careerdart.fly.dev"
    export REACT_APP_ENV="production"
    ;;
esac

echo "Switched to $ENV environment"
echo "API URL: $REACT_APP_API_URL"
```

## 📊 Monitoring Development Environment

### 1. Health Checks

```bash
# Check development backend health
curl https://careerdart-dev.fly.dev/health

# Check development frontend
curl https://careerdart-dev.netlify.app

# Monitor Fly.io development app
fly status -a careerdart-dev
fly logs -a careerdart-dev
```

### 2. Database Monitoring

```bash
# Connect to development database
fly postgres connect -a careerdart-dev-db

# Monitor database performance
fly postgres db list -a careerdart-dev-db
```

## 🔄 Maintenance Commands

```bash
# Update development environment
./scripts/deploy-dev.sh

# Reset development database
fly postgres db dump -a careerdart-dev-db > backup.sql
fly postgres db restore -a careerdart-dev-db < fresh-seed.sql

# Scale development resources
fly scale memory 1024 -a careerdart-dev
fly scale count 1 -a careerdart-dev

# View development logs
fly logs -a careerdart-dev --follow
```

This setup gives you a complete development environment that mirrors production for thorough testing before deploying to live users. 