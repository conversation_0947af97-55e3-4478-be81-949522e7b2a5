# Development Environment Quick Reference 🚀

## 🛠️ Initial Setup (One-time)

```bash
# Setup development infrastructure
./scripts/setup-dev-env.sh

# This will:
# ✅ Install Fly.io and Netlify CLIs
# ✅ Create development apps on both platforms
# ✅ Set up development database
# ✅ Create GitHub Actions workflow
# ✅ Generate environment variables template
```

## 🚀 Deploy to Development

```bash
# Quick deploy to both platforms
./scripts/deploy-dev.sh

# Or deploy individually:
fly deploy -a careerdart-dev -c fly.dev.toml    # Backend only
netlify deploy --prod --site=careerdart-dev    # Frontend only
```

## 🌐 Environment URLs

| Environment | Frontend | Backend | Database |
|-------------|----------|---------|----------|
| **Development** | https://careerdart-dev.netlify.app | https://careerdart-dev.fly.dev | careerdart-dev-db |
| **Production** | https://careerdart.netlify.app | https://careerdart.fly.dev | careerdart-db |

## 🔧 Environment Variables

### Fly.io (Backend)
```bash
# Set all backend environment variables
fly secrets set -a careerdart-dev \
  JWT_SECRET="dev-secret" \
  STRIPE_SECRET_KEY="sk_test_..." \
  OPENAI_API_KEY="sk-..." \
  DATABASE_URL="postgres://..."

# View current secrets
fly secrets list -a careerdart-dev
```

### Netlify (Frontend)
```bash
# Via CLI
netlify env:set REACT_APP_API_URL https://careerdart-dev.fly.dev --site=careerdart-dev

# Or via Dashboard:
# https://app.netlify.com/sites/careerdart-dev/settings/deploys#environment-variables
```

## 📊 Monitoring & Debugging

### Backend (Fly.io)
```bash
# View logs
fly logs -a careerdart-dev --follow

# Check app status
fly status -a careerdart-dev

# SSH into container
fly ssh console -a careerdart-dev

# View metrics
fly metrics -a careerdart-dev

# Scale resources
fly scale memory 1024 -a careerdart-dev
fly scale count 2 -a careerdart-dev
```

### Frontend (Netlify)
```bash
# View deployment logs
netlify logs --site=careerdart-dev

# View site info
netlify status --site=careerdart-dev

# Open in browser
netlify open --site=careerdart-dev
```

### Database
```bash
# Connect to development database
fly postgres connect -a careerdart-dev-db

# View database metrics
fly postgres db list -a careerdart-dev-db

# Create backup
fly postgres db dump -a careerdart-dev-db > dev-backup.sql
```

## 🔄 Git Workflow

```bash
# Work on development branch
git checkout develop

# Make changes and test locally
wasp start

# Deploy to development environment
git add .
git commit -m "feat: add new feature"
git push origin develop

# GitHub Actions will automatically deploy to:
# ✅ Backend → Fly.io development
# ✅ Frontend → Netlify development
```

## 🧪 Testing Different Environments

```bash
# Test with different API endpoints
REACT_APP_API_URL=https://careerdart-dev.fly.dev npm start     # Dev backend
REACT_APP_API_URL=https://careerdart.fly.dev npm start        # Prod backend

# Or use the environment switcher
./scripts/switch-env.sh development
./scripts/switch-env.sh production
```

## 🚨 Troubleshooting

### Common Issues

1. **Build Fails on Netlify**
   ```bash
   # Check Node.js version
   netlify env:get NODE_VERSION --site=careerdart-dev
   
   # Set correct version
   netlify env:set NODE_VERSION 18 --site=careerdart-dev
   ```

2. **Database Connection Issues**
   ```bash
   # Check database status
   fly status -a careerdart-dev-db
   
   # Restart database if needed
   fly restart -a careerdart-dev-db
   ```

3. **Environment Variables Not Working**
   ```bash
   # Check if variables are set
   fly secrets list -a careerdart-dev
   netlify env:list --site=careerdart-dev
   
   # Re-deploy after setting variables
   fly deploy -a careerdart-dev -c fly.dev.toml
   ```

### Reset Development Environment

```bash
# Reset backend
fly destroy careerdart-dev
fly apps create careerdart-dev

# Reset frontend
netlify sites:delete careerdart-dev
netlify sites:create --name careerdart-dev

# Re-run setup
./scripts/setup-dev-env.sh
```

## 📱 Environment Switching in Code

```typescript
// Use environment configuration
import { currentEnvironment } from './config/environments';

console.log('Current environment:', currentEnvironment.name);
console.log('API URL:', currentEnvironment.config.apiUrl);
console.log('Debug mode:', currentEnvironment.config.debugMode);

// Conditional features based on environment
if (currentEnvironment.isDevelopment) {
  // Development-only features
  console.log('🔧 Development mode active');
}
```

## 🔐 Security Notes

- ✅ Use different JWT secrets for dev/prod
- ✅ Use Stripe test keys in development
- ✅ Never commit real API keys to git
- ✅ Use different database passwords
- ✅ Enable debug features only in development

## 📞 Support Commands

```bash
# Get help
fly help
netlify help

# View account info
fly auth whoami
netlify status

# View billing
fly dashboard
netlify billing:show
``` 