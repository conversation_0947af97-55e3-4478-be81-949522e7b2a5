# CareerDart Deployment Folder Structure 📁

This document provides a complete overview of the organized deployment configuration.

## 📂 Complete Folder Structure

```
project-root/
├── deployment/                           # 🚀 All deployment configurations
│   ├── README.md                        # Main deployment documentation
│   ├── FOLDER_STRUCTURE.md             # This file
│   ├── config/                          # Configuration files
│   │   ├── fly.dev.toml                # Fly.io development config
│   │   └── netlify.dev.toml            # Netlify development config
│   ├── docs/                           # Deployment documentation
│   │   ├── DEVELOPMENT_ENVIRONMENT_SETUP.md  # Complete setup guide
│   │   └── DEV_ENV_QUICK_REFERENCE.md       # Quick reference
│   └── templates/                      # Template files
│       ├── env-development.template    # Development environment variables
│       ├── env-production.template     # Production environment variables
│       ├── deploy-dev-workflow.yml     # GitHub Actions development
│       └── deploy-prod-workflow.yml    # GitHub Actions production
├── scripts/
│   └── deployment/                     # 🛠️ Deployment scripts
│       ├── deploy-dev.sh              # Deploy to development
│       ├── setup-dev-env.sh           # Setup development environment
│       └── switch-env.sh              # Switch between environments
├── src/client/config/
│   └── environments.ts                # Environment-specific React config
├── fly.toml                           # Fly.io production config (root)
├── netlify.toml                       # Netlify production config (root)
└── .github/workflows/                 # GitHub Actions (when copied from templates)
    ├── deploy-dev.yml                 # Development deployment workflow
    └── deploy-prod.yml                # Production deployment workflow
```

## 🎯 Purpose of Each Folder

### `/deployment/`
**Main deployment configuration folder**
- Central location for all deployment-related files
- Environment-specific configurations
- Documentation and templates

### `/deployment/config/`
**Platform configuration files**
- `fly.dev.toml` - Fly.io development environment settings
- `netlify.dev.toml` - Netlify development environment settings
- Production configs remain in project root for convention

### `/deployment/docs/`
**Deployment documentation**
- Complete setup guides
- Quick reference materials
- Environment-specific instructions

### `/deployment/templates/`
**Template files for various environments**
- Environment variable templates
- GitHub Actions workflow templates
- Easily copyable configuration files

### `/scripts/deployment/`
**Executable deployment scripts**
- Automated deployment scripts
- Environment setup scripts
- Utility scripts for environment management

## 🔄 Workflow

### Development Setup (One-time)
```bash
# 1. Run initial setup
./scripts/deployment/setup-dev-env.sh

# 2. Copy environment variables template and fill in values
cp deployment/templates/env-development.template .env.development.local
# Edit .env.development.local with your actual values

# 3. Set up GitHub Actions (optional)
cp deployment/templates/deploy-dev-workflow.yml .github/workflows/deploy-dev.yml
cp deployment/templates/deploy-prod-workflow.yml .github/workflows/deploy-prod.yml
```

### Daily Development
```bash
# Switch to development environment
./scripts/deployment/switch-env.sh development

# Deploy to development
./scripts/deployment/deploy-dev.sh

# Deploy to production (when ready)
./scripts/deployment/switch-env.sh production
./scripts/deployment/deploy-dev.sh  # Using production settings
```

## 📋 File Usage Guide

| File | Purpose | When to Use |
|------|---------|-------------|
| `deployment/README.md` | Main deployment overview | First time setup, reference |
| `deployment/config/fly.dev.toml` | Fly.io dev config | Automatic (used by scripts) |
| `deployment/config/netlify.dev.toml` | Netlify dev config | Manual upload to Netlify |
| `deployment/templates/env-*.template` | Environment variables reference | Setting up new environments |
| `deployment/templates/*-workflow.yml` | GitHub Actions templates | Setting up CI/CD |
| `scripts/deployment/setup-dev-env.sh` | One-time environment setup | Initial project setup |
| `scripts/deployment/deploy-dev.sh` | Development deployment | Regular development deploys |
| `scripts/deployment/switch-env.sh` | Environment switching | Testing different environments |

## 🌍 Environment Configuration

### Development Environment
- **Frontend**: https://careerdart-dev.netlify.app
- **Backend**: https://careerdart-dev.fly.dev
- **Database**: careerdart-dev-db (Fly.io)
- **Branch**: `develop`
- **Config**: `deployment/config/*.dev.toml`

### Production Environment
- **Frontend**: https://careerdart.netlify.app
- **Backend**: https://careerdart.fly.dev
- **Database**: careerdart-db (Fly.io)
- **Branch**: `main`
- **Config**: `fly.toml`, `netlify.toml` (root)

## 🔧 Customization

### Adding New Environment (e.g., Staging)
1. Create `deployment/config/fly.staging.toml`
2. Create `deployment/config/netlify.staging.toml`
3. Create `deployment/templates/env-staging.template`
4. Update `scripts/deployment/switch-env.sh` with staging case
5. Create staging deployment script

### Adding New Deployment Platform
1. Create config file in `deployment/config/`
2. Create environment template in `deployment/templates/`
3. Create deployment script in `scripts/deployment/`
4. Update documentation

## 🛡️ Security Best Practices

1. **Never commit real API keys**
   - Use `.template` files for reference only
   - Add `.env.*` to `.gitignore`

2. **Environment separation**
   - Different API keys for each environment
   - Separate databases for dev/prod
   - Different JWT secrets

3. **Access control**
   - Limit access to production secrets
   - Use environment-specific permissions
   - Regular key rotation

## 📞 Support & Troubleshooting

### Common Issues
- **Script permissions**: Run `chmod +x scripts/deployment/*.sh`
- **Missing tools**: Run setup script to install CLIs
- **Wrong config path**: Check script references to config files

### Getting Help
- Check `deployment/docs/DEV_ENV_QUICK_REFERENCE.md` for quick fixes
- Review `deployment/docs/DEVELOPMENT_ENVIRONMENT_SETUP.md` for detailed setup
- Check platform dashboards:
  - Fly.io: https://fly.io/dashboard
  - Netlify: https://app.netlify.com 