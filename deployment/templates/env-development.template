# Development Environment Variables Template
# Copy these values and set them in your respective platforms

# === FLY.IO BACKEND VARIABLES ===
# Set these using: fly secrets set -a careerdart-dev KEY=value

DATABASE_URL=postgresql://username:<EMAIL>:5432/careerdart_dev
JWT_SECRET=dev-jwt-secret-key-change-this-to-something-secure
GOOGLE_CLIENT_ID=your-google-oauth-client-id
GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret
STRIPE_SECRET_KEY=sk_test_your_stripe_test_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret
EMAIL_FROM=<EMAIL>
RESEND_API_KEY=re_your_resend_api_key
OPENAI_API_KEY=your-openai-api-key
NODE_ENV=development
WASP_ENV=development

# === NETLIFY FRONTEND VARIABLES ===
# Set these in Netlify Dashboard > Site Settings > Environment Variables

REACT_APP_API_URL=https://careerdart-dev.fly.dev
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_test_publishable_key
REACT_APP_GOOGLE_CLIENT_ID=your-google-oauth-client-id
REACT_APP_ENV=development
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_ENABLE_DEBUG=true
REACT_APP_ENABLE_PERFORMANCE_MONITORING=true
NODE_ENV=development
GENERATE_SOURCEMAP=true 