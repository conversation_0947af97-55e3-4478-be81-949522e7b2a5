name: Deploy to Production

on:
  push:
    branches: [main]
  release:
    types: [published]

jobs:
  deploy-backend:
    name: Deploy Backend to Fly.io Production
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Fly.io CLI
        uses: superfly/flyctl-actions/setup-flyctl@master
        
      - name: Deploy to Fly.io Production
        run: flyctl deploy -a careerdart -c fly.toml
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

  deploy-frontend:
    name: Deploy Frontend to Netlify Production
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Setup Wasp
        run: curl -sSL https://get.wasp-lang.dev/installer.sh | sh
        
      - name: Add Wasp to PATH
        run: echo "$HOME/.wasp/bin" >> $GITHUB_PATH
        
      - name: Build and Deploy to Netlify
        run: |
          wasp build
          cd .wasp/build/web-app
          npm ci
          npm run build
          npx netlify deploy --prod --dir=build --site=${{ secrets.NETLIFY_SITE_ID }}
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }} 