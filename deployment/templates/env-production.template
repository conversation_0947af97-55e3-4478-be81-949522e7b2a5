# Production Environment Variables Template
# Copy these values and set them in your respective platforms

# === FLY.IO BACKEND VARIABLES ===
# Set these using: fly secrets set -a careerdart KEY=value

DATABASE_URL=postgresql://username:<EMAIL>:5432/careerdart_prod
JWT_SECRET=production-jwt-secret-key-super-secure
GOOGLE_CLIENT_ID=your-google-oauth-client-id
GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret
STRIPE_SECRET_KEY=sk_live_your_stripe_live_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_live_webhook_secret
EMAIL_FROM=<EMAIL>
RESEND_API_KEY=re_your_resend_api_key
OPENAI_API_KEY=your-openai-api-key
NODE_ENV=production
WASP_ENV=production

# === NETLIFY FRONTEND VARIABLES ===
# Set these in Netlify Dashboard > Site Settings > Environment Variables

REACT_APP_API_URL=https://careerdart.fly.dev
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_live_publishable_key
REACT_APP_GOOGLE_CLIENT_ID=your-google-oauth-client-id
REACT_APP_ENV=production
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_ENABLE_DEBUG=false
REACT_APP_ENABLE_PERFORMANCE_MONITORING=true
REACT_APP_GA_MEASUREMENT_ID=G-XXXXXXXXXX
REACT_APP_SENTRY_DSN=your-sentry-production-dsn
NODE_ENV=production
GENERATE_SOURCEMAP=false 