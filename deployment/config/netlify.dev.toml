[build]
  command = "wasp build && cd .wasp/build/web-app && npm install && npm run build"
  publish = ".wasp/build/web-app/build"

[build.environment]
  NODE_VERSION = "18"
  NODE_ENV = "development"
  REACT_APP_ENV = "development"

[context.development]
  command = "wasp build && cd .wasp/build/web-app && npm install && npm run build"
  
[context.development.environment]
  REACT_APP_API_URL = "https://careerdart-dev.fly.dev"
  REACT_APP_STRIPE_PUBLISHABLE_KEY = "pk_test_51234567890123456789"
  REACT_APP_ENABLE_ANALYTICS = "false"
  REACT_APP_ENABLE_PERFORMANCE_MONITORING = "true"
  REACT_APP_ENABLE_DEBUG = "true"
  GENERATE_SOURCEMAP = "true"
  NODE_ENV = "development"

# Redirects for SPA routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = false

# API redirects to development backend
[[redirects]]
  from = "/api/*"
  to = "https://careerdart-dev.fly.dev/api/:splat"
  status = 200
  force = false

[[redirects]]
  from = "/auth/*"
  to = "https://careerdart-dev.fly.dev/auth/:splat"
  status = 200
  force = false

[[redirects]]
  from = "/operations/*"
  to = "https://careerdart-dev.fly.dev/operations/:splat"
  status = 200
  force = false

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable" 