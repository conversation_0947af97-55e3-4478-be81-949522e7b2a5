# Simple working Dockerfile for Wasp app
FROM node:18-alpine

# Install system dependencies
RUN apk add --no-cache python3 openssl

# Set working directory
WORKDIR /app

# Copy the entire .wasp directory structure to maintain all dependencies
COPY .wasp .wasp

# Install dependencies and Prisma CLI in the server directory
WORKDIR /app/.wasp/build/server

# Install the Wasp SDK as a local dependency
RUN npm install ../../out/sdk/wasp && npm install && npm install -g prisma

# Expose port
EXPOSE 8080

# Set environment
ENV NODE_ENV=production
ENV PORT=8080

# Start the server using npm script (no need to override anymore)
CMD ["npm", "run", "start-production"] 