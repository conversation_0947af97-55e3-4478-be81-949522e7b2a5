# Simplified Railway Dockerfile for Wasp app
FROM node:18-alpine

# Install system dependencies
RUN apk add --no-cache python3 make g++ openssl bash curl

# Install Wasp CLI
RUN curl -sSL https://get.wasp-lang.dev/installer.sh | sh -s -- -v 0.15.0
ENV PATH="/root/.local/bin:$PATH"

# Set working directory
WORKDIR /app

# Copy source files needed for build
COPY main.wasp ./
COPY schema.prisma ./
COPY src ./src
COPY package.json ./
COPY package-lock.json ./

# Build the Wasp project
RUN wasp build

# Move to server directory
WORKDIR /app/.wasp/build/server

# Install server dependencies
RUN npm install

# Generate Prisma client
RUN npx prisma generate --schema=../db/schema.prisma

# Expose port
EXPOSE 8080

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080

# Start the server
CMD ["npm", "run", "start-production"] 