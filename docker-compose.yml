version: '3'

services:
  postgres:
    image: postgres:14
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: careergpt
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
      - "3001:3001"
    depends_on:
      - postgres
    environment:
      - DATABASE_URL=********************************************/careergpt
      - WASP_SERVER_URL=${WASP_SERVER_URL:-http://localhost:3001}
      - WASP_WEB_CLIENT_URL=${WASP_WEB_CLIENT_URL:-http://localhost:3000}
    volumes:
      - .:/app
      - node_modules:/app/node_modules

volumes:
  postgres_data:
  node_modules:
