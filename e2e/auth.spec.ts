import { test, expect } from '@playwright/test';

test.describe('Authentication', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should display login page for unauthenticated users', async ({ page }) => {
    // Check if login page is displayed
    await expect(page.locator('h1')).toContainText('Welcome to CareerDart');
    await expect(page.getByRole('button', { name: /sign in with google/i })).toBeVisible();
  });

  test('should redirect to main page after successful login', async ({ page }) => {
    // Mock successful authentication
    await page.route('**/auth/google/login', async (route) => {
      await route.fulfill({
        status: 302,
        headers: {
          'Location': '/',
          'Set-Cookie': 'sessionId=test-session; Path=/; HttpOnly',
        },
      });
    });

    // Mock user data endpoint
    await page.route('**/api/user', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          hasPaid: true,
          credits: 3,
        }),
      });
    });

    // Click login button
    await page.getByRole('button', { name: /sign in with google/i }).click();

    // Should redirect to main page
    await expect(page).toHaveURL('/');
    await expect(page.locator('[data-testid="main-page"]')).toBeVisible();
  });

  test('should handle login errors gracefully', async ({ page }) => {
    // Mock failed authentication
    await page.route('**/auth/google/login', async (route) => {
      await route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Authentication failed' }),
      });
    });

    await page.getByRole('button', { name: /sign in with google/i }).click();

    // Should show error message
    await expect(page.locator('[role="alert"]')).toContainText('Authentication failed');
  });

  test('should logout successfully', async ({ page }) => {
    // First, mock being logged in
    await page.route('**/api/user', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          hasPaid: true,
          credits: 3,
        }),
      });
    });

    // Mock logout endpoint
    await page.route('**/auth/logout', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ success: true }),
      });
    });

    // Navigate to main page (as logged in user)
    await page.goto('/');

    // Click logout button (assuming it's in a menu)
    await page.getByRole('button', { name: /profile/i }).click();
    await page.getByRole('menuitem', { name: /logout/i }).click();

    // Should redirect to login page
    await expect(page.getByRole('button', { name: /sign in with google/i })).toBeVisible();
  });

  test('should protect authenticated routes', async ({ page }) => {
    // Try to access protected route without authentication
    await page.goto('/jobs');

    // Should redirect to login
    await expect(page.getByRole('button', { name: /sign in with google/i })).toBeVisible();
  });

  test('should remember user session on page refresh', async ({ page }) => {
    // Mock being logged in
    await page.route('**/api/user', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          hasPaid: true,
          credits: 3,
        }),
      });
    });

    // Set authentication cookie
    await page.context().addCookies([
      {
        name: 'sessionId',
        value: 'test-session',
        domain: 'localhost',
        path: '/',
        httpOnly: true,
      },
    ]);

    await page.goto('/');

    // Should be logged in
    await expect(page.locator('[data-testid="main-page"]')).toBeVisible();

    // Refresh page
    await page.reload();

    // Should still be logged in
    await expect(page.locator('[data-testid="main-page"]')).toBeVisible();
  });
});
