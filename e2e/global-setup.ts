import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup...');

  // Launch browser for setup
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Wait for the application to be ready
    console.log('⏳ Waiting for application to be ready...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle' });
    
    // Check if the app is loaded
    await page.waitForSelector('[data-testid="app-loaded"]', { timeout: 30000 });
    console.log('✅ Application is ready');

    // Setup test data if needed
    await setupTestData(page);

  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }

  console.log('✅ Global setup completed');
}

async function setupTestData(page: any) {
  // Create test user if needed
  // This would typically involve API calls or database seeding
  console.log('📝 Setting up test data...');
  
  // Example: Create test user via API
  // await page.request.post('/api/test/setup', {
  //   data: {
  //     user: {
  //       email: '<EMAIL>',
  //       username: 'testuser',
  //       hasPaid: true,
  //       credits: 10,
  //     },
  //   },
  // });

  console.log('✅ Test data setup completed');
}

export default globalSetup;
