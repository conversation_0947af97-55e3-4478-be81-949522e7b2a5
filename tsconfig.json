{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "bundler", "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "strict": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "noEmit": true, "outDir": ".wasp/phantom", "typeRoots": ["node_modules/@testing-library", "node_modules/@types"], "types": [], "allowJs": true, "checkJs": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": false, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "allowUnreachableCode": true, "allowUnusedLabels": true, "useUnknownInCatchVariables": false, "resolveJsonModule": true, "isolatedModules": true}, "include": ["src/**/*", "src/types/**/*"], "exclude": ["node_modules", ".wasp/build", "dist", "build", "**/*.test.*", "**/*.spec.*"]}