import{k as e,j as a,a0 as o,u as r,a1 as s,a as n,X as t,y as i,h as l,K as c,T as d,I as x,v as p,B as h,aG as b,aH as g,a4 as m,aI as f,aW as j,aJ as u}from"./ui-CxDIvMgK.js";import{u as v,L as C,F as y,d as S,e as w,f as k,g as z,h as _,m as I,A as R,b as M,a as W,n as Y,o as D,p as F}from"./index-Bu-v5N9o.js";import{G as L}from"./icons-DRFAkztb.js";import{L as T,T as Z}from"./Logo-C3fvvPlr.js";import{b as O}from"./vendor-BJbfb-22.js";import"./utils-DuWhH-qE.js";import"./charts-Bmy0KzL9.js";function B(e){return L({attr:{viewBox:"0 0 24 24",fill:"none"},child:[{tag:"path",attr:{fillRule:"evenodd",clipRule:"evenodd",d:"M16 9C16 11.2091 14.2091 13 12 13C9.79086 13 8 11.2091 8 9C8 6.79086 9.79086 5 12 5C14.2091 5 16 6.79086 16 9ZM14 9C14 10.1046 13.1046 11 12 11C10.8954 11 10 10.1046 10 9C10 7.89543 10.8954 7 12 7C13.1046 7 14 7.89543 14 9Z",fill:"currentColor"}},{tag:"path",attr:{fillRule:"evenodd",clipRule:"evenodd",d:"M12 1C5.92487 1 1 5.92487 1 12C1 18.0751 5.92487 23 12 23C18.0751 23 23 18.0751 23 12C23 5.92487 18.0751 1 12 1ZM3 12C3 14.0902 3.71255 16.014 4.90798 17.5417C6.55245 15.3889 9.14627 14 12.0645 14C14.9448 14 17.5092 15.3531 19.1565 17.4583C20.313 15.9443 21 14.0524 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12ZM12 21C9.84977 21 7.87565 20.2459 6.32767 18.9878C7.59352 17.1812 9.69106 16 12.0645 16C14.4084 16 16.4833 17.1521 17.7538 18.9209C16.1939 20.2191 14.1881 21 12 21Z",fill:"currentColor"}}]})(e)}const G=()=>{const r={position:"absolute",top:"-100vh",left:"8px",transform:"translateY(-100%)",opacity:0,zIndex:1e4,bg:e("white","gray.800"),color:"blue.500",px:4,py:2,borderRadius:"md",border:"2px solid",borderColor:e("gray.200","gray.700"),boxShadow:`0 4px 12px ${e("rgba(0, 0, 0, 0.1)","rgba(0, 0, 0, 0.3)")}`,fontWeight:"medium",fontSize:"sm",textDecoration:"none",whiteSpace:"nowrap",transition:"all 0.2s ease-in-out",_focus:{top:"8px",transform:"translateY(0)",opacity:1,outline:"2px solid",outlineColor:"blue.500",outlineOffset:"2px"},_hover:{textDecoration:"none",bg:e("blue.50","gray.700")}};return a.jsxs(a.Fragment,{children:[a.jsx(o,{href:"#main-content",sx:r,"aria-label":"Skip to main content",children:"Skip to main content"}),a.jsx(o,{href:"#navigation",sx:{...r,left:"180px"},"aria-label":"Skip to navigation",children:"Skip to navigation"}),a.jsx(o,{href:"#footer",sx:{...r,left:"320px"},"aria-label":"Skip to footer",children:"Skip to footer"})]})},J=O.createContext({isSidebarOpen:!1,toggleSidebar:()=>{},closeSidebar:()=>{},isCollapsed:!1,toggleCollapse:()=>{}}),N=()=>O.useContext(J),P=[{name:"Dashboard",path:"/dashboard",icon:y},{name:"Cover Letters",path:"/cover-letters",icon:S},{name:"Jobs",path:"/jobs",icon:w},{name:"Resume",path:"/resume",icon:S},{name:"Interview Prep",path:"/interview",icon:k},{name:"Learning",path:"/learning",icon:z},{name:"Tracker",path:"/tracker",icon:_}],U=({children:o,path:r,...s})=>{const t=M().pathname===r,i=e("brand.50","brand.900"),l=e("brand.700","brand.300"),c=e("gray.100","gray.700");return a.jsxs(n,{as:C,px:4,py:3,rounded:"lg",fontFamily:"Inter, system-ui, sans-serif",fontSize:"15px",fontWeight:t?"600":"500",color:t?l:"inherit",bg:t?i:"transparent",transition:"all 0.2s cubic-bezier(0.4, 0, 0.2, 1)",position:"relative",_hover:{textDecoration:"none",bg:t?i:c,transform:"translateY(-1px)",boxShadow:t?"lg":"md"},_active:{transform:"translateY(0)"},to:r,onClick:()=>window.scrollTo({top:0,behavior:"smooth"}),...s,children:[t&&a.jsx(n,{position:"absolute",left:"0",top:"0",bottom:"0",width:"3px",bg:"brand.500",borderRadius:"0 2px 2px 0"}),o]})},A=({user:o})=>{const r=W(),s=e("white","gray.800"),t=e("gray.200","gray.600");return a.jsxs(b,{children:[a.jsx(g,{as:x,variant:"ghost",size:"sm",rounded:"full",icon:a.jsx(m,{size:"sm",name:o?.username||"User",bg:"brand.500"}),_hover:{bg:"transparent",transform:"scale(1.05)"},_active:{transform:"scale(0.95)"},transition:"all 0.2s"}),a.jsxs(f,{bg:s,borderColor:t,boxShadow:"xl",py:2,minW:"200px",children:[a.jsxs(n,{px:4,py:2,children:[a.jsx(d,{fontSize:"sm",fontWeight:"600",color:"brand.500",children:o?.username||"User"}),a.jsx(d,{fontSize:"xs",color:"gray.500",children:o?.email||"<EMAIL>"})]}),a.jsx(j,{}),a.jsx(u,{icon:a.jsx(Y,{}),onClick:()=>{r("/profile"),window.scrollTo({top:0,behavior:"smooth"})},_hover:{bg:"brand.50"},children:"Profile & Settings"}),a.jsx(u,{icon:a.jsx(I,{}),_hover:{bg:"brand.50"},children:"Notifications"}),a.jsx(j,{}),a.jsx(u,{icon:a.jsx(F,{}),color:"red.500",_hover:{bg:"red.50"},onClick:()=>D(),children:"Sign Out"})]})]})};function H(){const{data:b}=v();r();const{toggleSidebar:g}=N();s({base:!0,lg:!1}),s({base:!1,lg:!0});const m=e("gray.200","gray.700"),f=e("rgba(255, 255, 255, 0.8)","rgba(26, 32, 44, 0.8)");return e("white","gray.800"),a.jsxs(a.Fragment,{children:[a.jsx(G,{}),a.jsx(n,{as:"nav",id:"navigation",role:"navigation","aria-label":"Main navigation",py:4,top:0,width:"full",position:"sticky",backdropFilter:"blur(20px)",borderBottom:"1px",borderColor:m,boxShadow:"sm",color:"gray.900",_dark:{color:"white"},zIndex:1e3,bg:f,transition:"all 0.2s",children:a.jsx(t,{maxW:"95%",children:a.jsxs(i,{align:"center",justify:"space-between",h:"60px",children:[a.jsx(n,{flex:"0 0 auto",children:a.jsx(o,{as:C,to:"/",_hover:{textDecoration:"none"},display:"flex",alignItems:"center",children:a.jsx(T,{})})}),a.jsx(i,{justify:"center",flex:1,display:{base:"none",lg:"flex"},mx:8,children:a.jsx(l,{spacing:2,children:P.slice(0,4).map((e=>a.jsx(U,{path:e.path,children:a.jsxs(l,{spacing:2,children:[a.jsx(c,{as:e.icon,boxSize:4}),a.jsx(d,{children:e.name})]})},e.name)))})}),a.jsxs(l,{spacing:4,alignItems:"center",flex:"0 0 auto",children:[b&&a.jsx(x,{"aria-label":"Notifications",icon:a.jsx(I,{}),variant:"ghost",size:"sm",position:"relative",_hover:{bg:"brand.50",transform:"translateY(-1px)"},display:{base:"none",md:"flex"},children:a.jsx(p,{position:"absolute",top:"-1",right:"-1",bg:"red.500",color:"white",borderRadius:"full",boxSize:"18px",fontSize:"10px",children:"3"})}),a.jsx(Z,{}),b?a.jsx(l,{spacing:3,display:{base:"none",md:"flex"},children:a.jsx(A,{user:b})}):a.jsx(h,{as:C,to:"/login",colorScheme:"brand",size:"sm",leftIcon:a.jsx(B,{}),display:{base:"none",md:"flex"},_hover:{transform:"translateY(-1px)",boxShadow:"lg"},children:"Sign In"}),a.jsx(x,{display:{base:"flex",lg:"none"},onClick:g,variant:"ghost","aria-label":"Open menu",size:"sm",icon:a.jsx(R,{}),_hover:{bg:"brand.50",transform:"scale(1.05)"},_active:{transform:"scale(0.95)"}})]})]})})})]})}export{J as SidebarContext,H as default,N as useSidebar};
