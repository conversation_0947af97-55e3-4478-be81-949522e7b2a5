var e=Object.defineProperty,t=(t,r,n)=>((t,r,n)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[r]=n)(t,"symbol"!=typeof r?r+"":r,n);const r=new class{constructor(){t(this,"metrics",{}),t(this,"isProduction",!0),t(this,"initialized",!1),t(this,"criticalResources",[]),t(this,"deferredResources",[]),"undefined"!=typeof window&&(this.setupPerformanceObserver(),this.initializeOptimizations())}setupPerformanceObserver(){if("PerformanceObserver"in window)try{const e=new PerformanceObserver((e=>{e.getEntries().forEach((e=>{if("largest-contentful-paint"===e.entryType)this.metrics.lcp=e.startTime,this.reportMetric("LCP",e.startTime);else if("first-input"===e.entryType){const t=e.processingStart-e.startTime;this.metrics.inp=t,this.reportMetric("INP",t)}else if("layout-shift"===e.entryType&&!e.hadRecentInput){const t=(this.metrics.cls||0)+e.value;this.metrics.cls=t,this.reportMetric("CLS",t)}}))}));e.observe({entryTypes:["largest-contentful-paint","first-input","layout-shift"]}),window.__PERFORMANCE_OBSERVER__=e}catch(e){}}async initializeOptimizations(){this.inlineCriticalCSS(),this.addResourceHints(),this.optimizeFonts(),"requestIdleCallback"in window?requestIdleCallback((()=>{this.deferNonCriticalResources(),this.optimizeImages(),this.setupServiceWorker(),this.optimizeMainThread(),this.preloadCriticalRoutes()})):setTimeout((()=>{this.deferNonCriticalResources(),this.optimizeImages(),this.setupServiceWorker(),this.optimizeMainThread(),this.preloadCriticalRoutes()}),100)}inlineCriticalCSS(){if(document.querySelector("[data-critical-inlined]"))return;const e=document.createElement("style");e.textContent="\n      /* Critical path CSS for immediate rendering */\n      :root {\n        --chakra-colors-blue-500: #3182CE;\n        --chakra-colors-gray-50: #F7FAFC;\n        --chakra-colors-gray-100: #EDF2F7;\n        --chakra-colors-gray-200: #E2E8F0;\n        --chakra-colors-white: #FFFFFF;\n        --chakra-fontSizes-md: 1rem;\n        --chakra-fontWeights-medium: 500;\n        --chakra-radii-md: 0.375rem;\n        --chakra-space-4: 1rem;\n        --chakra-space-6: 1.5rem;\n      }\n      \n      body {\n        margin: 0;\n        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n        line-height: 1.5;\n        -webkit-font-smoothing: antialiased;\n        -moz-osx-font-smoothing: grayscale;\n        overflow-x: hidden;\n      }\n      \n      /* Immediate layout elements */\n      .chakra-container {\n        width: 100%;\n        max-width: 1200px;\n        margin: 0 auto;\n        padding: 0 1rem;\n      }\n      \n      .chakra-flex {\n        display: flex;\n      }\n      \n      .chakra-stack {\n        display: flex;\n        flex-direction: column;\n      }\n      \n      .chakra-button {\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        font-weight: 500;\n        line-height: 1.2;\n        border-radius: var(--chakra-radii-md);\n        padding: var(--chakra-space-4) var(--chakra-space-6);\n        background: var(--chakra-colors-blue-500);\n        color: var(--chakra-colors-white);\n        border: none;\n        cursor: pointer;\n        transition: all 0.2s;\n      }\n      \n      .chakra-button:hover {\n        transform: translateY(-1px);\n      }\n      \n      /* Performance optimizations */\n      *, *::before, *::after {\n        box-sizing: border-box;\n      }\n      \n      img {\n        max-width: 100%;\n        height: auto;\n        loading: lazy;\n        decoding: async;\n      }\n      \n      /* Reduce layout shift */\n      .loading-placeholder {\n        background: var(--chakra-colors-gray-100);\n        border-radius: var(--chakra-radii-md);\n        animation: pulse 1.5s ease-in-out infinite;\n      }\n      \n      @keyframes pulse {\n        0%, 100% { opacity: 1; }\n        50% { opacity: 0.5; }\n      }\n      \n      /* Viewport optimizations */\n      @media (max-width: 768px) {\n        .chakra-container {\n          padding: 0 0.5rem;\n        }\n      }\n      \n      /* Reduce motion for accessibility */\n      @media (prefers-reduced-motion: reduce) {\n        *, *::before, *::after {\n          animation-duration: 0.01ms !important;\n          animation-iteration-count: 1 !important;\n          transition-duration: 0.01ms !important;\n        }\n      }\n    ",e.setAttribute("data-critical-inlined","true"),document.head.insertBefore(e,document.head.firstChild)}addResourceHints(){[{rel:"dns-prefetch",href:"//fonts.googleapis.com"},{rel:"dns-prefetch",href:"//fonts.gstatic.com"},{rel:"dns-prefetch",href:"//cdn.jsdelivr.net"},{rel:"preconnect",href:"https://fonts.gstatic.com",crossorigin:"anonymous"}].forEach((e=>{if(!document.querySelector(`link[rel="${e.rel}"][href="${e.href}"]`)){const t=document.createElement("link");t.rel=e.rel,t.href=e.href,e.crossorigin&&(t.crossOrigin=e.crossorigin),document.head.appendChild(t)}}))}optimizeFonts(){[{href:"https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2",weight:"400"}].forEach((e=>{if(!document.querySelector(`link[href="${e.href}"]`)){const t=document.createElement("link");t.rel="preload",t.as="font",t.type="font/woff2",t.href=e.href,t.crossOrigin="anonymous",document.head.appendChild(t)}}));const e=document.createElement("style");e.textContent="\n      @font-face {\n        font-display: swap;\n      }\n    ",e.setAttribute("data-font-display","true"),document.head.appendChild(e)}deferNonCriticalResources(){document.querySelectorAll('link[rel="stylesheet"]:not([data-critical])').forEach((e=>{if(e instanceof HTMLLinkElement){const t=e.href;if((t.includes("icon")||t.includes("font")||t.includes("animation")||t.includes("highlight"))&&!e.dataset.deferred){e.dataset.deferred="true",e.rel="preload",e.as="style";const t=()=>{e.rel="stylesheet",e.onload=null};e.onload=t,setTimeout(t,3e3)}}}));document.querySelectorAll('script[data-defer="true"]').forEach((e=>{e instanceof HTMLScriptElement&&!e.dataset.loaded&&(e.dataset.loaded="true","requestIdleCallback"in window?requestIdleCallback((()=>this.loadScript(e))):setTimeout((()=>this.loadScript(e)),2e3))}))}loadScript(e){const t=document.createElement("script");t.src=e.src,t.async=!0,t.defer=!0,document.head.appendChild(t)}optimizeImages(){if("IntersectionObserver"in window){const e=new IntersectionObserver((t=>{t.forEach((t=>{if(t.isIntersecting){const r=t.target;r.dataset.src&&!r.src&&(r.src=r.dataset.src,r.classList.remove("lazy"),e.unobserve(r))}}))}),{rootMargin:"50px 0px",threshold:.01});document.querySelectorAll("img[data-src]").forEach((t=>{e.observe(t)})),document.querySelectorAll("img:not([loading])").forEach(((e,t)=>{e instanceof HTMLImageElement&&(e.loading=t<3?"eager":"lazy",e.decoding="async",e.width||e.height||e.style.width||e.style.height||(e.style.aspectRatio="16/9"))}))}}async setupServiceWorker(){if("serviceWorker"in navigator&&"https:"===window.location.protocol)try{await navigator.serviceWorker.register("/sw.js")}catch(e){}}optimizeMainThread(){const e=[],t=()=>{const r=performance.now();for(;e.length>0&&performance.now()-r<5;){const t=e.shift();t&&t()}e.length>0&&("scheduler"in window&&"postTask"in window.scheduler?window.scheduler.postTask(t,{priority:"background"}):setTimeout(t,0))},r=["scroll","wheel","touchstart","touchmove"];return r.forEach((e=>{const t=EventTarget.prototype.addEventListener;EventTarget.prototype.addEventListener=function(e,n,i){return r.includes(e)&&("object"!=typeof i?i={passive:!0}:i&&"object"==typeof i&&!("passive"in i)&&(i.passive=!0)),t.call(this,e,n,i)}})),{addTask:r=>{e.push(r),1===e.length&&t()}}}preloadCriticalRoutes(){["/dashboard","/cover-letters","/jobs","/resume"].forEach((e=>{const t=document.createElement("link");t.rel="prefetch",t.href=e,document.head.appendChild(t)}))}reportMetric(e,t,r){this.isProduction&&"undefined"!=typeof window&&"gtag"in window&&"function"==typeof window.gtag&&window.gtag("event","performance_metric",{metric_name:e,metric_value:Math.round(t),...r})}optimizeChakraUI(){this.initialized||("undefined"!=typeof window&&(window.__CHAKRA_TREE_SHAKE__={unused:["Accordion","Alert","AlertDialog","Avatar","Badge","Breadcrumb","Checkbox","CircularProgress","CloseButton","Code","Divider","Drawer","Editable","FormControl","Icon","IconButton","Image","Kbd","Link","List","Menu","Modal","NumberInput","Popover","Progress","Radio","RangeSlider","Select","Skeleton","Slider","Spinner","Stat","Switch","Table","Tabs","Tag","Textarea","Toast","Tooltip"],critical:["Box","Flex","VStack","HStack","Text","Heading","Button","Input","Container","Stack","Grid","GridItem","Spacer"]}),this.initialized=!0)}deferNonCriticalJS(){this.deferNonCriticalResources()}getMetrics(){return{...this.metrics}}preloadRoute(e){const t=document.createElement("link");t.rel="prefetch",t.href=e,document.head.appendChild(t)}optimizeForRoute(e){switch(e){case"/dashboard":this.preloadRoute("/cover-letters"),this.preloadRoute("/jobs");break;case"/cover-letters":this.preloadRoute("/jobs");break;case"/jobs":this.preloadRoute("/cover-letters")}}},n=async e=>{try{return await e()}catch(t){return null}},i=(e,t)=>{let r;return(...n)=>{clearTimeout(r),r=setTimeout((()=>e(...n)),t)}},o=()=>{document.querySelectorAll('img[data-priority="high"]').forEach((e=>{e instanceof HTMLImageElement&&(e.setAttribute("fetchpriority","high"),e.loading="eager")}));document.querySelectorAll('img[data-priority="low"]').forEach((e=>{e instanceof HTMLImageElement&&(e.setAttribute("fetchpriority","low"),e.loading="lazy")}))},a=()=>({preloadRoute:e=>r.preloadRoute(e),optimizeImage:e=>{e.loading="lazy",e.decoding="async"},getMetrics:()=>r.getMetrics(),optimizeForRoute:e=>r.optimizeForRoute(e)});export{i as debounce,n as lazyImport,o as optimizeResourcePriority,r as performanceOptimizer,a as usePerformanceOptimization};
