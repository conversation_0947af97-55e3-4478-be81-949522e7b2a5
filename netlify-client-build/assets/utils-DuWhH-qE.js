import{d as e,b as t}from"./vendor-BJbfb-22.js";var r=e=>"checkbox"===e.type,s=e=>e instanceof Date,a=e=>null==e;const i=e=>"object"==typeof e;var n=e=>!a(e)&&!Array.isArray(e)&&i(e)&&!s(e),o="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function l(e){let t;const r=Array.isArray(e),s="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else{if(o&&(e instanceof Blob||s)||!r&&!n(e))return e;if(t=r?[]:{},r||(e=>{const t=e.constructor&&e.constructor.prototype;return n(t)&&t.hasOwnProperty("isPrototypeOf")})(e))for(const r in e)e.hasOwnProperty(r)&&(t[r]=l(e[r]));else t=e}return t}var u=e=>Array.isArray(e)?e.filter(Boolean):[],d=e=>void 0===e,f=(e,t,r)=>{if(!t||!n(e))return r;const s=u(t.split(/[,[\].]+?/)).reduce(((e,t)=>a(e)?e:e[t]),e);return d(s)||s===e?d(e[t])?r:e[t]:s},c=e=>"boolean"==typeof e,y=e=>/^\w*$/.test(e),m=e=>u(e.replace(/["|']|\]/g,"").split(/\.|\[/)),v=(e,t,r)=>{let s=-1;const a=y(t)?[t]:m(t),i=a.length,o=i-1;for(;++s<i;){const t=a[s];let i=r;if(s!==o){const r=e[t];i=n(r)||Array.isArray(r)?r:isNaN(+a[s+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=i,e=e[t]}};const h="blur",g="focusout",b="onBlur",p="onChange",_="onSubmit",V="onTouched",A="all",F="max",w="min",S="maxLength",x="minLength",D="pattern",k="required",E="validate";e.createContext(null);const O="undefined"!=typeof window?t.useLayoutEffect:t.useEffect;var C=e=>"string"==typeof e,L=(e,t,r,s,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:a||!0}}:{},T=e=>Array.isArray(e)?e:[e],N=()=>{let e=[];return{get observers(){return e},next:t=>{for(const r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter((e=>e!==t))}}),unsubscribe:()=>{e=[]}}},j=e=>a(e)||!i(e);function M(e,t){if(j(e)||j(t))return e===t;if(s(e)&&s(t))return e.getTime()===t.getTime();const r=Object.keys(e),a=Object.keys(t);if(r.length!==a.length)return!1;for(const i of r){const r=e[i];if(!a.includes(i))return!1;if("ref"!==i){const e=t[i];if(s(r)&&s(e)||n(r)&&n(e)||Array.isArray(r)&&Array.isArray(e)?!M(r,e):r!==e)return!1}}return!0}var B=e=>n(e)&&!Object.keys(e).length,U=e=>"file"===e.type,R=e=>"function"==typeof e,q=e=>{if(!o)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},P=e=>"select-multiple"===e.type,I=e=>"radio"===e.type,W=e=>q(e)&&e.isConnected;function H(e,t){const r=Array.isArray(t)?t:y(t)?[t]:m(t),s=1===r.length?e:function(e,t){const r=t.slice(0,-1).length;let s=0;for(;s<r;)e=d(e)?s++:e[t[s++]];return e}(e,r),a=r.length-1,i=r[a];return s&&delete s[i],0!==a&&(n(s)&&B(s)||Array.isArray(s)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!d(e[t]))return!1;return!0}(s))&&H(e,r.slice(0,-1)),e}var $=e=>{for(const t in e)if(R(e[t]))return!0;return!1};function z(e,t={}){const r=Array.isArray(e);if(n(e)||r)for(const s in e)Array.isArray(e[s])||n(e[s])&&!$(e[s])?(t[s]=Array.isArray(e[s])?[]:{},z(e[s],t[s])):a(e[s])||(t[s]=!0);return t}function G(e,t,r){const s=Array.isArray(e);if(n(e)||s)for(const i in e)Array.isArray(e[i])||n(e[i])&&!$(e[i])?d(t)||j(r[i])?r[i]=Array.isArray(e[i])?z(e[i],[]):{...z(e[i])}:G(e[i],a(t)?{}:t[i],r[i]):r[i]=!M(e[i],t[i]);return r}var J=(e,t)=>G(e,t,z(t));const K={value:!1,isValid:!1},Q={value:!0,isValid:!0};var X=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter((e=>e&&e.checked&&!e.disabled)).map((e=>e.value));return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!d(e[0].attributes.value)?d(e[0].value)||""===e[0].value?Q:{value:e[0].value,isValid:!0}:Q:K}return K},Y=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>d(e)?e:t?""===e?NaN:e?+e:e:r&&C(e)?new Date(e):s?s(e):e;const Z={isValid:!1,value:null};var ee=e=>Array.isArray(e)?e.reduce(((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e),Z):Z;function te(e){const t=e.ref;return U(t)?t.files:I(t)?ee(e.refs).value:P(t)?[...t.selectedOptions].map((({value:e})=>e)):r(t)?X(e.refs).value:Y(d(t.value)?e.ref.value:t.value,e)}var re=e=>e instanceof RegExp,se=e=>d(e)?e:re(e)?e.source:n(e)?re(e.value)?e.value.source:e.value:e,ae=e=>({isOnSubmit:!e||e===_,isOnBlur:e===b,isOnChange:e===p,isOnAll:e===A,isOnTouch:e===V});const ie="AsyncFunction";var ne=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some((t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length)))));const oe=(e,t,r,s)=>{for(const a of r||Object.keys(e)){const r=f(e,a);if(r){const{_f:e,...i}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!s)return!0;if(e.ref&&t(e.ref,e.name)&&!s)return!0;if(oe(i,t))break}else if(n(i)&&oe(i,t))break}}};function le(e,t,r){const s=f(e,r);if(s||y(r))return{error:s,name:r};const a=r.split(".");for(;a.length;){const s=a.join("."),i=f(t,s),n=f(e,s);if(i&&!Array.isArray(i)&&r!==s)return{name:r};if(n&&n.type)return{name:s,error:n};if(n&&n.root&&n.root.type)return{name:`${s}.root`,error:n.root};a.pop()}return{name:r}}var ue=(e,t,r)=>{const s=T(f(e,r));return v(s,"root",t[r]),v(e,r,s),e},de=e=>C(e);function fe(e,t,r="validate"){if(de(e)||Array.isArray(e)&&e.every(de)||c(e)&&!e)return{type:r,message:de(e)?e:"",ref:t}}var ce=e=>n(e)&&!re(e)?e:{value:e,message:""},ye=async(e,t,s,i,o,l)=>{const{ref:u,refs:y,required:m,maxLength:v,minLength:h,min:g,max:b,pattern:p,validate:_,name:V,valueAsNumber:A,mount:O}=e._f,T=f(s,V);if(!O||t.has(V))return{};const N=y?y[0]:u,j=e=>{o&&N.reportValidity&&(N.setCustomValidity(c(e)?"":e||""),N.reportValidity())},M={},P=I(u),W=r(u),H=P||W,$=(A||U(u))&&d(u.value)&&d(T)||q(u)&&""===u.value||""===T||Array.isArray(T)&&!T.length,z=L.bind(null,V,i,M),G=(e,t,r,s=S,a=x)=>{const i=e?t:r;M[V]={type:e?s:a,message:i,ref:u,...z(e?s:a,i)}};if(l?!Array.isArray(T)||!T.length:m&&(!H&&($||a(T))||c(T)&&!T||W&&!X(y).isValid||P&&!ee(y).isValid)){const{value:e,message:t}=de(m)?{value:!!m,message:m}:ce(m);if(e&&(M[V]={type:k,message:t,ref:N,...z(k,t)},!i))return j(t),M}if(!($||a(g)&&a(b))){let e,t;const r=ce(b),s=ce(g);if(a(T)||isNaN(T)){const a=u.valueAsDate||new Date(T),i=e=>new Date((new Date).toDateString()+" "+e),n="time"==u.type,o="week"==u.type;C(r.value)&&T&&(e=n?i(T)>i(r.value):o?T>r.value:a>new Date(r.value)),C(s.value)&&T&&(t=n?i(T)<i(s.value):o?T<s.value:a<new Date(s.value))}else{const i=u.valueAsNumber||(T?+T:T);a(r.value)||(e=i>r.value),a(s.value)||(t=i<s.value)}if((e||t)&&(G(!!e,r.message,s.message,F,w),!i))return j(M[V].message),M}if((v||h)&&!$&&(C(T)||l&&Array.isArray(T))){const e=ce(v),t=ce(h),r=!a(e.value)&&T.length>+e.value,s=!a(t.value)&&T.length<+t.value;if((r||s)&&(G(r,e.message,t.message),!i))return j(M[V].message),M}if(p&&!$&&C(T)){const{value:e,message:t}=ce(p);if(re(e)&&!T.match(e)&&(M[V]={type:D,message:t,ref:u,...z(D,t)},!i))return j(t),M}if(_)if(R(_)){const e=fe(await _(T,s),N);if(e&&(M[V]={...e,...z(E,e.message)},!i))return j(e.message),M}else if(n(_)){let e={};for(const t in _){if(!B(e)&&!i)break;const r=fe(await _[t](T,s),N,t);r&&(e={...r,...z(t,r.message)},j(r.message),i&&(M[V]=e))}if(!B(e)&&(M[V]={ref:N,...e},!i))return M}return j(!0),M};const me={mode:_,reValidateMode:p,shouldFocusError:!0};function ve(e={}){let t={...me,...e},i={submitCount:0,isDirty:!1,isReady:!1,isLoading:R(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1};const y={};let m,b=(n(t.defaultValues)||n(t.values))&&l(t.defaultValues||t.values)||{},p=t.shouldUnregister?{}:l(b),_={action:!1,mount:!1,watch:!1},V={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},F=0;const w={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let S={...w};const x={array:N(),state:N()},D=t.criteriaMode===A,k=async e=>{if(!t.disabled&&(w.isValid||S.isValid||e)){const e=t.resolver?B((await $()).errors):await z(y,!0);e!==i.isValid&&x.state.next({isValid:e})}},E=(e,r)=>{!t.disabled&&(w.isValidating||w.validatingFields||S.isValidating||S.validatingFields)&&((e||Array.from(V.mount)).forEach((e=>{e&&(r?v(i.validatingFields,e,r):H(i.validatingFields,e))})),x.state.next({validatingFields:i.validatingFields,isValidating:!B(i.validatingFields)}))},O=(e,t,r,s)=>{const a=f(y,e);if(a){const i=f(p,e,d(r)?f(b,e):r);d(i)||s&&s.defaultChecked||t?v(p,e,t?i:te(a._f)):Q(e,i),_.mount&&k()}},L=(e,r,s,a,n)=>{let o=!1,l=!1;const u={name:e};if(!t.disabled){if(!s||a){(w.isDirty||S.isDirty)&&(l=i.isDirty,i.isDirty=u.isDirty=G(),o=l!==u.isDirty);const t=M(f(b,e),r);l=!!f(i.dirtyFields,e),t?H(i.dirtyFields,e):v(i.dirtyFields,e,!0),u.dirtyFields=i.dirtyFields,o=o||(w.dirtyFields||S.dirtyFields)&&l!==!t}if(s){const t=f(i.touchedFields,e);t||(v(i.touchedFields,e,s),u.touchedFields=i.touchedFields,o=o||(w.touchedFields||S.touchedFields)&&t!==s)}o&&n&&x.state.next(u)}return o?u:{}},j=(e,r,s,a)=>{const n=f(i.errors,e),o=(w.isValid||S.isValid)&&c(r)&&i.isValid!==r;var l;if(t.delayError&&s?(l=()=>((e,t)=>{v(i.errors,e,t),x.state.next({errors:i.errors})})(e,s),m=e=>{clearTimeout(F),F=setTimeout(l,e)},m(t.delayError)):(clearTimeout(F),m=null,s?v(i.errors,e,s):H(i.errors,e)),(s?!M(n,s):n)||!B(a)||o){const t={...a,...o&&c(r)?{isValid:r}:{},errors:i.errors,name:e};i={...i,...t},x.state.next(t)}},$=async e=>{E(e,!0);const r=await t.resolver(p,t.context,((e,t,r,s)=>{const a={};for(const i of e){const e=f(t,i);e&&v(a,i,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:s}})(e||V.mount,y,t.criteriaMode,t.shouldUseNativeValidation));return E(e),r},z=async(e,r,s={valid:!0})=>{for(const o in e){const l=e[o];if(l){const{_f:e,...u}=l;if(e){const u=V.array.has(e.name),d=l._f&&(!!(a=l._f)&&!!a.validate&&!!(R(a.validate)&&a.validate.constructor.name===ie||n(a.validate)&&Object.values(a.validate).find((e=>e.constructor.name===ie))));d&&w.validatingFields&&E([o],!0);const c=await ye(l,V.disabled,p,D,t.shouldUseNativeValidation&&!r,u);if(d&&w.validatingFields&&E([o]),c[e.name]&&(s.valid=!1,r))break;!r&&(f(c,e.name)?u?ue(i.errors,c,e.name):v(i.errors,e.name,c[e.name]):H(i.errors,e.name))}!B(u)&&await z(u,r,s)}}var a;return s.valid},G=(e,r)=>!t.disabled&&(e&&r&&v(p,e,r),!M(fe(),b)),K=(e,t,r)=>((e,t,r,s,a)=>C(e)?(s&&t.watch.add(e),f(r,e,a)):Array.isArray(e)?e.map((e=>(s&&t.watch.add(e),f(r,e)))):(s&&(t.watchAll=!0),r))(e,V,{..._.mount?p:d(t)?b:C(e)?{[e]:t}:t},r,t),Q=(e,t,s={})=>{const i=f(y,e);let n=t;if(i){const s=i._f;s&&(!s.disabled&&v(p,e,Y(t,s)),n=q(s.ref)&&a(t)?"":t,P(s.ref)?[...s.ref.options].forEach((e=>e.selected=n.includes(e.value))):s.refs?r(s.ref)?s.refs.forEach((e=>{e.defaultChecked&&e.disabled||(Array.isArray(n)?e.checked=!!n.find((t=>t===e.value)):e.checked=n===e.value||!!n)})):s.refs.forEach((e=>e.checked=e.value===n)):U(s.ref)?s.ref.value="":(s.ref.value=n,s.ref.type||x.state.next({name:e,values:l(p)})))}(s.shouldDirty||s.shouldTouch)&&L(e,n,s.shouldTouch,s.shouldDirty,!0),s.shouldValidate&&de(e)},X=(e,t,r)=>{for(const a in t){if(!t.hasOwnProperty(a))return;const i=t[a],o=e+"."+a,l=f(y,o);(V.array.has(e)||n(i)||l&&!l._f)&&!s(i)?X(o,i,r):Q(o,i,r)}},Z=(e,t,r={})=>{const s=f(y,e),n=V.array.has(e),o=l(t);v(p,e,o),n?(x.array.next({name:e,values:l(p)}),(w.isDirty||w.dirtyFields||S.isDirty||S.dirtyFields)&&r.shouldDirty&&x.state.next({name:e,dirtyFields:J(b,p),isDirty:G(e,o)})):!s||s._f||a(o)?Q(e,o,r):X(e,o,r),ne(e,V)&&x.state.next({...i}),x.state.next({name:_.mount?e:void 0,values:l(p)})},ee=async e=>{_.mount=!0;const a=e.target;let o=a.name,u=!0;const d=f(y,o),c=e=>{u=Number.isNaN(e)||s(e)&&isNaN(e.getTime())||M(e,f(p,o,e))},b=ae(t.mode),A=ae(t.reValidateMode);if(d){let s,_;const O=a.type?te(d._f):(e=>n(e)&&e.target?r(e.target)?e.target.checked:e.target.value:e)(e),C=e.type===h||e.type===g,T=!((F=d._f).mount&&(F.required||F.min||F.max||F.maxLength||F.minLength||F.pattern||F.validate)||t.resolver||f(i.errors,o)||d._f.deps)||((e,t,r,s,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?s.isOnBlur:a.isOnBlur)?!e:!(r?s.isOnChange:a.isOnChange)||e))(C,f(i.touchedFields,o),i.isSubmitted,A,b),N=ne(o,V,C);v(p,o,O),C?(d._f.onBlur&&d._f.onBlur(e),m&&m(0)):d._f.onChange&&d._f.onChange(e);const M=L(o,O,C),U=!B(M)||N;if(!C&&x.state.next({name:o,type:e.type,values:l(p)}),T)return(w.isValid||S.isValid)&&("onBlur"===t.mode?C&&k():C||k()),U&&x.state.next({name:o,...N?{}:M});if(!C&&N&&x.state.next({...i}),t.resolver){const{errors:e}=await $([o]);if(c(O),u){const t=le(i.errors,y,o),r=le(e,y,t.name||o);s=r.error,o=r.name,_=B(e)}}else E([o],!0),s=(await ye(d,V.disabled,p,D,t.shouldUseNativeValidation))[o],E([o]),c(O),u&&(s?_=!1:(w.isValid||S.isValid)&&(_=await z(y,!0)));u&&(d._f.deps&&de(d._f.deps),j(o,_,s,M))}var F},re=(e,t)=>{if(f(i.errors,t)&&e.focus)return e.focus(),1},de=async(e,r={})=>{let s,a;const n=T(e);if(t.resolver){const t=await(async e=>{const{errors:t}=await $(e);if(e)for(const r of e){const e=f(t,r);e?v(i.errors,r,e):H(i.errors,r)}else i.errors=t;return t})(d(e)?e:n);s=B(t),a=e?!n.some((e=>f(t,e))):s}else e?(a=(await Promise.all(n.map((async e=>{const t=f(y,e);return await z(t&&t._f?{[e]:t}:t)})))).every(Boolean),(a||i.isValid)&&k()):a=s=await z(y);return x.state.next({...!C(e)||(w.isValid||S.isValid)&&s!==i.isValid?{}:{name:e},...t.resolver||!e?{isValid:s}:{},errors:i.errors}),r.shouldFocus&&!a&&oe(y,re,e?n:V.mount),a},fe=e=>{const t={..._.mount?p:b};return d(e)?t:C(e)?f(t,e):e.map((e=>f(t,e)))},ce=(e,t)=>({invalid:!!f((t||i).errors,e),isDirty:!!f((t||i).dirtyFields,e),error:f((t||i).errors,e),isValidating:!!f(i.validatingFields,e),isTouched:!!f((t||i).touchedFields,e)}),ve=(e,t,r)=>{const s=(f(y,e,{_f:{}})._f||{}).ref,a=f(i.errors,e)||{},{ref:n,message:o,type:l,...u}=a;v(i.errors,e,{...u,...t,ref:s}),x.state.next({name:e,errors:i.errors,isValid:!1}),r&&r.shouldFocus&&s&&s.focus&&s.focus()},he=e=>x.state.subscribe({next:t=>{var r,s,a;r=e.name,s=t.name,a=e.exact,r&&s&&r!==s&&!T(r).some((e=>e&&(a?e===s:e.startsWith(s)||s.startsWith(e))))||!((e,t,r,s)=>{r(e);const{name:a,...i}=e;return B(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find((e=>t[e]===(!s||A)))})(t,e.formState||w,we,e.reRenderRoot)||e.callback({values:{...p},...i,...t})}}).unsubscribe,ge=(e,r={})=>{for(const s of e?T(e):V.mount)V.mount.delete(s),V.array.delete(s),r.keepValue||(H(y,s),H(p,s)),!r.keepError&&H(i.errors,s),!r.keepDirty&&H(i.dirtyFields,s),!r.keepTouched&&H(i.touchedFields,s),!r.keepIsValidating&&H(i.validatingFields,s),!t.shouldUnregister&&!r.keepDefaultValue&&H(b,s);x.state.next({values:l(p)}),x.state.next({...i,...r.keepDirty?{isDirty:G()}:{}}),!r.keepIsValid&&k()},be=({disabled:e,name:t})=>{(c(e)&&_.mount||e||V.disabled.has(t))&&(e?V.disabled.add(t):V.disabled.delete(t))},pe=(e,s={})=>{let a=f(y,e);const i=c(s.disabled)||c(t.disabled);return v(y,e,{...a||{},_f:{...a&&a._f?a._f:{ref:{name:e}},name:e,mount:!0,...s}}),V.mount.add(e),a?be({disabled:c(s.disabled)?s.disabled:t.disabled,name:e}):O(e,!0,s.value),{...i?{disabled:s.disabled||t.disabled}:{},...t.progressive?{required:!!s.required,min:se(s.min),max:se(s.max),minLength:se(s.minLength),maxLength:se(s.maxLength),pattern:se(s.pattern)}:{},name:e,onChange:ee,onBlur:ee,ref:i=>{if(i){pe(e,s),a=f(y,e);const t=d(i.value)&&i.querySelectorAll&&i.querySelectorAll("input,select,textarea")[0]||i,n=(e=>I(e)||r(e))(t),o=a._f.refs||[];if(n?o.find((e=>e===t)):t===a._f.ref)return;v(y,e,{_f:{...a._f,...n?{refs:[...o.filter(W),t,...Array.isArray(f(b,e))?[{}]:[]],ref:{type:t.type,name:e}}:{ref:t}}}),O(e,!1,void 0,t)}else a=f(y,e,{}),a._f&&(a._f.mount=!1),(t.shouldUnregister||s.shouldUnregister)&&(!((e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)))(V.array,e)||!_.action)&&V.unMount.add(e)}}},_e=()=>t.shouldFocusError&&oe(y,re,V.mount),Ve=(e,r)=>async s=>{let a;s&&(s.preventDefault&&s.preventDefault(),s.persist&&s.persist());let n=l(p);if(x.state.next({isSubmitting:!0}),t.resolver){const{errors:e,values:t}=await $();i.errors=e,n=t}else await z(y);if(V.disabled.size)for(const e of V.disabled)v(n,e,void 0);if(H(i.errors,"root"),B(i.errors)){x.state.next({errors:{}});try{await e(n,s)}catch(o){a=o}}else r&&await r({...i.errors},s),_e(),setTimeout(_e);if(x.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:B(i.errors)&&!a,submitCount:i.submitCount+1,errors:i.errors}),a)throw a},Ae=(e,r={})=>{const s=e?l(e):b,a=l(s),n=B(e),u=n?b:a;if(r.keepDefaultValues||(b=s),!r.keepValues){if(r.keepDirtyValues){const e=new Set([...V.mount,...Object.keys(J(b,p))]);for(const t of Array.from(e))f(i.dirtyFields,t)?v(u,t,f(p,t)):Z(t,f(u,t))}else{if(o&&d(e))for(const e of V.mount){const t=f(y,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(q(e)){const t=e.closest("form");if(t){t.reset();break}}}}for(const e of V.mount)Z(e,f(u,e))}p=l(u),x.array.next({values:{...u}}),x.state.next({values:{...u}})}V={mount:r.keepDirtyValues?V.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},_.mount=!w.isValid||!!r.keepIsValid||!!r.keepDirtyValues,_.watch=!!t.shouldUnregister,x.state.next({submitCount:r.keepSubmitCount?i.submitCount:0,isDirty:!n&&(r.keepDirty?i.isDirty:!(!r.keepDefaultValues||M(e,b))),isSubmitted:!!r.keepIsSubmitted&&i.isSubmitted,dirtyFields:n?{}:r.keepDirtyValues?r.keepDefaultValues&&p?J(b,p):i.dirtyFields:r.keepDefaultValues&&e?J(b,e):r.keepDirty?i.dirtyFields:{},touchedFields:r.keepTouched?i.touchedFields:{},errors:r.keepErrors?i.errors:{},isSubmitSuccessful:!!r.keepIsSubmitSuccessful&&i.isSubmitSuccessful,isSubmitting:!1})},Fe=(e,t)=>Ae(R(e)?e(p):e,t),we=e=>{i={...i,...e}},Se={control:{register:pe,unregister:ge,getFieldState:ce,handleSubmit:Ve,setError:ve,_subscribe:he,_runSchema:$,_focusError:_e,_getWatch:K,_getDirty:G,_setValid:k,_setFieldArray:(e,r=[],s,a,n=!0,o=!0)=>{if(a&&s&&!t.disabled){if(_.action=!0,o&&Array.isArray(f(y,e))){const t=s(f(y,e),a.argA,a.argB);n&&v(y,e,t)}if(o&&Array.isArray(f(i.errors,e))){const t=s(f(i.errors,e),a.argA,a.argB);n&&v(i.errors,e,t),((e,t)=>{!u(f(e,t)).length&&H(e,t)})(i.errors,e)}if((w.touchedFields||S.touchedFields)&&o&&Array.isArray(f(i.touchedFields,e))){const t=s(f(i.touchedFields,e),a.argA,a.argB);n&&v(i.touchedFields,e,t)}(w.dirtyFields||S.dirtyFields)&&(i.dirtyFields=J(b,p)),x.state.next({name:e,isDirty:G(e,r),dirtyFields:i.dirtyFields,errors:i.errors,isValid:i.isValid})}else v(p,e,r)},_setDisabledField:be,_setErrors:e=>{i.errors=e,x.state.next({errors:i.errors,isValid:!1})},_getFieldArray:e=>u(f(_.mount?p:b,e,t.shouldUnregister?f(b,e,[]):[])),_reset:Ae,_resetDefaultValues:()=>R(t.defaultValues)&&t.defaultValues().then((e=>{Fe(e,t.resetOptions),x.state.next({isLoading:!1})})),_removeUnmounted:()=>{for(const e of V.unMount){const t=f(y,e);t&&(t._f.refs?t._f.refs.every((e=>!W(e))):!W(t._f.ref))&&ge(e)}V.unMount=new Set},_disableForm:e=>{c(e)&&(x.state.next({disabled:e}),oe(y,((t,r)=>{const s=f(y,r);s&&(t.disabled=s._f.disabled||e,Array.isArray(s._f.refs)&&s._f.refs.forEach((t=>{t.disabled=s._f.disabled||e})))}),0,!1))},_subjects:x,_proxyFormState:w,get _fields(){return y},get _formValues(){return p},get _state(){return _},set _state(e){_=e},get _defaultValues(){return b},get _names(){return V},set _names(e){V=e},get _formState(){return i},get _options(){return t},set _options(e){t={...t,...e}}},subscribe:e=>(_.mount=!0,S={...S,...e.formState},he({...e,formState:S})),trigger:de,register:pe,handleSubmit:Ve,watch:(e,t)=>R(e)?x.state.subscribe({next:r=>e(K(void 0,t),r)}):K(e,t,!0),setValue:Z,getValues:fe,reset:Fe,resetField:(e,t={})=>{f(y,e)&&(d(t.defaultValue)?Z(e,l(f(b,e))):(Z(e,t.defaultValue),v(b,e,l(t.defaultValue))),t.keepTouched||H(i.touchedFields,e),t.keepDirty||(H(i.dirtyFields,e),i.isDirty=t.defaultValue?G(e,l(f(b,e))):G()),t.keepError||(H(i.errors,e),w.isValid&&k()),x.state.next({...i}))},clearErrors:e=>{e&&T(e).forEach((e=>H(i.errors,e))),x.state.next({errors:e?i.errors:{}})},unregister:ge,setError:ve,setFocus:(e,t={})=>{const r=f(y,e),s=r&&r._f;if(s){const e=s.refs?s.refs[0]:s.ref;e.focus&&(e.focus(),t.shouldSelect&&R(e.select)&&e.select())}},getFieldState:ce};return{...Se,formControl:Se}}function he(t={}){const r=e.useRef(void 0),s=e.useRef(void 0),[a,i]=e.useState({isDirty:!1,isValidating:!1,isLoading:R(t.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1,isReady:!1,defaultValues:R(t.defaultValues)?void 0:t.defaultValues});r.current||(r.current={...t.formControl?t.formControl:ve(t),formState:a},t.formControl&&t.defaultValues&&!R(t.defaultValues)&&t.formControl.reset(t.defaultValues,t.resetOptions));const n=r.current.control;return n._options=t,O((()=>{const e=n._subscribe({formState:n._proxyFormState,callback:()=>i({...n._formState}),reRenderRoot:!0});return i((e=>({...e,isReady:!0}))),n._formState.isReady=!0,e}),[n]),e.useEffect((()=>n._disableForm(t.disabled)),[n,t.disabled]),e.useEffect((()=>{t.mode&&(n._options.mode=t.mode),t.reValidateMode&&(n._options.reValidateMode=t.reValidateMode)}),[n,t.mode,t.reValidateMode]),e.useEffect((()=>{t.errors&&(n._setErrors(t.errors),n._focusError())}),[n,t.errors]),e.useEffect((()=>{t.shouldUnregister&&n._subjects.state.next({values:n._getWatch()})}),[n,t.shouldUnregister]),e.useEffect((()=>{if(n._proxyFormState.isDirty){const e=n._getDirty();e!==a.isDirty&&n._subjects.state.next({isDirty:e})}}),[n,a.isDirty]),e.useEffect((()=>{t.values&&!M(t.values,s.current)?(n._reset(t.values,n._options.resetOptions),s.current=t.values,i((e=>({...e})))):n._resetDefaultValues()}),[n,t.values]),e.useEffect((()=>{n._state.mount||(n._setValid(),n._state.mount=!0),n._state.watch&&(n._state.watch=!1,n._subjects.state.next({...n._formState})),n._removeUnmounted()})),r.current.formState=((e,t,r,s=!0)=>{const a={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(a,i,{get:()=>{const r=i;return t._proxyFormState[r]!==A&&(t._proxyFormState[r]=!s||A),e[r]}});return a})(a,n),r.current}export{he as u};
