import{ap as e,k as i,j as t,B as a,K as l,a as r,v as o,h as s,T as h,o as n}from"./ui-CyFeMaoA.js";import{G as C}from"./icons-CbBu1Zhv.js";import{m as c}from"./vendor-BJbfb-22.js";function d(e){return C({attr:{fill:"currentColor",viewBox:"0 0 16 16"},child:[{tag:"path",attr:{d:"M6 .278a.768.768 0 0 1 .08.858 7.208 7.208 0 0 0-.878 3.46c0 4.021 3.278 7.277 7.318 7.277.527 0 1.04-.055 1.533-.16a.787.787 0 0 1 .81.316.733.733 0 0 1-.031.893A8.349 8.349 0 0 1 8.344 16C3.734 16 0 12.286 0 7.71 0 4.266 2.114 1.312 5.124.06A.752.752 0 0 1 6 .278zM4.858 1.311A7.269 7.269 0 0 0 1.025 7.71c0 4.02 3.279 7.276 7.319 7.276a7.316 7.316 0 0 0 5.205-2.162c-.337.042-.68.063-1.029.063-4.61 0-8.343-3.714-8.343-8.29 0-1.167.242-2.278.681-3.286z"}},{tag:"path",attr:{d:"M10.794 3.148a.217.217 0 0 1 .412 0l.387 1.162c.173.518.579.924 1.097 1.097l1.162.387a.217.217 0 0 1 0 .412l-1.162.387a1.734 1.734 0 0 0-1.097 1.097l-.387 1.162a.217.217 0 0 1-.412 0l-.387-1.162A1.734 1.734 0 0 0 9.31 6.593l-1.162-.387a.217.217 0 0 1 0-.412l1.162-.387a1.734 1.734 0 0 0 1.097-1.097l.387-1.162zM13.863.099a.145.145 0 0 1 .274 0l.258.774c.115.346.386.617.732.732l.774.258a.145.145 0 0 1 0 .274l-.774.258a1.156 1.156 0 0 0-.732.732l-.258.774a.145.145 0 0 1-.274 0l-.258-.774a1.156 1.156 0 0 0-.732-.732l-.774-.258a.145.145 0 0 1 0-.274l.774-.258c.346-.115.617-.386.732-.732L13.863.1z"}}]})(e)}function x(e){return C({attr:{fill:"currentColor",viewBox:"0 0 16 16"},child:[{tag:"path",attr:{d:"M8 11a3 3 0 1 1 0-6 3 3 0 0 1 0 6zm0 1a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM8 0a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 0zm0 13a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 13zm8-5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2a.5.5 0 0 1 .5.5zM3 8a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2A.5.5 0 0 1 3 8zm10.657-5.657a.5.5 0 0 1 0 .707l-1.414 1.415a.5.5 0 1 1-.707-.708l1.414-1.414a.5.5 0 0 1 .707 0zm-9.193 9.193a.5.5 0 0 1 0 .707L3.05 13.657a.5.5 0 0 1-.707-.707l1.414-1.414a.5.5 0 0 1 .707 0zm9.193 2.121a.5.5 0 0 1-.707 0l-1.414-1.414a.5.5 0 0 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .707zM4.464 4.465a.5.5 0 0 1-.707 0L2.343 3.05a.5.5 0 1 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .708z"}}]})(e)}const g=()=>{const{colorMode:r,toggleColorMode:o}=e(),s=i("white","gray.700"),h=i("gray.100","gray.600"),n=i("gray.600","yellow.300");return t.jsx(a,{mr:"3",border:"1px",borderColor:"border-contrast-sm",bg:s,_hover:{bg:h},p:"2",size:"sm",onClick:o,"aria-label":`Switch to ${"light"===r?"dark":"light"} mode`,transition:"all 0.2s ease-in-out",children:t.jsx(l,{as:"dark"===r?x:d,color:n,boxSize:4})})};function p({size:e="50px",cColor:i,dartColor:a}){return t.jsx(r,{width:e,height:e,display:"inline-block",children:t.jsxs("svg",{width:e,height:e,viewBox:"0 0 500 500",xmlns:"http://www.w3.org/2000/svg",style:{display:"block"},children:[t.jsxs("defs",{children:[t.jsx("mask",{id:"leftHalf",children:t.jsx("rect",{x:"0",y:"0",width:"250",height:"500",fill:"white"})}),t.jsx("mask",{id:"rightHalf",children:t.jsx("rect",{x:"250",y:"0",width:"250",height:"500",fill:"white"})})]}),t.jsx("path",{d:"M 235.348 10.420 C 221.654 13.741, 209.945 23.229, 203.602 36.142 C 195.470 52.697, 196.585 70.321, 206.740 85.751 C 211.473 92.942, 218.028 98.457, 226.932 102.740 C 233.658 105.975, 235.475 106.394, 244.537 106.797 C 260.252 107.496, 271.635 103.270, 282.026 92.879 C 291.722 83.183, 296 72.637, 296 58.433 C 296 43.065, 291.525 32.515, 280.639 22.220 C 268.772 10.997, 251.510 6.499, 235.348 10.420 M 237.500 131.107 C 192.321 135.857, 155.629 152.217, 125.623 180.988 C 80.736 224.027, 62.278 285.218, 75.095 348.500 C 86.787 406.234, 131.691 458.712, 187 479.282 C 209.077 487.493, 228.598 489.974, 271.250 489.990 L 299 490 299 464 L 299 438 266.110 438 C 223.377 438, 215.626 436.606, 192.962 424.844 C 164.023 409.826, 140.297 382.061, 130.039 351.211 C 125.027 336.136, 123.703 327.870, 123.678 311.500 C 123.653 295.224, 125.176 284.842, 129.558 271.419 C 139.503 240.952, 160.686 215.121, 188.839 199.128 C 199.600 193.015, 213.298 187.889, 227 184.846 C 241.520 181.622, 266.542 181.627, 281.734 184.856 C 311.426 191.168, 334.643 205.349, 351.451 227.440 C 367.160 248.087, 375.057 275.600, 372.219 299.791 C 367.699 338.312, 343.661 364.265, 310.250 366.697 L 302 367.297 302 393.298 L 302 419.300 312.750 418.710 C 336.722 417.396, 361.280 407.534, 379.184 392.032 C 411.307 364.220, 428.400 320.439, 424.194 276.750 C 419.471 227.689, 394.248 185.451, 353.500 158.363 C 321.356 136.995, 278.120 126.836, 237.500 131.107 M 246 312.441 C 246 383.400, 246.338 419.091, 247.015 419.510 C 247.574 419.855, 259.274 419.815, 273.015 419.420 L 298 418.704 298 357.852 L 298 297 323.083 297 C 338.127 297, 348.033 296.628, 347.833 296.070 C 346.955 293.619, 248.678 206.068, 246.750 206.019 C 246.338 206.009, 246 253.898, 246 312.441",fill:i,stroke:i,strokeWidth:"3",fillRule:"evenodd",mask:"url(#leftHalf)"}),t.jsx("path",{d:"M 235.348 10.420 C 221.654 13.741, 209.945 23.229, 203.602 36.142 C 195.470 52.697, 196.585 70.321, 206.740 85.751 C 211.473 92.942, 218.028 98.457, 226.932 102.740 C 233.658 105.975, 235.475 106.394, 244.537 106.797 C 260.252 107.496, 271.635 103.270, 282.026 92.879 C 291.722 83.183, 296 72.637, 296 58.433 C 296 43.065, 291.525 32.515, 280.639 22.220 C 268.772 10.997, 251.510 6.499, 235.348 10.420 M 237.500 131.107 C 192.321 135.857, 155.629 152.217, 125.623 180.988 C 80.736 224.027, 62.278 285.218, 75.095 348.500 C 86.787 406.234, 131.691 458.712, 187 479.282 C 209.077 487.493, 228.598 489.974, 271.250 489.990 L 299 490 299 464 L 299 438 266.110 438 C 223.377 438, 215.626 436.606, 192.962 424.844 C 164.023 409.826, 140.297 382.061, 130.039 351.211 C 125.027 336.136, 123.703 327.870, 123.678 311.500 C 123.653 295.224, 125.176 284.842, 129.558 271.419 C 139.503 240.952, 160.686 215.121, 188.839 199.128 C 199.600 193.015, 213.298 187.889, 227 184.846 C 241.520 181.622, 266.542 181.627, 281.734 184.856 C 311.426 191.168, 334.643 205.349, 351.451 227.440 C 367.160 248.087, 375.057 275.600, 372.219 299.791 C 367.699 338.312, 343.661 364.265, 310.250 366.697 L 302 367.297 302 393.298 L 302 419.300 312.750 418.710 C 336.722 417.396, 361.280 407.534, 379.184 392.032 C 411.307 364.220, 428.400 320.439, 424.194 276.750 C 419.471 227.689, 394.248 185.451, 353.500 158.363 C 321.356 136.995, 278.120 126.836, 237.500 131.107 M 246 312.441 C 246 383.400, 246.338 419.091, 247.015 419.510 C 247.574 419.855, 259.274 419.815, 273.015 419.420 L 298 418.704 298 357.852 L 298 297 323.083 297 C 338.127 297, 348.033 296.628, 347.833 296.070 C 346.955 293.619, 248.678 206.068, 246.750 206.019 C 246.338 206.009, 246 253.898, 246 312.441",fill:a,stroke:a,strokeWidth:"3",fillRule:"evenodd",mask:"url(#rightHalf)"}),t.jsx("line",{x1:"250",y1:"0",x2:"250",y2:"500",stroke:"white",strokeWidth:"3",opacity:"0.8"})]})})}function m({variant:e="minimized",size:a="md"}){const l=i("gray.800","white");i("blue.600","blue.300"),i("cyan.600","cyan.400"),i("linear(135deg, blue.500 0%, cyan.600 100%)","linear(135deg, blue.400 0%, cyan.500 100%)"),i("blue.200","blue.600"),i("blue.400","blue.300");const C=i("gray.700","white"),c=i("blue.500","white"),d={sm:{width:"120px",height:"60px",iconSize:"30px",fontSize:"md"},md:{width:"200px",height:"120px",iconSize:"40px",fontSize:"xl"},lg:{width:"300px",height:"180px",iconSize:"60px",fontSize:"2xl"}}[a];return"static"===e?t.jsx(r,{width:d.width,height:d.height,children:t.jsx(o,{src:"/images/careerdart_logo.svg",alt:"CareerDart - Your Personal Career Companion",width:d.width,height:d.height,objectFit:"contain"})}):"icon"===e?t.jsx(r,{width:d.iconSize,height:d.iconSize,children:t.jsx(p,{size:d.iconSize,cColor:C,dartColor:c})}):"minimized"===e?t.jsxs(r,{position:"relative",children:[t.jsxs(s,{spacing:3,alignItems:"center",children:[t.jsx(p,{size:"40px",cColor:C,dartColor:c}),t.jsxs(h,{fontSize:"24px",color:l,letterSpacing:"-0.01em",lineHeight:"1",fontFamily:"Montserrat, -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif",children:[t.jsx(h,{as:"span",color:i("gray.900","white"),fontWeight:"700",children:"career"}),t.jsx(h,{as:"span",color:i("blue.600","blue.400"),fontWeight:"400",ml:"1px",children:"dart"})]})]}),t.jsx(n,{position:"absolute",top:"-8px",right:"-6px",colorScheme:"blue",variant:"solid",fontSize:"2xs",px:1,py:.5,borderRadius:"full",textTransform:"uppercase",fontWeight:"500",boxShadow:"xs",zIndex:10,minH:"auto",lineHeight:"1",h:"14px",minW:"24px",children:"Beta"})]}):t.jsx(r,{width:d.width,height:d.height,children:t.jsx(o,{src:"/images/careerdart_logo.svg",alt:"CareerDart - Your Personal Career Companion",width:d.width,height:d.height,objectFit:"contain"})})}c(r),c.path;export{m as L,g as T};
