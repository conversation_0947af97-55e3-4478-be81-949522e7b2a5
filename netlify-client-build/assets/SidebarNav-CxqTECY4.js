import{k as e,a1 as a,j as s,aT as i,l,aU as r,o as t,n as o,p as n,a as d,V as c,y as p,I as m,Q as b,T as x,K as h}from"./ui-CxDIvMgK.js";import{u as j,a as g,b as u,F as f,c as y,d as v,e as w,f as k,g as z,h as C,i as S,j as T,k as I,l as R}from"./index-Bu-v5N9o.js";import{useSidebar as W}from"./NavBar-Dj0e1LFA.js";import"./vendor-BJbfb-22.js";import"./icons-DRFAkztb.js";import"./utils-DuWhH-qE.js";import"./charts-Bmy0KzL9.js";import"./Logo-C3fvvPlr.js";const A=[{label:"Dashboard",icon:f,path:"/dashboard"}],F=[{label:"Cover Letters",icon:y,path:"/cover-letters"},{label:"Resume",icon:v,path:"/resume"},{label:"Jobs",icon:w,path:"/jobs"}],L=[{label:"Interview Prep",icon:k,path:"/interview"},{label:"Learning",icon:z,path:"/learning"},{label:"Tracker",icon:C,path:"/tracker"}],M=[{label:"Profile",icon:T,path:"/profile"}],_=[{label:"Admin Dashboard",icon:I,path:"/admin"}];function B(){const f=e("white","gray.900"),y=e("blue.50","gray.700"),v=e("gray.700","gray.200"),w=e("gray.200","gray.700"),{data:k}=j(),z=g(),C=u(),{isSidebarOpen:T,closeSidebar:I,isCollapsed:B,toggleCollapse:D}=W(),E=a({base:!0,lg:!1}),O=k?.email?.includes("admin")||k?.email?.endsWith("@admin.careerdart.com"),P=({item:a})=>{const i=C.pathname===a.path,l=e("blue.50","blue.900"),r=e("blue.600","blue.300");return s.jsxs(d,{as:"button",onClick:()=>{return e=a.path,void(k||"/"===e?(z(e),E&&I(),window.scrollTo({top:0,behavior:"smooth"})):z("/login"));var e},display:"flex",alignItems:"center",padding:"0.5rem",borderRadius:"0.375rem",color:i?r:v,bg:i?l:"transparent",width:"100%",textAlign:"left",border:"none",cursor:"pointer",_hover:{bg:i?l:y},transition:"all 0.2s",title:B?a.label:void 0,children:[s.jsx(h,{as:a.icon,mr:B?0:3,color:"blue.500",boxSize:5}),!B&&s.jsx(x,{fontWeight:i?"semibold":"medium",children:a.label})]},a.label)},U=({title:e})=>B?s.jsx(b,{my:2}):s.jsxs(p,{justify:"space-between",align:"center",mb:1,mt:3,px:1,children:[s.jsx(x,{fontSize:"xs",fontWeight:"medium",textTransform:"uppercase",color:"gray.500",_dark:{color:"gray.400"},letterSpacing:"wider",children:e}),"Main"===e&&s.jsx(m,{"aria-label":B?"Expand sidebar":"Collapse sidebar",icon:s.jsx(R,{size:"10px"}),size:"xs",onClick:D,variant:"ghost",colorScheme:"blue",borderRadius:"full"})]}),J=()=>s.jsxs(c,{align:"stretch",spacing:2,mt:2,children:[B&&s.jsx(p,{justify:"center",mb:3,children:s.jsx(m,{"aria-label":"Expand sidebar",icon:s.jsx(S,{size:"12px"}),size:"sm",onClick:D,variant:"ghost",colorScheme:"blue",borderRadius:"full"})}),s.jsx(U,{title:"Main"}),A.map((e=>s.jsx(P,{item:e},e.label))),s.jsx(b,{my:2}),s.jsx(U,{title:"Content"}),F.map((e=>s.jsx(P,{item:e},e.label))),s.jsx(b,{my:2}),s.jsx(U,{title:"Tools & Resources"}),L.map((e=>s.jsx(P,{item:e},e.label))),k&&s.jsxs(s.Fragment,{children:[s.jsx(b,{my:2}),s.jsx(U,{title:"User"}),M.map((e=>s.jsx(P,{item:e},e.label)))]}),k&&O&&s.jsxs(s.Fragment,{children:[s.jsx(b,{my:2}),s.jsx(U,{title:"Admin"}),_.map((e=>s.jsx(P,{item:e},e.label)))]})]});return E?s.jsxs(i,{isOpen:T,placement:"left",onClose:I,size:"xs",children:[s.jsx(l,{}),s.jsxs(r,{bg:f,zIndex:4,children:[s.jsx(t,{}),s.jsx(o,{borderBottomWidth:"1px",borderColor:w,py:3,px:4,fontSize:"md",fontWeight:"semibold",color:"blue.600",_dark:{color:"blue.300"},children:"Menu"}),s.jsx(n,{px:3,py:2,children:s.jsx(J,{})})]})]}):s.jsx(d,{as:"nav",w:B?"60px":"220px",bg:f,h:"100vh",p:B?2:4,boxShadow:"md",position:"fixed",top:"0",left:"0",zIndex:"5",pt:"80px",display:{base:"none",lg:"/"!==C.pathname?"block":"none"},transition:"width 0.3s ease, padding 0.3s ease",children:s.jsx(J,{})})}export{B as default};
