import{r as e,a as t,g as r,R as n,b as o,c as a,d as i,u as s,m as l,A as c,e as u}from"./vendor-BJbfb-22.js";function d(e,t){for(var r=0;r<t.length;r++){const n=t[r];if("string"!=typeof n&&!Array.isArray(n))for(const t in n)if("default"!==t&&!(t in e)){const r=Object.getOwnPropertyDescriptor(n,t);r&&Object.defineProperty(e,t,r.get?r:{enumerable:!0,get:()=>n[t]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var f,p,m={exports:{}},h={};var b=(p||(p=1,m.exports=function(){if(f)return h;f=1;var t=e(),r=Symbol.for("react.element"),n=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,a=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,i={key:!0,ref:!0,__self:!0,__source:!0};function s(e,t,n){var s,l={},c=null,u=null;for(s in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)o.call(t,s)&&!i.hasOwnProperty(s)&&(l[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps)void 0===l[s]&&(l[s]=t[s]);return{$$typeof:r,type:e,key:c,ref:u,props:l,_owner:a.current}}return h.Fragment=n,h.jsx=s,h.jsxs=s,h}()),m.exports),v=t();const g=d({__proto__:null,default:r(v)},[v]);var y=function(){function e(e){var t=this;this._insertTag=function(e){var r;r=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,r),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)==0&&this._insertTag(function(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),void 0!==e.nonce&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}(this));var t=this.tags[this.tags.length-1];if(this.isSpeedy){var r=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(t);try{r.insertRule(e,r.cssRules.length)}catch(n){}}else t.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach((function(e){var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)})),this.tags=[],this.ctr=0},e}(),x="-ms-",w="-moz-",k="-webkit-",S="comm",_="rule",C="decl",E="@keyframes",j=Math.abs,N=String.fromCharCode,O=Object.assign;function A(e){return e.trim()}function z(e,t,r){return e.replace(t,r)}function I(e,t){return e.indexOf(t)}function P(e,t){return 0|e.charCodeAt(t)}function R(e,t,r){return e.slice(t,r)}function T(e){return e.length}function M(e){return e.length}function $(e,t){return t.push(e),e}var D=1,B=1,F=0,L=0,W=0,H="";function q(e,t,r,n,o,a,i){return{value:e,root:t,parent:r,type:n,props:o,children:a,line:D,column:B,length:i,return:""}}function V(e,t){return O(q("",null,null,"",null,null,0),e,{length:-e.length},t)}function U(){return W=L<F?P(H,L++):0,B++,10===W&&(B=1,D++),W}function G(){return P(H,L)}function X(){return L}function Y(e,t){return R(H,e,t)}function K(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Z(e){return D=B=1,F=T(H=e),L=0,[]}function Q(e){return H="",e}function J(e){return A(Y(L-1,re(91===e?e+2:40===e?e+1:e)))}function ee(e){for(;(W=G())&&W<33;)U();return K(e)>2||K(W)>3?"":" "}function te(e,t){for(;--t&&U()&&!(W<48||W>102||W>57&&W<65||W>70&&W<97););return Y(e,X()+(t<6&&32==G()&&32==U()))}function re(e){for(;U();)switch(W){case e:return L;case 34:case 39:34!==e&&39!==e&&re(W);break;case 40:41===e&&re(e);break;case 92:U()}return L}function ne(e,t){for(;U()&&e+W!==57&&(e+W!==84||47!==G()););return"/*"+Y(t,L-1)+"*"+N(47===e?e:U())}function oe(e){for(;!K(G());)U();return Y(e,L)}function ae(e){return Q(ie("",null,null,null,[""],e=Z(e),0,[0],e))}function ie(e,t,r,n,o,a,i,s,l){for(var c=0,u=0,d=i,f=0,p=0,m=0,h=1,b=1,v=1,g=0,y="",x=o,w=a,k=n,S=y;b;)switch(m=g,g=U()){case 40:if(108!=m&&58==P(S,d-1)){-1!=I(S+=z(J(g),"&","&\f"),"&\f")&&(v=-1);break}case 34:case 39:case 91:S+=J(g);break;case 9:case 10:case 13:case 32:S+=ee(m);break;case 92:S+=te(X()-1,7);continue;case 47:switch(G()){case 42:case 47:$(le(ne(U(),X()),t,r),l);break;default:S+="/"}break;case 123*h:s[c++]=T(S)*v;case 125*h:case 59:case 0:switch(g){case 0:case 125:b=0;case 59+u:-1==v&&(S=z(S,/\f/g,"")),p>0&&T(S)-d&&$(p>32?ce(S+";",n,r,d-1):ce(z(S," ","")+";",n,r,d-2),l);break;case 59:S+=";";default:if($(k=se(S,t,r,c,u,o,s,y,x=[],w=[],d),a),123===g)if(0===u)ie(S,t,k,k,x,a,d,s,w);else switch(99===f&&110===P(S,3)?100:f){case 100:case 108:case 109:case 115:ie(e,k,k,n&&$(se(e,k,k,0,0,o,s,y,o,x=[],d),w),o,w,d,s,n?x:w);break;default:ie(S,k,k,k,[""],w,0,s,w)}}c=u=p=0,h=v=1,y=S="",d=i;break;case 58:d=1+T(S),p=m;default:if(h<1)if(123==g)--h;else if(125==g&&0==h++&&125==(W=L>0?P(H,--L):0,B--,10===W&&(B=1,D--),W))continue;switch(S+=N(g),g*h){case 38:v=u>0?1:(S+="\f",-1);break;case 44:s[c++]=(T(S)-1)*v,v=1;break;case 64:45===G()&&(S+=J(U())),f=G(),u=d=T(y=S+=oe(X())),g++;break;case 45:45===m&&2==T(S)&&(h=0)}}return a}function se(e,t,r,n,o,a,i,s,l,c,u){for(var d=o-1,f=0===o?a:[""],p=M(f),m=0,h=0,b=0;m<n;++m)for(var v=0,g=R(e,d+1,d=j(h=i[m])),y=e;v<p;++v)(y=A(h>0?f[v]+" "+g:z(g,/&\f/g,f[v])))&&(l[b++]=y);return q(e,t,r,0===o?_:s,l,c,u)}function le(e,t,r){return q(e,t,r,S,N(W),R(e,2,-2),0)}function ce(e,t,r,n){return q(e,t,r,C,R(e,0,n),R(e,n+1,-1),n)}function ue(e,t){for(var r="",n=M(e),o=0;o<n;o++)r+=t(e[o],o,e,t)||"";return r}function de(e,t,r,n){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case C:return e.return=e.return||e.value;case S:return"";case E:return e.return=e.value+"{"+ue(e.children,n)+"}";case _:e.value=e.props.join(",")}return T(r=ue(e.children,n))?e.return=e.value+"{"+r+"}":""}function fe(e){var t=Object.create(null);return function(r){return void 0===t[r]&&(t[r]=e(r)),t[r]}}var pe=function(e,t,r){for(var n=0,o=0;n=o,o=G(),38===n&&12===o&&(t[r]=1),!K(o);)U();return Y(e,L)},me=function(e,t){return Q(function(e,t){var r=-1,n=44;do{switch(K(n)){case 0:38===n&&12===G()&&(t[r]=1),e[r]+=pe(L-1,t,r);break;case 2:e[r]+=J(n);break;case 4:if(44===n){e[++r]=58===G()?"&\f":"",t[r]=e[r].length;break}default:e[r]+=N(n)}}while(n=U());return e}(Z(e),t))},he=new WeakMap,be=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,r=e.parent,n=e.column===r.column&&e.line===r.line;"rule"!==r.type;)if(!(r=r.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||he.get(r))&&!n){he.set(e,!0);for(var o=[],a=me(t,o),i=r.props,s=0,l=0;s<a.length;s++)for(var c=0;c<i.length;c++,l++)e.props[l]=o[s]?a[s].replace(/&\f/g,i[c]):i[c]+" "+a[s]}}},ve=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}};function ge(e,t){switch(function(e,t){return 45^P(e,0)?(((t<<2^P(e,0))<<2^P(e,1))<<2^P(e,2))<<2^P(e,3):0}(e,t)){case 5103:return k+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return k+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return k+e+w+e+x+e+e;case 6828:case 4268:return k+e+x+e+e;case 6165:return k+e+x+"flex-"+e+e;case 5187:return k+e+z(e,/(\w+).+(:[^]+)/,k+"box-$1$2"+x+"flex-$1$2")+e;case 5443:return k+e+x+"flex-item-"+z(e,/flex-|-self/,"")+e;case 4675:return k+e+x+"flex-line-pack"+z(e,/align-content|flex-|-self/,"")+e;case 5548:return k+e+x+z(e,"shrink","negative")+e;case 5292:return k+e+x+z(e,"basis","preferred-size")+e;case 6060:return k+"box-"+z(e,"-grow","")+k+e+x+z(e,"grow","positive")+e;case 4554:return k+z(e,/([^-])(transform)/g,"$1"+k+"$2")+e;case 6187:return z(z(z(e,/(zoom-|grab)/,k+"$1"),/(image-set)/,k+"$1"),e,"")+e;case 5495:case 3959:return z(e,/(image-set\([^]*)/,k+"$1$`$1");case 4968:return z(z(e,/(.+:)(flex-)?(.*)/,k+"box-pack:$3"+x+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+k+e+e;case 4095:case 3583:case 4068:case 2532:return z(e,/(.+)-inline(.+)/,k+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(T(e)-1-t>6)switch(P(e,t+1)){case 109:if(45!==P(e,t+4))break;case 102:return z(e,/(.+:)(.+)-([^]+)/,"$1"+k+"$2-$3$1"+w+(108==P(e,t+3)?"$3":"$2-$3"))+e;case 115:return~I(e,"stretch")?ge(z(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(115!==P(e,t+1))break;case 6444:switch(P(e,T(e)-3-(~I(e,"!important")&&10))){case 107:return z(e,":",":"+k)+e;case 101:return z(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+k+(45===P(e,14)?"inline-":"")+"box$3$1"+k+"$2$3$1"+x+"$2box$3")+e}break;case 5936:switch(P(e,t+11)){case 114:return k+e+x+z(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return k+e+x+z(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return k+e+x+z(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return k+e+x+e+e}return e}var ye=[function(e,t,r,n){if(e.length>-1&&!e.return)switch(e.type){case C:e.return=ge(e.value,e.length);break;case E:return ue([V(e,{value:z(e.value,"@","@"+k)})],n);case _:if(e.length)return function(e,t){return e.map(t).join("")}(e.props,(function(t){switch(function(e,t){return(e=t.exec(e))?e[0]:e}(t,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return ue([V(e,{props:[z(t,/:(read-\w+)/,":-moz-$1")]})],n);case"::placeholder":return ue([V(e,{props:[z(t,/:(plac\w+)/,":"+k+"input-$1")]}),V(e,{props:[z(t,/:(plac\w+)/,":-moz-$1")]}),V(e,{props:[z(t,/:(plac\w+)/,x+"input-$1")]})],n)}return""}))}}],xe=function(e){var t=e.key;if("css"===t){var r=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(r,(function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))}))}var n,o,a=e.stylisPlugins||ye,i={},s=[];n=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),(function(e){for(var t=e.getAttribute("data-emotion").split(" "),r=1;r<t.length;r++)i[t[r]]=!0;s.push(e)}));var l,c,u,d,f=[de,(d=function(e){l.insert(e)},function(e){e.root||(e=e.return)&&d(e)})],p=(c=[be,ve].concat(a,f),u=M(c),function(e,t,r,n){for(var o="",a=0;a<u;a++)o+=c[a](e,t,r,n)||"";return o});o=function(e,t,r,n){l=r,ue(ae(e?e+"{"+t.styles+"}":t.styles),p),n&&(m.inserted[t.name]=!0)};var m={key:t,sheet:new y({key:t,container:n,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:i,registered:{},insert:o};return m.sheet.hydrate(s),m};function we(){return we=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},we.apply(null,arguments)}var ke,Se,_e,Ce,Ee=function(e){var t=new WeakMap;return function(r){if(t.has(r))return t.get(r);var n=e(r);return t.set(r,n),n}},je={exports:{}},Ne={};function Oe(){return Se||(Se=1,je.exports=function(){if(ke)return Ne;ke=1;var e="function"==typeof Symbol&&Symbol.for,t=e?Symbol.for("react.element"):60103,r=e?Symbol.for("react.portal"):60106,n=e?Symbol.for("react.fragment"):60107,o=e?Symbol.for("react.strict_mode"):60108,a=e?Symbol.for("react.profiler"):60114,i=e?Symbol.for("react.provider"):60109,s=e?Symbol.for("react.context"):60110,l=e?Symbol.for("react.async_mode"):60111,c=e?Symbol.for("react.concurrent_mode"):60111,u=e?Symbol.for("react.forward_ref"):60112,d=e?Symbol.for("react.suspense"):60113,f=e?Symbol.for("react.suspense_list"):60120,p=e?Symbol.for("react.memo"):60115,m=e?Symbol.for("react.lazy"):60116,h=e?Symbol.for("react.block"):60121,b=e?Symbol.for("react.fundamental"):60117,v=e?Symbol.for("react.responder"):60118,g=e?Symbol.for("react.scope"):60119;function y(e){if("object"==typeof e&&null!==e){var f=e.$$typeof;switch(f){case t:switch(e=e.type){case l:case c:case n:case a:case o:case d:return e;default:switch(e=e&&e.$$typeof){case s:case u:case m:case p:case i:return e;default:return f}}case r:return f}}}function x(e){return y(e)===c}return Ne.AsyncMode=l,Ne.ConcurrentMode=c,Ne.ContextConsumer=s,Ne.ContextProvider=i,Ne.Element=t,Ne.ForwardRef=u,Ne.Fragment=n,Ne.Lazy=m,Ne.Memo=p,Ne.Portal=r,Ne.Profiler=a,Ne.StrictMode=o,Ne.Suspense=d,Ne.isAsyncMode=function(e){return x(e)||y(e)===l},Ne.isConcurrentMode=x,Ne.isContextConsumer=function(e){return y(e)===s},Ne.isContextProvider=function(e){return y(e)===i},Ne.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===t},Ne.isForwardRef=function(e){return y(e)===u},Ne.isFragment=function(e){return y(e)===n},Ne.isLazy=function(e){return y(e)===m},Ne.isMemo=function(e){return y(e)===p},Ne.isPortal=function(e){return y(e)===r},Ne.isProfiler=function(e){return y(e)===a},Ne.isStrictMode=function(e){return y(e)===o},Ne.isSuspense=function(e){return y(e)===d},Ne.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===n||e===c||e===a||e===o||e===d||e===f||"object"==typeof e&&null!==e&&(e.$$typeof===m||e.$$typeof===p||e.$$typeof===i||e.$$typeof===s||e.$$typeof===u||e.$$typeof===b||e.$$typeof===v||e.$$typeof===g||e.$$typeof===h)},Ne.typeOf=y,Ne}()),je.exports}!function(){if(Ce)return _e;Ce=1;var e=Oe(),t={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},r={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},n={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},o={};function a(r){return e.isMemo(r)?n:o[r.$$typeof]||t}o[e.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},o[e.Memo]=n;var i=Object.defineProperty,s=Object.getOwnPropertyNames,l=Object.getOwnPropertySymbols,c=Object.getOwnPropertyDescriptor,u=Object.getPrototypeOf,d=Object.prototype;_e=function e(t,n,o){if("string"!=typeof n){if(d){var f=u(n);f&&f!==d&&e(t,f,o)}var p=s(n);l&&(p=p.concat(l(n)));for(var m=a(t),h=a(n),b=0;b<p.length;++b){var v=p[b];if(!(r[v]||o&&o[v]||h&&h[v]||m&&m[v])){var g=c(n,v);try{i(t,v,g)}catch(y){}}}}return t}}();var Ae=function(e,t,r){var n=e.key+"-"+t.name;!1===r&&void 0===e.registered[n]&&(e.registered[n]=t.styles)},ze=function(e,t,r){Ae(e,t,r);var n=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var o=t;do{e.insert(t===o?"."+n:"",o,e.sheet,!0),o=o.next}while(void 0!==o)}};var Ie={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},Pe=/[A-Z]|^ms/g,Re=/_EMO_([^_]+?)_([^]*?)_EMO_/g,Te=function(e){return 45===e.charCodeAt(1)},Me=function(e){return null!=e&&"boolean"!=typeof e},$e=fe((function(e){return Te(e)?e:e.replace(Pe,"-$&").toLowerCase()})),De=function(e,t){switch(e){case"animation":case"animationName":if("string"==typeof t)return t.replace(Re,(function(e,t,r){return Fe={name:t,styles:r,next:Fe},t}))}return 1===Ie[e]||Te(e)||"number"!=typeof t||0===t?t:t+"px"};function Be(e,t,r){if(null==r)return"";var n=r;if(void 0!==n.__emotion_styles)return n;switch(typeof r){case"boolean":return"";case"object":var o=r;if(1===o.anim)return Fe={name:o.name,styles:o.styles,next:Fe},o.name;var a=r;if(void 0!==a.styles){var i=a.next;if(void 0!==i)for(;void 0!==i;)Fe={name:i.name,styles:i.styles,next:Fe},i=i.next;return a.styles+";"}return function(e,t,r){var n="";if(Array.isArray(r))for(var o=0;o<r.length;o++)n+=Be(e,t,r[o])+";";else for(var a in r){var i=r[a];if("object"!=typeof i){var s=i;null!=t&&void 0!==t[s]?n+=a+"{"+t[s]+"}":Me(s)&&(n+=$e(a)+":"+De(a,s)+";")}else if(!Array.isArray(i)||"string"!=typeof i[0]||null!=t&&void 0!==t[i[0]]){var l=Be(e,t,i);switch(a){case"animation":case"animationName":n+=$e(a)+":"+l+";";break;default:n+=a+"{"+l+"}"}}else for(var c=0;c<i.length;c++)Me(i[c])&&(n+=$e(a)+":"+De(a,i[c])+";")}return n}(e,t,r);case"function":if(void 0!==e){var s=Fe,l=r(e);return Fe=s,Be(e,t,l)}}var c=r;if(null==t)return c;var u=t[c];return void 0!==u?u:c}var Fe,Le=/label:\s*([^\s;{]+)\s*(;|$)/g;function We(e,t,r){if(1===e.length&&"object"==typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var n=!0,o="";Fe=void 0;var a=e[0];null==a||void 0===a.raw?(n=!1,o+=Be(r,t,a)):o+=a[0];for(var i=1;i<e.length;i++){if(o+=Be(r,t,e[i]),n)o+=a[i]}Le.lastIndex=0;for(var s,l="";null!==(s=Le.exec(o));)l+="-"+s[1];var c=function(e){for(var t,r=0,n=0,o=e.length;o>=4;++n,o-=4)t=***********(65535&(t=255&e.charCodeAt(n)|(255&e.charCodeAt(++n))<<8|(255&e.charCodeAt(++n))<<16|(255&e.charCodeAt(++n))<<24))+(59797*(t>>>16)<<16),r=***********(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^***********(65535&r)+(59797*(r>>>16)<<16);switch(o){case 3:r^=(255&e.charCodeAt(n+2))<<16;case 2:r^=(255&e.charCodeAt(n+1))<<8;case 1:r=***********(65535&(r^=255&e.charCodeAt(n)))+(59797*(r>>>16)<<16)}return(((r=***********(65535&(r^=r>>>13))+(59797*(r>>>16)<<16))^r>>>15)>>>0).toString(36)}(o)+l;return{name:c,styles:o,next:Fe}}var He=!!n.useInsertionEffect&&n.useInsertionEffect,qe=He||function(e){return e()},Ve=He||o.useLayoutEffect,Ue=o.createContext("undefined"!=typeof HTMLElement?xe({key:"css"}):null);Ue.Provider;var Ge=function(e){return o.forwardRef((function(t,r){var n=o.useContext(Ue);return e(t,n,r)}))},Xe=o.createContext({}),Ye=Ee((function(e){return Ee((function(t){return function(e,t){return"function"==typeof t?t(e):we({},e,t)}(e,t)}))})),Ke=function(e){var t=o.useContext(Xe);return e.theme!==t&&(t=Ye(t)(e.theme)),o.createElement(Xe.Provider,{value:t},e.children)},Ze=Ge((function(e,t){var r=We([e.styles],void 0,o.useContext(Xe)),n=o.useRef();return Ve((function(){var e=t.key+"-global",o=new t.sheet.constructor({key:e,nonce:t.sheet.nonce,container:t.sheet.container,speedy:t.sheet.isSpeedy}),a=!1,i=document.querySelector('style[data-emotion="'+e+" "+r.name+'"]');return t.sheet.tags.length&&(o.before=t.sheet.tags[0]),null!==i&&(a=!0,i.setAttribute("data-emotion",e),o.hydrate([i])),n.current=[o,a],function(){o.flush()}}),[t]),Ve((function(){var e=n.current,o=e[0];if(e[1])e[1]=!1;else{if(void 0!==r.next&&ze(t,r.next,!0),o.tags.length){var a=o.tags[o.tags.length-1].nextElementSibling;o.before=a,o.flush()}t.insert("",r,o,!1)}}),[t,r.name]),null}));function Qe(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return We(t)}var Je=function(){var e=Qe.apply(void 0,arguments),t="animation-"+e.name;return{name:t,styles:"@keyframes "+t+"{"+e.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}},et=String.raw,tt=et`
  :root,
  :host {
    --chakra-vh: 100vh;
  }

  @supports (height: -webkit-fill-available) {
    :root,
    :host {
      --chakra-vh: -webkit-fill-available;
    }
  }

  @supports (height: -moz-fill-available) {
    :root,
    :host {
      --chakra-vh: -moz-fill-available;
    }
  }

  @supports (height: 100dvh) {
    :root,
    :host {
      --chakra-vh: 100dvh;
    }
  }
`,rt=()=>b.jsx(Ze,{styles:tt}),nt=({scope:e=""})=>b.jsx(Ze,{styles:et`
      html {
        line-height: 1.5;
        -webkit-text-size-adjust: 100%;
        font-family: system-ui, sans-serif;
        -webkit-font-smoothing: antialiased;
        text-rendering: optimizeLegibility;
        -moz-osx-font-smoothing: grayscale;
        touch-action: manipulation;
      }

      body {
        position: relative;
        min-height: 100%;
        margin: 0;
        font-feature-settings: "kern";
      }

      ${e} :where(*, *::before, *::after) {
        border-width: 0;
        border-style: solid;
        box-sizing: border-box;
        word-wrap: break-word;
      }

      main {
        display: block;
      }

      ${e} hr {
        border-top-width: 1px;
        box-sizing: content-box;
        height: 0;
        overflow: visible;
      }

      ${e} :where(pre, code, kbd,samp) {
        font-family: SFMono-Regular, Menlo, Monaco, Consolas, monospace;
        font-size: 1em;
      }

      ${e} a {
        background-color: transparent;
        color: inherit;
        text-decoration: inherit;
      }

      ${e} abbr[title] {
        border-bottom: none;
        text-decoration: underline;
        -webkit-text-decoration: underline dotted;
        text-decoration: underline dotted;
      }

      ${e} :where(b, strong) {
        font-weight: bold;
      }

      ${e} small {
        font-size: 80%;
      }

      ${e} :where(sub,sup) {
        font-size: 75%;
        line-height: 0;
        position: relative;
        vertical-align: baseline;
      }

      ${e} sub {
        bottom: -0.25em;
      }

      ${e} sup {
        top: -0.5em;
      }

      ${e} img {
        border-style: none;
      }

      ${e} :where(button, input, optgroup, select, textarea) {
        font-family: inherit;
        font-size: 100%;
        line-height: 1.15;
        margin: 0;
      }

      ${e} :where(button, input) {
        overflow: visible;
      }

      ${e} :where(button, select) {
        text-transform: none;
      }

      ${e} :where(
          button::-moz-focus-inner,
          [type="button"]::-moz-focus-inner,
          [type="reset"]::-moz-focus-inner,
          [type="submit"]::-moz-focus-inner
        ) {
        border-style: none;
        padding: 0;
      }

      ${e} fieldset {
        padding: 0.35em 0.75em 0.625em;
      }

      ${e} legend {
        box-sizing: border-box;
        color: inherit;
        display: table;
        max-width: 100%;
        padding: 0;
        white-space: normal;
      }

      ${e} progress {
        vertical-align: baseline;
      }

      ${e} textarea {
        overflow: auto;
      }

      ${e} :where([type="checkbox"], [type="radio"]) {
        box-sizing: border-box;
        padding: 0;
      }

      ${e} input[type="number"]::-webkit-inner-spin-button,
      ${e} input[type="number"]::-webkit-outer-spin-button {
        -webkit-appearance: none !important;
      }

      ${e} input[type="number"] {
        -moz-appearance: textfield;
      }

      ${e} input[type="search"] {
        -webkit-appearance: textfield;
        outline-offset: -2px;
      }

      ${e} input[type="search"]::-webkit-search-decoration {
        -webkit-appearance: none !important;
      }

      ${e} ::-webkit-file-upload-button {
        -webkit-appearance: button;
        font: inherit;
      }

      ${e} details {
        display: block;
      }

      ${e} summary {
        display: list-item;
      }

      template {
        display: none;
      }

      [hidden] {
        display: none !important;
      }

      ${e} :where(
          blockquote,
          dl,
          dd,
          h1,
          h2,
          h3,
          h4,
          h5,
          h6,
          hr,
          figure,
          p,
          pre
        ) {
        margin: 0;
      }

      ${e} button {
        background: transparent;
        padding: 0;
      }

      ${e} fieldset {
        margin: 0;
        padding: 0;
      }

      ${e} :where(ol, ul) {
        margin: 0;
        padding: 0;
      }

      ${e} textarea {
        resize: vertical;
      }

      ${e} :where(button, [role="button"]) {
        cursor: pointer;
      }

      ${e} button::-moz-focus-inner {
        border: 0 !important;
      }

      ${e} table {
        border-collapse: collapse;
      }

      ${e} :where(h1, h2, h3, h4, h5, h6) {
        font-size: inherit;
        font-weight: inherit;
      }

      ${e} :where(button, input, optgroup, select, textarea) {
        padding: 0;
        line-height: inherit;
        color: inherit;
      }

      ${e} :where(img, svg, video, canvas, audio, iframe, embed, object) {
        display: block;
      }

      ${e} :where(img, video) {
        max-width: 100%;
        height: auto;
      }

      [data-js-focus-visible]
        :focus:not([data-focus-visible-added]):not(
          [data-focus-visible-disabled]
        ) {
        outline: none;
        box-shadow: none;
      }

      ${e} select::-ms-expand {
        display: none;
      }

      ${tt}
    `});function ot(e={}){const{name:t,strict:r=!0,hookName:n="useContext",providerName:a="Provider",errorMessage:i,defaultValue:s}=e,l=o.createContext(s);return l.displayName=t,[l.Provider,function e(){var t;const s=o.useContext(l);if(!s&&r){const r=new Error(null!=i?i:`${n} returned \`undefined\`. Seems you forgot to wrap component within ${a}`);throw r.name="ContextError",null==(t=Error.captureStackTrace)||t.call(Error,r,e),r}return s},l]}var[at,it]=ot({strict:!1,name:"PortalManagerContext"});function st(e){const{children:t,zIndex:r}=e;return b.jsx(at,{value:{zIndex:r},children:t})}st.displayName="PortalManager";var lt=Boolean(null==globalThis?void 0:globalThis.document)?o.useLayoutEffect:o.useEffect,[ct,ut]=ot({strict:!1,name:"PortalContext"}),dt="chakra-portal",ft=e=>b.jsx("div",{className:"chakra-portal-zIndex",style:{position:"absolute",zIndex:e.zIndex,top:0,left:0,right:0},children:e.children}),pt=e=>{const{appendToParentPortal:t,children:r}=e,[n,a]=o.useState(null),i=o.useRef(null),[,s]=o.useState({});o.useEffect((()=>s({})),[]);const l=ut(),c=it();lt((()=>{if(!n)return;const e=n.ownerDocument,r=t&&null!=l?l:e.body;if(!r)return;i.current=e.createElement("div"),i.current.className=dt,r.appendChild(i.current),s({});const o=i.current;return()=>{r.contains(o)&&r.removeChild(o)}}),[n]);const u=(null==c?void 0:c.zIndex)?b.jsx(ft,{zIndex:null==c?void 0:c.zIndex,children:r}):r;return i.current?v.createPortal(b.jsx(ct,{value:i.current,children:u}),i.current):b.jsx("span",{ref:e=>{e&&a(e)}})},mt=e=>{const{children:t,containerRef:r,appendToParentPortal:n}=e,a=r.current,i=null!=a?a:"undefined"!=typeof window?document.body:void 0,s=o.useMemo((()=>{const e=null==a?void 0:a.ownerDocument.createElement("div");return e&&(e.className=dt),e}),[a]),[,l]=o.useState({});return lt((()=>l({})),[]),lt((()=>{if(s&&i)return i.appendChild(s),()=>{i.removeChild(s)}}),[s,i]),i&&s?v.createPortal(b.jsx(ct,{value:n?s:null,children:t}),s):null};function ht(e){const t={appendToParentPortal:!0,...e},{containerRef:r,...n}=t;return r?b.jsx(mt,{containerRef:r,...n}):b.jsx(pt,{...n})}function bt(){const e=o.useContext(Xe);if(!e)throw Error("useTheme: `theme` is undefined. Seems you forgot to wrap your app in `<ChakraProvider />` or `<ThemeProvider />`");return e}ht.className=dt,ht.selector=".chakra-portal",ht.displayName="Portal";var vt=o.createContext({});function gt(){const e=o.useContext(vt);if(void 0===e)throw new Error("useColorMode must be used within a ColorModeProvider");return e}function yt(e,t){const{colorMode:r}=gt();return"dark"===r?t:e}vt.displayName="ColorModeContext";var xt="chakra-ui-light",wt="chakra-ui-dark";var kt,St=(kt="chakra-ui-color-mode",{ssr:!1,type:"localStorage",get(e){if(!(null==globalThis?void 0:globalThis.document))return e;let t;try{t=localStorage.getItem(kt)||e}catch(r){}return t||e},set(e){try{localStorage.setItem(kt,e)}catch(t){}}}),_t=()=>{};function Ct(e,t){return"cookie"===e.type&&e.ssr?e.get(t):t}function Et(e){const{value:t,children:r,options:{useSystemColorMode:n,initialColorMode:a,disableTransitionOnChange:i}={},colorModeManager:s=St}=e,l="dark"===a?"dark":"light",[c,u]=o.useState((()=>Ct(s,l))),[d,f]=o.useState((()=>Ct(s))),{getSystemTheme:p,setClassName:m,setDataset:h,addListener:v}=o.useMemo((()=>function(e={}){const{preventTransition:t=!0}=e,r={setDataset:e=>{const n=t?r.preventTransition():void 0;document.documentElement.dataset.theme=e,document.documentElement.style.colorScheme=e,null==n||n()},setClassName(e){document.body.classList.add(e?wt:xt),document.body.classList.remove(e?xt:wt)},query:()=>window.matchMedia("(prefers-color-scheme: dark)"),getSystemTheme(e){var t;return(null!=(t=r.query().matches)?t:"dark"===e)?"dark":"light"},addListener(e){const t=r.query(),n=t=>{e(t.matches?"dark":"light")};return"function"==typeof t.addListener?t.addListener(n):t.addEventListener("change",n),()=>{"function"==typeof t.removeListener?t.removeListener(n):t.removeEventListener("change",n)}},preventTransition(){const e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),requestAnimationFrame((()=>{requestAnimationFrame((()=>{document.head.removeChild(e)}))}))}}};return r}({preventTransition:i})),[i]),g="system"!==a||c?c:d,y=o.useCallback((e=>{const t="system"===e?p():e;u(t),m("dark"===t),h(t),s.set(t)}),[s,p,m,h]);lt((()=>{"system"===a&&f(p())}),[]),o.useEffect((()=>{const e=s.get();y(e||("system"!==a?l:"system"))}),[s,l,a,y]);const x=o.useCallback((()=>{y("dark"===g?"light":"dark")}),[g,y]);o.useEffect((()=>{if(n)return v(y)}),[n,v,y]);const w=o.useMemo((()=>({colorMode:null!=t?t:g,toggleColorMode:t?_t:x,setColorMode:t?_t:y,forced:void 0!==t})),[g,x,y,t]);return b.jsx(vt.Provider,{value:w,children:r})}function jt(){return{...gt(),theme:bt()}}function Nt(e,t,r){const n=Array.isArray(t)?t:[t],o=Array.isArray(r)?r:[r];return r=>{const a=o.filter(Boolean),i=n.map(((t,n)=>{var o,i;if("breakpoints"===e)return function(e,t,r){var n,o;if(null==t)return t;const a=t=>{var r,n;return null==(n=null==(r=e.__breakpoints)?void 0:r.asArray)?void 0:n[t]};return null!=(o=null!=(n=a(t))?n:a(r))?o:r}(r,t,null!=(o=a[n])?o:t);return function(e,t,r){var n,o;if(null==t)return t;const a=t=>{var r,n;return null==(n=null==(r=e.__cssMap)?void 0:r[t])?void 0:n.value};return null!=(o=null!=(n=a(t))?n:a(r))?o:r}(r,`${e}.${t}`,null!=(i=a[n])?i:t)}));return Array.isArray(t)?i:i[0]}}Et.displayName="ColorModeProvider";var Ot=(...e)=>e.filter(Boolean).join(" ");function At(e){const t=typeof e;return null!=e&&("object"===t||"function"===t)&&!Array.isArray(e)}var zt=e=>{const{condition:t,message:r}=e};function It(e,...t){return Pt(e)?e(...t):e}var Pt=e=>"function"==typeof e,Rt=e=>e?"":void 0,Tt=e=>!!e||void 0;function Mt(...e){return function(t){e.some((e=>(null==e||e(t),null==t?void 0:t.defaultPrevented)))}}function $t(...e){return function(t){e.forEach((e=>{null==e||e(t)}))}}var Dt,Bt={exports:{}};var Ft=(Dt||(Dt=1,function(e,t){var r="__lodash_hash_undefined__",n=9007199254740991,o="[object Arguments]",i="[object Function]",s="[object Object]",l=/^\[object .+?Constructor\]$/,c=/^(?:0|[1-9]\d*)$/,u={};u["[object Float32Array]"]=u["[object Float64Array]"]=u["[object Int8Array]"]=u["[object Int16Array]"]=u["[object Int32Array]"]=u["[object Uint8Array]"]=u["[object Uint8ClampedArray]"]=u["[object Uint16Array]"]=u["[object Uint32Array]"]=!0,u[o]=u["[object Array]"]=u["[object ArrayBuffer]"]=u["[object Boolean]"]=u["[object DataView]"]=u["[object Date]"]=u["[object Error]"]=u[i]=u["[object Map]"]=u["[object Number]"]=u[s]=u["[object RegExp]"]=u["[object Set]"]=u["[object String]"]=u["[object WeakMap]"]=!1;var d,f="object"==typeof a&&a&&a.Object===Object&&a,p="object"==typeof self&&self&&self.Object===Object&&self,m=f||p||Function("return this")(),h=t&&!t.nodeType&&t,b=h&&e&&!e.nodeType&&e,v=b&&b.exports===h,g=v&&f.process,y=function(){try{var e=b&&b.require&&b.require("util").types;return e||g&&g.binding&&g.binding("util")}catch(t){}}(),x=y&&y.isTypedArray,w=Array.prototype,k=Function.prototype,S=Object.prototype,_=m["__core-js_shared__"],C=k.toString,E=S.hasOwnProperty,j=(d=/[^.]+$/.exec(_&&_.keys&&_.keys.IE_PROTO||""))?"Symbol(src)_1."+d:"",N=S.toString,O=C.call(Object),A=RegExp("^"+C.call(E).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),z=v?m.Buffer:void 0,I=m.Symbol,P=m.Uint8Array;z&&z.allocUnsafe;var R,T,M=(R=Object.getPrototypeOf,T=Object,function(e){return R(T(e))}),$=Object.create,D=S.propertyIsEnumerable,B=w.splice,F=I?I.toStringTag:void 0,L=function(){try{var e=fe(Object,"defineProperty");return e({},"",{}),e}catch(t){}}(),W=z?z.isBuffer:void 0,H=Math.max,q=Date.now,V=fe(m,"Map"),U=fe(Object,"create"),G=function(){function e(){}return function(t){if(!_e(t))return{};if($)return $(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}();function X(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function Y(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function K(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function Z(e){var t=this.__data__=new Y(e);this.size=t.size}function Q(e,t){var r=ye(e),n=!r&&ge(e),o=!r&&!n&&we(e),a=!r&&!n&&!o&&Ee(e),i=r||n||o||a,s=i?function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}(e.length,String):[],l=s.length;for(var c in e)i&&("length"==c||o&&("offset"==c||"parent"==c)||a&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||pe(c,l))||s.push(c);return s}function J(e,t,r){(void 0!==r&&!ve(e[t],r)||void 0===r&&!(t in e))&&re(e,t,r)}function ee(e,t,r){var n=e[t];E.call(e,t)&&ve(n,r)&&(void 0!==r||t in e)||re(e,t,r)}function te(e,t){for(var r=e.length;r--;)if(ve(e[r][0],t))return r;return-1}function re(e,t,r){"__proto__"==t&&L?L(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}X.prototype.clear=function(){this.__data__=U?U(null):{},this.size=0},X.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},X.prototype.get=function(e){var t=this.__data__;if(U){var n=t[e];return n===r?void 0:n}return E.call(t,e)?t[e]:void 0},X.prototype.has=function(e){var t=this.__data__;return U?void 0!==t[e]:E.call(t,e)},X.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=U&&void 0===t?r:t,this},Y.prototype.clear=function(){this.__data__=[],this.size=0},Y.prototype.delete=function(e){var t=this.__data__,r=te(t,e);return!(r<0||(r==t.length-1?t.pop():B.call(t,r,1),--this.size,0))},Y.prototype.get=function(e){var t=this.__data__,r=te(t,e);return r<0?void 0:t[r][1]},Y.prototype.has=function(e){return te(this.__data__,e)>-1},Y.prototype.set=function(e,t){var r=this.__data__,n=te(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this},K.prototype.clear=function(){this.size=0,this.__data__={hash:new X,map:new(V||Y),string:new X}},K.prototype.delete=function(e){var t=de(this,e).delete(e);return this.size-=t?1:0,t},K.prototype.get=function(e){return de(this,e).get(e)},K.prototype.has=function(e){return de(this,e).has(e)},K.prototype.set=function(e,t){var r=de(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this},Z.prototype.clear=function(){this.__data__=new Y,this.size=0},Z.prototype.delete=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r},Z.prototype.get=function(e){return this.__data__.get(e)},Z.prototype.has=function(e){return this.__data__.has(e)},Z.prototype.set=function(e,t){var r=this.__data__;if(r instanceof Y){var n=r.__data__;if(!V||n.length<199)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new K(n)}return r.set(e,t),this.size=r.size,this};var ne=function(e,t,r){for(var n=-1,o=Object(e),a=r(e),i=a.length;i--;){var s=a[++n];if(!1===t(o[s],s,o))break}return e};function oe(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":F&&F in Object(e)?function(e){var t=E.call(e,F),r=e[F];try{e[F]=void 0;var n=!0}catch(a){}var o=N.call(e);return n&&(t?e[F]=r:delete e[F]),o}(e):function(e){return N.call(e)}(e)}function ae(e){return Ce(e)&&oe(e)==o}function ie(e){return!(!_e(e)||function(e){return!!j&&j in e}(e))&&(ke(e)?A:l).test(function(e){if(null!=e){try{return C.call(e)}catch(t){}try{return e+""}catch(t){}}return""}(e))}function se(e){if(!_e(e))return function(e){var t=[];if(null!=e)for(var r in Object(e))t.push(r);return t}(e);var t=me(e),r=[];for(var n in e)("constructor"!=n||!t&&E.call(e,n))&&r.push(n);return r}function le(e,t,r,n,o){e!==t&&ne(t,(function(a,i){if(o||(o=new Z),_e(a))!function(e,t,r,n,o,a,i){var l=he(e,r),c=he(t,r),u=i.get(c);if(u)J(e,r,u);else{var d,f=a?a(l,c,r+"",e,t,i):void 0,p=void 0===f;if(p){var m=ye(c),h=!m&&we(c),b=!m&&!h&&Ee(c);f=c,m||h||b?ye(l)?f=l:Ce(d=l)&&xe(d)?f=function(e,t){var r=-1,n=e.length;for(t||(t=Array(n));++r<n;)t[r]=e[r];return t}(l):h?(p=!1,f=c.slice()):b?(p=!1,f=function(e){var t,r,n=(t=e.buffer,r=new t.constructor(t.byteLength),new P(r).set(new P(t)),r);return new e.constructor(n,e.byteOffset,e.length)}(c)):f=[]:function(e){if(!Ce(e)||oe(e)!=s)return!1;var t=M(e);if(null===t)return!0;var r=E.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&C.call(r)==O}(c)||ge(c)?(f=l,ge(l)?f=function(e){return function(e,t,r){var n=!r;r||(r={});for(var o=-1,a=t.length;++o<a;){var i=t[o],s=void 0;void 0===s&&(s=e[i]),n?re(r,i,s):ee(r,i,s)}return r}(e,je(e))}(l):_e(l)&&!ke(l)||(f=function(e){return"function"!=typeof e.constructor||me(e)?{}:G(M(e))}(c))):p=!1}p&&(i.set(c,f),o(f,c,n,a,i),i.delete(c)),J(e,r,f)}}(e,t,i,r,le,n,o);else{var l=n?n(he(e,i),a,i+"",e,t,o):void 0;void 0===l&&(l=a),J(e,i,l)}}),je)}function ce(e,t){return be(function(e,t,r){return t=H(void 0===t?e.length-1:t,0),function(){for(var n=arguments,o=-1,a=H(n.length-t,0),i=Array(a);++o<a;)i[o]=n[t+o];o=-1;for(var s=Array(t+1);++o<t;)s[o]=n[o];return s[t]=r(i),function(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}(e,this,s)}}(e,t,Ae),e+"")}var ue=L?function(e,t){return L(e,"toString",{configurable:!0,enumerable:!1,value:(r=t,function(){return r}),writable:!0});var r}:Ae;function de(e,t){var r,n,o=e.__data__;return("string"==(n=typeof(r=t))||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==r:null===r)?o["string"==typeof t?"string":"hash"]:o.map}function fe(e,t){var r=function(e,t){return null==e?void 0:e[t]}(e,t);return ie(r)?r:void 0}function pe(e,t){var r=typeof e;return!!(t=null==t?n:t)&&("number"==r||"symbol"!=r&&c.test(e))&&e>-1&&e%1==0&&e<t}function me(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||S)}function he(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}var be=function(e){var t=0,r=0;return function(){var n=q(),o=16-(n-r);if(r=n,o>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}(ue);function ve(e,t){return e===t||e!=e&&t!=t}var ge=ae(function(){return arguments}())?ae:function(e){return Ce(e)&&E.call(e,"callee")&&!D.call(e,"callee")},ye=Array.isArray;function xe(e){return null!=e&&Se(e.length)&&!ke(e)}var we=W||function(){return!1};function ke(e){if(!_e(e))return!1;var t=oe(e);return t==i||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}function Se(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=n}function _e(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function Ce(e){return null!=e&&"object"==typeof e}var Ee=x?function(e){return function(t){return e(t)}}(x):function(e){return Ce(e)&&Se(e.length)&&!!u[oe(e)]};function je(e){return xe(e)?Q(e):se(e)}var Ne,Oe=(Ne=function(e,t,r,n){le(e,t,r,n)},ce((function(e,t){var r=-1,n=t.length,o=n>1?t[n-1]:void 0,a=n>2?t[2]:void 0;for(o=Ne.length>3&&"function"==typeof o?(n--,o):void 0,a&&function(e,t,r){if(!_e(r))return!1;var n=typeof t;return!!("number"==n?xe(r)&&pe(t,r.length):"string"==n&&t in r)&&ve(r[t],e)}(t[0],t[1],a)&&(o=n<3?void 0:o,n=1),e=Object(e);++r<n;){var i=t[r];i&&Ne(e,i,r,o)}return e})));function Ae(e){return e}e.exports=Oe}(Bt,Bt.exports)),Bt.exports);const Lt=r(Ft);var Wt=e=>"string"==typeof e?e.replace(/!(important)?$/,"").trim():e;function Ht(e){const{scale:t,transform:r,compose:n}=e;return(e,o)=>{var a;const i=((e,t)=>r=>{const n=String(t),o=(e=>/!(important)?$/.test(e))(n),a=Wt(n),i=e?`${e}.${a}`:a;let s=At(r.__cssMap)&&i in r.__cssMap?r.__cssMap[i].varRef:t;return s=Wt(s),o?`${s} !important`:s})(t,e)(o);let s=null!=(a=null==r?void 0:r(i,o))?a:i;return n&&(s=n(s,o)),s}}var qt=(...e)=>t=>e.reduce(((e,t)=>t(e)),t);function Vt(e,t){return r=>{const n={property:r,scale:e};return n.transform=Ht({scale:e,transform:t}),n}}var Ut=({rtl:e,ltr:t})=>r=>"rtl"===r.direction?e:t;var Gt=["rotate(var(--chakra-rotate, 0))","scaleX(var(--chakra-scale-x, 1))","scaleY(var(--chakra-scale-y, 1))","skewX(var(--chakra-skew-x, 0))","skewY(var(--chakra-skew-y, 0))"];var Xt={"--chakra-blur":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-brightness":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-contrast":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-grayscale":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-hue-rotate":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-invert":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-saturate":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-sepia":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-drop-shadow":"var(--chakra-empty,/*!*/ /*!*/)",filter:["var(--chakra-blur)","var(--chakra-brightness)","var(--chakra-contrast)","var(--chakra-grayscale)","var(--chakra-hue-rotate)","var(--chakra-invert)","var(--chakra-saturate)","var(--chakra-sepia)","var(--chakra-drop-shadow)"].join(" ")},Yt={backdropFilter:["var(--chakra-backdrop-blur)","var(--chakra-backdrop-brightness)","var(--chakra-backdrop-contrast)","var(--chakra-backdrop-grayscale)","var(--chakra-backdrop-hue-rotate)","var(--chakra-backdrop-invert)","var(--chakra-backdrop-opacity)","var(--chakra-backdrop-saturate)","var(--chakra-backdrop-sepia)"].join(" "),"--chakra-backdrop-blur":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-brightness":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-contrast":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-grayscale":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-hue-rotate":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-invert":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-opacity":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-saturate":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-backdrop-sepia":"var(--chakra-empty,/*!*/ /*!*/)"};var Kt={"row-reverse":{space:"--chakra-space-x-reverse",divide:"--chakra-divide-x-reverse"},"column-reverse":{space:"--chakra-space-y-reverse",divide:"--chakra-divide-y-reverse"}},Zt={"to-t":"to top","to-tr":"to top right","to-r":"to right","to-br":"to bottom right","to-b":"to bottom","to-bl":"to bottom left","to-l":"to left","to-tl":"to top left"},Qt=new Set(Object.values(Zt)),Jt=new Set(["none","-moz-initial","inherit","initial","revert","unset"]),er=e=>e.trim();var tr=e=>"string"==typeof e&&e.includes("(")&&e.includes(")");var rr=e=>t=>`${e}(${t})`,nr={filter:e=>"auto"!==e?e:Xt,backdropFilter:e=>"auto"!==e?e:Yt,ring:e=>function(e){return{"--chakra-ring-offset-shadow":"var(--chakra-ring-inset) 0 0 0 var(--chakra-ring-offset-width) var(--chakra-ring-offset-color)","--chakra-ring-shadow":"var(--chakra-ring-inset) 0 0 0 calc(var(--chakra-ring-width) + var(--chakra-ring-offset-width)) var(--chakra-ring-color)","--chakra-ring-width":e,boxShadow:["var(--chakra-ring-offset-shadow)","var(--chakra-ring-shadow)","var(--chakra-shadow, 0 0 #0000)"].join(", ")}}(nr.px(e)),bgClip:e=>"text"===e?{color:"transparent",backgroundClip:"text"}:{backgroundClip:e},transform:e=>"auto"===e?["translateX(var(--chakra-translate-x, 0))","translateY(var(--chakra-translate-y, 0))",...Gt].join(" "):"auto-gpu"===e?["translate3d(var(--chakra-translate-x, 0), var(--chakra-translate-y, 0), 0)",...Gt].join(" "):e,vh:e=>"$100vh"===e?"var(--chakra-vh)":e,px(e){if(null==e)return e;const{unitless:t}=(e=>{const t=parseFloat(e.toString()),r=e.toString().replace(String(t),"");return{unitless:!r,value:t,unit:r}})(e);return t||"number"==typeof e?`${e}px`:e},fraction:e=>"number"!=typeof e||e>1?e:100*e+"%",float:(e,t)=>"rtl"===t.direction?{left:"right",right:"left"}[e]:e,degree(e){if(function(e){return/^var\(--.+\)$/.test(e)}(e)||null==e)return e;const t="string"==typeof e&&!e.endsWith("deg");return"number"==typeof e||t?`${e}deg`:e},gradient:(e,t)=>function(e,t){if(null==e||Jt.has(e))return e;if(!tr(e)&&!Jt.has(e))return`url('${e}')`;const r=/(^[a-z-A-Z]+)\((.*)\)/g.exec(e),n=null==r?void 0:r[1],o=null==r?void 0:r[2];if(!n||!o)return e;const a=n.includes("-gradient")?n:`${n}-gradient`,[i,...s]=o.split(",").map(er).filter(Boolean);if(0===(null==s?void 0:s.length))return e;const l=i in Zt?Zt[i]:i;s.unshift(l);const c=s.map((e=>{if(Qt.has(e))return e;const r=e.indexOf(" "),[n,o]=-1!==r?[e.substr(0,r),e.substr(r+1)]:[e],a=tr(o)?o:o&&o.split(" "),i=`colors.${n}`,s=i in t.__cssMap?t.__cssMap[i].varRef:n;return a?[s,...Array.isArray(a)?a:[a]].join(" "):s}));return`${a}(${c.join(", ")})`}(e,null!=t?t:{}),blur:rr("blur"),opacity:rr("opacity"),brightness:rr("brightness"),contrast:rr("contrast"),dropShadow:rr("drop-shadow"),grayscale:rr("grayscale"),hueRotate:rr("hue-rotate"),invert:rr("invert"),saturate:rr("saturate"),sepia:rr("sepia"),bgImage(e){if(null==e)return e;return tr(e)||Jt.has(e)?e:`url(${e})`},outline(e){const t="0"===String(e)||"none"===String(e);return null!==e&&t?{outline:"2px solid transparent",outlineOffset:"2px"}:{outline:e}},flexDirection(e){var t;const{space:r,divide:n}=null!=(t=Kt[e])?t:{},o={flexDirection:e};return r&&(o[r]=1),n&&(o[n]=1),o}},or={borderWidths:Vt("borderWidths"),borderStyles:Vt("borderStyles"),colors:Vt("colors"),borders:Vt("borders"),gradients:Vt("gradients",nr.gradient),radii:Vt("radii",nr.px),space:Vt("space",qt(nr.vh,nr.px)),spaceT:Vt("space",qt(nr.vh,nr.px)),degreeT:e=>({property:e,transform:nr.degree}),prop:(e,t,r)=>({property:e,scale:t,...t&&{transform:Ht({scale:t,transform:r})}}),propT:(e,t)=>({property:e,transform:t}),sizes:Vt("sizes",qt(nr.vh,nr.px)),sizesT:Vt("sizes",qt(nr.vh,nr.fraction)),shadows:Vt("shadows"),logical:function(e){const{property:t,scale:r,transform:n}=e;return{scale:r,property:Ut(t),transform:r?Ht({scale:r,compose:n}):n}},blur:Vt("blur",nr.blur)},ar={background:or.colors("background"),backgroundColor:or.colors("backgroundColor"),backgroundImage:or.gradients("backgroundImage"),backgroundSize:!0,backgroundPosition:!0,backgroundRepeat:!0,backgroundAttachment:!0,backgroundClip:{transform:nr.bgClip},bgSize:or.prop("backgroundSize"),bgPosition:or.prop("backgroundPosition"),bg:or.colors("background"),bgColor:or.colors("backgroundColor"),bgPos:or.prop("backgroundPosition"),bgRepeat:or.prop("backgroundRepeat"),bgAttachment:or.prop("backgroundAttachment"),bgGradient:or.gradients("backgroundImage"),bgClip:{transform:nr.bgClip}};Object.assign(ar,{bgImage:ar.backgroundImage,bgImg:ar.backgroundImage});var ir={border:or.borders("border"),borderWidth:or.borderWidths("borderWidth"),borderStyle:or.borderStyles("borderStyle"),borderColor:or.colors("borderColor"),borderRadius:or.radii("borderRadius"),borderTop:or.borders("borderTop"),borderBlockStart:or.borders("borderBlockStart"),borderTopLeftRadius:or.radii("borderTopLeftRadius"),borderStartStartRadius:or.logical({scale:"radii",property:{ltr:"borderTopLeftRadius",rtl:"borderTopRightRadius"}}),borderEndStartRadius:or.logical({scale:"radii",property:{ltr:"borderBottomLeftRadius",rtl:"borderBottomRightRadius"}}),borderTopRightRadius:or.radii("borderTopRightRadius"),borderStartEndRadius:or.logical({scale:"radii",property:{ltr:"borderTopRightRadius",rtl:"borderTopLeftRadius"}}),borderEndEndRadius:or.logical({scale:"radii",property:{ltr:"borderBottomRightRadius",rtl:"borderBottomLeftRadius"}}),borderRight:or.borders("borderRight"),borderInlineEnd:or.borders("borderInlineEnd"),borderBottom:or.borders("borderBottom"),borderBlockEnd:or.borders("borderBlockEnd"),borderBottomLeftRadius:or.radii("borderBottomLeftRadius"),borderBottomRightRadius:or.radii("borderBottomRightRadius"),borderLeft:or.borders("borderLeft"),borderInlineStart:{property:"borderInlineStart",scale:"borders"},borderInlineStartRadius:or.logical({scale:"radii",property:{ltr:["borderTopLeftRadius","borderBottomLeftRadius"],rtl:["borderTopRightRadius","borderBottomRightRadius"]}}),borderInlineEndRadius:or.logical({scale:"radii",property:{ltr:["borderTopRightRadius","borderBottomRightRadius"],rtl:["borderTopLeftRadius","borderBottomLeftRadius"]}}),borderX:or.borders(["borderLeft","borderRight"]),borderInline:or.borders("borderInline"),borderY:or.borders(["borderTop","borderBottom"]),borderBlock:or.borders("borderBlock"),borderTopWidth:or.borderWidths("borderTopWidth"),borderBlockStartWidth:or.borderWidths("borderBlockStartWidth"),borderTopColor:or.colors("borderTopColor"),borderBlockStartColor:or.colors("borderBlockStartColor"),borderTopStyle:or.borderStyles("borderTopStyle"),borderBlockStartStyle:or.borderStyles("borderBlockStartStyle"),borderBottomWidth:or.borderWidths("borderBottomWidth"),borderBlockEndWidth:or.borderWidths("borderBlockEndWidth"),borderBottomColor:or.colors("borderBottomColor"),borderBlockEndColor:or.colors("borderBlockEndColor"),borderBottomStyle:or.borderStyles("borderBottomStyle"),borderBlockEndStyle:or.borderStyles("borderBlockEndStyle"),borderLeftWidth:or.borderWidths("borderLeftWidth"),borderInlineStartWidth:or.borderWidths("borderInlineStartWidth"),borderLeftColor:or.colors("borderLeftColor"),borderInlineStartColor:or.colors("borderInlineStartColor"),borderLeftStyle:or.borderStyles("borderLeftStyle"),borderInlineStartStyle:or.borderStyles("borderInlineStartStyle"),borderRightWidth:or.borderWidths("borderRightWidth"),borderInlineEndWidth:or.borderWidths("borderInlineEndWidth"),borderRightColor:or.colors("borderRightColor"),borderInlineEndColor:or.colors("borderInlineEndColor"),borderRightStyle:or.borderStyles("borderRightStyle"),borderInlineEndStyle:or.borderStyles("borderInlineEndStyle"),borderTopRadius:or.radii(["borderTopLeftRadius","borderTopRightRadius"]),borderBottomRadius:or.radii(["borderBottomLeftRadius","borderBottomRightRadius"]),borderLeftRadius:or.radii(["borderTopLeftRadius","borderBottomLeftRadius"]),borderRightRadius:or.radii(["borderTopRightRadius","borderBottomRightRadius"])};Object.assign(ir,{rounded:ir.borderRadius,roundedTop:ir.borderTopRadius,roundedTopLeft:ir.borderTopLeftRadius,roundedTopRight:ir.borderTopRightRadius,roundedTopStart:ir.borderStartStartRadius,roundedTopEnd:ir.borderStartEndRadius,roundedBottom:ir.borderBottomRadius,roundedBottomLeft:ir.borderBottomLeftRadius,roundedBottomRight:ir.borderBottomRightRadius,roundedBottomStart:ir.borderEndStartRadius,roundedBottomEnd:ir.borderEndEndRadius,roundedLeft:ir.borderLeftRadius,roundedRight:ir.borderRightRadius,roundedStart:ir.borderInlineStartRadius,roundedEnd:ir.borderInlineEndRadius,borderStart:ir.borderInlineStart,borderEnd:ir.borderInlineEnd,borderTopStartRadius:ir.borderStartStartRadius,borderTopEndRadius:ir.borderStartEndRadius,borderBottomStartRadius:ir.borderEndStartRadius,borderBottomEndRadius:ir.borderEndEndRadius,borderStartRadius:ir.borderInlineStartRadius,borderEndRadius:ir.borderInlineEndRadius,borderStartWidth:ir.borderInlineStartWidth,borderEndWidth:ir.borderInlineEndWidth,borderStartColor:ir.borderInlineStartColor,borderEndColor:ir.borderInlineEndColor,borderStartStyle:ir.borderInlineStartStyle,borderEndStyle:ir.borderInlineEndStyle});var sr={color:or.colors("color"),textColor:or.colors("color"),fill:or.colors("fill"),stroke:or.colors("stroke")},lr={boxShadow:or.shadows("boxShadow"),mixBlendMode:!0,blendMode:or.prop("mixBlendMode"),backgroundBlendMode:!0,bgBlendMode:or.prop("backgroundBlendMode"),opacity:!0};Object.assign(lr,{shadow:lr.boxShadow});var cr={filter:{transform:nr.filter},blur:or.blur("--chakra-blur"),brightness:or.propT("--chakra-brightness",nr.brightness),contrast:or.propT("--chakra-contrast",nr.contrast),hueRotate:or.degreeT("--chakra-hue-rotate"),invert:or.propT("--chakra-invert",nr.invert),saturate:or.propT("--chakra-saturate",nr.saturate),dropShadow:or.propT("--chakra-drop-shadow",nr.dropShadow),backdropFilter:{transform:nr.backdropFilter},backdropBlur:or.blur("--chakra-backdrop-blur"),backdropBrightness:or.propT("--chakra-backdrop-brightness",nr.brightness),backdropContrast:or.propT("--chakra-backdrop-contrast",nr.contrast),backdropHueRotate:or.degreeT("--chakra-backdrop-hue-rotate"),backdropInvert:or.propT("--chakra-backdrop-invert",nr.invert),backdropSaturate:or.propT("--chakra-backdrop-saturate",nr.saturate)},ur={alignItems:!0,alignContent:!0,justifyItems:!0,justifyContent:!0,flexWrap:!0,flexDirection:{transform:nr.flexDirection},flex:!0,flexFlow:!0,flexGrow:!0,flexShrink:!0,flexBasis:or.sizes("flexBasis"),justifySelf:!0,alignSelf:!0,order:!0,placeItems:!0,placeContent:!0,placeSelf:!0,gap:or.space("gap"),rowGap:or.space("rowGap"),columnGap:or.space("columnGap")};Object.assign(ur,{flexDir:ur.flexDirection});var dr={gridGap:or.space("gridGap"),gridColumnGap:or.space("gridColumnGap"),gridRowGap:or.space("gridRowGap"),gridColumn:!0,gridRow:!0,gridAutoFlow:!0,gridAutoColumns:!0,gridColumnStart:!0,gridColumnEnd:!0,gridRowStart:!0,gridRowEnd:!0,gridAutoRows:!0,gridTemplate:!0,gridTemplateColumns:!0,gridTemplateRows:!0,gridTemplateAreas:!0,gridArea:!0},fr={appearance:!0,cursor:!0,resize:!0,userSelect:!0,pointerEvents:!0,outline:{transform:nr.outline},outlineOffset:!0,outlineColor:or.colors("outlineColor")},pr={width:or.sizesT("width"),inlineSize:or.sizesT("inlineSize"),height:or.sizes("height"),blockSize:or.sizes("blockSize"),boxSize:or.sizes(["width","height"]),minWidth:or.sizes("minWidth"),minInlineSize:or.sizes("minInlineSize"),minHeight:or.sizes("minHeight"),minBlockSize:or.sizes("minBlockSize"),maxWidth:or.sizes("maxWidth"),maxInlineSize:or.sizes("maxInlineSize"),maxHeight:or.sizes("maxHeight"),maxBlockSize:or.sizes("maxBlockSize"),overflow:!0,overflowX:!0,overflowY:!0,overscrollBehavior:!0,overscrollBehaviorX:!0,overscrollBehaviorY:!0,display:!0,aspectRatio:!0,hideFrom:{scale:"breakpoints",transform:(e,t)=>{var r,n,o;const a=`@media screen and (min-width: ${null!=(o=null==(n=null==(r=t.__breakpoints)?void 0:r.get(e))?void 0:n.minW)?o:e})`;return{[a]:{display:"none"}}}},hideBelow:{scale:"breakpoints",transform:(e,t)=>{var r,n,o;const a=`@media screen and (max-width: ${null!=(o=null==(n=null==(r=t.__breakpoints)?void 0:r.get(e))?void 0:n._minW)?o:e})`;return{[a]:{display:"none"}}}},verticalAlign:!0,boxSizing:!0,boxDecorationBreak:!0,float:or.propT("float",nr.float),objectFit:!0,objectPosition:!0,visibility:!0,isolation:!0};Object.assign(pr,{w:pr.width,h:pr.height,minW:pr.minWidth,maxW:pr.maxWidth,minH:pr.minHeight,maxH:pr.maxHeight,overscroll:pr.overscrollBehavior,overscrollX:pr.overscrollBehaviorX,overscrollY:pr.overscrollBehaviorY});var mr={listStyleType:!0,listStylePosition:!0,listStylePos:or.prop("listStylePosition"),listStyleImage:!0,listStyleImg:or.prop("listStyleImage")};var hr=(e=>{const t=new WeakMap;return(r,n,o,a)=>{if(void 0===r)return e(r,n,o);t.has(r)||t.set(r,new Map);const i=t.get(r);if(i.has(n))return i.get(n);const s=e(r,n,o,a);return i.set(n,s),s}})((function(e,t,r,n){const o="string"==typeof t?t.split("."):[t];for(n=0;n<o.length&&e;n+=1)e=e[o[n]];return void 0===e?r:e})),br={border:"0px",clip:"rect(0, 0, 0, 0)",width:"1px",height:"1px",margin:"-1px",padding:"0px",overflow:"hidden",whiteSpace:"nowrap",position:"absolute"},vr={position:"static",width:"auto",height:"auto",clip:"auto",padding:"0",margin:"0",overflow:"visible",whiteSpace:"normal"},gr=(e,t,r)=>{const n={},o=hr(e,t,{});for(const a in o){a in r&&null!=r[a]||(n[a]=o[a])}return n},yr={srOnly:{transform:e=>!0===e?br:"focusable"===e?vr:{}},layerStyle:{processResult:!0,transform:(e,t,r)=>gr(t,`layerStyles.${e}`,r)},textStyle:{processResult:!0,transform:(e,t,r)=>gr(t,`textStyles.${e}`,r)},apply:{processResult:!0,transform:(e,t,r)=>gr(t,e,r)}},xr={position:!0,pos:or.prop("position"),zIndex:or.prop("zIndex","zIndices"),inset:or.spaceT("inset"),insetX:or.spaceT(["left","right"]),insetInline:or.spaceT("insetInline"),insetY:or.spaceT(["top","bottom"]),insetBlock:or.spaceT("insetBlock"),top:or.spaceT("top"),insetBlockStart:or.spaceT("insetBlockStart"),bottom:or.spaceT("bottom"),insetBlockEnd:or.spaceT("insetBlockEnd"),left:or.spaceT("left"),insetInlineStart:or.logical({scale:"space",property:{ltr:"left",rtl:"right"}}),right:or.spaceT("right"),insetInlineEnd:or.logical({scale:"space",property:{ltr:"right",rtl:"left"}})};Object.assign(xr,{insetStart:xr.insetInlineStart,insetEnd:xr.insetInlineEnd});var wr={ring:{transform:nr.ring},ringColor:or.colors("--chakra-ring-color"),ringOffset:or.prop("--chakra-ring-offset-width"),ringOffsetColor:or.colors("--chakra-ring-offset-color"),ringInset:or.prop("--chakra-ring-inset")},kr={margin:or.spaceT("margin"),marginTop:or.spaceT("marginTop"),marginBlockStart:or.spaceT("marginBlockStart"),marginRight:or.spaceT("marginRight"),marginInlineEnd:or.spaceT("marginInlineEnd"),marginBottom:or.spaceT("marginBottom"),marginBlockEnd:or.spaceT("marginBlockEnd"),marginLeft:or.spaceT("marginLeft"),marginInlineStart:or.spaceT("marginInlineStart"),marginX:or.spaceT(["marginInlineStart","marginInlineEnd"]),marginInline:or.spaceT("marginInline"),marginY:or.spaceT(["marginTop","marginBottom"]),marginBlock:or.spaceT("marginBlock"),padding:or.space("padding"),paddingTop:or.space("paddingTop"),paddingBlockStart:or.space("paddingBlockStart"),paddingRight:or.space("paddingRight"),paddingBottom:or.space("paddingBottom"),paddingBlockEnd:or.space("paddingBlockEnd"),paddingLeft:or.space("paddingLeft"),paddingInlineStart:or.space("paddingInlineStart"),paddingInlineEnd:or.space("paddingInlineEnd"),paddingX:or.space(["paddingInlineStart","paddingInlineEnd"]),paddingInline:or.space("paddingInline"),paddingY:or.space(["paddingTop","paddingBottom"]),paddingBlock:or.space("paddingBlock")};Object.assign(kr,{m:kr.margin,mt:kr.marginTop,mr:kr.marginRight,me:kr.marginInlineEnd,marginEnd:kr.marginInlineEnd,mb:kr.marginBottom,ml:kr.marginLeft,ms:kr.marginInlineStart,marginStart:kr.marginInlineStart,mx:kr.marginX,my:kr.marginY,p:kr.padding,pt:kr.paddingTop,py:kr.paddingY,px:kr.paddingX,pb:kr.paddingBottom,pl:kr.paddingLeft,ps:kr.paddingInlineStart,paddingStart:kr.paddingInlineStart,pr:kr.paddingRight,pe:kr.paddingInlineEnd,paddingEnd:kr.paddingInlineEnd});var Sr={textDecorationColor:or.colors("textDecorationColor"),textDecoration:!0,textDecor:{property:"textDecoration"},textDecorationLine:!0,textDecorationStyle:!0,textDecorationThickness:!0,textUnderlineOffset:!0,textShadow:or.shadows("textShadow")},_r={clipPath:!0,transform:or.propT("transform",nr.transform),transformOrigin:!0,translateX:or.spaceT("--chakra-translate-x"),translateY:or.spaceT("--chakra-translate-y"),skewX:or.degreeT("--chakra-skew-x"),skewY:or.degreeT("--chakra-skew-y"),scaleX:or.prop("--chakra-scale-x"),scaleY:or.prop("--chakra-scale-y"),scale:or.prop(["--chakra-scale-x","--chakra-scale-y"]),rotate:or.degreeT("--chakra-rotate")},Cr={transition:!0,transitionDelay:!0,animation:!0,willChange:!0,transitionDuration:or.prop("transitionDuration","transition.duration"),transitionProperty:or.prop("transitionProperty","transition.property"),transitionTimingFunction:or.prop("transitionTimingFunction","transition.easing")},Er={fontFamily:or.prop("fontFamily","fonts"),fontSize:or.prop("fontSize","fontSizes",nr.px),fontWeight:or.prop("fontWeight","fontWeights"),lineHeight:or.prop("lineHeight","lineHeights"),letterSpacing:or.prop("letterSpacing","letterSpacings"),textAlign:!0,fontStyle:!0,textIndent:!0,wordBreak:!0,overflowWrap:!0,textOverflow:!0,textTransform:!0,whiteSpace:!0,isTruncated:{transform(e){if(!0===e)return{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}}},noOfLines:{static:{overflow:"hidden",textOverflow:"ellipsis",display:"-webkit-box",WebkitBoxOrient:"vertical",WebkitLineClamp:"var(--chakra-line-clamp)"},property:"--chakra-line-clamp"}},jr={scrollBehavior:!0,scrollSnapAlign:!0,scrollSnapStop:!0,scrollSnapType:!0,scrollMargin:or.spaceT("scrollMargin"),scrollMarginTop:or.spaceT("scrollMarginTop"),scrollMarginBottom:or.spaceT("scrollMarginBottom"),scrollMarginLeft:or.spaceT("scrollMarginLeft"),scrollMarginRight:or.spaceT("scrollMarginRight"),scrollMarginX:or.spaceT(["scrollMarginLeft","scrollMarginRight"]),scrollMarginY:or.spaceT(["scrollMarginTop","scrollMarginBottom"]),scrollPadding:or.spaceT("scrollPadding"),scrollPaddingTop:or.spaceT("scrollPaddingTop"),scrollPaddingBottom:or.spaceT("scrollPaddingBottom"),scrollPaddingLeft:or.spaceT("scrollPaddingLeft"),scrollPaddingRight:or.spaceT("scrollPaddingRight"),scrollPaddingX:or.spaceT(["scrollPaddingLeft","scrollPaddingRight"]),scrollPaddingY:or.spaceT(["scrollPaddingTop","scrollPaddingBottom"])};function Nr(e){return At(e)&&e.reference?e.reference:String(e)}var Or=(e,...t)=>t.map(Nr).join(` ${e} `).replace(/calc/g,""),Ar=(...e)=>`calc(${Or("+",...e)})`,zr=(...e)=>`calc(${Or("-",...e)})`,Ir=(...e)=>`calc(${Or("*",...e)})`,Pr=(...e)=>`calc(${Or("/",...e)})`,Rr=e=>{const t=Nr(e);return null==t||Number.isNaN(parseFloat(t))?Ir(t,-1):String(t).startsWith("-")?String(t).slice(1):`-${t}`},Tr=Object.assign((e=>({add:(...t)=>Tr(Ar(e,...t)),subtract:(...t)=>Tr(zr(e,...t)),multiply:(...t)=>Tr(Ir(e,...t)),divide:(...t)=>Tr(Pr(e,...t)),negate:()=>Tr(Rr(e)),toString:()=>e.toString()})),{add:Ar,subtract:zr,multiply:Ir,divide:Pr,negate:Rr});function Mr(e){return function(e){return e.replace(/[!-,/:-@[-^`{-~]/g,"\\$&")}(function(e){if(e.includes("\\."))return e;const t=!Number.isInteger(parseFloat(e.toString()));return t?e.replace(".","\\."):e}(function(e,t="-"){return e.replace(/\s+/g,t)}(e.toString())))}function $r(e,t){return`var(${e}${t?`, ${t}`:""})`}function Dr(e,t=""){return Mr(`--${function(e,t=""){return[t,e].filter(Boolean).join("-")}(e,t)}`)}function Br(e,t,r){const n=Dr(e,r);return{variable:n,reference:$r(n,t)}}function Fr(e){const t=null==e?0:e.length;return t?e[t-1]:void 0}function Lr(e){if(null==e)return e;const{unitless:t}=function(e){const t=parseFloat(e.toString()),r=e.toString().replace(String(t),"");return{unitless:!r,value:t,unit:r}}(e);return t||"number"==typeof e?`${e}px`:e}var Wr=(e,t)=>parseInt(e[1],10)>parseInt(t[1],10)?1:-1,Hr=e=>Object.fromEntries(Object.entries(e).sort(Wr));function qr(e){const t=Hr(e);return Object.assign(Object.values(t),t)}function Vr(e){var t;if(!e)return e;e=null!=(t=Lr(e))?t:e;return"number"==typeof e?`${e+-.02}`:e.replace(/(\d+\.?\d*)/u,(e=>`${parseFloat(e)+-.02}`))}function Ur(e,t){const r=["@media screen"];return e&&r.push("and",`(min-width: ${Lr(e)})`),t&&r.push("and",`(max-width: ${Lr(t)})`),r.join(" ")}function Gr(e){var t;if(!e)return null;e.base=null!=(t=e.base)?t:"0px";const r=qr(e),n=Object.entries(e).sort(Wr).map((([e,t],r,n)=>{var o;let[,a]=null!=(o=n[r+1])?o:[];return a=parseFloat(a)>0?Vr(a):void 0,{_minW:Vr(t),breakpoint:e,minW:t,maxW:a,maxWQuery:Ur(null,a),minWQuery:Ur(t),minMaxQuery:Ur(t,a)}})),o=function(e){const t=Object.keys(Hr(e));return new Set(t)}(e),a=Array.from(o.values());return{keys:o,normalized:r,isResponsive(e){const t=Object.keys(e);return t.length>0&&t.every((e=>o.has(e)))},asObject:Hr(e),asArray:qr(e),details:n,get:e=>n.find((t=>t.breakpoint===e)),media:[null,...r.map((e=>Ur(e))).slice(1)],toArrayValue(e){if(!At(e))throw new Error("toArrayValue: value must be an object");const t=a.map((t=>{var r;return null!=(r=e[t])?r:null}));for(;null===Fr(t);)t.pop();return t},toObjectValue(e){if(!Array.isArray(e))throw new Error("toObjectValue: value must be an array");return e.reduce(((e,t,r)=>{const n=a[r];return null!=n&&null!=t&&(e[n]=t),e}),{})}}}var Xr=(e,t)=>`${e}:hover ${t}, ${e}[data-hover] ${t}`,Yr=(e,t)=>`${e}:focus ${t}, ${e}[data-focus] ${t}`,Kr=(e,t)=>`${e}:focus-visible ${t}`,Zr=(e,t)=>`${e}:focus-within ${t}`,Qr=(e,t)=>`${e}:active ${t}, ${e}[data-active] ${t}`,Jr=(e,t)=>`${e}:disabled ${t}, ${e}[data-disabled] ${t}`,en=(e,t)=>`${e}:invalid ${t}, ${e}[data-invalid] ${t}`,tn=(e,t)=>`${e}:checked ${t}, ${e}[data-checked] ${t}`,rn=(e,t)=>`${e}:placeholder-shown ${t}`,nn=e=>an((t=>e(t,"&")),"[role=group]","[data-group]",".group"),on=e=>an((t=>e(t,"~ &")),"[data-peer]",".peer"),an=(e,...t)=>t.map(e).join(", "),sn={_hover:"&:hover, &[data-hover]",_active:"&:active, &[data-active]",_focus:"&:focus, &[data-focus]",_highlighted:"&[data-highlighted]",_focusWithin:"&:focus-within",_focusVisible:"&:focus-visible, &[data-focus-visible]",_disabled:"&:disabled, &[disabled], &[aria-disabled=true], &[data-disabled]",_readOnly:"&[aria-readonly=true], &[readonly], &[data-readonly]",_before:"&::before",_after:"&::after",_empty:"&:empty",_expanded:"&[aria-expanded=true], &[data-expanded]",_checked:"&[aria-checked=true], &[data-checked]",_grabbed:"&[aria-grabbed=true], &[data-grabbed]",_pressed:"&[aria-pressed=true], &[data-pressed]",_invalid:"&[aria-invalid=true], &[data-invalid]",_valid:"&[data-valid], &[data-state=valid]",_loading:"&[data-loading], &[aria-busy=true]",_selected:"&[aria-selected=true], &[data-selected]",_hidden:"&[hidden], &[data-hidden]",_autofill:"&:-webkit-autofill",_even:"&:nth-of-type(even)",_odd:"&:nth-of-type(odd)",_first:"&:first-of-type",_firstLetter:"&::first-letter",_last:"&:last-of-type",_notFirst:"&:not(:first-of-type)",_notLast:"&:not(:last-of-type)",_visited:"&:visited",_activeLink:"&[aria-current=page]",_activeStep:"&[aria-current=step]",_indeterminate:"&:indeterminate, &[aria-checked=mixed], &[data-indeterminate]",_groupHover:nn(Xr),_peerHover:on(Xr),_groupFocus:nn(Yr),_peerFocus:on(Yr),_groupFocusVisible:nn(Kr),_peerFocusVisible:on(Kr),_groupActive:nn(Qr),_peerActive:on(Qr),_groupDisabled:nn(Jr),_peerDisabled:on(Jr),_groupInvalid:nn(en),_peerInvalid:on(en),_groupChecked:nn(tn),_peerChecked:on(tn),_groupFocusWithin:nn(Zr),_peerFocusWithin:on(Zr),_peerPlaceholderShown:on(rn),_placeholder:"&::placeholder",_placeholderShown:"&:placeholder-shown",_fullScreen:"&:fullscreen",_selection:"&::selection",_rtl:"[dir=rtl] &, &[dir=rtl]",_ltr:"[dir=ltr] &, &[dir=ltr]",_mediaDark:"@media (prefers-color-scheme: dark)",_mediaReduceMotion:"@media (prefers-reduced-motion: reduce)",_dark:".chakra-ui-dark &:not([data-theme]),[data-theme=dark] &:not([data-theme]),&[data-theme=dark]",_light:".chakra-ui-light &:not([data-theme]),[data-theme=light] &:not([data-theme]),&[data-theme=light]",_horizontal:"&[data-orientation=horizontal]",_vertical:"&[data-orientation=vertical]"},ln=Object.keys(sn);function cn(e,t){return Br(String(e).replace(/\./g,"-"),void 0,t)}function un(e,t,r={}){const{stop:n,getKey:o}=r;return function e(r,a=[]){var i;if(function(e){return"object"==typeof e&&null!=e&&!Array.isArray(e)}(r)||Array.isArray(r)){const s={};for(const[l,c]of Object.entries(r)){const u=null!=(i=null==o?void 0:o(l))?i:l,d=[...a,u];if(null==n?void 0:n(r,d))return t(r,a);s[u]=e(c,d)}return s}return t(r,a)}(e)}var dn=["colors","borders","borderWidths","borderStyles","fonts","fontSizes","fontWeights","gradients","letterSpacings","lineHeights","radii","space","shadows","sizes","zIndices","transition","blur","breakpoints"];var fn=e=>ln.includes(e)||"default"===e;function pn(e){var t;const r=function(e){const{__cssMap:t,__cssVars:r,__breakpoints:n,...o}=e;return o}(e),n=function(e){return function(e,t){const r={};for(const n of t)n in e&&(r[n]=e[n]);return r}(e,dn)}(r),o=function(e){return e.semanticTokens}(r),a=function({tokens:e,semanticTokens:t}){const r={};return un(e,((e,t)=>{null!=e&&(r[t.join(".")]={isSemantic:!1,value:e})})),un(t,((e,t)=>{null!=e&&(r[t.join(".")]={isSemantic:!0,value:e})}),{stop:e=>Object.keys(e).every(fn)}),r}({tokens:n,semanticTokens:o}),i=null==(t=r.config)?void 0:t.cssVarPrefix,{cssMap:s,cssVars:l}=function(e,t){let r={};const n={};for(const[o,a]of Object.entries(e)){const{isSemantic:i,value:s}=a,{variable:l,reference:c}=cn(o,null==t?void 0:t.cssVarPrefix);if(!i){if(o.startsWith("space")){const e=o.split("."),[t,...r]=e,a=`${t}.-${r.join(".")}`,i=Tr.negate(s),u=Tr.negate(c);n[a]={value:i,var:l,varRef:u}}r[l]=s,n[o]={value:s,var:l,varRef:c};continue}const u=r=>{const n=[String(o).split(".")[0],r].join(".");if(!e[n])return r;const{reference:a}=cn(n,null==t?void 0:t.cssVarPrefix);return a},d=At(s)?s:{default:s};r=Lt(r,Object.entries(d).reduce(((e,[t,r])=>{var n,o;if(!r)return e;const a=u(`${r}`);return"default"===t?(e[l]=a,e):(e[null!=(o=null==(n=sn)?void 0:n[t])?o:t]={[l]:a},e)}),{})),n[o]={value:c,var:l,varRef:c}}return{cssVars:r,cssMap:n}}(a,{cssVarPrefix:i});return Object.assign(r,{__cssVars:{"--chakra-ring-inset":"var(--chakra-empty,/*!*/ /*!*/)","--chakra-ring-offset-width":"0px","--chakra-ring-offset-color":"#fff","--chakra-ring-color":"rgba(66, 153, 225, 0.6)","--chakra-ring-offset-shadow":"0 0 #0000","--chakra-ring-shadow":"0 0 #0000","--chakra-space-x-reverse":"0","--chakra-space-y-reverse":"0",...l},__cssMap:s,__breakpoints:Gr(r.breakpoints)}),r}var mn=Lt({},ar,ir,sr,ur,pr,cr,wr,fr,dr,yr,xr,lr,kr,jr,Er,Sr,_r,mr,Cr),hn=Object.assign({},kr,pr,ur,dr,xr),bn=Object.keys(hn),vn=[...Object.keys(mn),...ln],gn={...mn,...sn};var yn=(e,t)=>e.startsWith("--")&&"string"==typeof t&&!function(e){return/^var\(--.+\)$/.test(e)}(t),xn=(e,t)=>{var r,n;if(null==t)return t;const o=t=>{var r,n;return null==(n=null==(r=e.__cssMap)?void 0:r[t])?void 0:n.varRef},a=e=>{var t;return null!=(t=o(e))?t:e},[i,s]=function(e){const t=[];let r="",n=!1;for(let o=0;o<e.length;o++){const a=e[o];"("===a?(n=!0,r+=a):")"===a?(n=!1,r+=a):","!==a||n?r+=a:(t.push(r),r="")}return r=r.trim(),r&&t.push(r),t}(t);return t=null!=(n=null!=(r=o(i))?r:a(s))?n:a(t)};function wn(e){const{configs:t={},pseudos:r={},theme:n}=e,o=(e,a=!1)=>{var i,s,l;const c=It(e,n),u=(e=>t=>{if(!t.__breakpoints)return e;const{isResponsive:r,toArrayValue:n,media:o}=t.__breakpoints,a={};for(const i in e){let s=It(e[i],t);if(null==s)continue;if(s=At(s)&&r(s)?n(s):s,!Array.isArray(s)){a[i]=s;continue}const l=s.slice(0,o.length).length;for(let e=0;e<l;e+=1){const t=null==o?void 0:o[e];t?(a[t]=a[t]||{},null!=s[e]&&(a[t][i]=s[e])):a[i]=s[e]}}return a})(c)(n);let d={};for(let f in u){let e=It(u[f],n);f in r&&(f=r[f]),yn(f,e)&&(e=xn(n,e));let p=t[f];if(!0===p&&(p={property:f}),At(e)){d[f]=null!=(i=d[f])?i:{},d[f]=Lt({},d[f],o(e,!0));continue}let m=null!=(l=null==(s=null==p?void 0:p.transform)?void 0:s.call(p,e,n,c))?l:e;m=(null==p?void 0:p.processResult)?o(m,!0):m;const h=It(null==p?void 0:p.property,n);if(!a&&(null==p?void 0:p.static)){const e=It(p.static,n);d=Lt({},d,e)}if(h&&Array.isArray(h))for(const t of h)d[t]=m;else h?"&"===h&&At(m)?d=Lt({},d,m):d[h]=m:At(m)?d=Lt({},d,m):d[f]=m}return d};return o}var kn=e=>t=>wn({theme:t,pseudos:sn,configs:mn})(e);function Sn(e){return{definePartsStyle:e=>e,defineMultiStyleConfig:t=>({parts:e,...t})}}function _n(e,t){for(let r=t+1;r<e.length;r++)if(null!=e[r])return r;return-1}function Cn(e){const t=e.__breakpoints;return function(e,r,n,o){var a,i;if(!t)return;const s={},l=function(e,t){return Array.isArray(e)?e:At(e)?t(e):null!=e?[e]:void 0}(n,t.toArrayValue);if(!l)return s;const c=l.length,u=1===c,d=!!e.parts;for(let f=0;f<c;f++){const n=t.details[f],c=t.details[_n(l,f)],p=Ur(n.minW,null==c?void 0:c._minW),m=It(null==(a=e[r])?void 0:a[l[f]],o);m&&(d?null==(i=e.parts)||i.forEach((e=>{Lt(s,{[e]:u?m[e]:{[p]:m[e]}})})):d?s[p]=m:u?Lt(s,m):s[p]=m)}return s}}function En(e){return function(e,t=[]){const r=Object.assign({},e);for(const n of t)n in r&&delete r[n];return r}(e,["styleConfig","size","variant","colorScheme"])}var jn=["borders","breakpoints","colors","components","config","direction","fonts","fontSizes","fontWeights","letterSpacings","lineHeights","radii","shadows","sizes","space","styles","transition","zIndices"];var Nn={property:{common:"background-color, border-color, color, fill, stroke, opacity, box-shadow, transform",colors:"background-color, border-color, color, fill, stroke",dimensions:"width, height",position:"left, right, top, bottom",background:"background-color, background-image, background-position"},easing:{"ease-in":"cubic-bezier(0.4, 0, 1, 1)","ease-out":"cubic-bezier(0, 0, 0.2, 1)","ease-in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},duration:{"ultra-fast":"50ms",faster:"100ms",fast:"150ms",normal:"200ms",slow:"300ms",slower:"400ms","ultra-slow":"500ms"}},On={hide:-1,auto:"auto",base:0,docked:10,dropdown:1e3,sticky:1100,banner:1200,overlay:1300,modal:1400,popover:1500,skipLink:1600,toast:1700,tooltip:1800},An={none:0,"1px":"1px solid","2px":"2px solid","4px":"4px solid","8px":"8px solid"},zn={base:"0em",sm:"30em",md:"48em",lg:"62em",xl:"80em","2xl":"96em"},In={transparent:"transparent",current:"currentColor",black:"#000000",white:"#FFFFFF",whiteAlpha:{50:"rgba(255, 255, 255, 0.04)",100:"rgba(255, 255, 255, 0.06)",200:"rgba(255, 255, 255, 0.08)",300:"rgba(255, 255, 255, 0.16)",400:"rgba(255, 255, 255, 0.24)",500:"rgba(255, 255, 255, 0.36)",600:"rgba(255, 255, 255, 0.48)",700:"rgba(255, 255, 255, 0.64)",800:"rgba(255, 255, 255, 0.80)",900:"rgba(255, 255, 255, 0.92)"},blackAlpha:{50:"rgba(0, 0, 0, 0.04)",100:"rgba(0, 0, 0, 0.06)",200:"rgba(0, 0, 0, 0.08)",300:"rgba(0, 0, 0, 0.16)",400:"rgba(0, 0, 0, 0.24)",500:"rgba(0, 0, 0, 0.36)",600:"rgba(0, 0, 0, 0.48)",700:"rgba(0, 0, 0, 0.64)",800:"rgba(0, 0, 0, 0.80)",900:"rgba(0, 0, 0, 0.92)"},gray:{50:"#F7FAFC",100:"#EDF2F7",200:"#E2E8F0",300:"#CBD5E0",400:"#A0AEC0",500:"#718096",600:"#4A5568",700:"#2D3748",800:"#1A202C",900:"#171923"},red:{50:"#FFF5F5",100:"#FED7D7",200:"#FEB2B2",300:"#FC8181",400:"#F56565",500:"#E53E3E",600:"#C53030",700:"#9B2C2C",800:"#822727",900:"#63171B"},orange:{50:"#FFFAF0",100:"#FEEBC8",200:"#FBD38D",300:"#F6AD55",400:"#ED8936",500:"#DD6B20",600:"#C05621",700:"#9C4221",800:"#7B341E",900:"#652B19"},yellow:{50:"#FFFFF0",100:"#FEFCBF",200:"#FAF089",300:"#F6E05E",400:"#ECC94B",500:"#D69E2E",600:"#B7791F",700:"#975A16",800:"#744210",900:"#5F370E"},green:{50:"#F0FFF4",100:"#C6F6D5",200:"#9AE6B4",300:"#68D391",400:"#48BB78",500:"#38A169",600:"#2F855A",700:"#276749",800:"#22543D",900:"#1C4532"},teal:{50:"#E6FFFA",100:"#B2F5EA",200:"#81E6D9",300:"#4FD1C5",400:"#38B2AC",500:"#319795",600:"#2C7A7B",700:"#285E61",800:"#234E52",900:"#1D4044"},blue:{50:"#ebf8ff",100:"#bee3f8",200:"#90cdf4",300:"#63b3ed",400:"#4299e1",500:"#3182ce",600:"#2b6cb0",700:"#2c5282",800:"#2a4365",900:"#1A365D"},cyan:{50:"#EDFDFD",100:"#C4F1F9",200:"#9DECF9",300:"#76E4F7",400:"#0BC5EA",500:"#00B5D8",600:"#00A3C4",700:"#0987A0",800:"#086F83",900:"#065666"},purple:{50:"#FAF5FF",100:"#E9D8FD",200:"#D6BCFA",300:"#B794F4",400:"#9F7AEA",500:"#805AD5",600:"#6B46C1",700:"#553C9A",800:"#44337A",900:"#322659"},pink:{50:"#FFF5F7",100:"#FED7E2",200:"#FBB6CE",300:"#F687B3",400:"#ED64A6",500:"#D53F8C",600:"#B83280",700:"#97266D",800:"#702459",900:"#521B41"},linkedin:{50:"#E8F4F9",100:"#CFEDFB",200:"#9BDAF3",300:"#68C7EC",400:"#34B3E4",500:"#00A0DC",600:"#008CC9",700:"#0077B5",800:"#005E93",900:"#004471"},facebook:{50:"#E8F4F9",100:"#D9DEE9",200:"#B7C2DA",300:"#6482C0",400:"#4267B2",500:"#385898",600:"#314E89",700:"#29487D",800:"#223B67",900:"#1E355B"},messenger:{50:"#D0E6FF",100:"#B9DAFF",200:"#A2CDFF",300:"#7AB8FF",400:"#2E90FF",500:"#0078FF",600:"#0063D1",700:"#0052AC",800:"#003C7E",900:"#002C5C"},whatsapp:{50:"#dffeec",100:"#b9f5d0",200:"#90edb3",300:"#65e495",400:"#3cdd78",500:"#22c35e",600:"#179848",700:"#0c6c33",800:"#01421c",900:"#001803"},twitter:{50:"#E5F4FD",100:"#C8E9FB",200:"#A8DCFA",300:"#83CDF7",400:"#57BBF5",500:"#1DA1F2",600:"#1A94DA",700:"#1681BF",800:"#136B9E",900:"#0D4D71"},telegram:{50:"#E3F2F9",100:"#C5E4F3",200:"#A2D4EC",300:"#7AC1E4",400:"#47A9DA",500:"#0088CC",600:"#007AB8",700:"#006BA1",800:"#005885",900:"#003F5E"}},Pn={none:"0",sm:"0.125rem",base:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},Rn={xs:"0 0 0 1px rgba(0, 0, 0, 0.05)",sm:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",base:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",md:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",lg:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",xl:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)","2xl":"0 25px 50px -12px rgba(0, 0, 0, 0.25)",outline:"0 0 0 3px rgba(66, 153, 225, 0.6)",inner:"inset 0 2px 4px 0 rgba(0,0,0,0.06)",none:"none","dark-lg":"rgba(0, 0, 0, 0.1) 0px 0px 0px 1px, rgba(0, 0, 0, 0.2) 0px 5px 10px, rgba(0, 0, 0, 0.4) 0px 15px 40px"},Tn={none:0,sm:"4px",base:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},Mn={letterSpacings:{tighter:"-0.05em",tight:"-0.025em",normal:"0",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeights:{normal:"normal",none:1,shorter:1.25,short:1.375,base:1.5,tall:1.625,taller:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},fontWeights:{hairline:100,thin:200,light:300,normal:400,medium:500,semibold:600,bold:700,extrabold:800,black:900},fonts:{heading:'-apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',body:'-apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',mono:'SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace'},fontSizes:{"3xs":"0.45rem","2xs":"0.625rem",xs:"0.75rem",sm:"0.875rem",md:"1rem",lg:"1.125rem",xl:"1.25rem","2xl":"1.5rem","3xl":"1.875rem","4xl":"2.25rem","5xl":"3rem","6xl":"3.75rem","7xl":"4.5rem","8xl":"6rem","9xl":"8rem"}},$n={px:"1px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},Dn={...$n,max:"max-content",min:"min-content",full:"100%","3xs":"14rem","2xs":"16rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem","8xl":"90rem",prose:"60ch",container:{sm:"640px",md:"768px",lg:"1024px",xl:"1280px"}},Bn={breakpoints:zn,zIndices:On,radii:Pn,blur:Tn,colors:In,...Mn,sizes:Dn,shadows:Rn,space:$n,borders:An,transition:Nn},{defineMultiStyleConfig:Fn,definePartsStyle:Ln}=Sn(["stepper","step","title","description","indicator","separator","icon","number"]),Wn=Br("stepper-indicator-size"),Hn=Br("stepper-icon-size"),qn=Br("stepper-title-font-size"),Vn=Br("stepper-description-font-size"),Un=Br("stepper-accent-color"),Gn=Fn({baseStyle:Ln((({colorScheme:e})=>({stepper:{display:"flex",justifyContent:"space-between",gap:"4","&[data-orientation=vertical]":{flexDirection:"column",alignItems:"flex-start"},"&[data-orientation=horizontal]":{flexDirection:"row",alignItems:"center"},[Un.variable]:`colors.${e}.500`,_dark:{[Un.variable]:`colors.${e}.200`}},title:{fontSize:qn.reference,fontWeight:"medium"},description:{fontSize:Vn.reference,color:"chakra-subtle-text"},number:{fontSize:qn.reference},step:{flexShrink:0,position:"relative",display:"flex",gap:"2","&[data-orientation=horizontal]":{alignItems:"center"},flex:"1","&:last-of-type:not([data-stretch])":{flex:"initial"}},icon:{flexShrink:0,width:Hn.reference,height:Hn.reference},indicator:{flexShrink:0,borderRadius:"full",width:Wn.reference,height:Wn.reference,display:"flex",justifyContent:"center",alignItems:"center","&[data-status=active]":{borderWidth:"2px",borderColor:Un.reference},"&[data-status=complete]":{bg:Un.reference,color:"chakra-inverse-text"},"&[data-status=incomplete]":{borderWidth:"2px"}},separator:{bg:"chakra-border-color",flex:"1","&[data-status=complete]":{bg:Un.reference},"&[data-orientation=horizontal]":{width:"100%",height:"2px",marginStart:"2"},"&[data-orientation=vertical]":{width:"2px",position:"absolute",height:"100%",maxHeight:`calc(100% - ${Wn.reference} - 8px)`,top:`calc(${Wn.reference} + 4px)`,insetStart:`calc(${Wn.reference} / 2 - 1px)`}}}))),sizes:{xs:Ln({stepper:{[Wn.variable]:"sizes.4",[Hn.variable]:"sizes.3",[qn.variable]:"fontSizes.xs",[Vn.variable]:"fontSizes.xs"}}),sm:Ln({stepper:{[Wn.variable]:"sizes.6",[Hn.variable]:"sizes.4",[qn.variable]:"fontSizes.sm",[Vn.variable]:"fontSizes.xs"}}),md:Ln({stepper:{[Wn.variable]:"sizes.8",[Hn.variable]:"sizes.5",[qn.variable]:"fontSizes.md",[Vn.variable]:"fontSizes.sm"}}),lg:Ln({stepper:{[Wn.variable]:"sizes.10",[Hn.variable]:"sizes.6",[qn.variable]:"fontSizes.lg",[Vn.variable]:"fontSizes.md"}})},defaultProps:{size:"md",colorScheme:"blue"}});function Xn(e,t={}){let r=!1;function n(t){const r=`chakra-${(["container","root"].includes(null!=t?t:"")?[e]:[e,t]).filter(Boolean).join("__")}`;return{className:r,selector:`.${r}`,toString:()=>t}}return{parts:function(...o){!function(){if(r)throw new Error("[anatomy] .part(...) should only be called once. Did you mean to use .extend(...) ?");r=!0}();for(const e of o)t[e]=n(e);return Xn(e,t)},toPart:n,extend:function(...r){for(const e of r)e in t||(t[e]=n(e));return Xn(e,t)},selectors:function(){const e=Object.fromEntries(Object.entries(t).map((([e,t])=>[e,t.selector])));return e},classnames:function(){const e=Object.fromEntries(Object.entries(t).map((([e,t])=>[e,t.className])));return e},get keys(){return Object.keys(t)},__type:{}}}var Yn=Xn("accordion").parts("root","container","button","panel").extend("icon"),Kn=Xn("alert").parts("title","description","container").extend("icon","spinner"),Zn=Xn("avatar").parts("label","badge","container").extend("excessLabel","group"),Qn=Xn("breadcrumb").parts("link","item","container").extend("separator");Xn("button").parts();var Jn=Xn("checkbox").parts("control","icon","container").extend("label");Xn("progress").parts("track","filledTrack").extend("label");var eo=Xn("drawer").parts("overlay","dialogContainer","dialog").extend("header","closeButton","body","footer"),to=Xn("editable").parts("preview","input","textarea"),ro=Xn("form").parts("container","requiredIndicator","helperText"),no=Xn("formError").parts("text","icon"),oo=Xn("input").parts("addon","field","element","group"),ao=Xn("list").parts("container","item","icon"),io=Xn("menu").parts("button","list","item").extend("groupTitle","icon","command","divider"),so=Xn("modal").parts("overlay","dialogContainer","dialog").extend("header","closeButton","body","footer"),lo=Xn("numberinput").parts("root","field","stepperGroup","stepper");Xn("pininput").parts("field");var co=Xn("popover").parts("content","header","body","footer").extend("popper","arrow","closeButton"),uo=Xn("progress").parts("label","filledTrack","track"),fo=Xn("radio").parts("container","control","label"),po=Xn("select").parts("field","icon"),mo=Xn("slider").parts("container","track","thumb","filledTrack","mark"),ho=Xn("stat").parts("container","label","helpText","number","icon"),bo=Xn("switch").parts("container","track","thumb"),vo=Xn("table").parts("table","thead","tbody","tr","th","td","tfoot","caption"),go=Xn("tabs").parts("root","tab","tablist","tabpanel","tabpanels","indicator"),yo=Xn("tag").parts("container","label","closeButton"),xo=Xn("card").parts("container","header","body","footer");function wo(e,t,r){return Math.min(Math.max(e,r),t)}class ko extends Error{constructor(e){super(`Failed to parse color: "${e}"`)}}var So=ko;function _o(e){if("string"!=typeof e)throw new So(e);if("transparent"===e.trim().toLowerCase())return[0,0,0,0];let t=e.trim();t=Io.test(e)?function(e){const t=e.toLowerCase().trim(),r=Eo[function(e){let t=5381,r=e.length;for(;r;)t=33*t^e.charCodeAt(--r);return(t>>>0)%2341}(t)];if(!r)throw new So(e);return`#${r}`}(e):e;const r=No.exec(t);if(r){const e=Array.from(r).slice(1);return[...e.slice(0,3).map((e=>parseInt(jo(e,2),16))),parseInt(jo(e[3]||"f",2),16)/255]}const n=Oo.exec(t);if(n){const e=Array.from(n).slice(1);return[...e.slice(0,3).map((e=>parseInt(e,16))),parseInt(e[3]||"ff",16)/255]}const o=Ao.exec(t);if(o){const e=Array.from(o).slice(1);return[...e.slice(0,3).map((e=>parseInt(e,10))),parseFloat(e[3]||"1")]}const a=zo.exec(t);if(a){const[t,r,n,o]=Array.from(a).slice(1).map(parseFloat);if(wo(0,100,r)!==r)throw new So(e);if(wo(0,100,n)!==n)throw new So(e);return[...Ro(t,r,n),Number.isNaN(o)?1:o]}throw new So(e)}const Co=e=>parseInt(e.replace(/_/g,""),36),Eo="1q29ehhb 1n09sgk7 1kl1ekf_ _yl4zsno 16z9eiv3 1p29lhp8 _bd9zg04 17u0____ _iw9zhe5 _to73___ _r45e31e _7l6g016 _jh8ouiv _zn3qba8 1jy4zshs 11u87k0u 1ro9yvyo 1aj3xael 1gz9zjz0 _3w8l4xo 1bf1ekf_ _ke3v___ _4rrkb__ 13j776yz _646mbhl _nrjr4__ _le6mbhl 1n37ehkb _m75f91n _qj3bzfz 1939yygw 11i5z6x8 _1k5f8xs 1509441m 15t5lwgf _ae2th1n _tg1ugcv 1lp1ugcv 16e14up_ _h55rw7n _ny9yavn _7a11xb_ 1ih442g9 _pv442g9 1mv16xof 14e6y7tu 1oo9zkds 17d1cisi _4v9y70f _y98m8kc 1019pq0v 12o9zda8 _348j4f4 1et50i2o _8epa8__ _ts6senj 1o350i2o 1mi9eiuo 1259yrp0 1ln80gnw _632xcoy 1cn9zldc _f29edu4 1n490c8q _9f9ziet 1b94vk74 _m49zkct 1kz6s73a 1eu9dtog _q58s1rz 1dy9sjiq __u89jo3 _aj5nkwg _ld89jo3 13h9z6wx _qa9z2ii _l119xgq _bs5arju 1hj4nwk9 1qt4nwk9 1ge6wau6 14j9zlcw 11p1edc_ _ms1zcxe _439shk6 _jt9y70f _754zsow 1la40eju _oq5p___ _x279qkz 1fa5r3rv _yd2d9ip _424tcku _8y1di2_ _zi2uabw _yy7rn9h 12yz980_ __39ljp6 1b59zg0x _n39zfzp 1fy9zest _b33k___ _hp9wq92 1il50hz4 _io472ub _lj9z3eo 19z9ykg0 _8t8iu3a 12b9bl4a 1ak5yw0o _896v4ku _tb8k8lv _s59zi6t _c09ze0p 1lg80oqn 1id9z8wb _238nba5 1kq6wgdi _154zssg _tn3zk49 _da9y6tc 1sg7cv4f _r12jvtt 1gq5fmkz 1cs9rvci _lp9jn1c _xw1tdnb 13f9zje6 16f6973h _vo7ir40 _bt5arjf _rc45e4t _hr4e100 10v4e100 _hc9zke2 _w91egv_ _sj2r1kk 13c87yx8 _vqpds__ _ni8ggk8 _tj9yqfb 1ia2j4r4 _7x9b10u 1fc9ld4j 1eq9zldr _5j9lhpx _ez9zl6o _md61fzm".split(" ").reduce(((e,t)=>{const r=Co(t.substring(0,3)),n=Co(t.substring(3)).toString(16);let o="";for(let a=0;a<6-n.length;a++)o+="0";return e[r]=`${o}${n}`,e}),{});const jo=(e,t)=>Array.from(Array(t)).map((()=>e)).join(""),No=new RegExp(`^#${jo("([a-f0-9])",3)}([a-f0-9])?$`,"i"),Oo=new RegExp(`^#${jo("([a-f0-9]{2})",3)}([a-f0-9]{2})?$`,"i"),Ao=new RegExp(`^rgba?\\(\\s*(\\d+)\\s*${jo(",\\s*(\\d+)\\s*",2)}(?:,\\s*([\\d.]+))?\\s*\\)$`,"i"),zo=/^hsla?\(\s*([\d.]+)\s*,\s*([\d.]+)%\s*,\s*([\d.]+)%(?:\s*,\s*([\d.]+))?\s*\)$/i,Io=/^[a-z]+$/i,Po=e=>Math.round(255*e),Ro=(e,t,r)=>{let n=r/100;if(0===t)return[n,n,n].map(Po);const o=(e%360+360)%360/60,a=(1-Math.abs(2*n-1))*(t/100),i=a*(1-Math.abs(o%2-1));let s=0,l=0,c=0;o>=0&&o<1?(s=a,l=i):o>=1&&o<2?(s=i,l=a):o>=2&&o<3?(l=a,c=i):o>=3&&o<4?(l=i,c=a):o>=4&&o<5?(s=i,c=a):o>=5&&o<6&&(s=a,c=i);const u=n-a/2;return[s+u,l+u,c+u].map(Po)};function To(e,t){const[r,n,o,a]=_o(e);return i=n,s=o,l=a-t,`rgba(${wo(0,255,r).toFixed()}, ${wo(0,255,i).toFixed()}, ${wo(0,255,s).toFixed()}, ${parseFloat(wo(0,1,l).toFixed(3))})`;var i,s,l}var Mo=(e,t,r)=>{const n=function(e,t,r,n,o){for(t=t.split?t.split("."):t,n=0;n<t.length;n++)e=e?e[t[n]]:o;return e===o?r:e}(e,`colors.${t}`,t);try{return function(e){const[t,r,n,o]=_o(e);let a=e=>{const t=wo(0,255,e).toString(16);return 1===t.length?`0${t}`:t};a(t),a(r),a(n),o<1&&a(Math.round(255*o))}(n),n}catch{return null!=r?r:"#000000"}},$o=e=>t=>{const r=(e=>{const[t,r,n]=_o(e);return(299*t+587*r+114*n)/1e3})(Mo(t,e));return r<128?"dark":"light"},Do=(e,t)=>r=>To(Mo(r,e),1-t);function Bo(e="1rem",t="rgba(255, 255, 255, 0.15)"){return{backgroundImage:`linear-gradient(\n    45deg,\n    ${t} 25%,\n    transparent 25%,\n    transparent 50%,\n    ${t} 50%,\n    ${t} 75%,\n    transparent 75%,\n    transparent\n  )`,backgroundSize:`${e} ${e}`}}function Fo(e){const t=`#${Math.floor(16777215*Math.random()).toString(16).padEnd(6,"0")}`;return e&&(r=e,0!==Object.keys(r).length)?e.string&&e.colors?function(e,t){let r=0;if(0===e.length)return t[0];for(let n=0;n<e.length;n+=1)r=e.charCodeAt(n)+((r<<5)-r),r&=r;return r=(r%t.length+t.length)%t.length,t[r]}(e.string,e.colors):e.string&&!e.colors?function(e){let t=0;if(0===e.length)return t.toString();for(let n=0;n<e.length;n+=1)t=e.charCodeAt(n)+((t<<5)-t),t&=t;let r="#";for(let n=0;n<3;n+=1){r+=`00${(t>>8*n&255).toString(16)}`.substr(-2)}return r}(e.string):e.colors&&!e.string?(n=e.colors)[Math.floor(Math.random()*n.length)]:t:t;var r,n}function Lo(e,t){return r=>"dark"===r.colorMode?t:e}function Wo(e){const{orientation:t,vertical:r,horizontal:n}=e;return t?"vertical"===t?r:n:{}}function Ho(e){return At(e)&&e.reference?e.reference:String(e)}var qo=(e,...t)=>t.map(Ho).join(` ${e} `).replace(/calc/g,""),Vo=(...e)=>`calc(${qo("+",...e)})`,Uo=(...e)=>`calc(${qo("-",...e)})`,Go=(...e)=>`calc(${qo("*",...e)})`,Xo=(...e)=>`calc(${qo("/",...e)})`,Yo=e=>{const t=Ho(e);return null==t||Number.isNaN(parseFloat(t))?Go(t,-1):String(t).startsWith("-")?String(t).slice(1):`-${t}`},Ko=Object.assign((e=>({add:(...t)=>Ko(Vo(e,...t)),subtract:(...t)=>Ko(Uo(e,...t)),multiply:(...t)=>Ko(Go(e,...t)),divide:(...t)=>Ko(Xo(e,...t)),negate:()=>Ko(Yo(e)),toString:()=>e.toString()})),{add:Vo,subtract:Uo,multiply:Go,divide:Xo,negate:Yo});function Zo(e){const t=function(e,t="-"){return e.replace(/\s+/g,t)}(e.toString());return t.includes("\\.")?e:function(e){return!Number.isInteger(parseFloat(e.toString()))}(e)?t.replace(".","\\."):e}function Qo(e,t){return`var(${Zo(e)}${t?`, ${t}`:""})`}function Jo(e,t=""){return`--${function(e,t=""){return[t,Zo(e)].filter(Boolean).join("-")}(e,t)}`}function ea(e,t){const r=Jo(e,void 0);return{variable:r,reference:Qo(r,(n=void 0,null==n?void 0:n.reference))};var n}var{defineMultiStyleConfig:ta,definePartsStyle:ra}=Sn(bo.keys),na=ea("switch-track-width"),oa=ea("switch-track-height"),aa=ea("switch-track-diff"),ia=Ko.subtract(na,oa),sa=ea("switch-thumb-x"),la=ea("switch-bg"),ca=e=>{const{colorScheme:t}=e;return{borderRadius:"full",p:"0.5",width:[na.reference],height:[oa.reference],transitionProperty:"common",transitionDuration:"fast",[la.variable]:"colors.gray.300",_dark:{[la.variable]:"colors.whiteAlpha.400"},_focusVisible:{boxShadow:"outline"},_disabled:{opacity:.4,cursor:"not-allowed"},_checked:{[la.variable]:`colors.${t}.500`,_dark:{[la.variable]:`colors.${t}.200`}},bg:la.reference}},ua={bg:"white",transitionProperty:"transform",transitionDuration:"normal",borderRadius:"inherit",width:[oa.reference],height:[oa.reference],_checked:{transform:`translateX(${sa.reference})`}},da=ta({baseStyle:ra((e=>({container:{[aa.variable]:ia,[sa.variable]:aa.reference,_rtl:{[sa.variable]:Ko(aa).negate().toString()}},track:ca(e),thumb:ua}))),sizes:{sm:ra({container:{[na.variable]:"1.375rem",[oa.variable]:"sizes.3"}}),md:ra({container:{[na.variable]:"1.875rem",[oa.variable]:"sizes.4"}}),lg:ra({container:{[na.variable]:"2.875rem",[oa.variable]:"sizes.6"}})},defaultProps:{size:"md",colorScheme:"blue"}}),{defineMultiStyleConfig:fa,definePartsStyle:pa}=Sn(vo.keys),ma=pa({table:{fontVariantNumeric:"lining-nums tabular-nums",borderCollapse:"collapse",width:"full"},th:{fontFamily:"heading",fontWeight:"bold",textTransform:"uppercase",letterSpacing:"wider",textAlign:"start"},td:{textAlign:"start"},caption:{mt:4,fontFamily:"heading",textAlign:"center",fontWeight:"medium"}}),ha={"&[data-is-numeric=true]":{textAlign:"end"}},ba=fa({baseStyle:ma,variants:{simple:pa((e=>{const{colorScheme:t}=e;return{th:{color:Lo("gray.600","gray.400")(e),borderBottom:"1px",borderColor:Lo(`${t}.100`,`${t}.700`)(e),...ha},td:{borderBottom:"1px",borderColor:Lo(`${t}.100`,`${t}.700`)(e),...ha},caption:{color:Lo("gray.600","gray.100")(e)},tfoot:{tr:{"&:last-of-type":{th:{borderBottomWidth:0}}}}}})),striped:pa((e=>{const{colorScheme:t}=e;return{th:{color:Lo("gray.600","gray.400")(e),borderBottom:"1px",borderColor:Lo(`${t}.100`,`${t}.700`)(e),...ha},td:{borderBottom:"1px",borderColor:Lo(`${t}.100`,`${t}.700`)(e),...ha},caption:{color:Lo("gray.600","gray.100")(e)},tbody:{tr:{"&:nth-of-type(odd)":{"th, td":{borderBottomWidth:"1px",borderColor:Lo(`${t}.100`,`${t}.700`)(e)},td:{background:Lo(`${t}.100`,`${t}.700`)(e)}}}},tfoot:{tr:{"&:last-of-type":{th:{borderBottomWidth:0}}}}}})),unstyled:{}},sizes:{sm:pa({th:{px:"4",py:"1",lineHeight:"4",fontSize:"xs"},td:{px:"4",py:"2",fontSize:"sm",lineHeight:"4"},caption:{px:"4",py:"2",fontSize:"xs"}}),md:pa({th:{px:"6",py:"3",lineHeight:"4",fontSize:"xs"},td:{px:"6",py:"4",lineHeight:"5"},caption:{px:"6",py:"2",fontSize:"sm"}}),lg:pa({th:{px:"8",py:"4",lineHeight:"5",fontSize:"sm"},td:{px:"8",py:"5",lineHeight:"6"},caption:{px:"6",py:"2",fontSize:"md"}})},defaultProps:{variant:"simple",size:"md",colorScheme:"gray"}}),va=Br("tabs-color"),ga=Br("tabs-bg"),ya=Br("tabs-border-color"),{defineMultiStyleConfig:xa,definePartsStyle:wa}=Sn(go.keys),ka=e=>{const{orientation:t}=e;return{display:"vertical"===t?"flex":"block"}},Sa=e=>{const{isFitted:t}=e;return{flex:t?1:void 0,transitionProperty:"common",transitionDuration:"normal",_focusVisible:{zIndex:1,boxShadow:"outline"},_disabled:{cursor:"not-allowed",opacity:.4}}},_a=e=>{const{align:t="start",orientation:r}=e;return{justifyContent:{end:"flex-end",center:"center",start:"flex-start"}[t],flexDirection:"vertical"===r?"column":"row"}},Ca={p:4},Ea=wa((e=>({root:ka(e),tab:Sa(e),tablist:_a(e),tabpanel:Ca}))),ja={sm:wa({tab:{py:1,px:4,fontSize:"sm"}}),md:wa({tab:{fontSize:"md",py:2,px:4}}),lg:wa({tab:{fontSize:"lg",py:3,px:4}})},Na=wa((e=>{const{colorScheme:t,orientation:r}=e,n="vertical"===r,o=n?"borderStart":"borderBottom",a=n?"marginStart":"marginBottom";return{tablist:{[o]:"2px solid",borderColor:"inherit"},tab:{[o]:"2px solid",borderColor:"transparent",[a]:"-2px",_selected:{[va.variable]:`colors.${t}.600`,_dark:{[va.variable]:`colors.${t}.300`},borderColor:"currentColor"},_active:{[ga.variable]:"colors.gray.200",_dark:{[ga.variable]:"colors.whiteAlpha.300"}},_disabled:{_active:{bg:"none"}},color:va.reference,bg:ga.reference}}})),Oa=wa((e=>{const{colorScheme:t}=e;return{tab:{borderTopRadius:"md",border:"1px solid",borderColor:"transparent",mb:"-1px",[ya.variable]:"transparent",_selected:{[va.variable]:`colors.${t}.600`,[ya.variable]:"colors.white",_dark:{[va.variable]:`colors.${t}.300`,[ya.variable]:"colors.gray.800"},borderColor:"inherit",borderBottomColor:ya.reference},color:va.reference},tablist:{mb:"-1px",borderBottom:"1px solid",borderColor:"inherit"}}})),Aa=wa((e=>{const{colorScheme:t}=e;return{tab:{border:"1px solid",borderColor:"inherit",[ga.variable]:"colors.gray.50",_dark:{[ga.variable]:"colors.whiteAlpha.50"},mb:"-1px",_notLast:{marginEnd:"-1px"},_selected:{[ga.variable]:"colors.white",[va.variable]:`colors.${t}.600`,_dark:{[ga.variable]:"colors.gray.800",[va.variable]:`colors.${t}.300`},borderColor:"inherit",borderTopColor:"currentColor",borderBottomColor:"transparent"},color:va.reference,bg:ga.reference},tablist:{mb:"-1px",borderBottom:"1px solid",borderColor:"inherit"}}})),za=wa((e=>{const{colorScheme:t,theme:r}=e;return{tab:{borderRadius:"full",fontWeight:"semibold",color:"gray.600",_selected:{color:Mo(r,`${t}.700`),bg:Mo(r,`${t}.100`)}}}})),Ia=xa({baseStyle:Ea,sizes:ja,variants:{line:Na,enclosed:Oa,"enclosed-colored":Aa,"soft-rounded":za,"solid-rounded":wa((e=>{const{colorScheme:t}=e;return{tab:{borderRadius:"full",fontWeight:"semibold",[va.variable]:"colors.gray.600",_dark:{[va.variable]:"inherit"},_selected:{[va.variable]:"colors.white",[ga.variable]:`colors.${t}.600`,_dark:{[va.variable]:"colors.gray.800",[ga.variable]:`colors.${t}.300`}},color:va.reference,bg:ga.reference}}})),unstyled:wa({})},defaultProps:{size:"md",variant:"line",colorScheme:"blue"}}),Pa=function(e,t){const r={};for(const n of t)if(Array.isArray(n)){const[t,o]=n;r[t]=Br(`${e}-${t}`,o)}else r[n]=Br(`${e}-${n}`);return r}("badge",["bg","color","shadow"]),Ra=e=>{const{colorScheme:t,theme:r}=e,n=Do(`${t}.500`,.6)(r);return{[Pa.bg.variable]:`colors.${t}.500`,[Pa.color.variable]:"colors.white",_dark:{[Pa.bg.variable]:n,[Pa.color.variable]:"colors.whiteAlpha.800"}}},Ta=e=>{const{colorScheme:t,theme:r}=e,n=Do(`${t}.200`,.16)(r);return{[Pa.bg.variable]:`colors.${t}.100`,[Pa.color.variable]:`colors.${t}.800`,_dark:{[Pa.bg.variable]:n,[Pa.color.variable]:`colors.${t}.200`}}},Ma=e=>{const{colorScheme:t,theme:r}=e,n=Do(`${t}.200`,.8)(r);return{[Pa.color.variable]:`colors.${t}.500`,_dark:{[Pa.color.variable]:n},[Pa.shadow.variable]:`inset 0 0 0px 1px ${Pa.color.reference}`}},$a={baseStyle:{px:1,textTransform:"uppercase",fontSize:"xs",borderRadius:"sm",fontWeight:"bold",bg:Pa.bg.reference,color:Pa.color.reference,boxShadow:Pa.shadow.reference},variants:{solid:Ra,subtle:Ta,outline:Ma},defaultProps:{variant:"subtle",colorScheme:"gray"}},{defineMultiStyleConfig:Da,definePartsStyle:Ba}=Sn(yo.keys),Fa=Br("tag-bg"),La=Br("tag-color"),Wa=Br("tag-shadow"),Ha=Br("tag-min-height"),qa=Br("tag-min-width"),Va=Br("tag-font-size"),Ua=Br("tag-padding-inline"),Ga=Ba({container:{fontWeight:"medium",lineHeight:1.2,outline:0,[La.variable]:Pa.color.reference,[Fa.variable]:Pa.bg.reference,[Wa.variable]:Pa.shadow.reference,color:La.reference,bg:Fa.reference,boxShadow:Wa.reference,borderRadius:"md",minH:Ha.reference,minW:qa.reference,fontSize:Va.reference,px:Ua.reference,_focusVisible:{[Wa.variable]:"shadows.outline"}},label:{lineHeight:1.2,overflow:"visible"},closeButton:{fontSize:"lg",w:"5",h:"5",transitionProperty:"common",transitionDuration:"normal",borderRadius:"full",marginStart:"1.5",marginEnd:"-1",opacity:.5,_disabled:{opacity:.4},_focusVisible:{boxShadow:"outline",bg:"rgba(0, 0, 0, 0.14)"},_hover:{opacity:.8},_active:{opacity:1}}}),Xa={sm:Ba({container:{[Ha.variable]:"sizes.5",[qa.variable]:"sizes.5",[Va.variable]:"fontSizes.xs",[Ua.variable]:"space.2"},closeButton:{marginEnd:"-2px",marginStart:"0.35rem"}}),md:Ba({container:{[Ha.variable]:"sizes.6",[qa.variable]:"sizes.6",[Va.variable]:"fontSizes.sm",[Ua.variable]:"space.2"}}),lg:Ba({container:{[Ha.variable]:"sizes.8",[qa.variable]:"sizes.8",[Va.variable]:"fontSizes.md",[Ua.variable]:"space.3"}})},Ya=Da({variants:{subtle:Ba((e=>{var t;return{container:null==(t=$a.variants)?void 0:t.subtle(e)}})),solid:Ba((e=>{var t;return{container:null==(t=$a.variants)?void 0:t.solid(e)}})),outline:Ba((e=>{var t;return{container:null==(t=$a.variants)?void 0:t.outline(e)}}))},baseStyle:Ga,sizes:Xa,defaultProps:{size:"md",variant:"subtle",colorScheme:"gray"}}),{definePartsStyle:Ka,defineMultiStyleConfig:Za}=Sn(oo.keys),Qa=Br("input-height"),Ja=Br("input-font-size"),ei=Br("input-padding"),ti=Br("input-border-radius"),ri=Ka({addon:{height:Qa.reference,fontSize:Ja.reference,px:ei.reference,borderRadius:ti.reference},field:{width:"100%",height:Qa.reference,fontSize:Ja.reference,px:ei.reference,borderRadius:ti.reference,minWidth:0,outline:0,position:"relative",appearance:"none",transitionProperty:"common",transitionDuration:"normal",_disabled:{opacity:.4,cursor:"not-allowed"}}}),ni={lg:{[Ja.variable]:"fontSizes.lg",[ei.variable]:"space.4",[ti.variable]:"radii.md",[Qa.variable]:"sizes.12"},md:{[Ja.variable]:"fontSizes.md",[ei.variable]:"space.4",[ti.variable]:"radii.md",[Qa.variable]:"sizes.10"},sm:{[Ja.variable]:"fontSizes.sm",[ei.variable]:"space.3",[ti.variable]:"radii.sm",[Qa.variable]:"sizes.8"},xs:{[Ja.variable]:"fontSizes.xs",[ei.variable]:"space.2",[ti.variable]:"radii.sm",[Qa.variable]:"sizes.6"}},oi={lg:Ka({field:ni.lg,group:ni.lg}),md:Ka({field:ni.md,group:ni.md}),sm:Ka({field:ni.sm,group:ni.sm}),xs:Ka({field:ni.xs,group:ni.xs})};function ai(e){const{focusBorderColor:t,errorBorderColor:r}=e;return{focusBorderColor:t||Lo("blue.500","blue.300")(e),errorBorderColor:r||Lo("red.500","red.300")(e)}}var ii,si,li,ci,ui,di,fi,pi,mi,hi,bi,vi=Ka((e=>{const{theme:t}=e,{focusBorderColor:r,errorBorderColor:n}=ai(e);return{field:{border:"1px solid",borderColor:"inherit",bg:"inherit",_hover:{borderColor:Lo("gray.300","whiteAlpha.400")(e)},_readOnly:{boxShadow:"none !important",userSelect:"all"},_invalid:{borderColor:Mo(t,n),boxShadow:`0 0 0 1px ${Mo(t,n)}`},_focusVisible:{zIndex:1,borderColor:Mo(t,r),boxShadow:`0 0 0 1px ${Mo(t,r)}`}},addon:{border:"1px solid",borderColor:Lo("inherit","whiteAlpha.50")(e),bg:Lo("gray.100","whiteAlpha.300")(e)}}})),gi=Ka((e=>{const{theme:t}=e,{focusBorderColor:r,errorBorderColor:n}=ai(e);return{field:{border:"2px solid",borderColor:"transparent",bg:Lo("gray.100","whiteAlpha.50")(e),_hover:{bg:Lo("gray.200","whiteAlpha.100")(e)},_readOnly:{boxShadow:"none !important",userSelect:"all"},_invalid:{borderColor:Mo(t,n)},_focusVisible:{bg:"transparent",borderColor:Mo(t,r)}},addon:{border:"2px solid",borderColor:"transparent",bg:Lo("gray.100","whiteAlpha.50")(e)}}})),yi=Ka((e=>{const{theme:t}=e,{focusBorderColor:r,errorBorderColor:n}=ai(e);return{field:{borderBottom:"1px solid",borderColor:"inherit",borderRadius:"0",px:"0",bg:"transparent",_readOnly:{boxShadow:"none !important",userSelect:"all"},_invalid:{borderColor:Mo(t,n),boxShadow:`0px 1px 0px 0px ${Mo(t,n)}`},_focusVisible:{borderColor:Mo(t,r),boxShadow:`0px 1px 0px 0px ${Mo(t,r)}`}},addon:{borderBottom:"2px solid",borderColor:"inherit",borderRadius:"0",px:"0",bg:"transparent"}}})),xi=Za({baseStyle:ri,sizes:oi,variants:{outline:vi,filled:gi,flushed:yi,unstyled:Ka({field:{bg:"transparent",px:"0",height:"auto"},addon:{bg:"transparent",px:"0",height:"auto"}})},defaultProps:{size:"md",variant:"outline"}}),wi={...null==(ii=xi.baseStyle)?void 0:ii.field,paddingY:"2",minHeight:"20",lineHeight:"short",verticalAlign:"top"},ki={outline:e=>{var t,r;return null!=(r=null==(t=xi.variants)?void 0:t.outline(e).field)?r:{}},flushed:e=>{var t,r;return null!=(r=null==(t=xi.variants)?void 0:t.flushed(e).field)?r:{}},filled:e=>{var t,r;return null!=(r=null==(t=xi.variants)?void 0:t.filled(e).field)?r:{}},unstyled:null!=(li=null==(si=xi.variants)?void 0:si.unstyled.field)?li:{}},Si={baseStyle:wi,sizes:{xs:null!=(ui=null==(ci=xi.sizes)?void 0:ci.xs.field)?ui:{},sm:null!=(fi=null==(di=xi.sizes)?void 0:di.sm.field)?fi:{},md:null!=(mi=null==(pi=xi.sizes)?void 0:pi.md.field)?mi:{},lg:null!=(bi=null==(hi=xi.sizes)?void 0:hi.lg.field)?bi:{}},variants:ki,defaultProps:{size:"md",variant:"outline"}},_i=ea("tooltip-bg"),Ci=ea("tooltip-fg"),Ei=ea("popper-arrow-bg"),ji={baseStyle:{bg:_i.reference,color:Ci.reference,[_i.variable]:"colors.gray.700",[Ci.variable]:"colors.whiteAlpha.900",_dark:{[_i.variable]:"colors.gray.300",[Ci.variable]:"colors.gray.900"},[Ei.variable]:_i.reference,px:"2",py:"0.5",borderRadius:"sm",fontWeight:"medium",fontSize:"sm",boxShadow:"md",maxW:"xs",zIndex:"tooltip"}},{defineMultiStyleConfig:Ni,definePartsStyle:Oi}=Sn(uo.keys),Ai=e=>{const{colorScheme:t,theme:r,isIndeterminate:n,hasStripe:o}=e,a=Lo(Bo(),Bo("1rem","rgba(0,0,0,0.1)"))(e),i=Lo(`${t}.500`,`${t}.200`)(e),s=`linear-gradient(\n    to right,\n    transparent 0%,\n    ${Mo(r,i)} 50%,\n    transparent 100%\n  )`;return{...!n&&o&&a,...n?{bgImage:s}:{bgColor:i}}},zi={lineHeight:"1",fontSize:"0.25em",fontWeight:"bold",color:"white"},Ii=e=>({bg:Lo("gray.100","whiteAlpha.300")(e)}),Pi=e=>({transitionProperty:"common",transitionDuration:"slow",...Ai(e)}),Ri=Oi((e=>({label:zi,filledTrack:Pi(e),track:Ii(e)}))),Ti=Ni({sizes:{xs:Oi({track:{h:"1"}}),sm:Oi({track:{h:"2"}}),md:Oi({track:{h:"3"}}),lg:Oi({track:{h:"4"}})},baseStyle:Ri,defaultProps:{size:"md",colorScheme:"blue"}});function Mi(e,...t){return"function"==typeof e?e(...t):e}var $i,Di,Bi,Fi,Li,Wi,Hi,qi,Vi,{definePartsStyle:Ui,defineMultiStyleConfig:Gi}=Sn(Jn.keys),Xi=Br("checkbox-size"),Yi=e=>{const{colorScheme:t}=e;return{w:Xi.reference,h:Xi.reference,transitionProperty:"box-shadow",transitionDuration:"normal",border:"2px solid",borderRadius:"sm",borderColor:"inherit",color:"white",_checked:{bg:Lo(`${t}.500`,`${t}.200`)(e),borderColor:Lo(`${t}.500`,`${t}.200`)(e),color:Lo("white","gray.900")(e),_hover:{bg:Lo(`${t}.600`,`${t}.300`)(e),borderColor:Lo(`${t}.600`,`${t}.300`)(e)},_disabled:{borderColor:Lo("gray.200","transparent")(e),bg:Lo("gray.200","whiteAlpha.300")(e),color:Lo("gray.500","whiteAlpha.500")(e)}},_indeterminate:{bg:Lo(`${t}.500`,`${t}.200`)(e),borderColor:Lo(`${t}.500`,`${t}.200`)(e),color:Lo("white","gray.900")(e)},_disabled:{bg:Lo("gray.100","whiteAlpha.100")(e),borderColor:Lo("gray.100","transparent")(e)},_focusVisible:{boxShadow:"outline"},_invalid:{borderColor:Lo("red.500","red.300")(e)}}},Ki={_disabled:{cursor:"not-allowed"}},Zi={userSelect:"none",_disabled:{opacity:.4}},Qi={transitionProperty:"transform",transitionDuration:"normal"},Ji=Gi({baseStyle:Ui((e=>({icon:Qi,container:Ki,control:Mi(Yi,e),label:Zi}))),sizes:{sm:Ui({control:{[Xi.variable]:"sizes.3"},label:{fontSize:"sm"},icon:{fontSize:"3xs"}}),md:Ui({control:{[Xi.variable]:"sizes.4"},label:{fontSize:"md"},icon:{fontSize:"2xs"}}),lg:Ui({control:{[Xi.variable]:"sizes.5"},label:{fontSize:"lg"},icon:{fontSize:"2xs"}})},defaultProps:{size:"md",colorScheme:"blue"}}),{defineMultiStyleConfig:es,definePartsStyle:ts}=Sn(fo.keys),rs=e=>{var t;const r=null==(t=Mi(Ji.baseStyle,e))?void 0:t.control;return{...r,borderRadius:"full",_checked:{...null==r?void 0:r._checked,_before:{content:'""',display:"inline-block",pos:"relative",w:"50%",h:"50%",borderRadius:"50%",bg:"currentColor"}}}},ns=es({baseStyle:ts((e=>{var t,r,n,o;return{label:null==(r=(t=Ji).baseStyle)?void 0:r.call(t,e).label,container:null==(o=(n=Ji).baseStyle)?void 0:o.call(n,e).container,control:rs(e)}})),sizes:{md:ts({control:{w:"4",h:"4"},label:{fontSize:"md"}}),lg:ts({control:{w:"5",h:"5"},label:{fontSize:"lg"}}),sm:ts({control:{width:"3",height:"3"},label:{fontSize:"sm"}})},defaultProps:{size:"md",colorScheme:"blue"}}),{defineMultiStyleConfig:os,definePartsStyle:as}=Sn(po.keys),is=Br("select-bg"),ss={paddingInlineEnd:"8"},ls=os({baseStyle:as({field:{...null==($i=xi.baseStyle)?void 0:$i.field,appearance:"none",paddingBottom:"1px",lineHeight:"normal",bg:is.reference,[is.variable]:"colors.white",_dark:{[is.variable]:"colors.gray.700"},"> option, > optgroup":{bg:is.reference}},icon:{width:"6",height:"100%",insetEnd:"2",position:"relative",color:"currentColor",fontSize:"xl",_disabled:{opacity:.5}}}),sizes:{lg:{...null==(Di=xi.sizes)?void 0:Di.lg,field:{...null==(Bi=xi.sizes)?void 0:Bi.lg.field,...ss}},md:{...null==(Fi=xi.sizes)?void 0:Fi.md,field:{...null==(Li=xi.sizes)?void 0:Li.md.field,...ss}},sm:{...null==(Wi=xi.sizes)?void 0:Wi.sm,field:{...null==(Hi=xi.sizes)?void 0:Hi.sm.field,...ss}},xs:{...null==(qi=xi.sizes)?void 0:qi.xs,field:{...null==(Vi=xi.sizes)?void 0:Vi.xs.field,...ss},icon:{insetEnd:"1"}}},variants:xi.variants,defaultProps:xi.defaultProps}),cs=Br("skeleton-start-color"),us=Br("skeleton-end-color"),ds={baseStyle:{[cs.variable]:"colors.gray.100",[us.variable]:"colors.gray.400",_dark:{[cs.variable]:"colors.gray.800",[us.variable]:"colors.gray.600"},background:cs.reference,borderColor:us.reference,opacity:.7,borderRadius:"sm"}},fs=Br("skip-link-bg"),ps={baseStyle:{borderRadius:"md",fontWeight:"semibold",_focusVisible:{boxShadow:"outline",padding:"4",position:"fixed",top:"6",insetStart:"6",[fs.variable]:"colors.white",_dark:{[fs.variable]:"colors.gray.700"},bg:fs.reference}}},{defineMultiStyleConfig:ms,definePartsStyle:hs}=Sn(mo.keys),bs=Br("slider-thumb-size"),vs=Br("slider-track-size"),gs=Br("slider-bg"),ys=e=>{const{orientation:t}=e;return{display:"inline-block",position:"relative",cursor:"pointer",_disabled:{opacity:.6,cursor:"default",pointerEvents:"none"},...Wo({orientation:t,vertical:{h:"100%"},horizontal:{w:"100%"}})}},xs=e=>({...Wo({orientation:e.orientation,horizontal:{h:vs.reference},vertical:{w:vs.reference}}),overflow:"hidden",borderRadius:"sm",[gs.variable]:"colors.gray.200",_dark:{[gs.variable]:"colors.whiteAlpha.200"},_disabled:{[gs.variable]:"colors.gray.300",_dark:{[gs.variable]:"colors.whiteAlpha.300"}},bg:gs.reference}),ws=e=>{const{orientation:t}=e;return{...Wo({orientation:t,vertical:{left:"50%",transform:"translateX(-50%)",_active:{transform:"translateX(-50%) scale(1.15)"}},horizontal:{top:"50%",transform:"translateY(-50%)",_active:{transform:"translateY(-50%) scale(1.15)"}}}),w:bs.reference,h:bs.reference,display:"flex",alignItems:"center",justifyContent:"center",position:"absolute",outline:0,zIndex:1,borderRadius:"full",bg:"white",boxShadow:"base",border:"1px solid",borderColor:"transparent",transitionProperty:"transform",transitionDuration:"normal",_focusVisible:{boxShadow:"outline"},_disabled:{bg:"gray.300"}}},ks=e=>{const{colorScheme:t}=e;return{width:"inherit",height:"inherit",[gs.variable]:`colors.${t}.500`,_dark:{[gs.variable]:`colors.${t}.200`},bg:gs.reference}},Ss=ms({baseStyle:hs((e=>({container:ys(e),track:xs(e),thumb:ws(e),filledTrack:ks(e)}))),sizes:{lg:hs({container:{[bs.variable]:"sizes.4",[vs.variable]:"sizes.1"}}),md:hs({container:{[bs.variable]:"sizes.3.5",[vs.variable]:"sizes.1"}}),sm:hs({container:{[bs.variable]:"sizes.2.5",[vs.variable]:"sizes.0.5"}})},defaultProps:{size:"md",colorScheme:"blue"}}),_s=ea("spinner-size"),Cs={baseStyle:{width:[_s.reference],height:[_s.reference]},sizes:{xs:{[_s.variable]:"sizes.3"},sm:{[_s.variable]:"sizes.4"},md:{[_s.variable]:"sizes.6"},lg:{[_s.variable]:"sizes.8"},xl:{[_s.variable]:"sizes.12"}},defaultProps:{size:"md"}},{defineMultiStyleConfig:Es,definePartsStyle:js}=Sn(ho.keys),Ns=Es({baseStyle:js({container:{},label:{fontWeight:"medium"},helpText:{opacity:.8,marginBottom:"2"},number:{verticalAlign:"baseline",fontWeight:"semibold"},icon:{marginEnd:1,w:"3.5",h:"3.5",verticalAlign:"middle"}}),sizes:{md:js({label:{fontSize:"sm"},helpText:{fontSize:"sm"},number:{fontSize:"2xl"}})},defaultProps:{size:"md"}}),Os=Br("kbd-bg"),As={baseStyle:{[Os.variable]:"colors.gray.100",_dark:{[Os.variable]:"colors.whiteAlpha.100"},bg:Os.reference,borderRadius:"md",borderWidth:"1px",borderBottomWidth:"3px",fontSize:"0.8em",fontWeight:"bold",lineHeight:"normal",px:"0.4em",whiteSpace:"nowrap"}},zs={baseStyle:{transitionProperty:"common",transitionDuration:"fast",transitionTimingFunction:"ease-out",cursor:"pointer",textDecoration:"none",outline:"none",color:"inherit",_hover:{textDecoration:"underline"},_focusVisible:{boxShadow:"outline"}}},{defineMultiStyleConfig:Is,definePartsStyle:Ps}=Sn(ao.keys),Rs=Is({baseStyle:Ps({icon:{marginEnd:"2",display:"inline",verticalAlign:"text-bottom"}})}),{defineMultiStyleConfig:Ts,definePartsStyle:Ms}=Sn(io.keys),$s=Br("menu-bg"),Ds=Br("menu-shadow"),Bs=Ts({baseStyle:Ms({button:{transitionProperty:"common",transitionDuration:"normal"},list:{[$s.variable]:"#fff",[Ds.variable]:"shadows.sm",_dark:{[$s.variable]:"colors.gray.700",[Ds.variable]:"shadows.dark-lg"},color:"inherit",minW:"3xs",py:"2",zIndex:1,borderRadius:"md",borderWidth:"1px",bg:$s.reference,boxShadow:Ds.reference},item:{py:"1.5",px:"3",transitionProperty:"background",transitionDuration:"ultra-fast",transitionTimingFunction:"ease-in",_focus:{[$s.variable]:"colors.gray.100",_dark:{[$s.variable]:"colors.whiteAlpha.100"}},_active:{[$s.variable]:"colors.gray.200",_dark:{[$s.variable]:"colors.whiteAlpha.200"}},_expanded:{[$s.variable]:"colors.gray.100",_dark:{[$s.variable]:"colors.whiteAlpha.100"}},_disabled:{opacity:.4,cursor:"not-allowed"},bg:$s.reference},groupTitle:{mx:4,my:2,fontWeight:"semibold",fontSize:"sm"},icon:{display:"inline-flex",alignItems:"center",justifyContent:"center",flexShrink:0},command:{opacity:.6},divider:{border:0,borderBottom:"1px solid",borderColor:"inherit",my:"2",opacity:.6}})}),{defineMultiStyleConfig:Fs,definePartsStyle:Ls}=Sn(so.keys),Ws=Br("modal-bg"),Hs=Br("modal-shadow"),qs={bg:"blackAlpha.600",zIndex:"modal"},Vs=e=>{const{isCentered:t,scrollBehavior:r}=e;return{display:"flex",zIndex:"modal",justifyContent:"center",alignItems:t?"center":"flex-start",overflow:"inside"===r?"hidden":"auto",overscrollBehaviorY:"none"}},Us=e=>{const{isCentered:t,scrollBehavior:r}=e;return{borderRadius:"md",color:"inherit",my:t?"auto":"16",mx:t?"auto":void 0,zIndex:"modal",maxH:"inside"===r?"calc(100% - 7.5rem)":void 0,[Ws.variable]:"colors.white",[Hs.variable]:"shadows.lg",_dark:{[Ws.variable]:"colors.gray.700",[Hs.variable]:"shadows.dark-lg"},bg:Ws.reference,boxShadow:Hs.reference}},Gs={px:"6",py:"4",fontSize:"xl",fontWeight:"semibold"},Xs={position:"absolute",top:"2",insetEnd:"3"},Ys=e=>{const{scrollBehavior:t}=e;return{px:"6",py:"2",flex:"1",overflow:"inside"===t?"auto":void 0}},Ks={px:"6",py:"4"};function Zs(e){return Ls("full"===e?{dialog:{maxW:"100vw",minH:"$100vh",my:"0",borderRadius:"0"}}:{dialog:{maxW:e}})}var Qs=Fs({baseStyle:Ls((e=>({overlay:qs,dialogContainer:Mi(Vs,e),dialog:Mi(Us,e),header:Gs,closeButton:Xs,body:Mi(Ys,e),footer:Ks}))),sizes:{xs:Zs("xs"),sm:Zs("sm"),md:Zs("md"),lg:Zs("lg"),xl:Zs("xl"),"2xl":Zs("2xl"),"3xl":Zs("3xl"),"4xl":Zs("4xl"),"5xl":Zs("5xl"),"6xl":Zs("6xl"),full:Zs("full")},defaultProps:{size:"md"}}),{defineMultiStyleConfig:Js,definePartsStyle:el}=Sn(lo.keys),tl=ea("number-input-stepper-width"),rl=ea("number-input-input-padding"),nl=Ko(tl).add("0.5rem").toString(),ol=ea("number-input-bg"),al=ea("number-input-color"),il=ea("number-input-border-color"),sl={[tl.variable]:"sizes.6",[rl.variable]:nl},ll=e=>{var t,r;return null!=(r=null==(t=Mi(xi.baseStyle,e))?void 0:t.field)?r:{}},cl={width:tl.reference},ul={borderStart:"1px solid",borderStartColor:il.reference,color:al.reference,bg:ol.reference,[al.variable]:"colors.chakra-body-text",[il.variable]:"colors.chakra-border-color",_dark:{[al.variable]:"colors.whiteAlpha.800",[il.variable]:"colors.whiteAlpha.300"},_active:{[ol.variable]:"colors.gray.200",_dark:{[ol.variable]:"colors.whiteAlpha.300"}},_disabled:{opacity:.4,cursor:"not-allowed"}};function dl(e){var t,r,n;const o=null==(t=xi.sizes)?void 0:t[e],a={lg:"md",md:"md",sm:"sm",xs:"sm"},i=null!=(n=null==(r=o.field)?void 0:r.fontSize)?n:"md",s=Mn.fontSizes[i];return el({field:{...o.field,paddingInlineEnd:rl.reference,verticalAlign:"top"},stepper:{fontSize:Ko(s).multiply(.75).toString(),_first:{borderTopEndRadius:a[e]},_last:{borderBottomEndRadius:a[e],mt:"-1px",borderTopWidth:1}}})}var fl,pl,ml,hl=Js({baseStyle:el((e=>{var t;return{root:sl,field:null!=(t=Mi(ll,e))?t:{},stepperGroup:cl,stepper:ul}})),sizes:{xs:dl("xs"),sm:dl("sm"),md:dl("md"),lg:dl("lg")},variants:xi.variants,defaultProps:xi.defaultProps}),bl={baseStyle:{...null==(fl=xi.baseStyle)?void 0:fl.field,textAlign:"center"},sizes:{lg:{fontSize:"lg",w:12,h:12,borderRadius:"md"},md:{fontSize:"md",w:10,h:10,borderRadius:"md"},sm:{fontSize:"sm",w:8,h:8,borderRadius:"sm"},xs:{fontSize:"xs",w:6,h:6,borderRadius:"sm"}},variants:{outline:e=>{var t,r,n;return null!=(n=null==(r=Mi(null==(t=xi.variants)?void 0:t.outline,e))?void 0:r.field)?n:{}},flushed:e=>{var t,r,n;return null!=(n=null==(r=Mi(null==(t=xi.variants)?void 0:t.flushed,e))?void 0:r.field)?n:{}},filled:e=>{var t,r,n;return null!=(n=null==(r=Mi(null==(t=xi.variants)?void 0:t.filled,e))?void 0:r.field)?n:{}},unstyled:null!=(ml=null==(pl=xi.variants)?void 0:pl.unstyled.field)?ml:{}},defaultProps:xi.defaultProps},{defineMultiStyleConfig:vl,definePartsStyle:gl}=Sn(co.keys),yl=ea("popper-bg"),xl=ea("popper-arrow-bg"),wl=ea("popper-arrow-shadow-color"),kl=vl({baseStyle:gl({popper:{zIndex:10},content:{[yl.variable]:"colors.white",bg:yl.reference,[xl.variable]:yl.reference,[wl.variable]:"colors.gray.200",_dark:{[yl.variable]:"colors.gray.700",[wl.variable]:"colors.whiteAlpha.300"},width:"xs",border:"1px solid",borderColor:"inherit",borderRadius:"md",boxShadow:"sm",zIndex:"inherit",_focusVisible:{outline:0,boxShadow:"outline"}},header:{px:3,py:2,borderBottomWidth:"1px"},body:{px:3,py:2},footer:{px:3,py:2,borderTopWidth:"1px"},closeButton:{position:"absolute",borderRadius:"md",top:1,insetEnd:2,padding:2}})}),{definePartsStyle:Sl,defineMultiStyleConfig:_l}=Sn(eo.keys),Cl=Br("drawer-bg"),El=Br("drawer-box-shadow");function jl(e){return Sl("full"===e?{dialog:{maxW:"100vw",h:"100vh"}}:{dialog:{maxW:e}})}var Nl={bg:"blackAlpha.600",zIndex:"overlay"},Ol={display:"flex",zIndex:"modal",justifyContent:"center"},Al=e=>{const{isFullHeight:t}=e;return{...t&&{height:"100vh"},zIndex:"modal",maxH:"100vh",color:"inherit",[Cl.variable]:"colors.white",[El.variable]:"shadows.lg",_dark:{[Cl.variable]:"colors.gray.700",[El.variable]:"shadows.dark-lg"},bg:Cl.reference,boxShadow:El.reference}},zl={px:"6",py:"4",fontSize:"xl",fontWeight:"semibold"},Il={position:"absolute",top:"2",insetEnd:"3"},Pl={px:"6",py:"2",flex:"1",overflow:"auto"},Rl={px:"6",py:"4"},Tl=_l({baseStyle:Sl((e=>({overlay:Nl,dialogContainer:Ol,dialog:Mi(Al,e),header:zl,closeButton:Il,body:Pl,footer:Rl}))),sizes:{xs:jl("xs"),sm:jl("md"),md:jl("lg"),lg:jl("2xl"),xl:jl("4xl"),full:jl("full")},defaultProps:{size:"xs"}}),{definePartsStyle:Ml,defineMultiStyleConfig:$l}=Sn(to.keys),Dl=$l({baseStyle:Ml({preview:{borderRadius:"md",py:"1",transitionProperty:"common",transitionDuration:"normal"},input:{borderRadius:"md",py:"1",transitionProperty:"common",transitionDuration:"normal",width:"full",_focusVisible:{boxShadow:"outline"},_placeholder:{opacity:.6}},textarea:{borderRadius:"md",py:"1",transitionProperty:"common",transitionDuration:"normal",width:"full",_focusVisible:{boxShadow:"outline"},_placeholder:{opacity:.6}}})}),{definePartsStyle:Bl,defineMultiStyleConfig:Fl}=Sn(ro.keys),Ll=Br("form-control-color"),Wl=Fl({baseStyle:Bl({container:{width:"100%",position:"relative"},requiredIndicator:{marginStart:"1",[Ll.variable]:"colors.red.500",_dark:{[Ll.variable]:"colors.red.300"},color:Ll.reference},helperText:{mt:"2",[Ll.variable]:"colors.gray.600",_dark:{[Ll.variable]:"colors.whiteAlpha.600"},color:Ll.reference,lineHeight:"normal",fontSize:"sm"}})}),{definePartsStyle:Hl,defineMultiStyleConfig:ql}=Sn(no.keys),Vl=Br("form-error-color"),Ul=ql({baseStyle:Hl({text:{[Vl.variable]:"colors.red.500",_dark:{[Vl.variable]:"colors.red.300"},color:Vl.reference,mt:"2",fontSize:"sm",lineHeight:"normal"},icon:{marginEnd:"0.5em",[Vl.variable]:"colors.red.500",_dark:{[Vl.variable]:"colors.red.300"},color:Vl.reference}})}),Gl={baseStyle:{fontSize:"md",marginEnd:"3",mb:"2",fontWeight:"medium",transitionProperty:"common",transitionDuration:"normal",opacity:1,_disabled:{opacity:.4}}},Xl={baseStyle:{fontFamily:"heading",fontWeight:"bold"},sizes:{"4xl":{fontSize:["6xl",null,"7xl"],lineHeight:1},"3xl":{fontSize:["5xl",null,"6xl"],lineHeight:1},"2xl":{fontSize:["4xl",null,"5xl"],lineHeight:[1.2,null,1]},xl:{fontSize:["3xl",null,"4xl"],lineHeight:[1.33,null,1.2]},lg:{fontSize:["2xl",null,"3xl"],lineHeight:[1.33,null,1.2]},md:{fontSize:"xl",lineHeight:1.2},sm:{fontSize:"md",lineHeight:1.2},xs:{fontSize:"sm",lineHeight:1.2}},defaultProps:{size:"xl"}},{defineMultiStyleConfig:Yl,definePartsStyle:Kl}=Sn(Qn.keys),Zl=Br("breadcrumb-link-decor"),Ql=Yl({baseStyle:Kl({link:{transitionProperty:"common",transitionDuration:"fast",transitionTimingFunction:"ease-out",outline:"none",color:"inherit",textDecoration:Zl.reference,[Zl.variable]:"none","&:not([aria-current=page])":{cursor:"pointer",_hover:{[Zl.variable]:"underline"},_focusVisible:{boxShadow:"outline"}}}})}),Jl=e=>{const{colorScheme:t,theme:r}=e;if("gray"===t)return{color:Lo("gray.800","whiteAlpha.900")(e),_hover:{bg:Lo("gray.100","whiteAlpha.200")(e)},_active:{bg:Lo("gray.200","whiteAlpha.300")(e)}};const n=Do(`${t}.200`,.12)(r),o=Do(`${t}.200`,.24)(r);return{color:Lo(`${t}.600`,`${t}.200`)(e),bg:"transparent",_hover:{bg:Lo(`${t}.50`,n)(e)},_active:{bg:Lo(`${t}.100`,o)(e)}}},ec={yellow:{bg:"yellow.400",color:"black",hoverBg:"yellow.500",activeBg:"yellow.600"},cyan:{bg:"cyan.400",color:"black",hoverBg:"cyan.500",activeBg:"cyan.600"}},tc={baseStyle:{lineHeight:"1.2",borderRadius:"md",fontWeight:"semibold",transitionProperty:"common",transitionDuration:"normal",_focusVisible:{boxShadow:"outline"},_disabled:{opacity:.4,cursor:"not-allowed",boxShadow:"none"},_hover:{_disabled:{bg:"initial"}}},variants:{ghost:Jl,outline:e=>{const{colorScheme:t}=e,r=Lo("gray.200","whiteAlpha.300")(e);return{border:"1px solid",borderColor:"gray"===t?r:"currentColor",".chakra-button__group[data-attached][data-orientation=horizontal] > &:not(:last-of-type)":{marginEnd:"-1px"},".chakra-button__group[data-attached][data-orientation=vertical] > &:not(:last-of-type)":{marginBottom:"-1px"},...Mi(Jl,e)}},solid:e=>{var t;const{colorScheme:r}=e;if("gray"===r){const t=Lo("gray.100","whiteAlpha.200")(e);return{bg:t,color:Lo("gray.800","whiteAlpha.900")(e),_hover:{bg:Lo("gray.200","whiteAlpha.300")(e),_disabled:{bg:t}},_active:{bg:Lo("gray.300","whiteAlpha.400")(e)}}}const{bg:n=`${r}.500`,color:o="white",hoverBg:a=`${r}.600`,activeBg:i=`${r}.700`}=null!=(t=ec[r])?t:{},s=Lo(n,`${r}.200`)(e);return{bg:s,color:Lo(o,"gray.800")(e),_hover:{bg:Lo(a,`${r}.300`)(e),_disabled:{bg:s}},_active:{bg:Lo(i,`${r}.400`)(e)}}},link:e=>{const{colorScheme:t}=e;return{padding:0,height:"auto",lineHeight:"normal",verticalAlign:"baseline",color:Lo(`${t}.500`,`${t}.200`)(e),_hover:{textDecoration:"underline",_disabled:{textDecoration:"none"}},_active:{color:Lo(`${t}.700`,`${t}.500`)(e)}}},unstyled:{bg:"none",color:"inherit",display:"inline",lineHeight:"inherit",m:"0",p:"0"}},sizes:{lg:{h:"12",minW:"12",fontSize:"lg",px:"6"},md:{h:"10",minW:"10",fontSize:"md",px:"4"},sm:{h:"8",minW:"8",fontSize:"sm",px:"3"},xs:{h:"6",minW:"6",fontSize:"xs",px:"2"}},defaultProps:{variant:"solid",size:"md",colorScheme:"gray"}},{definePartsStyle:rc,defineMultiStyleConfig:nc}=Sn(xo.keys),oc=Br("card-bg"),ac=Br("card-padding"),ic=Br("card-shadow"),sc=Br("card-radius"),lc=Br("card-border-width","0"),cc=Br("card-border-color"),uc=rc({container:{[oc.variable]:"colors.chakra-body-bg",backgroundColor:oc.reference,boxShadow:ic.reference,borderRadius:sc.reference,color:"chakra-body-text",borderWidth:lc.reference,borderColor:cc.reference},body:{padding:ac.reference,flex:"1 1 0%"},header:{padding:ac.reference},footer:{padding:ac.reference}}),dc={sm:rc({container:{[sc.variable]:"radii.base",[ac.variable]:"space.3"}}),md:rc({container:{[sc.variable]:"radii.md",[ac.variable]:"space.5"}}),lg:rc({container:{[sc.variable]:"radii.xl",[ac.variable]:"space.7"}})},fc=nc({baseStyle:uc,variants:{elevated:rc({container:{[ic.variable]:"shadows.base",_dark:{[oc.variable]:"colors.gray.700"}}}),outline:rc({container:{[lc.variable]:"1px",[cc.variable]:"colors.chakra-border-color"}}),filled:rc({container:{[oc.variable]:"colors.chakra-subtle-bg"}}),unstyled:{body:{[ac.variable]:0},header:{[ac.variable]:0},footer:{[ac.variable]:0}}},sizes:dc,defaultProps:{variant:"elevated",size:"md"}}),pc=ea("close-button-size"),mc=ea("close-button-bg"),hc={baseStyle:{w:[pc.reference],h:[pc.reference],borderRadius:"md",transitionProperty:"common",transitionDuration:"normal",_disabled:{opacity:.4,cursor:"not-allowed",boxShadow:"none"},_hover:{[mc.variable]:"colors.blackAlpha.100",_dark:{[mc.variable]:"colors.whiteAlpha.100"}},_active:{[mc.variable]:"colors.blackAlpha.200",_dark:{[mc.variable]:"colors.whiteAlpha.200"}},_focusVisible:{boxShadow:"outline"},bg:mc.reference},sizes:{lg:{[pc.variable]:"sizes.10",fontSize:"md"},md:{[pc.variable]:"sizes.8",fontSize:"xs"},sm:{[pc.variable]:"sizes.6",fontSize:"2xs"}},defaultProps:{size:"md"}},{variants:bc,defaultProps:vc}=$a,gc={baseStyle:{fontFamily:"mono",fontSize:"sm",px:"0.2em",borderRadius:"sm",bg:Pa.bg.reference,color:Pa.color.reference,boxShadow:Pa.shadow.reference},variants:bc,defaultProps:vc},yc={baseStyle:{w:"100%",mx:"auto",maxW:"prose",px:"4"}},xc={baseStyle:{opacity:.6,borderColor:"inherit"},variants:{solid:{borderStyle:"solid"},dashed:{borderStyle:"dashed"}},defaultProps:{variant:"solid"}},{definePartsStyle:wc,defineMultiStyleConfig:kc}=Sn(Yn.keys),Sc=kc({baseStyle:wc({container:{borderTopWidth:"1px",borderColor:"inherit",_last:{borderBottomWidth:"1px"}},button:{transitionProperty:"common",transitionDuration:"normal",fontSize:"md",_focusVisible:{boxShadow:"outline"},_hover:{bg:"blackAlpha.50"},_disabled:{opacity:.4,cursor:"not-allowed"},px:"4",py:"2"},panel:{pt:"2",px:"4",pb:"5"},icon:{fontSize:"1.25em"}})}),{definePartsStyle:_c,defineMultiStyleConfig:Cc}=Sn(Kn.keys),Ec=Br("alert-fg"),jc=Br("alert-bg");function Nc(e){const{theme:t,colorScheme:r}=e;return{light:`colors.${r}.100`,dark:Do(`${r}.200`,.16)(t)}}var Oc=Cc({baseStyle:_c({container:{bg:jc.reference,px:"4",py:"3"},title:{fontWeight:"bold",lineHeight:"6",marginEnd:"2"},description:{lineHeight:"6"},icon:{color:Ec.reference,flexShrink:0,marginEnd:"3",w:"5",h:"6"},spinner:{color:Ec.reference,flexShrink:0,marginEnd:"3",w:"5",h:"5"}}),variants:{subtle:_c((e=>{const{colorScheme:t}=e,r=Nc(e);return{container:{[Ec.variable]:`colors.${t}.500`,[jc.variable]:r.light,_dark:{[Ec.variable]:`colors.${t}.200`,[jc.variable]:r.dark}}}})),"left-accent":_c((e=>{const{colorScheme:t}=e,r=Nc(e);return{container:{[Ec.variable]:`colors.${t}.500`,[jc.variable]:r.light,_dark:{[Ec.variable]:`colors.${t}.200`,[jc.variable]:r.dark},paddingStart:"3",borderStartWidth:"4px",borderStartColor:Ec.reference}}})),"top-accent":_c((e=>{const{colorScheme:t}=e,r=Nc(e);return{container:{[Ec.variable]:`colors.${t}.500`,[jc.variable]:r.light,_dark:{[Ec.variable]:`colors.${t}.200`,[jc.variable]:r.dark},pt:"2",borderTopWidth:"4px",borderTopColor:Ec.reference}}})),solid:_c((e=>{const{colorScheme:t}=e;return{container:{[Ec.variable]:"colors.white",[jc.variable]:`colors.${t}.500`,_dark:{[Ec.variable]:"colors.gray.900",[jc.variable]:`colors.${t}.200`},color:Ec.reference}}}))},defaultProps:{variant:"subtle",colorScheme:"blue"}}),{definePartsStyle:Ac,defineMultiStyleConfig:zc}=Sn(Zn.keys),Ic=Br("avatar-border-color"),Pc=Br("avatar-bg"),Rc=Br("avatar-font-size"),Tc=Br("avatar-size"),Mc={borderRadius:"full",border:"0.2em solid",borderColor:Ic.reference,[Ic.variable]:"white",_dark:{[Ic.variable]:"colors.gray.800"}},$c={bg:Pc.reference,fontSize:Rc.reference,width:Tc.reference,height:Tc.reference,lineHeight:"1",[Pc.variable]:"colors.gray.200",_dark:{[Pc.variable]:"colors.whiteAlpha.400"}},Dc=e=>{const{name:t,theme:r}=e,n=t?Fo({string:t}):"colors.gray.400",o=(e=>t=>"dark"===$o(e)(t))(n)(r);let a="white";return o||(a="gray.800"),{bg:Pc.reference,fontSize:Rc.reference,color:a,borderColor:Ic.reference,verticalAlign:"top",width:Tc.reference,height:Tc.reference,"&:not([data-loaded])":{[Pc.variable]:n},[Ic.variable]:"colors.white",_dark:{[Ic.variable]:"colors.gray.800"}}},Bc={fontSize:Rc.reference,lineHeight:"1"};function Fc(e){const t="100%"!==e?Dn[e]:void 0;return Ac({container:{[Tc.variable]:null!=t?t:e,[Rc.variable]:`calc(${null!=t?t:e} / 2.5)`},excessLabel:{[Tc.variable]:null!=t?t:e,[Rc.variable]:`calc(${null!=t?t:e} / 2.5)`}})}var Lc={semanticTokens:{colors:{"chakra-body-text":{_light:"gray.800",_dark:"whiteAlpha.900"},"chakra-body-bg":{_light:"white",_dark:"gray.800"},"chakra-border-color":{_light:"gray.200",_dark:"whiteAlpha.300"},"chakra-inverse-text":{_light:"white",_dark:"gray.800"},"chakra-subtle-bg":{_light:"gray.100",_dark:"gray.700"},"chakra-subtle-text":{_light:"gray.600",_dark:"gray.400"},"chakra-placeholder-color":{_light:"gray.500",_dark:"whiteAlpha.400"}}},direction:"ltr",...Bn,components:{Accordion:Sc,Alert:Oc,Avatar:zc({baseStyle:Ac((e=>({badge:Mi(Mc,e),excessLabel:Mi($c,e),container:Mi(Dc,e),label:Bc}))),sizes:{"2xs":Fc(4),xs:Fc(6),sm:Fc(8),md:Fc(12),lg:Fc(16),xl:Fc(24),"2xl":Fc(32),full:Fc("100%")},defaultProps:{size:"md"}}),Badge:$a,Breadcrumb:Ql,Button:tc,Checkbox:Ji,CloseButton:hc,Code:gc,Container:yc,Divider:xc,Drawer:Tl,Editable:Dl,Form:Wl,FormError:Ul,FormLabel:Gl,Heading:Xl,Input:xi,Kbd:As,Link:zs,List:Rs,Menu:Bs,Modal:Qs,NumberInput:hl,PinInput:bl,Popover:kl,Progress:Ti,Radio:ns,Select:ls,Skeleton:ds,SkipLink:ps,Slider:Ss,Spinner:Cs,Stat:Ns,Switch:da,Table:ba,Tabs:Ia,Tag:Ya,Textarea:Si,Tooltip:ji,Card:fc,Stepper:Gn},styles:{global:{body:{fontFamily:"body",color:"chakra-body-text",bg:"chakra-body-bg",transitionProperty:"background-color",transitionDuration:"normal",lineHeight:"base"},"*::placeholder":{color:"chakra-placeholder-color"},"*, *::before, &::after":{borderColor:"chakra-border-color"}}},config:{useSystemColorMode:!1,initialColorMode:"light",cssVarPrefix:"chakra"}};function Wc(e){return"function"==typeof e}var Hc,qc=(Hc=Lc,function(...e){let t=[...e],r=e[e.length-1];var n;return At(n=r)&&jn.every((e=>Object.prototype.hasOwnProperty.call(n,e)))&&t.length>1?t=t.slice(0,t.length-1):r=Hc,function(...e){return t=>e.reduce(((e,t)=>t(e)),t)}(...t.map((e=>t=>Wc(e)?e(t):function(...e){return Lt({},...e,Vc)}(t,e))))(r)});function Vc(e,t,r,n){if((Wc(e)||Wc(t))&&Object.prototype.hasOwnProperty.call(n,r))return(...r)=>{const n=Wc(e)?e(...r):e,o=Wc(t)?t(...r):t;return Lt({},n,o,Vc)}}function Uc(){return!("undefined"==typeof window||!window.document||!window.document.createElement)}var Gc=Uc();var Xc=(e=>{const t=new WeakMap;return(r,n,o,a)=>{if(void 0===r)return e(r,n,o);t.has(r)||t.set(r,new Map);const i=t.get(r);if(i.has(n))return i.get(n);const s=e(r,n,o,a);return i.set(n,s),s}})((function(e,t,r,n){const o="string"==typeof t?t.split("."):[t];for(n=0;n<o.length&&e;n+=1)e=e[o[n]];return void 0===e?r:e}));function Yc(e,t){const r={};return Object.keys(e).forEach((n=>{const o=e[n];t(o,n,e)&&(r[n]=o)})),r}var Kc=e=>Yc(e,(e=>null!=e));function Zc(e,...t){return"function"==typeof e?e(...t):e}function Qc(...e){return function(t){e.some((e=>(null==e||e(t),null==t?void 0:t.defaultPrevented)))}}const Jc=1/60*1e3,eu="undefined"!=typeof performance?()=>performance.now():()=>Date.now(),tu="undefined"!=typeof window?e=>window.requestAnimationFrame(e):e=>setTimeout((()=>e(eu())),Jc);let ru=!0,nu=!1,ou=!1;const au={delta:0,timestamp:0},iu=["read","update","preRender","render","postRender"],su=iu.reduce(((e,t)=>(e[t]=function(e){let t=[],r=[],n=0,o=!1,a=!1;const i=new WeakSet,s={schedule:(e,a=!1,s=!1)=>{const l=s&&o,c=l?t:r;return a&&i.add(e),-1===c.indexOf(e)&&(c.push(e),l&&o&&(n=t.length)),e},cancel:e=>{const t=r.indexOf(e);-1!==t&&r.splice(t,1),i.delete(e)},process:l=>{if(o)a=!0;else{if(o=!0,[t,r]=[r,t],r.length=0,n=t.length,n)for(let r=0;r<n;r++){const n=t[r];n(l),i.has(n)&&(s.schedule(n),e())}o=!1,a&&(a=!1,s.process(l))}}};return s}((()=>nu=!0)),e)),{}),lu=iu.reduce(((e,t)=>{const r=su[t];return e[t]=(e,t=!1,n=!1)=>(nu||fu(),r.schedule(e,t,n)),e}),{}),cu=iu.reduce(((e,t)=>(e[t]=su[t].cancel,e)),{});iu.reduce(((e,t)=>(e[t]=()=>su[t].process(au),e)),{});const uu=e=>su[e].process(au),du=e=>{nu=!1,au.delta=ru?Jc:Math.max(Math.min(e-au.timestamp,40),1),au.timestamp=e,ou=!0,iu.forEach(uu),ou=!1,nu&&(ru=!1,tu(du))},fu=()=>{nu=!0,ru=!0,ou||tu(du)},pu=()=>au;var mu,hu;const bu=r(function(){if(hu)return mu;hu=1;var e="undefined"!=typeof Element,t="function"==typeof Map,r="function"==typeof Set,n="function"==typeof ArrayBuffer&&!!ArrayBuffer.isView;function o(a,i){if(a===i)return!0;if(a&&i&&"object"==typeof a&&"object"==typeof i){if(a.constructor!==i.constructor)return!1;var s,l,c,u;if(Array.isArray(a)){if((s=a.length)!=i.length)return!1;for(l=s;0!==l--;)if(!o(a[l],i[l]))return!1;return!0}if(t&&a instanceof Map&&i instanceof Map){if(a.size!==i.size)return!1;for(u=a.entries();!(l=u.next()).done;)if(!i.has(l.value[0]))return!1;for(u=a.entries();!(l=u.next()).done;)if(!o(l.value[1],i.get(l.value[0])))return!1;return!0}if(r&&a instanceof Set&&i instanceof Set){if(a.size!==i.size)return!1;for(u=a.entries();!(l=u.next()).done;)if(!i.has(l.value[0]))return!1;return!0}if(n&&ArrayBuffer.isView(a)&&ArrayBuffer.isView(i)){if((s=a.length)!=i.length)return!1;for(l=s;0!==l--;)if(a[l]!==i[l])return!1;return!0}if(a.constructor===RegExp)return a.source===i.source&&a.flags===i.flags;if(a.valueOf!==Object.prototype.valueOf&&"function"==typeof a.valueOf&&"function"==typeof i.valueOf)return a.valueOf()===i.valueOf();if(a.toString!==Object.prototype.toString&&"function"==typeof a.toString&&"function"==typeof i.toString)return a.toString()===i.toString();if((s=(c=Object.keys(a)).length)!==Object.keys(i).length)return!1;for(l=s;0!==l--;)if(!Object.prototype.hasOwnProperty.call(i,c[l]))return!1;if(e&&a instanceof Element)return!1;for(l=s;0!==l--;)if(("_owner"!==c[l]&&"__v"!==c[l]&&"__o"!==c[l]||!a.$$typeof)&&!o(a[c[l]],i[c[l]]))return!1;return!0}return a!=a&&i!=i}return mu=function(e,t){try{return o(e,t)}catch(r){if((r.message||"").match(/stack|recursion/i))return!1;throw r}}}());function vu(e,t={}){var r;const{styleConfig:n,...a}=t,{theme:i,colorMode:s}=jt(),l=e?Xc(i,`components.${e}`):void 0,c=n||l,u=Lt({theme:i,colorMode:s},null!=(r=null==c?void 0:c.defaultProps)?r:{},Kc(function(e,t){const r={};return Object.keys(e).forEach((n=>{t.includes(n)||(r[n]=e[n])})),r}(a,["children"]))),d=o.useRef({});if(c){const e=(f=c,e=>{var t;const{variant:r,size:n,theme:o}=e,a=Cn(o);return Lt({},It(null!=(t=f.baseStyle)?t:{},e),a(f,"sizes",n,e),a(f,"variants",r,e))}),t=e(u);bu(d.current,t)||(d.current=t)}var f;return d.current}function gu(e,t={}){return vu(e,t)}function yu(e,t={}){return vu(e,t)}var xu=new Set([...vn,"textStyle","layerStyle","apply","noOfLines","focusBorderColor","errorBorderColor","as","__css","css","sx"]),wu=new Set(["htmlWidth","htmlHeight","htmlSize","htmlTranslate"]);function ku(e){return wu.has(e)||!xu.has(e)}function Su(e){const t=Object.assign({},e);for(let r in t)void 0===t[r]&&delete t[r];return t}var _u,Cu=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,Eu=fe((function(e){return Cu.test(e)||111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)<91})),ju=function(e){return"theme"!==e},Nu=function(e){return"string"==typeof e&&e.charCodeAt(0)>96?Eu:ju},Ou=function(e,t,r){var n;if(t){var o=t.shouldForwardProp;n=e.__emotion_forwardProp&&o?function(t){return e.__emotion_forwardProp(t)&&o(t)}:o}return"function"!=typeof n&&r&&(n=e.__emotion_forwardProp),n},Au=function(e){var t=e.cache,r=e.serialized,n=e.isStringTag;return Ae(t,r,n),qe((function(){return ze(t,r,n)})),null},zu=function e(t,r){var n,a,i=t.__emotion_real===t,s=i&&t.__emotion_base||t;void 0!==r&&(n=r.label,a=r.target);var l=Ou(t,r,i),c=l||Nu(s),u=!c("as");return function(){var d=arguments,f=i&&void 0!==t.__emotion_styles?t.__emotion_styles.slice(0):[];if(void 0!==n&&f.push("label:"+n+";"),null==d[0]||void 0===d[0].raw)f.push.apply(f,d);else{f.push(d[0][0]);for(var p=d.length,m=1;m<p;m++)f.push(d[m],d[0][m])}var h=Ge((function(e,t,r){var n,i,d,p,m=u&&e.as||s,h="",b=[],v=e;if(null==e.theme){for(var g in v={},e)v[g]=e[g];v.theme=o.useContext(Xe)}"string"==typeof e.className?(n=t.registered,i=b,d=e.className,p="",d.split(" ").forEach((function(e){void 0!==n[e]?i.push(n[e]+";"):e&&(p+=e+" ")})),h=p):null!=e.className&&(h=e.className+" ");var y=We(f.concat(b),t.registered,v);h+=t.key+"-"+y.name,void 0!==a&&(h+=" "+a);var x=u&&void 0===l?Nu(m):c,w={};for(var k in e)u&&"as"===k||x(k)&&(w[k]=e[k]);return w.className=h,w.ref=r,o.createElement(o.Fragment,null,o.createElement(Au,{cache:t,serialized:y,isStringTag:"string"==typeof m}),o.createElement(m,w))}));return h.displayName=void 0!==n?n:"Styled("+("string"==typeof s?s:s.displayName||s.name||"Component")+")",h.defaultProps=t.defaultProps,h.__emotion_real=h,h.__emotion_base=s,h.__emotion_styles=f,h.__emotion_forwardProp=l,Object.defineProperty(h,"toString",{value:function(){return"."+a}}),h.withComponent=function(t,n){return e(t,we({},r,n,{shouldForwardProp:Ou(h,n,!0)})).apply(void 0,f)},h}}.bind();["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"].forEach((function(e){zu[e]=zu(e)}));var Iu=null!=(_u=zu.default)?_u:zu,Pu=({baseStyle:e})=>t=>{const{theme:r,css:n,__css:o,sx:a,...i}=t,s=Yc(i,((e,t)=>(e=>e in gn)(t))),l=function(e,...t){if(null==e)throw new TypeError("Cannot convert undefined or null to object");const r={...e};for(const n of t)if(null!=n)for(const e in n)Object.prototype.hasOwnProperty.call(n,e)&&(e in r&&delete r[e],r[e]=n[e]);return r}({},o,Zc(e,t),Kc(s),a),c=kn(l)(t.theme);return n?[c,n]:c};function Ru(e,t){const{baseStyle:r,...n}=null!=t?t:{};n.shouldForwardProp||(n.shouldForwardProp=ku);const o=Pu({baseStyle:r}),a=Iu(e,n)(o);return i.forwardRef((function(e,t){const{colorMode:r,forced:n}=gt();return i.createElement(a,{ref:t,"data-theme":n?r:void 0,...e})}))}var Tu=function(){const e=new Map;return new Proxy(Ru,{apply:(e,t,r)=>Ru(...r),get:(t,r)=>(e.has(r)||e.set(r,Ru(r)),e.get(r))})}();function Mu(e){return o.forwardRef(e)}function $u(e={}){const{strict:t=!0,errorMessage:r="useContext: `context` is undefined. Seems you forgot to wrap component within the Provider",name:n}=e,a=o.createContext(void 0);return a.displayName=n,[a.Provider,function e(){var n;const i=o.useContext(a);if(!i&&t){const t=new Error(r);throw t.name="ContextError",null==(n=Error.captureStackTrace)||n.call(Error,t,e),t}return i},a]}function Du(e){const{cssVarsRoot:t,theme:r,children:n}=e,a=o.useMemo((()=>pn(r)),[r]);return b.jsxs(Ke,{theme:a,children:[b.jsx(Bu,{root:t}),n]})}function Bu({root:e=":host, :root"}){const t=[e,"[data-theme]"].join(",");return b.jsx(Ze,{styles:e=>({[t]:e.__cssVars})})}function Fu(){const{colorMode:e}=gt();return b.jsx(Ze,{styles:t=>{const r=Zc(Xc(t,"styles.global"),{theme:t,colorMode:e});if(!r)return;return kn(r)(t)}})}$u({name:"StylesContext",errorMessage:"useStyles: `styles` is undefined. Seems you forgot to wrap the components in `<StylesProvider />` "});var Lu=o.createContext({getDocument:()=>document,getWindow:()=>window});function Wu(e){const{children:t,environment:r,disabled:n}=e,a=o.useRef(null),i=o.useMemo((()=>r||{getDocument:()=>{var e,t;return null!=(t=null==(e=a.current)?void 0:e.ownerDocument)?t:document},getWindow:()=>{var e,t;return null!=(t=null==(e=a.current)?void 0:e.ownerDocument.defaultView)?t:window}}),[r]),s=!n||!r;return b.jsxs(Lu.Provider,{value:i,children:[t,s&&b.jsx("span",{id:"__chakra_env",hidden:!0,ref:a})]})}Lu.displayName="EnvironmentContext",Wu.displayName="EnvironmentProvider";var Hu=e=>{const{children:t,colorModeManager:r,portalZIndex:n,resetScope:o,resetCSS:a=!0,theme:i={},environment:s,cssVarsRoot:l,disableEnvironment:c,disableGlobalStyle:u}=e,d=b.jsx(Wu,{environment:s,disabled:c,children:t});return b.jsx(Du,{theme:i,cssVarsRoot:l,children:b.jsxs(Et,{colorModeManager:r,options:i.config,children:[a?b.jsx(nt,{scope:o}):b.jsx(rt,{}),!u&&b.jsx(Fu,{}),n?b.jsx(st,{zIndex:n,children:d}):d]})})},qu=(e,t)=>e.find((e=>e.id===t));function Vu(e,t){const r=Uu(e,t);return{position:r,index:r?e[r].findIndex((e=>e.id===t)):-1}}function Uu(e,t){for(const[r,n]of Object.entries(e))if(qu(n,t))return r}function Gu(e){return{position:"fixed",zIndex:"var(--toast-z-index, 5500)",pointerEvents:"none",display:"flex",flexDirection:"column",margin:"top"===e||"bottom"===e?"0 auto":void 0,top:e.includes("top")?"env(safe-area-inset-top, 0px)":void 0,bottom:e.includes("bottom")?"env(safe-area-inset-bottom, 0px)":void 0,right:e.includes("left")?void 0:"env(safe-area-inset-right, 0px)",left:e.includes("right")?void 0:"env(safe-area-inset-left, 0px)"}}function Xu(e,t=[]){const r=o.useRef(e);return o.useEffect((()=>{r.current=e})),o.useCallback(((...e)=>{var t;return null==(t=r.current)?void 0:t.call(r,...e)}),t)}function Yu(e,t){const r=o.useRef(!1),n=o.useRef(!1);o.useEffect((()=>{if(r.current&&n.current)return e();n.current=!0}),t),o.useEffect((()=>(r.current=!0,()=>{r.current=!1})),[])}var Ku={initial:e=>{const{position:t}=e,r=["top","bottom"].includes(t)?"y":"x";let n=["top-right","bottom-right"].includes(t)?1:-1;return"bottom"===t&&(n=1),{opacity:0,[r]:24*n}},animate:{opacity:1,y:0,x:0,scale:1,transition:{duration:.4,ease:[.4,0,.2,1]}},exit:{opacity:0,scale:.85,transition:{duration:.2,ease:[.4,0,1,1]}}},Zu=o.memo((e=>{const{id:t,message:r,onCloseComplete:n,onRequestRemove:a,requestClose:i=!1,position:c="bottom",duration:u=5e3,containerStyle:d,motionVariants:f=Ku,toastSpacing:p="0.5rem"}=e,[m,h]=o.useState(u),v=s();Yu((()=>{v||null==n||n()}),[v]),Yu((()=>{h(u)}),[u]);const g=()=>{v&&a()};o.useEffect((()=>{v&&i&&a()}),[v,i,a]),function(e,t){const r=Xu(e);o.useEffect((()=>{if(null==t)return;let e=null;return e=window.setTimeout((()=>{r()}),t),()=>{e&&window.clearTimeout(e)}}),[t,r])}(g,m);const y=o.useMemo((()=>({pointerEvents:"auto",maxWidth:560,minWidth:300,margin:p,...d})),[d,p]),x=o.useMemo((()=>function(e){let t="center";return e.includes("right")&&(t="flex-end"),e.includes("left")&&(t="flex-start"),{display:"flex",flexDirection:"column",alignItems:t}}(c)),[c]);return b.jsx(l.div,{layout:!0,className:"chakra-toast",variants:f,initial:"initial",animate:"animate",exit:"exit",onHoverStart:()=>h(null),onHoverEnd:()=>h(u),custom:{position:c},style:x,children:b.jsx(Tu.div,{role:"status","aria-atomic":"true",className:"chakra-toast__inner",__css:y,children:It(r,{id:t,onClose:g})})})}));function Qu(e,t){var r;const n=null!=e?e:"bottom",o={"top-start":{ltr:"top-left",rtl:"top-right"},"top-end":{ltr:"top-right",rtl:"top-left"},"bottom-start":{ltr:"bottom-left",rtl:"bottom-right"},"bottom-end":{ltr:"bottom-right",rtl:"bottom-left"}}[n];return null!=(r=null==o?void 0:o[t])?r:n}Zu.displayName="ToastComponent";var Ju={path:b.jsxs("g",{stroke:"currentColor",strokeWidth:"1.5",children:[b.jsx("path",{strokeLinecap:"round",fill:"none",d:"M9,9a3,3,0,1,1,4,2.829,1.5,1.5,0,0,0-1,1.415V14.25"}),b.jsx("path",{fill:"currentColor",strokeLinecap:"round",d:"M12,17.25a.375.375,0,1,0,.375.375A.375.375,0,0,0,12,17.25h0"}),b.jsx("circle",{fill:"none",strokeMiterlimit:"10",cx:"12",cy:"12",r:"11.25"})]}),viewBox:"0 0 24 24"},ed=Mu(((e,t)=>{const{as:r,viewBox:n,color:o="currentColor",focusable:a=!1,children:i,className:s,__css:l,...c}=e,u={ref:t,focusable:a,className:Ot("chakra-icon",s),__css:{w:"1em",h:"1em",display:"inline-block",lineHeight:"1em",flexShrink:0,color:o,...l,...gu("Icon",e)}},d=null!=n?n:Ju.viewBox;if(r&&"string"!=typeof r)return b.jsx(Tu.svg,{as:r,...u,...c});const f=null!=i?i:Ju.path;return b.jsx(Tu.svg,{verticalAlign:"middle",viewBox:d,...u,...c,children:f})}));function td(e){const{viewBox:t="0 0 24 24",d:r,displayName:n,defaultProps:a={}}=e,i=o.Children.toArray(e.path),s=Mu(((e,n)=>b.jsx(ed,{ref:n,viewBox:t,...a,...e,children:i.length?i:b.jsx("path",{fill:"currentColor",d:r})})));return s.displayName=n,s}function rd(e){return b.jsx(ed,{viewBox:"0 0 24 24",...e,children:b.jsx("path",{fill:"currentColor",d:"M11.983,0a12.206,12.206,0,0,0-8.51,3.653A11.8,11.8,0,0,0,0,12.207,11.779,11.779,0,0,0,11.8,24h.214A12.111,12.111,0,0,0,24,11.791h0A11.766,11.766,0,0,0,11.983,0ZM10.5,16.542a1.476,1.476,0,0,1,1.449-1.53h.027a1.527,1.527,0,0,1,1.523,1.47,1.475,1.475,0,0,1-1.449,1.53h-.027A1.529,1.529,0,0,1,10.5,16.542ZM11,12.5v-6a1,1,0,0,1,2,0v6a1,1,0,1,1-2,0Z"})})}ed.displayName="Icon";var nd=Je({"0%":{transform:"rotate(0deg)"},"100%":{transform:"rotate(360deg)"}}),od=Mu(((e,t)=>{const r=gu("Spinner",e),{label:n="Loading...",thickness:o="2px",speed:a="0.45s",emptyColor:i="transparent",className:s,...l}=En(e),c=Ot("chakra-spinner",s),u={display:"inline-block",borderColor:"currentColor",borderStyle:"solid",borderRadius:"99999px",borderWidth:o,borderBottomColor:i,borderLeftColor:i,animation:`${nd} ${a} linear infinite`,...r};return b.jsx(Tu.div,{ref:t,__css:u,className:c,...l,children:n&&b.jsx(Tu.span,{srOnly:!0,children:n})})}));od.displayName="Spinner";var[ad,id]=ot({name:"AlertContext",hookName:"useAlertContext",providerName:"<Alert />"}),[sd,ld]=ot({name:"AlertStylesContext",hookName:"useAlertStyles",providerName:"<Alert />"}),cd={info:{icon:function(e){return b.jsx(ed,{viewBox:"0 0 24 24",...e,children:b.jsx("path",{fill:"currentColor",d:"M12,0A12,12,0,1,0,24,12,12.013,12.013,0,0,0,12,0Zm.25,5a1.5,1.5,0,1,1-1.5,1.5A1.5,1.5,0,0,1,12.25,5ZM14.5,18.5h-4a1,1,0,0,1,0-2h.75a.25.25,0,0,0,.25-.25v-4.5a.25.25,0,0,0-.25-.25H10.5a1,1,0,0,1,0-2h1a2,2,0,0,1,2,2v4.75a.25.25,0,0,0,.25.25h.75a1,1,0,1,1,0,2Z"})})},colorScheme:"blue"},warning:{icon:rd,colorScheme:"orange"},success:{icon:function(e){return b.jsx(ed,{viewBox:"0 0 24 24",...e,children:b.jsx("path",{fill:"currentColor",d:"M12,0A12,12,0,1,0,24,12,12.014,12.014,0,0,0,12,0Zm6.927,8.2-6.845,9.289a1.011,1.011,0,0,1-1.43.188L5.764,13.769a1,1,0,1,1,1.25-1.562l4.076,3.261,6.227-8.451A1,1,0,1,1,18.927,8.2Z"})})},colorScheme:"green"},error:{icon:rd,colorScheme:"red"},loading:{icon:od,colorScheme:"blue"}};var ud=Mu((function(e,t){const r=ld(),{status:n}=id(),o={display:"inline",...r.description};return b.jsx(Tu.div,{ref:t,"data-status":n,...e,className:Ot("chakra-alert__desc",e.className),__css:o})}));function dd(e){const{status:t}=id(),r=function(e){return cd[e].icon}(t),n=ld(),o="loading"===t?n.spinner:n.icon;return b.jsx(Tu.span,{display:"inherit","data-status":t,...e,className:Ot("chakra-alert__icon",e.className),__css:o,children:e.children||b.jsx(r,{h:"100%",w:"100%"})})}ud.displayName="AlertDescription",dd.displayName="AlertIcon";var fd=Mu((function(e,t){const r=ld(),{status:n}=id();return b.jsx(Tu.div,{ref:t,"data-status":n,...e,className:Ot("chakra-alert__title",e.className),__css:r.title})}));fd.displayName="AlertTitle";var pd=Mu((function(e,t){var r;const{status:n="info",addRole:o=!0,...a}=En(e),i=null!=(r=e.colorScheme)?r:function(e){return cd[e].colorScheme}(n),s=yu("Alert",{...e,colorScheme:i}),l={width:"100%",display:"flex",alignItems:"center",position:"relative",overflow:"hidden",...s.container};return b.jsx(ad,{value:{status:n},children:b.jsx(sd,{value:s,children:b.jsx(Tu.div,{"data-status":n,role:o?"alert":void 0,ref:t,...a,className:Ot("chakra-alert",e.className),__css:l})})})}));function md(e){return b.jsx(ed,{focusable:"false","aria-hidden":!0,...e,children:b.jsx("path",{fill:"currentColor",d:"M.439,21.44a1.5,1.5,0,0,0,2.122,2.121L11.823,14.3a.25.25,0,0,1,.354,0l9.262,9.263a1.5,1.5,0,1,0,2.122-2.121L14.3,12.177a.25.25,0,0,1,0-.354l9.263-9.262A1.5,1.5,0,0,0,21.439.44L12.177,9.7a.25.25,0,0,1-.354,0L2.561.44A1.5,1.5,0,0,0,.439,2.561L9.7,11.823a.25.25,0,0,1,0,.354Z"})})}pd.displayName="Alert";var hd=Mu((function(e,t){const r=gu("CloseButton",e),{children:n,isDisabled:o,__css:a,...i}=En(e);return b.jsx(Tu.button,{type:"button","aria-label":"Close",ref:t,disabled:o,__css:{outline:0,display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,...r,...a},...i,children:n||b.jsx(md,{width:"1em",height:"1em"})})}));hd.displayName="CloseButton";var bd=function(e){let t=e;const r=new Set,n=e=>{t=e(t),r.forEach((e=>e()))};return{getState:()=>t,subscribe:t=>(r.add(t),()=>{n((()=>e)),r.delete(t)}),removeToast:(e,t)=>{n((r=>({...r,[t]:r[t].filter((t=>t.id!=e))})))},notify:(e,t)=>{const r=function(e,t={}){var r,n;vd+=1;const o=null!=(r=t.id)?r:vd,a=null!=(n=t.position)?n:"bottom";return{id:o,message:e,position:a,duration:t.duration,onCloseComplete:t.onCloseComplete,onRequestRemove:()=>bd.removeToast(String(o),a),status:t.status,requestClose:!1,containerStyle:t.containerStyle}}(e,t),{position:o,id:a}=r;return n((e=>{var t,n;const a=o.includes("top")?[r,...null!=(t=e[o])?t:[]]:[...null!=(n=e[o])?n:[],r];return{...e,[o]:a}})),a},update:(e,t)=>{e&&n((r=>{const n={...r},{position:o,index:a}=Vu(n,e);return o&&-1!==a&&(n[o][a]={...n[o][a],...t,message:yd(t)}),n}))},closeAll:({positions:e}={})=>{n((t=>(null!=e?e:["bottom","bottom-right","bottom-left","top","top-left","top-right"]).reduce(((e,r)=>(e[r]=t[r].map((e=>({...e,requestClose:!0}))),e)),{...t})))},close:e=>{n((t=>{const r=Uu(t,e);return r?{...t,[r]:t[r].map((t=>t.id==e?{...t,requestClose:!0}:t))}:t}))},isActive:e=>Boolean(Vu(bd.getState(),e).position)}}({top:[],"top-left":[],"top-right":[],"bottom-left":[],bottom:[],"bottom-right":[]});var vd=0;var gd=e=>{const{status:t,variant:r="solid",id:n,title:o,isClosable:a,onClose:i,description:s,colorScheme:l,icon:c}=e,u=n?{root:`toast-${n}`,title:`toast-${n}-title`,description:`toast-${n}-description`}:void 0;return b.jsxs(pd,{addRole:!1,status:t,variant:r,id:null==u?void 0:u.root,alignItems:"start",borderRadius:"md",boxShadow:"lg",paddingEnd:8,textAlign:"start",width:"auto",colorScheme:l,children:[b.jsx(dd,{children:c}),b.jsxs(Tu.div,{flex:"1",maxWidth:"100%",children:[o&&b.jsx(fd,{id:null==u?void 0:u.title,children:o}),s&&b.jsx(ud,{id:null==u?void 0:u.description,display:"block",children:s})]}),a&&b.jsx(hd,{size:"sm",onClick:i,position:"absolute",insetEnd:1,top:1})]})};function yd(e={}){const{render:t,toastComponent:r=gd}=e;return n=>"function"==typeof t?t({...n,...e}):b.jsx(r,{...n,...e})}var[xd,wd]=ot({name:"ToastOptionsContext",strict:!1}),kd=e=>{const t=o.useSyncExternalStore(bd.subscribe,bd.getState,bd.getState),{motionVariants:r,component:n=Zu,portalProps:a}=e,i=Object.keys(t).map((e=>{const o=t[e];return b.jsx("div",{role:"region","aria-live":"polite","aria-label":"Notifications",id:`chakra-toast-manager-${e}`,style:Gu(e),children:b.jsx(c,{initial:!1,children:o.map((e=>b.jsx(n,{motionVariants:r,...e},e.id)))})},e)}));return b.jsx(ht,{...a,children:i})};function Sd(e){const{theme:t}=jt(),r=wd();return o.useMemo((()=>function(e,t){const r=r=>{var n;return{...t,...r,position:Qu(null!=(n=null==r?void 0:r.position)?n:null==t?void 0:t.position,e)}},n=e=>{const t=r(e),n=yd(t);return bd.notify(n,t)};return n.update=(e,t)=>{bd.update(e,r(t))},n.promise=(e,t)=>{const r=n({...t.loading,status:"loading",duration:null});e.then((e=>n.update(r,{status:"success",duration:5e3,...It(t.success,e)}))).catch((e=>n.update(r,{status:"error",duration:5e3,...It(t.error,e)})))},n.closeAll=bd.closeAll,n.close=bd.close,n.isActive=bd.isActive,n}(t.direction,{...r,...e})),[e,t.direction,r])}var _d,Cd=(_d=Lc,function({children:e,theme:t=_d,toastOptions:r,...n}){return b.jsxs(Hu,{theme:t,...n,children:[b.jsx(xd,{value:null==r?void 0:r.defaultOptions,children:e}),b.jsx(kd,{...r})]})}),Ed=Object.defineProperty,jd=(e,t,r)=>(((e,t,r)=>{t in e?Ed(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r})(e,"symbol"!=typeof t?t+"":t,r),r);function Nd(e){return e.sort(((e,t)=>{const r=e.compareDocumentPosition(t);if(r&Node.DOCUMENT_POSITION_FOLLOWING||r&Node.DOCUMENT_POSITION_CONTAINED_BY)return-1;if(r&Node.DOCUMENT_POSITION_PRECEDING||r&Node.DOCUMENT_POSITION_CONTAINS)return 1;if(r&Node.DOCUMENT_POSITION_DISCONNECTED||r&Node.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC)throw Error("Cannot sort the given nodes.");return 0}))}function Od(e,t,r){let n=e+1;return r&&n>=t&&(n=0),n}function Ad(e,t,r){let n=e-1;return r&&n<0&&(n=t),n}var zd="undefined"!=typeof window?o.useLayoutEffect:o.useEffect,Id=class{constructor(){jd(this,"descendants",new Map),jd(this,"register",(e=>{var t;if(null!=e)return"object"==typeof(t=e)&&"nodeType"in t&&t.nodeType===Node.ELEMENT_NODE?this.registerNode(e):t=>{this.registerNode(t,e)}})),jd(this,"unregister",(e=>{this.descendants.delete(e);const t=Nd(Array.from(this.descendants.keys()));this.assignIndex(t)})),jd(this,"destroy",(()=>{this.descendants.clear()})),jd(this,"assignIndex",(e=>{this.descendants.forEach((t=>{const r=e.indexOf(t.node);t.index=r,t.node.dataset.index=t.index.toString()}))})),jd(this,"count",(()=>this.descendants.size)),jd(this,"enabledCount",(()=>this.enabledValues().length)),jd(this,"values",(()=>Array.from(this.descendants.values()).sort(((e,t)=>e.index-t.index)))),jd(this,"enabledValues",(()=>this.values().filter((e=>!e.disabled)))),jd(this,"item",(e=>{if(0!==this.count())return this.values()[e]})),jd(this,"enabledItem",(e=>{if(0!==this.enabledCount())return this.enabledValues()[e]})),jd(this,"first",(()=>this.item(0))),jd(this,"firstEnabled",(()=>this.enabledItem(0))),jd(this,"last",(()=>this.item(this.descendants.size-1))),jd(this,"lastEnabled",(()=>{const e=this.enabledValues().length-1;return this.enabledItem(e)})),jd(this,"indexOf",(e=>{var t,r;return e&&null!=(r=null==(t=this.descendants.get(e))?void 0:t.index)?r:-1})),jd(this,"enabledIndexOf",(e=>null==e?-1:this.enabledValues().findIndex((t=>t.node.isSameNode(e))))),jd(this,"next",((e,t=!0)=>{const r=Od(e,this.count(),t);return this.item(r)})),jd(this,"nextEnabled",((e,t=!0)=>{const r=this.item(e);if(!r)return;const n=Od(this.enabledIndexOf(r.node),this.enabledCount(),t);return this.enabledItem(n)})),jd(this,"prev",((e,t=!0)=>{const r=Ad(e,this.count()-1,t);return this.item(r)})),jd(this,"prevEnabled",((e,t=!0)=>{const r=this.item(e);if(!r)return;const n=Ad(this.enabledIndexOf(r.node),this.enabledCount()-1,t);return this.enabledItem(n)})),jd(this,"registerNode",((e,t)=>{if(!e||this.descendants.has(e))return;const r=Nd(Array.from(this.descendants.keys()).concat(e));(null==t?void 0:t.disabled)&&(t.disabled=!!t.disabled);const n={node:e,index:-1,...t};this.descendants.set(e,n),this.assignIndex(r)}))}};function Pd(...e){return t=>{e.forEach((e=>{!function(e,t){if(null!=e)if("function"!=typeof e)try{e.current=t}catch(r){throw new Error(`Cannot assign value '${t}' to ref '${e}'`)}else e(t)}(e,t)}))}}function Rd(...e){return o.useMemo((()=>Pd(...e)),e)}var[Td,Md]=ot({name:"DescendantsProvider",errorMessage:"useDescendantsContext must be used within DescendantsProvider"});function $d(){return[Td,()=>Md(),()=>function(){const e=o.useRef(new Id);return zd((()=>()=>e.current.destroy())),e.current}(),e=>function(e){const t=Md(),[r,n]=o.useState(-1),a=o.useRef(null);zd((()=>()=>{a.current&&t.unregister(a.current)}),[]),zd((()=>{if(!a.current)return;const e=Number(a.current.dataset.index);r==e||Number.isNaN(e)||n(e)}));const i=e?t.register(e):t.register;return{descendants:t,index:r,enabledIndex:t.enabledIndexOf(a.current),register:Pd(i,a)}}(e)]}var[Dd,Bd]=ot({name:"AccordionStylesContext",hookName:"useAccordionStyles",providerName:"<Accordion />"}),[Fd,Ld]=ot({name:"AccordionItemContext",hookName:"useAccordionItemContext",providerName:"<AccordionItem />"}),[Wd,Hd,qd,Vd]=$d(),Ud=Mu((function(e,t){const{getButtonProps:r}=Ld(),n=r(e,t),o={display:"flex",alignItems:"center",width:"100%",outline:0,...Bd().button};return b.jsx(Tu.button,{...n,className:Ot("chakra-accordion__button",e.className),__css:o})}));function Gd(e){const{value:t,defaultValue:r,onChange:n,shouldUpdate:a=(e,t)=>e!==t}=e,i=Xu(n),s=Xu(a),[l,c]=o.useState(r),u=void 0!==t,d=u?t:l,f=Xu((e=>{const t="function"==typeof e?e(d):e;s(d,t)&&(u||c(t),i(t))}),[u,i,d,s]);return[d,f]}function Xd(e){const{onChange:t,defaultIndex:r,index:n,allowMultiple:a,allowToggle:i,...s}=e;!function(e){const t=e.index||e.defaultIndex,r=null!=t&&!Array.isArray(t)&&e.allowMultiple;zt({condition:!!r,message:`If 'allowMultiple' is passed, then 'index' or 'defaultIndex' must be an array. You passed: ${typeof t},`})}(e),function(e){zt({condition:!(!e.allowMultiple||!e.allowToggle),message:"If 'allowMultiple' is passed, 'allowToggle' will be ignored. Either remove 'allowToggle' or 'allowMultiple' depending on whether you want multiple accordions visible or not"})}(e);const l=qd(),[c,u]=o.useState(-1);o.useEffect((()=>()=>{u(-1)}),[]);const[d,f]=Gd({value:n,defaultValue:()=>a?null!=r?r:[]:null!=r?r:-1,onChange:t});return{index:d,setIndex:f,htmlProps:s,getAccordionItemProps:e=>{let t=!1;null!==e&&(t=Array.isArray(d)?d.includes(e):d===e);return{isOpen:t,onChange:t=>{if(null!==e)if(a&&Array.isArray(d)){const r=t?d.concat(e):d.filter((t=>t!==e));f(r)}else t?f(e):i&&f(-1)}}},focusedIndex:c,setFocusedIndex:u,descendants:l}}Ud.displayName="AccordionButton";var[Yd,Kd]=ot({name:"AccordionContext",hookName:"useAccordionContext",providerName:"Accordion"});function Zd(e){const{isDisabled:t,isFocusable:r,id:n,...a}=e,{getAccordionItemProps:i,setFocusedIndex:s}=Kd(),l=o.useRef(null),c=o.useId(),u=null!=n?n:c,d=`accordion-button-${u}`,f=`accordion-panel-${u}`;!function(e){zt({condition:!(!e.isFocusable||e.isDisabled),message:"Using only 'isFocusable', this prop is reserved for situations where you pass 'isDisabled' but you still want the element to receive focus (A11y). Either remove it or pass 'isDisabled' as well.\n    "})}(e);const{register:p,index:m,descendants:h}=Vd({disabled:t&&!r}),{isOpen:b,onChange:v}=i(-1===m?null:m);!function(e){zt({condition:e.isOpen&&!!e.isDisabled,message:"Cannot open a disabled accordion item"})}({isOpen:b,isDisabled:t});const g=o.useCallback((()=>{null==v||v(!b),s(m)}),[m,s,b,v]),y=o.useCallback((e=>{const t={ArrowDown:()=>{const e=h.nextEnabled(m);null==e||e.node.focus()},ArrowUp:()=>{const e=h.prevEnabled(m);null==e||e.node.focus()},Home:()=>{const e=h.firstEnabled();null==e||e.node.focus()},End:()=>{const e=h.lastEnabled();null==e||e.node.focus()}}[e.key];t&&(e.preventDefault(),t(e))}),[h,m]),x=o.useCallback((()=>{s(m)}),[s,m]),w=o.useCallback((function(e={},r=null){return{...e,type:"button",ref:Pd(p,l,r),id:d,disabled:!!t,"aria-expanded":!!b,"aria-controls":f,onClick:Mt(e.onClick,g),onFocus:Mt(e.onFocus,x),onKeyDown:Mt(e.onKeyDown,y)}}),[d,t,b,g,x,y,f,p]),k=o.useCallback((function(e={},t=null){return{...e,ref:t,role:"region",id:f,"aria-labelledby":d,hidden:!b}}),[d,b,f]);return{isOpen:b,isDisabled:t,isFocusable:r,onOpen:()=>{null==v||v(!0)},onClose:()=>{null==v||v(!1)},getButtonProps:w,getPanelProps:k,htmlProps:a}}function Qd(e){const{isOpen:t,isDisabled:r}=Ld(),{reduceMotion:n}=Kd(),o=Ot("chakra-accordion__icon",e.className),a={opacity:r?.4:1,transform:t?"rotate(-180deg)":void 0,transition:n?void 0:"transform 0.2s",transformOrigin:"center",...Bd().icon};return b.jsx(ed,{viewBox:"0 0 24 24","aria-hidden":!0,className:o,__css:a,...e,children:b.jsx("path",{fill:"currentColor",d:"M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"})})}Qd.displayName="AccordionIcon";var Jd=Mu((function(e,t){const{children:r,className:n}=e,{htmlProps:a,...i}=Zd(e),s={...Bd().container,overflowAnchor:"none"},l=o.useMemo((()=>i),[i]);return b.jsx(Fd,{value:l,children:b.jsx(Tu.div,{ref:t,...a,className:Ot("chakra-accordion__item",n),__css:s,children:"function"==typeof r?r({isExpanded:!!i.isOpen,isDisabled:!!i.isDisabled}):r})})}));Jd.displayName="AccordionItem";var ef={ease:[.25,.1,.25,1],easeIn:[.4,0,1,1],easeOut:[0,0,.2,1],easeInOut:[.4,0,.2,1]},tf={position:{left:0,top:0,bottom:0,width:"100%"},enter:{x:0,y:0},exit:{x:"-100%",y:0}},rf={position:{right:0,top:0,bottom:0,width:"100%"},enter:{x:0,y:0},exit:{x:"100%",y:0}},nf={position:{top:0,left:0,right:0,maxWidth:"100vw"},enter:{x:0,y:0},exit:{x:0,y:"-100%"}},of={position:{bottom:0,left:0,right:0,maxWidth:"100vw"},enter:{x:0,y:0},exit:{x:0,y:"100%"}};function af(e){var t;switch(null!=(t=null==e?void 0:e.direction)?t:"right"){case"right":default:return rf;case"left":return tf;case"bottom":return of;case"top":return nf}}var sf={enter:{duration:.2,ease:ef.easeOut},exit:{duration:.1,ease:ef.easeIn}},lf=(e,t)=>({...e,delay:"number"==typeof t?t:null==t?void 0:t.enter}),cf=(e,t)=>({...e,delay:"number"==typeof t?t:null==t?void 0:t.exit}),uf={exit:{height:{duration:.2,ease:ef.ease},opacity:{duration:.3,ease:ef.ease}},enter:{height:{duration:.3,ease:ef.ease},opacity:{duration:.4,ease:ef.ease}}},df={exit:({animateOpacity:e,startingHeight:t,transition:r,transitionEnd:n,delay:o})=>{var a,i;return{...e&&{opacity:(i=t,null!=i&&parseInt(i.toString(),10)>0?1:0)},height:t,transitionEnd:null==n?void 0:n.exit,transition:null!=(a=null==r?void 0:r.exit)?a:cf(uf.exit,o)}},enter:({animateOpacity:e,endingHeight:t,transition:r,transitionEnd:n,delay:o})=>{var a;return{...e&&{opacity:1},height:t,transitionEnd:null==n?void 0:n.enter,transition:null!=(a=null==r?void 0:r.enter)?a:lf(uf.enter,o)}}},ff=o.forwardRef(((e,t)=>{const{in:r,unmountOnExit:n,animateOpacity:a=!0,startingHeight:i=0,endingHeight:s="auto",style:u,className:d,transition:f,transitionEnd:p,...m}=e,[h,v]=o.useState(!1);o.useEffect((()=>{const e=setTimeout((()=>{v(!0)}));return()=>clearTimeout(e)}),[]),zt({condition:Number(i)>0&&!!n,message:"startingHeight and unmountOnExit are mutually exclusive. You can't use them together"});const g=parseFloat(i.toString())>0,y={startingHeight:i,endingHeight:s,animateOpacity:a,transition:h?f:{enter:{duration:0}},transitionEnd:{enter:null==p?void 0:p.enter,exit:n?null==p?void 0:p.exit:{...null==p?void 0:p.exit,display:g?"block":"none"}}},x=!n||r,w=r||n?"enter":"exit";return b.jsx(c,{initial:!1,custom:y,children:x&&b.jsx(l.div,{ref:t,...m,className:Ot("chakra-collapse",d),style:{overflow:"hidden",display:"block",...u},custom:y,variants:df,initial:!!n&&"exit",animate:w,exit:"exit"})})}));ff.displayName="Collapse";var pf={initial:"exit",animate:"enter",exit:"exit",variants:{enter:({transition:e,transitionEnd:t,delay:r}={})=>{var n;return{opacity:1,transition:null!=(n=null==e?void 0:e.enter)?n:lf(sf.enter,r),transitionEnd:null==t?void 0:t.enter}},exit:({transition:e,transitionEnd:t,delay:r}={})=>{var n;return{opacity:0,transition:null!=(n=null==e?void 0:e.exit)?n:cf(sf.exit,r),transitionEnd:null==t?void 0:t.exit}}}};o.forwardRef((function(e,t){const{unmountOnExit:r,in:n,className:o,transition:a,transitionEnd:i,delay:s,...u}=e,d=n||r?"enter":"exit",f=!r||n&&r,p={transition:a,transitionEnd:i,delay:s};return b.jsx(c,{custom:p,children:f&&b.jsx(l.div,{ref:t,className:Ot("chakra-fade",o),custom:p,...pf,animate:d,...u})})})).displayName="Fade";var mf={initial:"exit",animate:"enter",exit:"exit",variants:{exit:({reverse:e,initialScale:t,transition:r,transitionEnd:n,delay:o})=>{var a;return{opacity:0,...e?{scale:t,transitionEnd:null==n?void 0:n.exit}:{transitionEnd:{scale:t,...null==n?void 0:n.exit}},transition:null!=(a=null==r?void 0:r.exit)?a:cf(sf.exit,o)}},enter:({transitionEnd:e,transition:t,delay:r})=>{var n;return{opacity:1,scale:1,transition:null!=(n=null==t?void 0:t.enter)?n:lf(sf.enter,r),transitionEnd:null==e?void 0:e.enter}}}};o.forwardRef((function(e,t){const{unmountOnExit:r,in:n,reverse:o=!0,initialScale:a=.95,className:i,transition:s,transitionEnd:u,delay:d,...f}=e,p=!r||n&&r,m=n||r?"enter":"exit",h={initialScale:a,reverse:o,transition:s,transitionEnd:u,delay:d};return b.jsx(c,{custom:h,children:p&&b.jsx(l.div,{ref:t,className:Ot("chakra-offset-slide",i),...mf,animate:m,custom:h,...f})})})).displayName="ScaleFade";var hf={initial:"initial",animate:"enter",exit:"exit",variants:{initial:({offsetX:e,offsetY:t,transition:r,transitionEnd:n,delay:o})=>{var a;return{opacity:0,x:e,y:t,transition:null!=(a=null==r?void 0:r.exit)?a:cf(sf.exit,o),transitionEnd:null==n?void 0:n.exit}},enter:({transition:e,transitionEnd:t,delay:r})=>{var n;return{opacity:1,x:0,y:0,transition:null!=(n=null==e?void 0:e.enter)?n:lf(sf.enter,r),transitionEnd:null==t?void 0:t.enter}},exit:({offsetY:e,offsetX:t,transition:r,transitionEnd:n,reverse:o,delay:a})=>{var i;const s={x:t,y:e};return{opacity:0,transition:null!=(i=null==r?void 0:r.exit)?i:cf(sf.exit,a),...o?{...s,transitionEnd:null==n?void 0:n.exit}:{transitionEnd:{...s,...null==n?void 0:n.exit}}}}}};o.forwardRef((function(e,t){const{unmountOnExit:r,in:n,reverse:o=!0,className:a,offsetX:i=0,offsetY:s=8,transition:u,transitionEnd:d,delay:f,...p}=e,m=!r||n&&r,h=n||r?"enter":"exit",v={offsetX:i,offsetY:s,reverse:o,transition:u,transitionEnd:d,delay:f};return b.jsx(c,{custom:v,children:m&&b.jsx(l.div,{ref:t,className:Ot("chakra-offset-slide",a),custom:v,...hf,animate:h,...p})})})).displayName="SlideFade";var bf={exit:{duration:.15,ease:ef.easeInOut},enter:{type:"spring",damping:25,stiffness:180}},vf={exit:({direction:e,transition:t,transitionEnd:r,delay:n})=>{var o;const{exit:a}=af({direction:e});return{...a,transition:null!=(o=null==t?void 0:t.exit)?o:cf(bf.exit,n),transitionEnd:null==r?void 0:r.exit}},enter:({direction:e,transitionEnd:t,transition:r,delay:n})=>{var o;const{enter:a}=af({direction:e});return{...a,transition:null!=(o=null==r?void 0:r.enter)?o:lf(bf.enter,n),transitionEnd:null==t?void 0:t.enter}}},gf=o.forwardRef((function(e,t){const{direction:r="right",style:n,unmountOnExit:o,in:a,className:i,transition:s,transitionEnd:u,delay:d,motionProps:f,...p}=e,m=af({direction:r}),h=Object.assign({position:"fixed"},m.position,n),v=!o||a&&o,g=a||o?"enter":"exit",y={transitionEnd:u,transition:s,direction:r,delay:d};return b.jsx(c,{custom:y,children:v&&b.jsx(l.div,{...p,ref:t,initial:"exit",className:Ot("chakra-slide",i),animate:g,exit:"exit",custom:y,variants:vf,style:h,...f})})}));gf.displayName="Slide";var yf=Mu((function(e,t){const{className:r,motionProps:n,...o}=e,{reduceMotion:a}=Kd(),{getPanelProps:i,isOpen:s}=Ld(),l=i(o,t),c=Ot("chakra-accordion__panel",r),u=Bd();a||delete l.hidden;const d=b.jsx(Tu.div,{...l,__css:u.panel,className:c});return a?d:b.jsx(ff,{in:s,...n,children:d})}));yf.displayName="AccordionPanel";var xf=Mu((function({children:e,reduceMotion:t,...r},n){const a=yu("Accordion",r),i=En(r),{htmlProps:s,descendants:l,...c}=Xd(i),u=o.useMemo((()=>({...c,reduceMotion:!!t})),[c,t]);return b.jsx(Wd,{value:l,children:b.jsx(Yd,{value:u,children:b.jsx(Dd,{value:a,children:b.jsx(Tu.div,{ref:n,...s,className:Ot("chakra-accordion",r.className),__css:a.root,children:e})})})})}));xf.displayName="Accordion";var[wf,kf]=ot({name:"AvatarStylesContext",hookName:"useAvatarStyles",providerName:"<Avatar/>"});function Sf(e){var t;const r=e.split(" "),n=null!=(t=r[0])?t:"",o=r.length>1?r[r.length-1]:"";return n&&o?`${n.charAt(0)}${o.charAt(0)}`:n.charAt(0)}function _f(e){const{name:t,getInitials:r,...n}=e,o=kf();return b.jsx(Tu.div,{role:"img","aria-label":t,...n,__css:o.label,children:t?null==r?void 0:r(t):null})}_f.displayName="AvatarName";var Cf=e=>b.jsxs(Tu.svg,{viewBox:"0 0 128 128",color:"#fff",width:"100%",height:"100%",className:"chakra-avatar__svg",...e,children:[b.jsx("path",{fill:"currentColor",d:"M103,102.1388 C93.094,111.92 79.3504,118 64.1638,118 C48.8056,118 34.9294,111.768 25,101.7892 L25,95.2 C25,86.8096 31.981,80 40.6,80 L87.4,80 C96.019,80 103,86.8096 103,95.2 L103,102.1388 Z"}),b.jsx("path",{fill:"currentColor",d:"M63.9961647,24 C51.2938136,24 41,34.2938136 41,46.9961647 C41,59.7061864 51.2938136,70 63.9961647,70 C76.6985159,70 87,59.7061864 87,46.9961647 C87,34.2938136 76.6985159,24 63.9961647,24"})]}),Ef=Mu((function(e,t){const{htmlWidth:r,htmlHeight:n,alt:o,...a}=e;return b.jsx("img",{width:r,height:n,ref:t,alt:o,...a})}));function jf(e){const{loading:t,src:r,srcSet:n,onLoad:a,onError:i,crossOrigin:s,sizes:l,ignoreFallback:c}=e,[u,d]=o.useState("pending");o.useEffect((()=>{d(r?"loading":"pending")}),[r]);const f=o.useRef(),p=o.useCallback((()=>{if(!r)return;m();const e=new Image;e.src=r,s&&(e.crossOrigin=s),n&&(e.srcset=n),l&&(e.sizes=l),t&&(e.loading=t),e.onload=e=>{m(),d("loaded"),null==a||a(e)},e.onerror=e=>{m(),d("failed"),null==i||i(e)},f.current=e}),[r,s,n,l,a,i,t]),m=()=>{f.current&&(f.current.onload=null,f.current.onerror=null,f.current=null)};return lt((()=>{if(!c)return"loading"===u&&p(),()=>{m()}}),[u,p,c]),c?"loaded":u}Ef.displayName="NativeImage";function Nf(e,t=[]){const r=Object.assign({},e);for(const n of t)n in r&&delete r[n];return r}var Of=Mu((function(e,t){const{fallbackSrc:r,fallback:n,src:o,srcSet:a,align:i,fit:s,loading:l,ignoreFallback:c,crossOrigin:u,fallbackStrategy:d="beforeLoadOrError",referrerPolicy:f,...p}=e,m=null!=l||c||!(void 0!==r||void 0!==n),h=((e,t)=>"loaded"!==e&&"beforeLoadOrError"===t||"failed"===e&&"onError"===t)(jf({...e,crossOrigin:u,ignoreFallback:m}),d),v={ref:t,objectFit:s,objectPosition:i,...m?p:Nf(p,["onError","onLoad"])};return h?n||b.jsx(Tu.img,{as:Ef,className:"chakra-image__placeholder",src:r,...v}):b.jsx(Tu.img,{as:Ef,src:o,srcSet:a,crossOrigin:u,loading:l,referrerPolicy:f,className:"chakra-image",...v})}));function Af(e){const{src:t,srcSet:r,onError:n,onLoad:a,getInitials:i,name:s,borderRadius:l,loading:c,iconLabel:u,icon:d=b.jsx(Cf,{}),ignoreFallback:f,referrerPolicy:p,crossOrigin:m}=e,h=jf({src:t,onError:n,crossOrigin:m,ignoreFallback:f});return!t||!("loaded"===h)?s?b.jsx(_f,{className:"chakra-avatar__initials",getInitials:i,name:s}):o.cloneElement(d,{role:"img","aria-label":u}):b.jsx(Tu.img,{src:t,srcSet:r,alt:s,onLoad:a,referrerPolicy:p,crossOrigin:null!=m?m:void 0,className:"chakra-avatar__img",loading:c,__css:{width:"100%",height:"100%",objectFit:"cover",borderRadius:l}})}Of.displayName="Image",Af.displayName="AvatarImage";var zf={display:"inline-flex",alignItems:"center",justifyContent:"center",textAlign:"center",textTransform:"uppercase",fontWeight:"medium",position:"relative",flexShrink:0},If=Mu(((e,t)=>{const r=yu("Avatar",e),[n,a]=o.useState(!1),{src:i,srcSet:s,name:l,showBorder:c,borderRadius:u="full",onError:d,onLoad:f,getInitials:p=Sf,icon:m=b.jsx(Cf,{}),iconLabel:h=" avatar",loading:v,children:g,borderColor:y,ignoreFallback:x,crossOrigin:w,referrerPolicy:k,...S}=En(e),_={borderRadius:u,borderWidth:c?"2px":void 0,...zf,...r.container};return y&&(_.borderColor=y),b.jsx(Tu.span,{ref:t,...S,className:Ot("chakra-avatar",e.className),"data-loaded":Rt(n),__css:_,children:b.jsxs(wf,{value:r,children:[b.jsx(Af,{src:i,srcSet:s,loading:v,onLoad:Mt(f,(()=>{a(!0)})),onError:d,getInitials:p,name:l,borderRadius:u,icon:m,iconLabel:h,ignoreFallback:x,crossOrigin:w,referrerPolicy:k}),g]})})}));function Pf(e){return o.Children.toArray(e).filter((e=>o.isValidElement(e)))}If.displayName="Avatar";var[Rf,Tf]=ot({name:"BreadcrumbStylesContext",errorMessage:"useBreadcrumbStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Breadcrumb />\" "}),Mf=Mu((function(e,t){const{isCurrentPage:r,as:n,className:o,href:a,...i}=e,s=Tf(),l={ref:t,as:n,className:Ot("chakra-breadcrumb__link",o),...i};return r?b.jsx(Tu.span,{"aria-current":"page",__css:s.link,...l}):b.jsx(Tu.a,{__css:s.link,href:a,...l})}));Mf.displayName="BreadcrumbLink";var $f=Mu((function(e,t){const{spacing:r,...n}=e,o={mx:r,...Tf().separator};return b.jsx(Tu.span,{ref:t,role:"presentation",...n,__css:o})}));$f.displayName="BreadcrumbSeparator";var Df=Mu((function(e,t){const{isCurrentPage:r,separator:n,isLastChild:a,spacing:i,children:s,className:l,...c}=e,u=Pf(s).map((e=>e.type===Mf?o.cloneElement(e,{isCurrentPage:r}):e.type===$f?o.cloneElement(e,{spacing:i,children:e.props.children||n}):e)),d={display:"inline-flex",alignItems:"center",...Tf().item},f=Ot("chakra-breadcrumb__list-item",l);return b.jsxs(Tu.li,{ref:t,className:f,...c,__css:d,children:[u,!a&&b.jsx($f,{spacing:i,children:n})]})}));Df.displayName="BreadcrumbItem";var Bf=Mu((function(e,t){const r=yu("Breadcrumb",e),n=En(e),{children:a,spacing:i="0.5rem",separator:s="/",className:l,listProps:c,...u}=n,d=Pf(a),f=d.length,p=d.map(((e,t)=>o.cloneElement(e,{separator:s,spacing:i,isLastChild:f===t+1}))),m=Ot("chakra-breadcrumb",l);return b.jsx(Tu.nav,{ref:t,"aria-label":"breadcrumb",className:m,__css:r.container,...u,children:b.jsx(Rf,{value:r,children:b.jsx(Tu.ol,{className:"chakra-breadcrumb__list",...c,__css:{display:"flex",alignItems:"center",...r.list},children:p})})})}));Bf.displayName="Breadcrumb";var[Ff,Lf]=ot({strict:!1,name:"ButtonGroupContext"}),Wf={horizontal:{"> *:first-of-type:not(:last-of-type)":{borderEndRadius:0},"> *:not(:first-of-type):not(:last-of-type)":{borderRadius:0},"> *:not(:first-of-type):last-of-type":{borderStartRadius:0}},vertical:{"> *:first-of-type:not(:last-of-type)":{borderBottomRadius:0},"> *:not(:first-of-type):not(:last-of-type)":{borderRadius:0},"> *:not(:first-of-type):last-of-type":{borderTopRadius:0}}},Hf={horizontal:e=>({"& > *:not(style) ~ *:not(style)":{marginStart:e}}),vertical:e=>({"& > *:not(style) ~ *:not(style)":{marginTop:e}})},qf=Mu((function(e,t){const{size:r,colorScheme:n,variant:a,className:i,spacing:s="0.5rem",isAttached:l,isDisabled:c,orientation:u="horizontal",...d}=e,f=Ot("chakra-button__group",i),p=o.useMemo((()=>({size:r,colorScheme:n,variant:a,isDisabled:c})),[r,n,a,c]);let m={display:"inline-flex",...l?Wf[u]:Hf[u](s)};const h="vertical"===u;return b.jsx(Ff,{value:p,children:b.jsx(Tu.div,{ref:t,role:"group",__css:m,className:f,"data-attached":l?"":void 0,"data-orientation":u,flexDir:h?"column":void 0,...d})})}));function Vf(e){const{children:t,className:r,...n}=e,a=o.isValidElement(t)?o.cloneElement(t,{"aria-hidden":!0,focusable:!1}):t,i=Ot("chakra-button__icon",r);return b.jsx(Tu.span,{display:"inline-flex",alignSelf:"center",flexShrink:0,...n,className:i,children:a})}function Uf(e){const{label:t,placement:r,spacing:n="0.5rem",children:a=b.jsx(od,{color:"currentColor",width:"1em",height:"1em"}),className:i,__css:s,...l}=e,c=Ot("chakra-button__spinner",i),u="start"===r?"marginEnd":"marginStart",d=o.useMemo((()=>({display:"flex",alignItems:"center",position:t?"relative":"absolute",[u]:t?n:0,fontSize:"1em",lineHeight:"normal",...s})),[s,t,u,n]);return b.jsx(Tu.div,{className:c,...l,__css:d,children:a})}qf.displayName="ButtonGroup",Vf.displayName="ButtonIcon",Uf.displayName="ButtonSpinner";var Gf=Mu(((e,t)=>{const r=Lf(),n=gu("Button",{...r,...e}),{isDisabled:a=(null==r?void 0:r.isDisabled),isLoading:i,isActive:s,children:l,leftIcon:c,rightIcon:u,loadingText:d,iconSpacing:f="0.5rem",type:p,spinner:m,spinnerPlacement:h="start",className:v,as:g,...y}=En(e),x=o.useMemo((()=>{const e={...null==n?void 0:n._focus,zIndex:1};return{display:"inline-flex",appearance:"none",alignItems:"center",justifyContent:"center",userSelect:"none",position:"relative",whiteSpace:"nowrap",verticalAlign:"middle",outline:"none",...n,...!!r&&{_focus:e}}}),[n,r]),{ref:w,type:k}=function(e){const[t,r]=o.useState(!e);return{ref:o.useCallback((e=>{e&&r("BUTTON"===e.tagName)}),[]),type:t?"button":void 0}}(g),S={rightIcon:u,leftIcon:c,iconSpacing:f,children:l};return b.jsxs(Tu.button,{ref:Rd(t,w),as:g,type:null!=p?p:k,"data-active":Rt(s),"data-loading":Rt(i),__css:x,className:Ot("chakra-button",v),...y,disabled:a||i,children:[i&&"start"===h&&b.jsx(Uf,{className:"chakra-button__spinner--start",label:d,placement:"start",spacing:f,children:m}),i?d||b.jsx(Tu.span,{opacity:0,children:b.jsx(Xf,{...S})}):b.jsx(Xf,{...S}),i&&"end"===h&&b.jsx(Uf,{className:"chakra-button__spinner--end",label:d,placement:"end",spacing:f,children:m})]})}));function Xf(e){const{leftIcon:t,rightIcon:r,children:n,iconSpacing:o}=e;return b.jsxs(b.Fragment,{children:[t&&b.jsx(Vf,{marginEnd:o,children:t}),n,r&&b.jsx(Vf,{marginStart:o,children:r})]})}Gf.displayName="Button";var Yf=Mu(((e,t)=>{const{icon:r,children:n,isRound:a,"aria-label":i,...s}=e,l=r||n,c=o.isValidElement(l)?o.cloneElement(l,{"aria-hidden":!0,focusable:!1}):null;return b.jsx(Gf,{padding:"0",borderRadius:a?"full":void 0,ref:t,"aria-label":i,...s,children:c})}));Yf.displayName="IconButton";var Kf,[Zf,Qf]=$u({name:`${Kf="Card"}StylesContext`,errorMessage:`useStyles: "styles" is undefined. Seems you forgot to wrap the components in "<${Kf} />" `}),Jf=Mu((function(e,t){const{className:r,...n}=e,o=Qf();return b.jsx(Tu.div,{ref:t,className:Ot("chakra-card__body",r),__css:o.body,...n})})),ep=Mu((function(e,t){const{className:r,...n}=e,o=Qf();return b.jsx(Tu.div,{ref:t,className:Ot("chakra-card__header",r),__css:o.header,...n})})),tp=Mu((function(e,t){const{className:r,children:n,direction:o="column",justify:a,align:i,...s}=En(e),l=yu("Card",e);return b.jsx(Tu.div,{ref:t,className:Ot("chakra-card",r),__css:{display:"flex",flexDirection:o,justifyContent:a,alignItems:i,position:"relative",minWidth:0,wordWrap:"break-word",...l.container},...s,children:b.jsx(Zf,{value:l,children:n})})})),[rp,np]=ot({name:"CheckboxGroupContext",strict:!1});function op(e){return b.jsx(Tu.svg,{width:"1.2em",viewBox:"0 0 12 10",style:{fill:"none",strokeWidth:2,stroke:"currentColor",strokeDasharray:16},...e,children:b.jsx("polyline",{points:"1.5 6 4.5 9 10.5 1"})})}function ap(e){return b.jsx(Tu.svg,{width:"1.2em",viewBox:"0 0 24 24",style:{stroke:"currentColor",strokeWidth:4},...e,children:b.jsx("line",{x1:"21",x2:"3",y1:"12",y2:"12"})})}function ip(e){const{isIndeterminate:t,isChecked:r,...n}=e,o=t?ap:op;return r||t?b.jsx(Tu.div,{style:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%"},children:b.jsx(o,{...n})}):null}var[sp,lp]=ot({name:"FormControlStylesContext",errorMessage:"useFormControlStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<FormControl />\" "}),[cp,up]=ot({strict:!1,name:"FormControlContext"});var dp=Mu((function(e,t){const r=yu("Form",e),n=En(e),{getRootProps:a,htmlProps:i,...s}=function(e){const{id:t,isRequired:r,isInvalid:n,isDisabled:a,isReadOnly:i,...s}=e,l=o.useId(),c=t||`field-${l}`,u=`${c}-label`,d=`${c}-feedback`,f=`${c}-helptext`,[p,m]=o.useState(!1),[h,b]=o.useState(!1),[v,g]=o.useState(!1),y=o.useCallback(((e={},t=null)=>({id:f,...e,ref:Pd(t,(e=>{e&&b(!0)}))})),[f]),x=o.useCallback(((e={},t=null)=>({...e,ref:t,"data-focus":Rt(v),"data-disabled":Rt(a),"data-invalid":Rt(n),"data-readonly":Rt(i),id:void 0!==e.id?e.id:u,htmlFor:void 0!==e.htmlFor?e.htmlFor:c})),[c,a,v,n,i,u]),w=o.useCallback(((e={},t=null)=>({id:d,...e,ref:Pd(t,(e=>{e&&m(!0)})),"aria-live":"polite"})),[d]),k=o.useCallback(((e={},t=null)=>({...e,...s,ref:t,role:"group"})),[s]),S=o.useCallback(((e={},t=null)=>({...e,ref:t,role:"presentation","aria-hidden":!0,children:e.children||"*"})),[]);return{isRequired:!!r,isInvalid:!!n,isReadOnly:!!i,isDisabled:!!a,isFocused:!!v,onFocus:()=>g(!0),onBlur:()=>g(!1),hasFeedbackText:p,setHasFeedbackText:m,hasHelpText:h,setHasHelpText:b,id:c,labelId:u,feedbackId:d,helpTextId:f,htmlProps:s,getHelpTextProps:y,getErrorMessageProps:w,getRootProps:k,getLabelProps:x,getRequiredIndicatorProps:S}}(n),l=Ot("chakra-form-control",e.className);return b.jsx(cp,{value:s,children:b.jsx(sp,{value:r,children:b.jsx(Tu.div,{...a({},t),className:l,__css:r.container})})})}));dp.displayName="FormControl",Mu((function(e,t){const r=up(),n=lp(),o=Ot("chakra-form__helper-text",e.className);return b.jsx(Tu.div,{...null==r?void 0:r.getHelpTextProps(e,t),__css:n.helperText,className:o})})).displayName="FormHelperText";var[fp,pp]=ot({name:"FormErrorStylesContext",errorMessage:"useFormErrorStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<FormError />\" "}),mp=Mu(((e,t)=>{const r=yu("FormError",e),n=En(e),o=up();return(null==o?void 0:o.isInvalid)?b.jsx(fp,{value:r,children:b.jsx(Tu.div,{...null==o?void 0:o.getErrorMessageProps(n,t),className:Ot("chakra-form__error-message",e.className),__css:{display:"flex",alignItems:"center",...r.text}})}):null}));mp.displayName="FormErrorMessage",Mu(((e,t)=>{const r=pp(),n=up();if(!(null==n?void 0:n.isInvalid))return null;const o=Ot("chakra-form__error-icon",e.className);return b.jsx(ed,{ref:t,"aria-hidden":!0,...e,__css:r.icon,className:o,children:b.jsx("path",{fill:"currentColor",d:"M11.983,0a12.206,12.206,0,0,0-8.51,3.653A11.8,11.8,0,0,0,0,12.207,11.779,11.779,0,0,0,11.8,24h.214A12.111,12.111,0,0,0,24,11.791h0A11.766,11.766,0,0,0,11.983,0ZM10.5,16.542a1.476,1.476,0,0,1,1.449-1.53h.027a1.527,1.527,0,0,1,1.523,1.47,1.475,1.475,0,0,1-1.449,1.53h-.027A1.529,1.529,0,0,1,10.5,16.542ZM11,12.5v-6a1,1,0,0,1,2,0v6a1,1,0,1,1-2,0Z"})})})).displayName="FormErrorIcon";var hp=Mu((function(e,t){var r;const n=gu("FormLabel",e),o=En(e),{className:a,children:i,requiredIndicator:s=b.jsx(bp,{}),optionalIndicator:l=null,...c}=o,u=up(),d=null!=(r=null==u?void 0:u.getLabelProps(c,t))?r:{ref:t,...c};return b.jsxs(Tu.label,{...d,className:Ot("chakra-form__label",o.className),__css:{display:"block",textAlign:"start",...n},children:[i,(null==u?void 0:u.isRequired)?s:l]})}));hp.displayName="FormLabel";var bp=Mu((function(e,t){const r=up(),n=lp();if(!(null==r?void 0:r.isRequired))return null;const o=Ot("chakra-form__required-indicator",e.className);return b.jsx(Tu.span,{...null==r?void 0:r.getRequiredIndicatorProps(e,t),__css:n.requiredIndicator,className:o})}));function vp(e){const{isDisabled:t,isInvalid:r,isReadOnly:n,isRequired:o,...a}=gp(e);return{...a,disabled:t,readOnly:n,required:o,"aria-invalid":Tt(r),"aria-required":Tt(o),"aria-readonly":Tt(n)}}function gp(e){var t,r,n;const o=up(),{id:a,disabled:i,readOnly:s,required:l,isRequired:c,isInvalid:u,isReadOnly:d,isDisabled:f,onFocus:p,onBlur:m,...h}=e,b=e["aria-describedby"]?[e["aria-describedby"]]:[];return(null==o?void 0:o.hasFeedbackText)&&(null==o?void 0:o.isInvalid)&&b.push(o.feedbackId),(null==o?void 0:o.hasHelpText)&&b.push(o.helpTextId),{...h,"aria-describedby":b.join(" ")||void 0,id:null!=a?a:null==o?void 0:o.id,isDisabled:null!=(t=null!=i?i:f)?t:null==o?void 0:o.isDisabled,isReadOnly:null!=(r=null!=s?s:d)?r:null==o?void 0:o.isReadOnly,isRequired:null!=(n=null!=l?l:c)?n:null==o?void 0:o.isRequired,isInvalid:null!=u?u:null==o?void 0:o.isInvalid,onFocus:Mt(null==o?void 0:o.onFocus,p),onBlur:Mt(null==o?void 0:o.onBlur,m)}}bp.displayName="RequiredIndicator";var yp={border:"0",clip:"rect(0, 0, 0, 0)",height:"1px",width:"1px",margin:"-1px",padding:"0",overflow:"hidden",whiteSpace:"nowrap",position:"absolute"};let xp=!1,wp=null,kp=!1,Sp=!1;const _p=new Set;function Cp(e,t){_p.forEach((r=>r(e,t)))}const Ep="undefined"!=typeof window&&null!=window.navigator&&/^Mac/.test(window.navigator.platform);function jp(e){var t;kp=!0,(t=e).metaKey||!Ep&&t.altKey||t.ctrlKey||"Control"===t.key||"Shift"===t.key||"Meta"===t.key||(wp="keyboard",Cp("keyboard",e))}function Np(e){if(wp="pointer","mousedown"===e.type||"pointerdown"===e.type){kp=!0;const t=e.composedPath?e.composedPath()[0]:e.target;let r=!1;try{r=t.matches(":focus-visible")}catch{}if(r)return;Cp("pointer",e)}}function Op(e){var t;(0===(t=e).mozInputSource&&t.isTrusted||0===t.detail&&!t.pointerType)&&(kp=!0,wp="virtual")}function Ap(e){e.target!==window&&e.target!==document&&(kp||Sp||(wp="virtual",Cp("virtual",e)),kp=!1,Sp=!1)}function zp(){kp=!1,Sp=!0}function Ip(){return"pointer"!==wp}function Pp(){if("undefined"==typeof document||xp)return;const{focus:e}=HTMLElement.prototype;HTMLElement.prototype.focus=function(...t){kp=!0,e.apply(this,t)},document.addEventListener("keydown",jp,!0),document.addEventListener("keyup",jp,!0),document.addEventListener("click",Op,!0),window.addEventListener("focus",Ap,!0),window.addEventListener("blur",zp,!1),"undefined"!=typeof PointerEvent?(document.addEventListener("pointerdown",Np,!0),document.addEventListener("pointermove",Np,!0),document.addEventListener("pointerup",Np,!0)):(document.addEventListener("mousedown",Np,!0),document.addEventListener("mousemove",Np,!0),document.addEventListener("mouseup",Np,!0)),xp=!0}function Rp(e={}){const t=gp(e),{isDisabled:r,isReadOnly:n,isRequired:a,isInvalid:i,id:s,onBlur:l,onFocus:c,"aria-describedby":u}=t,{defaultChecked:d,isChecked:f,isFocusable:p,onChange:m,isIndeterminate:h,name:b,value:v,tabIndex:g,"aria-label":y,"aria-labelledby":x,"aria-invalid":w,...k}=e,S=function(e,t=[]){const r=Object.assign({},e);for(const n of t)n in r&&delete r[n];return r}(k,["isDisabled","isReadOnly","isRequired","isInvalid","id","onBlur","onFocus","aria-describedby"]),_=Xu(m),C=Xu(l),E=Xu(c),[j,N]=o.useState(!1),[O,A]=o.useState(!1),[z,I]=o.useState(!1),[P,R]=o.useState(!1);o.useEffect((()=>function(e){Pp(),e(Ip());const t=()=>e(Ip());return _p.add(t),()=>{_p.delete(t)}}(N)),[]);const T=o.useRef(null),[M,$]=o.useState(!0),[D,B]=o.useState(!!d),F=void 0!==f,L=F?f:D,W=o.useCallback((e=>{n||r?e.preventDefault():(F||B(L?e.target.checked:!!h||e.target.checked),null==_||_(e))}),[n,r,L,F,h,_]);lt((()=>{T.current&&(T.current.indeterminate=Boolean(h))}),[h]),Yu((()=>{r&&A(!1)}),[r,A]),lt((()=>{const e=T.current;if(!(null==e?void 0:e.form))return;const t=()=>{B(!!d)};return e.form.addEventListener("reset",t),()=>{var r;return null==(r=e.form)?void 0:r.removeEventListener("reset",t)}}),[]);const H=r&&!p,q=o.useCallback((e=>{" "===e.key&&R(!0)}),[R]),V=o.useCallback((e=>{" "===e.key&&R(!1)}),[R]);lt((()=>{if(!T.current)return;T.current.checked!==L&&B(T.current.checked)}),[T.current]);const U=o.useCallback(((e={},t=null)=>({...e,ref:t,"data-active":Rt(P),"data-hover":Rt(z),"data-checked":Rt(L),"data-focus":Rt(O),"data-focus-visible":Rt(O&&j),"data-indeterminate":Rt(h),"data-disabled":Rt(r),"data-invalid":Rt(i),"data-readonly":Rt(n),"aria-hidden":!0,onMouseDown:Mt(e.onMouseDown,(e=>{O&&e.preventDefault(),R(!0)})),onMouseUp:Mt(e.onMouseUp,(()=>R(!1))),onMouseEnter:Mt(e.onMouseEnter,(()=>I(!0))),onMouseLeave:Mt(e.onMouseLeave,(()=>I(!1)))})),[P,L,r,O,j,z,h,i,n]),G=o.useCallback(((e={},t=null)=>({...e,ref:t,"data-active":Rt(P),"data-hover":Rt(z),"data-checked":Rt(L),"data-focus":Rt(O),"data-focus-visible":Rt(O&&j),"data-indeterminate":Rt(h),"data-disabled":Rt(r),"data-invalid":Rt(i),"data-readonly":Rt(n)})),[P,L,r,O,j,z,h,i,n]),X=o.useCallback(((e={},t=null)=>({...S,...e,ref:Pd(t,(e=>{e&&$("LABEL"===e.tagName)})),onClick:Mt(e.onClick,(()=>{var e;M||(null==(e=T.current)||e.click(),requestAnimationFrame((()=>{var e;null==(e=T.current)||e.focus({preventScroll:!0})})))})),"data-disabled":Rt(r),"data-checked":Rt(L),"data-invalid":Rt(i)})),[S,r,L,i,M]),Y=o.useCallback(((e={},t=null)=>({...e,ref:Pd(T,t),type:"checkbox",name:b,value:v,id:s,tabIndex:g,onChange:Mt(e.onChange,W),onBlur:Mt(e.onBlur,C,(()=>A(!1))),onFocus:Mt(e.onFocus,E,(()=>A(!0))),onKeyDown:Mt(e.onKeyDown,q),onKeyUp:Mt(e.onKeyUp,V),required:a,checked:L,disabled:H,readOnly:n,"aria-label":y,"aria-labelledby":x,"aria-invalid":w?Boolean(w):i,"aria-describedby":u,"aria-disabled":r,style:yp})),[b,v,s,W,C,E,q,V,a,L,H,n,y,x,w,i,u,r,g]),K=o.useCallback(((e={},t=null)=>({...e,ref:t,onMouseDown:Mt(e.onMouseDown,Tp),"data-disabled":Rt(r),"data-checked":Rt(L),"data-invalid":Rt(i)})),[L,r,i]);return{state:{isInvalid:i,isFocused:O,isChecked:L,isActive:P,isHovered:z,isIndeterminate:h,isDisabled:r,isReadOnly:n,isRequired:a},getRootProps:X,getCheckboxProps:U,getIndicatorProps:G,getInputProps:Y,getLabelProps:K,htmlProps:S}}function Tp(e){e.preventDefault(),e.stopPropagation()}var Mp={display:"inline-flex",alignItems:"center",justifyContent:"center",verticalAlign:"top",userSelect:"none",flexShrink:0},$p={cursor:"pointer",display:"inline-flex",alignItems:"center",verticalAlign:"top",position:"relative"},Dp=Je({from:{opacity:0,strokeDashoffset:16,transform:"scale(0.95)"},to:{opacity:1,strokeDashoffset:0,transform:"scale(1)"}}),Bp=Je({from:{opacity:0},to:{opacity:1}}),Fp=Je({from:{transform:"scaleX(0.65)"},to:{transform:"scaleX(1)"}}),Lp=Mu((function(e,t){const r=np(),n=yu("Checkbox",{...r,...e}),a=En(e),{spacing:i="0.5rem",className:s,children:l,iconColor:c,iconSize:u,icon:d=b.jsx(ip,{}),isChecked:f,isDisabled:p=(null==r?void 0:r.isDisabled),onChange:m,inputProps:h,...v}=a;let g=f;(null==r?void 0:r.value)&&a.value&&(g=r.value.includes(a.value));let y=m;(null==r?void 0:r.onChange)&&a.value&&(y=$t(r.onChange,m));const{state:x,getInputProps:w,getCheckboxProps:k,getLabelProps:S,getRootProps:_}=Rp({...v,isDisabled:p,isChecked:g,onChange:y}),C=function(e){const[t,r]=o.useState(e),[n,a]=o.useState(!1);return e!==t&&(a(!0),r(e)),n}(x.isChecked),E=o.useMemo((()=>({animation:C?x.isIndeterminate?`${Bp} 20ms linear, ${Fp} 200ms linear`:`${Dp} 200ms linear`:void 0,fontSize:u,color:c,...n.icon})),[c,u,C,x.isIndeterminate,n.icon]),j=o.cloneElement(d,{__css:E,isIndeterminate:x.isIndeterminate,isChecked:x.isChecked});return b.jsxs(Tu.label,{__css:{...$p,...n.container},className:Ot("chakra-checkbox",s),..._(),children:[b.jsx("input",{className:"chakra-checkbox__input",...w(h,t)}),b.jsx(Tu.span,{__css:{...Mp,...n.control},className:"chakra-checkbox__control",...k(),children:j}),l&&b.jsx(Tu.span,{className:"chakra-checkbox__label",...S(),__css:{marginStart:i,...n.label},children:l})]})}));function Wp(e,t){let r=function(e){const t=parseFloat(e);return"number"!=typeof t||Number.isNaN(t)?0:t}(e);const n=10**(null!=t?t:10);return r=Math.round(r*n)/n,t?r.toFixed(t):r.toString()}function Hp(e,t,r){return 100*(e-t)/(r-t)}function qp(e,t,r){const n=Math.round((e-t)/r)*r+t,o=function(e){if(!Number.isFinite(e))return 0;let t=1,r=0;for(;Math.round(e*t)/t!==e;)t*=10,r+=1;return r}(r);return Wp(n,o)}function Vp(e,t,r){return null==e?e:Math.min(Math.max(e,t),r)}function Up(e,t,r,n){const a=Xu(r);return o.useEffect((()=>{const o="function"==typeof e?e():null!=e?e:document;if(r&&o)return o.addEventListener(t,a,n),()=>{o.removeEventListener(t,a,n)}}),[t,e,n,a,r]),()=>{const r="function"==typeof e?e():null!=e?e:document;null==r||r.removeEventListener(t,a,n)}}Lp.displayName="Checkbox";var Gp="data-focus-lock",Xp="data-focus-lock-disabled";function Yp(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var Kp="undefined"!=typeof window?o.useLayoutEffect:o.useEffect,Zp=new WeakMap;function Qp(e,t){var r,n,a,i=(r=null,n=function(t){return e.forEach((function(e){return Yp(e,t)}))},(a=o.useState((function(){return{value:r,callback:n,facade:{get current(){return a.value},set current(e){var t=a.value;t!==e&&(a.value=e,a.callback(e,t))}}}}))[0]).callback=n,a.facade);return Kp((function(){var t=Zp.get(i);if(t){var r=new Set(t),n=new Set(e),o=i.current;r.forEach((function(e){n.has(e)||Yp(e,null)})),n.forEach((function(e){r.has(e)||Yp(e,o)}))}Zp.set(i,e)}),[e]),i}var Jp={width:"1px",height:"0px",padding:0,overflow:"hidden",position:"fixed",top:"1px",left:"1px"},em=function(){return em=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},em.apply(this,arguments)};function tm(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]])}return r}function rm(e){return e}function nm(e,t){void 0===t&&(t=rm);var r=[],n=!1;return{read:function(){if(n)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:e},useMedium:function(e){var o=t(e,n);return r.push(o),function(){r=r.filter((function(e){return e!==o}))}},assignSyncMedium:function(e){for(n=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){n=!0;var t=[];if(r.length){var o=r;r=[],o.forEach(e),t=r}var a=function(){var r=t;t=[],r.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}}}function om(e,t){return void 0===t&&(t=rm),nm(e,t)}function am(e){void 0===e&&(e={});var t=nm(null);return t.options=em({async:!0,ssr:!1},e),t}"function"==typeof SuppressedError&&SuppressedError;var im=function(e){var t=e.sideCar,r=tm(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw new Error("Sidecar medium not found");return o.createElement(n,em({},r))};im.isSideCarExport=!0;var sm=om({},(function(e){return{target:e.target,currentTarget:e.currentTarget}})),lm=om(),cm=om(),um=am({async:!0,ssr:"undefined"!=typeof document}),dm=o.createContext(void 0),fm=[],pm=o.forwardRef((function(e,t){var r,n=o.useState(),a=n[0],s=n[1],l=o.useRef(),c=o.useRef(!1),u=o.useRef(null),d=o.useState({})[1],f=e.children,p=e.disabled,m=void 0!==p&&p,h=e.noFocusGuards,b=void 0!==h&&h,v=e.persistentFocus,g=void 0!==v&&v,y=e.crossFrame,x=void 0===y||y,w=e.autoFocus,k=void 0===w||w;e.allowTextSelection;var S=e.group,_=e.className,C=e.whiteList,E=e.hasPositiveIndices,j=e.shards,N=void 0===j?fm:j,O=e.as,A=void 0===O?"div":O,z=e.lockProps,I=void 0===z?{}:z,P=e.sideCar,R=e.returnFocus,T=void 0!==R&&R,M=e.focusOptions,$=e.onActivation,D=e.onDeactivation,B=o.useState({})[0],F=o.useCallback((function(e){var t=e.captureFocusRestore;if(!u.current){var r,n=null==(r=document)?void 0:r.activeElement;u.current=n,n!==document.body&&(u.current=t(n))}l.current&&$&&$(l.current),c.current=!0,d()}),[$]),L=o.useCallback((function(){c.current=!1,D&&D(l.current),d()}),[D]),W=o.useCallback((function(e){var t=u.current;if(t){var r=("function"==typeof t?t():t)||document.body,n="function"==typeof T?T(r):T;if(n){var o="object"==typeof n?n:void 0;u.current=null,e?Promise.resolve().then((function(){return r.focus(o)})):r.focus(o)}}}),[T]),H=o.useCallback((function(e){c.current&&sm.useMedium(e)}),[]),q=lm.useMedium,V=o.useCallback((function(e){l.current!==e&&(l.current=e,s(e))}),[]),U=we(((r={})[Xp]=m&&"disabled",r[Gp]=S,r),I),G=!0!==b,X=G&&"tail"!==b,Y=Qp([t,V]),K=o.useMemo((function(){return{observed:l,shards:N,enabled:!m,active:c.current}}),[m,c.current,N,a]);return i.createElement(o.Fragment,null,G&&[i.createElement("div",{key:"guard-first","data-focus-guard":!0,tabIndex:m?-1:0,style:Jp}),E?i.createElement("div",{key:"guard-nearest","data-focus-guard":!0,tabIndex:m?-1:1,style:Jp}):null],!m&&i.createElement(P,{id:B,sideCar:um,observed:a,disabled:m,persistentFocus:g,crossFrame:x,autoFocus:k,whiteList:C,shards:N,onActivation:F,onDeactivation:L,returnFocus:W,focusOptions:M,noFocusGuards:b}),i.createElement(A,we({ref:Y},U,{className:_,onBlur:q,onFocus:H}),i.createElement(dm.Provider,{value:K},f)),X&&i.createElement("div",{"data-focus-guard":!0,tabIndex:m?-1:0,style:Jp}))}));function mm(e,t){return(mm=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function hm(e){return(hm="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function bm(e){var t=function(e,t){if("object"!=hm(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=hm(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==hm(t)?t:t+""}pm.propTypes={};var vm=function(e){for(var t=Array(e.length),r=0;r<e.length;++r)t[r]=e[r];return t},gm=function(e){return Array.isArray(e)?e:[e]},ym=function(e){return Array.isArray(e)?e[0]:e},xm=function(e){return e.parentNode&&e.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE?e.parentNode.host:e.parentNode},wm=function(e){return e===document||e&&e.nodeType===Node.DOCUMENT_NODE},km=function(e,t){return!e||wm(e)||!function(e){if(e.nodeType!==Node.ELEMENT_NODE)return!1;var t=window.getComputedStyle(e,null);return!(!t||!t.getPropertyValue||"none"!==t.getPropertyValue("display")&&"hidden"!==t.getPropertyValue("visibility"))}(e)&&!function(e){return e.hasAttribute("inert")}(e)&&t(xm(e))},Sm=function(e,t){var r=e.get(t);if(void 0!==r)return r;var n=km(t,Sm.bind(void 0,e));return e.set(t,n),n},_m=function(e,t){var r=e.get(t);if(void 0!==r)return r;var n=function(e,t){return!(e&&!wm(e))||!!Nm(e)&&t(xm(e))}(t,_m.bind(void 0,e));return e.set(t,n),n},Cm=function(e){return e.dataset},Em=function(e){return"INPUT"===e.tagName},jm=function(e){return Em(e)&&"radio"===e.type},Nm=function(e){var t=e.getAttribute("data-no-autofocus");return![!0,"true",""].includes(t)},Om=function(e){var t;return Boolean(e&&(null===(t=Cm(e))||void 0===t?void 0:t.focusGuard))},Am=function(e){return!Om(e)},zm=function(e){return Boolean(e)},Im=function(e,t){var r=Math.max(0,e.tabIndex),n=Math.max(0,t.tabIndex),o=r-n,a=e.index-t.index;if(o){if(!r)return 1;if(!n)return-1}return o||a},Pm=function(e,t,r){return vm(e).map((function(e,t){var n=function(e){return e.tabIndex<0&&!e.hasAttribute("tabindex")?0:e.tabIndex}(e);return{node:e,index:t,tabIndex:r&&-1===n?(e.dataset||{}).focusGuard?0:-1:n}})).filter((function(e){return!t||e.tabIndex>=0})).sort(Im)},Rm=["button:enabled","select:enabled","textarea:enabled","input:enabled","a[href]","area[href]","summary","iframe","object","embed","audio[controls]","video[controls]","[tabindex]","[contenteditable]","[autofocus]"].join(","),Tm="".concat(Rm,", [data-focus-guard]"),Mm=function(e,t){return vm((e.shadowRoot||e).children).reduce((function(e,r){return e.concat(r.matches(t?Tm:Rm)?[r]:[],Mm(r))}),[])},$m=function(e,t){return e.reduce((function(e,r){var n,o=Mm(r,t),a=(n=[]).concat.apply(n,o.map((function(e){return function(e,t){var r;return e instanceof HTMLIFrameElement&&(null===(r=e.contentDocument)||void 0===r?void 0:r.body)?$m([e.contentDocument.body],t):[e]}(e,t)})));return e.concat(a,r.parentNode?vm(r.parentNode.querySelectorAll(Rm)).filter((function(e){return e===r})):[])}),[])},Dm=function(e,t){return vm(e).filter((function(e){return Sm(t,e)})).filter((function(e){return function(e){return!((Em(e)||function(e){return"BUTTON"===e.tagName}(e))&&("hidden"===e.type||e.disabled))}(e)}))},Bm=function(e,t){return void 0===t&&(t=new Map),vm(e).filter((function(e){return _m(t,e)}))},Fm=function(e,t,r){return Pm(Dm($m(e,r),t),!0,r)},Lm=function(e,t){return Pm(Dm($m(e),t),!1)},Wm=function(e,t){return Dm((r=e.querySelectorAll("[".concat("data-autofocus-inside","]")),vm(r).map((function(e){return $m([e])})).reduce((function(e,t){return e.concat(t)}),[])),t);var r},Hm=function(e,t){return e.shadowRoot?Hm(e.shadowRoot,t):!(void 0===Object.getPrototypeOf(e).contains||!Object.getPrototypeOf(e).contains.call(e,t))||vm(e.children).some((function(e){var r;if(e instanceof HTMLIFrameElement){var n=null===(r=e.contentDocument)||void 0===r?void 0:r.body;return!!n&&Hm(n,t)}return Hm(e,t)}))},qm=function(e){return e.parentNode?qm(e.parentNode):e},Vm=function(e){return gm(e).filter(Boolean).reduce((function(e,t){var r=t.getAttribute(Gp);return e.push.apply(e,r?function(e){for(var t=new Set,r=e.length,n=0;n<r;n+=1)for(var o=n+1;o<r;o+=1){var a=e[n].compareDocumentPosition(e[o]);(a&Node.DOCUMENT_POSITION_CONTAINED_BY)>0&&t.add(o),(a&Node.DOCUMENT_POSITION_CONTAINS)>0&&t.add(n)}return e.filter((function(e,r){return!t.has(r)}))}(vm(qm(t).querySelectorAll("[".concat(Gp,'="').concat(r,'"]:not([').concat(Xp,'="disabled"])')))):[t]),e}),[])},Um=function(e){if(void 0===e&&(e=document),e&&e.activeElement){var t=e.activeElement;return t.shadowRoot?Um(t.shadowRoot):t instanceof HTMLIFrameElement&&function(e){try{return e()}catch(t){return}}((function(){return t.contentWindow.document}))?Um(t.contentWindow.document):t}},Gm=function(e,t){return void 0===t&&(t=Um(ym(e).ownerDocument)),!(!t||t.dataset&&t.dataset.focusGuard)&&Vm(e).some((function(e){return Hm(e,t)||function(e,t){return Boolean(vm(e.querySelectorAll("iframe")).some((function(e){return function(e,t){return e===t}(e,t)})))}(e,t)}))},Xm=function(e,t){return jm(e)&&e.name?function(e,t){return t.filter(jm).filter((function(t){return t.name===e.name})).filter((function(e){return e.checked}))[0]||e}(e,t):e},Ym=function(e){return e[0]&&e.length>1?Xm(e[0],e):e[0]},Km=function(e,t){return e.indexOf(Xm(t,e))},Zm="NEW_FOCUS",Qm=function(e,t,r,n,o){var a=e.length,i=e[0],s=e[a-1],l=Om(n);if(!(n&&e.indexOf(n)>=0)){var c=void 0!==n?r.indexOf(n):-1,u=o?r.indexOf(o):c,d=o?e.indexOf(o):-1;if(-1===c)return-1!==d?d:Zm;if(-1===d)return Zm;var f,p,m=c-u,h=r.indexOf(i),b=r.indexOf(s),v=(f=r,p=new Set,f.forEach((function(e){return p.add(Xm(e,f))})),f.filter((function(e){return p.has(e)}))),g=void 0!==n?v.indexOf(n):-1,y=o?v.indexOf(o):g,x=v.filter((function(e){return e.tabIndex>=0})),w=void 0!==n?x.indexOf(n):-1,k=o?x.indexOf(o):w,S=w>=0&&k>=0?k-w:y-g;if(!m&&d>=0)return d;if(0===t.length)return d;var _=Km(e,t[0]),C=Km(e,t[t.length-1]);return c<=h&&l&&Math.abs(m)>1?C:c>=b&&l&&Math.abs(m)>1?_:m&&Math.abs(S)>1?d:c<=h?C:c>b?_:m?Math.abs(m)>1?d:(a+d+m)%a:void 0}},Jm=function(e,t,r){var n,o=e.map((function(e){return e.node})),a=Bm(o.filter((n=r,function(e){var t,r=null===(t=Cm(e))||void 0===t?void 0:t.autofocus;return e.autofocus||void 0!==r&&"false"!==r||n.indexOf(e)>=0})));return a&&a.length?Ym(a):Ym(Bm(t))},eh=function(e,t){return void 0===t&&(t=[]),t.push(e),e.parentNode&&eh(e.parentNode.host||e.parentNode,t),t},th=function(e,t){for(var r=eh(e),n=eh(t),o=0;o<r.length;o+=1){var a=r[o];if(n.indexOf(a)>=0)return a}return!1},rh=function(e,t,r){var n=gm(e),o=gm(t),a=n[0],i=!1;return o.filter(Boolean).forEach((function(e){i=th(i||e,e)||i,r.filter(Boolean).forEach((function(e){var t=th(a,e);t&&(i=!i||Hm(t,i)?t:th(t,i))}))})),i},nh=function(e,t){return e.reduce((function(e,r){return e.concat(Wm(r,t))}),[])},oh=function(e,t){var r=Um(gm(e).length>0?document:ym(e).ownerDocument),n=Vm(e).filter(Am),o=rh(r||e,e,n),a=new Map,i=Lm(n,a),s=i.filter((function(e){var t=e.node;return Am(t)}));if(s[0]){var l,c,u,d=Lm([o],a).map((function(e){return e.node})),f=(l=d,c=s,u=new Map,c.forEach((function(e){return u.set(e.node,e)})),l.map((function(e){return u.get(e)})).filter(zm)),p=f.map((function(e){return e.node})),m=f.filter((function(e){return e.tabIndex>=0})).map((function(e){return e.node})),h=Qm(p,m,d,r,t);if(h===Zm){var b=Jm(i,m,nh(n,a))||Jm(i,p,nh(n,a));return b?{node:b}:void 0}return void 0===h?h:f[h]}},ah=function(e,t){e&&("focus"in e&&e.focus(t),"contentWindow"in e&&e.contentWindow&&e.contentWindow.focus())},ih=0,sh=!1,lh=function(e,t,r){void 0===r&&(r={});var n=oh(e,t);if(!sh&&n){if(ih>2)return sh=!0,void setTimeout((function(){sh=!1}),1);ih++,ah(n.node,r.focusOptions),ih--}};function ch(e){if(!e)return null;if("undefined"==typeof WeakRef)return function(){return e||null};var t=e?new WeakRef(e):null;return function(){return(null==t?void 0:t.deref())||null}}var uh=function(e){var t=function(e){if(!e)return null;for(var t=[],r=e;r&&r!==document.body;)t.push({current:ch(r),parent:ch(r.parentElement),left:ch(r.previousElementSibling),right:ch(r.nextElementSibling)}),r=r.parentElement;return{element:ch(e),stack:t,ownerDocument:e.ownerDocument}}(e);return function(){return function(e){var t,r,n,o,a;if(e)for(var i=e.stack,s=e.ownerDocument,l=new Map,c=0,u=i;c<u.length;c++){var d=u[c],f=null===(t=d.parent)||void 0===t?void 0:t.call(d);if(f&&s.contains(f)){for(var p=null===(r=d.left)||void 0===r?void 0:r.call(d),m=d.current(),h=f.contains(m)?m:void 0,b=null===(n=d.right)||void 0===n?void 0:n.call(d),v=Fm([f],l),g=null!==(a=null!==(o=null!=h?h:null==p?void 0:p.nextElementSibling)&&void 0!==o?o:b)&&void 0!==a?a:p;g;){for(var y=0,x=v;y<x.length;y++){var w=x[y];if(null==g?void 0:g.contains(w.node))return w.node}g=g.nextElementSibling}if(v.length)return v[0].node}}}(t)}},dh=function(e,t,r){void 0===t&&(t={});var n=function(e){return Object.assign({scope:document.body,cycle:!0,onlyTabbable:!0},e)}(t),o=function(e,t,r){if(!e||!t)return{};var n=gm(t);if(n.every((function(t){return!Hm(t,e)})))return{};var o=r?Fm(n,new Map):Lm(n,new Map),a=o.findIndex((function(t){return t.node===e}));return-1!==a?{prev:o[a-1],next:o[a+1],first:o[0],last:o[o.length-1]}:void 0}(e,n.scope,n.onlyTabbable);if(o){var a=r(o,n.cycle);a&&ah(a.node,n.focusOptions)}},fh=function(e,t,r){var n,o,a,i,s=(o=e,a=null===(n=t.onlyTabbable)||void 0===n||n,{first:(i=a?Fm(gm(o),new Map):Lm(gm(o),new Map))[0],last:i[i.length-1]})[r];s&&ah(s.node,t.focusOptions)};function ph(e){setTimeout(e,1)}var mh=function(e){return e&&"current"in e?e.current:e},hh=function(){return document&&document.activeElement===document.body},bh=function(){return hh()||function(e){void 0===e&&(e=document);var t=Um(e);return!!t&&vm(e.querySelectorAll("[".concat("data-no-focus-lock","]"))).some((function(e){return Hm(e,t)}))}()},vh=null,gh=null,yh=function(){return null},xh=null,wh=!1,kh=!1,Sh=function(){return!0};function _h(e,t,r,n){var o=null,a=e;do{var i=n[a];if(i.guard)i.node.dataset.focusAutoGuard&&(o=i);else{if(!i.lockItem)break;if(a!==e)return;o=null}}while((a+=r)!==t);o&&(o.node.tabIndex=0)}var Ch=function e(t,r,n){return r&&(r.host===t&&(!r.activeElement||n.contains(r.activeElement))||r.parentNode&&e(t,r.parentNode,n))},Eh=function(e){return Lm(e,new Map)},jh=function(){var e,t,r,n,o,a,i,s=!1;if(vh){var l=vh,c=l.observed,u=l.persistentFocus,d=l.autoFocus,f=l.shards,p=l.crossFrame,m=l.focusOptions,h=l.noFocusGuards,b=c||xh&&xh.portaledElement;if(hh()&&gh&&gh!==document.body&&(!document.body.contains(gh)||!Eh([(i=gh).parentNode]).some((function(e){return e.node===i})))){var v=yh();v&&v.focus()}var g=document&&document.activeElement;if(b){var y=[b].concat(f.map(mh).filter(Boolean));if(g&&!function(e){return(vh.whiteList||Sh)(e)}(g)||(u||function(){if(!(p?Boolean(wh):"meanwhile"===wh)||!h||!gh||kh)return!1;var e=Eh(y),t=e.findIndex((function(e){return e.node===gh}));return 0===t||t===e.length-1}()||!bh()||!gh&&d)&&(b&&!(Gm(y)||g&&function(e,t){return t.some((function(t){return Ch(e,t,t)}))}(g,y)||(a=g,xh&&xh.portaledElement===a))&&(document&&!gh&&g&&!d?(g.blur&&g.blur(),document.body.focus()):(s=lh(y,gh,{focusOptions:m}),xh={})),(gh=document&&document.activeElement)!==document.body&&(yh=uh(gh)),wh=!1),document&&g!==document.activeElement&&document.querySelector("[data-focus-auto-guard]")){var x=document&&document.activeElement,w=(t=Vm(e=y).filter(Am),r=rh(e,e,t),n=Pm($m([r],!0),!0,!0),o=$m(t,!1),n.map((function(e){var t=e.node;return{node:t,index:e.index,lockItem:o.indexOf(t)>=0,guard:Om(t)}}))),k=w.map((function(e){return e.node})).indexOf(x);k>-1&&(w.filter((function(e){var t=e.guard,r=e.node;return t&&r.dataset.focusAutoGuard})).forEach((function(e){return e.node.removeAttribute("tabIndex")})),_h(k,w.length,1,w),_h(k,-1,-1,w))}}}return s},Nh=function(e){jh()&&e&&(e.stopPropagation(),e.preventDefault())},Oh=function(){return ph(jh)},Ah=function(){kh=!0},zh=function(){kh=!1,wh="just",ph((function(){wh="meanwhile"}))};var Ih={moveFocusInside:lh,focusInside:Gm,focusNextElement:function(e,t){void 0===t&&(t={}),dh(e,t,(function(e,t){var r=e.next,n=e.first;return r||t&&n}))},focusPrevElement:function(e,t){void 0===t&&(t={}),dh(e,t,(function(e,t){var r=e.prev,n=e.last;return r||t&&n}))},focusFirstElement:function(e,t){void 0===t&&(t={}),fh(e,t,"first")},focusLastElement:function(e,t){void 0===t&&(t={}),fh(e,t,"last")},captureFocusRestore:uh};sm.assignSyncMedium((function(e){var t=e.target,r=e.currentTarget;r.contains(t)||(xh={observerNode:r,portaledElement:t})})),lm.assignMedium(Oh),cm.assignMedium((function(e){return e(Ih)}));const Ph=(Rh=function(e){return e.filter((function(e){return!e.disabled}))},Th=function(e){var t=e.slice(-1)[0];t&&!vh&&(document.addEventListener("focusin",Nh),document.addEventListener("focusout",Oh),window.addEventListener("focus",Ah),window.addEventListener("blur",zh));var r=vh,n=r&&t&&t.id===r.id;vh=t,r&&!n&&(r.onDeactivation(),e.filter((function(e){return e.id===r.id})).length||r.returnFocus(!t)),t?(gh=null,n&&r.observed===t.observed||t.onActivation(Ih),jh(),ph(jh)):(document.removeEventListener("focusin",Nh),document.removeEventListener("focusout",Oh),window.removeEventListener("focus",Ah),window.removeEventListener("blur",zh),gh=null)},function(e){var t,r=[];function n(){t=Rh(r.map((function(e){return e.props}))),Th(t)}var a,s,l,c=function(o){var a,s;function l(){return o.apply(this,arguments)||this}s=o,(a=l).prototype=Object.create(s.prototype),a.prototype.constructor=a,mm(a,s),l.peek=function(){return t};var c=l.prototype;return c.componentDidMount=function(){r.push(this),n()},c.componentDidUpdate=function(){n()},c.componentWillUnmount=function(){var e=r.indexOf(this);r.splice(e,1),n()},c.render=function(){return i.createElement(e,this.props)},l}(o.PureComponent);return a=c,s="displayName",l="SideEffect("+function(e){return e.displayName||e.name||"Component"}(e)+")",(s=bm(s))in a?Object.defineProperty(a,s,{value:l,enumerable:!0,configurable:!0,writable:!0}):a[s]=l,c})((function(){return null}));var Rh,Th,Mh=o.forwardRef((function(e,t){return i.createElement(pm,we({sideCar:Ph,ref:t},e))})),$h=pm.propTypes||{};function Dh(e){return null!=e&&"object"==typeof e&&"nodeType"in e&&e.nodeType===Node.ELEMENT_NODE}function Bh(e){var t;if(!Dh(e))return!1;return e instanceof(null!=(t=e.ownerDocument.defaultView)?t:window).HTMLElement}function Fh(e){return Dh(e)?e.ownerDocument:document}function Lh(e){return["html","body","#document"].includes(e.localName)?e.ownerDocument.body:Bh(e)&&function(e){const t=e.ownerDocument.defaultView||window,{overflow:r,overflowX:n,overflowY:o}=t.getComputedStyle(e);return/auto|scroll|overlay|hidden/.test(r+o+n)}(e)?e:Lh(function(e){return"html"===e.localName?e:e.assignedSlot||e.parentElement||e.ownerDocument.documentElement}(e))}$h.sideCar,function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}}($h,["sideCar"]),Mh.propTypes={};var Wh=e=>e.hasAttribute("tabindex");function Hh(e){return!(!e.parentElement||!Hh(e.parentElement))||e.hidden}function qh(e){if(!Bh(e)||Hh(e)||function(e){return!0===Boolean(e.getAttribute("disabled"))||!0===Boolean(e.getAttribute("aria-disabled"))}(e))return!1;const{localName:t}=e;if(["input","select","textarea","button"].indexOf(t)>=0)return!0;const r={a:()=>e.hasAttribute("href"),audio:()=>e.hasAttribute("controls"),video:()=>e.hasAttribute("controls")};return t in r?r[t]():!!function(e){const t=e.getAttribute("contenteditable");return"false"!==t&&null!=t}(e)||Wh(e)}function Vh(e){return!!e&&(Bh(e)&&qh(e)&&!(e=>Wh(e)&&-1===e.tabIndex)(e))}var Uh,Gh=["input:not(:disabled):not([disabled])","select:not(:disabled):not([disabled])","textarea:not(:disabled):not([disabled])","embed","iframe","object","a[href]","area[href]","button:not(:disabled):not([disabled])","[tabindex]","audio[controls]","video[controls]","*[tabindex]:not([aria-disabled])","*[contenteditable]"].join();var Xh=null!=(Uh=Mh.default)?Uh:Mh,Yh=e=>{const{initialFocusRef:t,finalFocusRef:r,contentRef:n,restoreFocus:a,children:i,isDisabled:s,autoFocus:l,persistentFocus:c,lockFocusAcrossFrames:u}=e,d=o.useCallback((()=>{if(null==t?void 0:t.current)t.current.focus();else if(null==n?void 0:n.current){0===function(e){const t=Array.from(e.querySelectorAll(Gh));return t.unshift(e),t.filter((e=>qh(e)&&(e=>e.offsetWidth>0&&e.offsetHeight>0)(e)))}(n.current).length&&requestAnimationFrame((()=>{var e;null==(e=n.current)||e.focus()}))}}),[t,n]),f=o.useCallback((()=>{var e;null==(e=null==r?void 0:r.current)||e.focus()}),[r]),p=a&&!r;return b.jsx(Xh,{crossFrame:u,persistentFocus:c,autoFocus:l,disabled:s,onActivation:d,onDeactivation:f,returnFocus:p,children:i})};Yh.displayName="FocusLock";var Kh,Zh,Qh,Jh,eb=Gc?o.useLayoutEffect:o.useEffect;function tb(e,t=[]){const r=o.useRef(e);return eb((()=>{r.current=e})),o.useCallback(((...e)=>{var t;return null==(t=r.current)?void 0:t.call(r,...e)}),t)}function rb(e={}){const{onClose:t,onOpen:r,isOpen:n,id:a}=e,i=tb(r),s=tb(t),[l,c]=o.useState(e.defaultIsOpen||!1),[u,d]=function(e,t){const r=void 0!==e;return[r,r&&void 0!==e?e:t]}(n,l),f=function(e,t){const r=o.useId();return o.useMemo((()=>e||[t,r].filter(Boolean).join("-")),[e,t,r])}(a,"disclosure"),p=o.useCallback((()=>{u||c(!1),null==s||s()}),[u,s]),m=o.useCallback((()=>{u||c(!0),null==i||i()}),[u,i]),h=o.useCallback((()=>{(d?p:m)()}),[d,m,p]);return{isOpen:!!d,onOpen:m,onClose:p,onToggle:h,isControlled:u,getButtonProps:(e={})=>({...e,"aria-expanded":d,"aria-controls":f,onClick:Qc(e.onClick,h)}),getDisclosureProps:(e={})=>({...e,hidden:!d,id:f})}}var nb=function(){if(Jh)return Qh;Jh=1;var e=Zh?Kh:(Zh=1,Kh=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,r=[],n=0;n<e.rangeCount;n++)r.push(e.getRangeAt(n));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||r.forEach((function(t){e.addRange(t)})),t&&t.focus()}}),t={"text/plain":"Text","text/html":"Url",default:"Text"};return Qh=function(r,n){var o,a,i,s,l,c=!1;n||(n={}),n.debug;try{if(a=e(),i=document.createRange(),s=document.getSelection(),(l=document.createElement("span")).textContent=r,l.ariaHidden="true",l.style.all="unset",l.style.position="fixed",l.style.top=0,l.style.clip="rect(0, 0, 0, 0)",l.style.whiteSpace="pre",l.style.webkitUserSelect="text",l.style.MozUserSelect="text",l.style.msUserSelect="text",l.style.userSelect="text",l.addEventListener("copy",(function(e){if(e.stopPropagation(),n.format)if(e.preventDefault(),void 0===e.clipboardData){window.clipboardData.clearData();var o=t[n.format]||t.default;window.clipboardData.setData(o,r)}else e.clipboardData.clearData(),e.clipboardData.setData(n.format,r);n.onCopy&&(e.preventDefault(),n.onCopy(e.clipboardData))})),document.body.appendChild(l),i.selectNodeContents(l),s.addRange(i),!document.execCommand("copy"))throw new Error("copy command was unsuccessful");c=!0}catch(u){try{window.clipboardData.setData(n.format||"text",r),n.onCopy&&n.onCopy(window.clipboardData),c=!0}catch(d){o=function(e){var t=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C";return e.replace(/#{\s*key\s*}/g,t)}("message"in n?n.message:"Copy to clipboard: #{key}, Enter"),window.prompt(o,r)}}finally{s&&("function"==typeof s.removeRange?s.removeRange(i):s.removeAllRanges()),l&&document.body.removeChild(l),a()}return c},Qh}();const ob=r(nb);function ab(e,t={}){const[r,n]=o.useState(!1),[a,i]=o.useState(e);o.useEffect((()=>i(e)),[e]);const{timeout:s=1500,...l}="number"==typeof t?{timeout:t}:t,c=o.useCallback((()=>{const e=ob(a,l);n(e)}),[a,l]);return o.useEffect((()=>{let e=null;return r&&(e=window.setTimeout((()=>{n(!1)}),s)),()=>{e&&window.clearTimeout(e)}}),[s,r]),{value:a,setValue:i,onCopy:c,hasCopied:r}}var[ib,sb]=ot({name:"InputGroupStylesContext",errorMessage:"useInputGroupStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<InputGroup />\" "}),lb=Mu((function(e,t){const r=yu("Input",e),{children:n,className:a,...i}=En(e),s=Ot("chakra-input__group",a),l={},c=Pf(n),u=r.field;c.forEach((e=>{var t,n;r&&(u&&"InputLeftElement"===e.type.id&&(l.paddingStart=null!=(t=u.height)?t:u.h),u&&"InputRightElement"===e.type.id&&(l.paddingEnd=null!=(n=u.height)?n:u.h),"InputRightAddon"===e.type.id&&(l.borderEndRadius=0),"InputLeftAddon"===e.type.id&&(l.borderStartRadius=0))}));const d=c.map((t=>{var r,n;const a=Su({size:(null==(r=t.props)?void 0:r.size)||e.size,variant:(null==(n=t.props)?void 0:n.variant)||e.variant});return"Input"!==t.type.id?o.cloneElement(t,a):o.cloneElement(t,Object.assign(a,l,t.props))}));return b.jsx(Tu.div,{className:s,ref:t,__css:{width:"100%",display:"flex",position:"relative",isolation:"isolate",...r.group},"data-group":!0,...i,children:b.jsx(ib,{value:r,children:d})})}));lb.displayName="InputGroup";var cb=Tu("div",{baseStyle:{display:"flex",alignItems:"center",justifyContent:"center",position:"absolute",top:"0",zIndex:2}}),ub=Mu((function(e,t){var r,n;const{placement:o="left",...a}=e,i=sb(),s=i.field,l="left"===o?"insetStart":"insetEnd",c={[l]:"0",width:null!=(r=null==s?void 0:s.height)?r:null==s?void 0:s.h,height:null!=(n=null==s?void 0:s.height)?n:null==s?void 0:s.h,fontSize:null==s?void 0:s.fontSize,...i.element};return b.jsx(cb,{ref:t,__css:c,...a})}));ub.id="InputElement",ub.displayName="InputElement";var db=Mu((function(e,t){const{className:r,...n}=e,o=Ot("chakra-input__left-element",r);return b.jsx(ub,{ref:t,placement:"left",className:o,...n})}));db.id="InputLeftElement",db.displayName="InputLeftElement";var fb=Mu((function(e,t){const{className:r,...n}=e,o=Ot("chakra-input__right-element",r);return b.jsx(ub,{ref:t,placement:"right",className:o,...n})}));fb.id="InputRightElement",fb.displayName="InputRightElement";var pb=Mu((function(e,t){const{htmlSize:r,...n}=e,o=yu("Input",n),a=vp(En(n)),i=Ot("chakra-input",e.className);return b.jsx(Tu.input,{size:r,...a,__css:o.field,ref:t,className:i})}));pb.displayName="Input",pb.id="Input";var mb=Mu((function(e,t){const r=gu("Link",e),{className:n,isExternal:o,...a}=En(e);return b.jsx(Tu.a,{target:o?"_blank":void 0,rel:o?"noopener":void 0,ref:t,className:Ot("chakra-link",n),...a,__css:r})}));mb.displayName="Link";var[hb,bb]=ot({name:"ListStylesContext",errorMessage:"useListStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<List />\" "}),vb=Mu((function(e,t){const r=yu("List",e),{children:n,styleType:o="none",stylePosition:a,spacing:i,...s}=En(e),l=Pf(n),c="& > *:not(style) ~ *:not(style)",u=i?{[c]:{mt:i}}:{};return b.jsx(hb,{value:r,children:b.jsx(Tu.ul,{ref:t,listStyleType:o,listStylePosition:a,role:"list",__css:{...r.container,...u},...s,children:l})})}));vb.displayName="List",Mu(((e,t)=>{const{as:r,...n}=e;return b.jsx(vb,{ref:t,as:"ol",styleType:"decimal",marginStart:"1em",...n})})).displayName="OrderedList";var gb=Mu((function(e,t){const{as:r,...n}=e;return b.jsx(vb,{ref:t,as:"ul",styleType:"initial",marginStart:"1em",...n})}));gb.displayName="UnorderedList";var yb=Mu((function(e,t){const r=bb();return b.jsx(Tu.li,{ref:t,...e,__css:r.item})}));yb.displayName="ListItem";var xb=Mu((function(e,t){const r=bb();return b.jsx(ed,{ref:t,role:"presentation",...e,__css:r.icon})}));xb.displayName="ListIcon";var wb=Mu((function(e,t){const{templateAreas:r,gap:n,rowGap:o,columnGap:a,column:i,row:s,autoFlow:l,autoRows:c,templateRows:u,autoColumns:d,templateColumns:f,...p}=e,m={display:"grid",gridTemplateAreas:r,gridGap:n,gridRowGap:o,gridColumnGap:a,gridAutoColumns:d,gridColumn:i,gridRow:s,gridAutoFlow:l,gridAutoRows:c,gridTemplateRows:u,gridTemplateColumns:f};return b.jsx(Tu.div,{ref:t,__css:m,...p})}));wb.displayName="Grid";var kb=Object.freeze(["base","sm","md","lg","xl","2xl"]);function Sb(e,t){return Array.isArray(e)?e.map((e=>null===e?null:t(e))):At(e)?Object.keys(e).reduce(((r,n)=>(r[n]=t(e[n]),r)),{}):null!=e?t(e):null}var _b=Mu((function(e,t){const{columns:r,spacingX:n,spacingY:o,spacing:a,minChildWidth:i,...s}=e,l=bt(),c=i?function(e,t){return Sb(e,(e=>{const r=Nt("sizes",e,"number"==typeof(n=e)?`${n}px`:n)(t);var n;return null===e?null:`repeat(auto-fit, minmax(${r}, 1fr))`}))}(i,l):Sb(r,(e=>null===e?null:`repeat(${e}, minmax(0, 1fr))`));return b.jsx(wb,{ref:t,gap:a,columnGap:n,rowGap:o,templateColumns:c,...s})}));_b.displayName="SimpleGrid";var Cb=Tu("div",{baseStyle:{flex:1,justifySelf:"stretch",alignSelf:"stretch"}});Cb.displayName="Spacer";var Eb=Mu((function(e,t){const r=gu("Text",e),{className:n,align:o,decoration:a,casing:i,...s}=En(e),l=Su({textAlign:e.align,textDecoration:e.decoration,textTransform:e.casing});return b.jsx(Tu.p,{ref:t,className:Ot("chakra-text",e.className),...l,...s,__css:r})}));Eb.displayName="Text";var jb=Mu((function(e,t){const{spacing:r="0.5rem",spacingX:n,spacingY:a,children:i,justify:s,direction:l,align:c,className:u,shouldWrapChildren:d,...f}=e,p=o.useMemo((()=>d?o.Children.map(i,((e,t)=>b.jsx(Nb,{children:e},t))):i),[i,d]);return b.jsx(Tu.div,{ref:t,className:Ot("chakra-wrap",u),...f,children:b.jsx(Tu.ul,{className:"chakra-wrap__list",__css:{display:"flex",flexWrap:"wrap",justifyContent:s,alignItems:c,flexDirection:l,listStyleType:"none",gap:r,columnGap:n,rowGap:a,padding:"0"},children:p})})}));jb.displayName="Wrap";var Nb=Mu((function(e,t){const{className:r,...n}=e;return b.jsx(Tu.li,{ref:t,__css:{display:"flex",alignItems:"flex-start"},className:Ot("chakra-wrap__listitem",r),...n})}));Nb.displayName="WrapItem";var Ob=e=>b.jsx(Tu.div,{className:"chakra-stack__item",...e,__css:{display:"inline-block",flex:"0 0 auto",minWidth:0,...e.__css}});Ob.displayName="StackItem";var Ab=Mu(((e,t)=>{const{isInline:r,direction:n,align:a,justify:i,spacing:s="0.5rem",wrap:l,children:c,divider:u,className:d,shouldWrapChildren:f,...p}=e,m=r?"row":null!=n?n:"column",h=o.useMemo((()=>function(e){const{spacing:t,direction:r}=e,n={column:{my:t,mx:0,borderLeftWidth:0,borderBottomWidth:"1px"},"column-reverse":{my:t,mx:0,borderLeftWidth:0,borderBottomWidth:"1px"},row:{mx:t,my:0,borderLeftWidth:"1px",borderBottomWidth:0},"row-reverse":{mx:t,my:0,borderLeftWidth:"1px",borderBottomWidth:0}};return{"&":Sb(r,(e=>n[e]))}}({spacing:s,direction:m})),[s,m]),v=!!u,g=!f&&!v,y=o.useMemo((()=>{const e=Pf(c);return g?e:e.map(((t,r)=>{const n=void 0!==t.key?t.key:r,a=r+1===e.length,i=f?b.jsx(Ob,{children:t},n):t;if(!v)return i;const s=o.cloneElement(u,{__css:h}),l=a?null:s;return b.jsxs(o.Fragment,{children:[i,l]},n)}))}),[u,h,v,g,f,c]),x=Ot("chakra-stack",d);return b.jsx(Tu.div,{ref:t,display:"flex",alignItems:a,justifyContent:i,flexDirection:m,flexWrap:l,gap:v?void 0:s,className:x,...p,children:y})}));Ab.displayName="Stack";var zb=Mu(((e,t)=>b.jsx(Ab,{align:"center",...e,direction:"column",ref:t})));zb.displayName="VStack";var Ib=Mu(((e,t)=>b.jsx(Ab,{align:"center",...e,direction:"row",ref:t})));function Pb(e){return Sb(e,(e=>"auto"===e?"auto":`span ${e}/span ${e}`))}Ib.displayName="HStack";var Rb=Mu((function(e,t){const{area:r,colSpan:n,colStart:o,colEnd:a,rowEnd:i,rowSpan:s,rowStart:l,...c}=e,u=Su({gridArea:r,gridColumn:Pb(n),gridRow:Pb(s),gridColumnStart:o,gridColumnEnd:a,gridRowStart:l,gridRowEnd:i});return b.jsx(Tu.div,{ref:t,__css:u,...c})}));Rb.displayName="GridItem";var Tb=Mu((function(e,t){const r=gu("Heading",e),{className:n,...o}=En(e);return b.jsx(Tu.h2,{ref:t,className:Ot("chakra-heading",e.className),...o,__css:r})}));Tb.displayName="Heading";var Mb=Tu("div");Mb.displayName="Box";var $b=Mu((function(e,t){const{size:r,centerContent:n=!0,...o}=e,a=n?{display:"flex",alignItems:"center",justifyContent:"center"}:{};return b.jsx(Mb,{ref:t,boxSize:r,__css:{...a,flexShrink:0,flexGrow:0},...o})}));$b.displayName="Square";var Db=Mu((function(e,t){const{size:r,...n}=e;return b.jsx($b,{size:r,ref:t,borderRadius:"9999px",...n})}));Db.displayName="Circle";var Bb=Mu((function(e,t){const{ratio:r=4/3,children:n,className:a,...i}=e,s=o.Children.only(n),l=Ot("chakra-aspect-ratio",a);return b.jsx(Tu.div,{ref:t,position:"relative",className:l,_before:{height:0,content:'""',display:"block",paddingBottom:Sb(r,(e=>1/e*100+"%"))},__css:{"& > *:not(style)":{overflow:"hidden",position:"absolute",top:"0",right:"0",bottom:"0",left:"0",display:"flex",justifyContent:"center",alignItems:"center",width:"100%",height:"100%"},"& > img, & > video":{objectFit:"cover"}},...i,children:s})}));Bb.displayName="AspectRatio";var Fb=Mu((function(e,t){const r=gu("Badge",e),{className:n,...o}=En(e);return b.jsx(Tu.span,{ref:t,className:Ot("chakra-badge",e.className),...o,__css:{display:"inline-block",whiteSpace:"nowrap",verticalAlign:"middle",...r}})}));Fb.displayName="Badge";var Lb=Tu("div",{baseStyle:{display:"flex",alignItems:"center",justifyContent:"center"}});Lb.displayName="Center";var Wb={horizontal:{insetStart:"50%",transform:"translateX(-50%)"},vertical:{top:"50%",transform:"translateY(-50%)"},both:{insetStart:"50%",top:"50%",transform:"translate(-50%, -50%)"}};Mu((function(e,t){const{axis:r="both",...n}=e;return b.jsx(Tu.div,{ref:t,__css:Wb[r],...n,position:"absolute"})}));var Hb=Mu((function(e,t){const r=gu("Code",e),{className:n,...o}=En(e);return b.jsx(Tu.code,{ref:t,className:Ot("chakra-code",e.className),...o,__css:{display:"inline-block",...r}})}));Hb.displayName="Code";var qb=Mu((function(e,t){const{className:r,centerContent:n,...o}=En(e),a=gu("Container",e);return b.jsx(Tu.div,{ref:t,className:Ot("chakra-container",r),...o,__css:{...a,...n&&{display:"flex",flexDirection:"column",alignItems:"center"}}})}));qb.displayName="Container";var Vb=Mu((function(e,t){const{borderLeftWidth:r,borderBottomWidth:n,borderTopWidth:o,borderRightWidth:a,borderWidth:i,borderStyle:s,borderColor:l,...c}=gu("Divider",e),{className:u,orientation:d="horizontal",__css:f,...p}=En(e),m={vertical:{borderLeftWidth:r||a||i||"1px",height:"100%"},horizontal:{borderBottomWidth:n||o||i||"1px",width:"100%"}};return b.jsx(Tu.hr,{ref:t,"aria-orientation":d,...p,__css:{...c,border:"0",borderColor:l,borderStyle:s,...m[d],...f},className:Ot("chakra-divider",u)})}));Vb.displayName="Divider";var Ub=Mu((function(e,t){const{direction:r,align:n,justify:o,wrap:a,basis:i,grow:s,shrink:l,...c}=e,u={display:"flex",flexDirection:r,alignItems:n,justifyContent:o,flexWrap:a,flexBasis:i,flexGrow:s,flexShrink:l};return b.jsx(Tu.div,{ref:t,__css:u,...c})}));function Gb(e,t={}){const{ssr:r=!0,fallback:n}=t,{getWindow:a}=function({defer:e}={}){const[,t]=o.useReducer((e=>e+1),0);return lt((()=>{e&&t()}),[e]),o.useContext(Lu)}(),i=Array.isArray(e)?e:[e];let s=Array.isArray(n)?n:[n];s=s.filter((e=>null!=e));const[l,c]=o.useState((()=>i.map(((e,t)=>({media:e,matches:r?!!s[t]:a().matchMedia(e).matches})))));return o.useEffect((()=>{const e=a();c(i.map((t=>({media:t,matches:e.matchMedia(t).matches}))));const t=i.map((t=>e.matchMedia(t))),r=e=>{c((t=>t.slice().map((t=>t.media===e.media?{...t,matches:e.matches}:t))))};return t.forEach((e=>{"function"==typeof e.addListener?e.addListener(r):e.addEventListener("change",r)})),()=>{t.forEach((e=>{"function"==typeof e.removeListener?e.removeListener(r):e.removeEventListener("change",r)}))}}),[a]),l.map((e=>e.matches))}function Xb(e,t){var r;const n=function(e){var t,r;const n=At(e)?e:{fallback:null!=e?e:"base"},o=bt().__breakpoints.details.map((({minMaxQuery:e,breakpoint:t})=>({breakpoint:t,query:e.replace("@media screen and ","")}))),a=o.map((e=>e.breakpoint===n.fallback)),i=Gb(o.map((e=>e.query)),{fallback:a,ssr:n.ssr});return null!=(r=null==(t=o[i.findIndex((e=>1==e))])?void 0:t.breakpoint)?r:n.fallback}(At(t)?t:{fallback:"base"}),o=bt();if(!n)return;const a=Array.from((null==(r=o.__breakpoints)?void 0:r.keys)||[]),i=Array.isArray(e)?Object.fromEntries(Object.entries(function(e,t=kb){const r={};return e.forEach(((e,n)=>{const o=t[n];null!=e&&(r[o]=e)})),r}(e,a)).map((([e,t])=>[e,t]))):e;return function(e,t,r=kb){let n=Object.keys(e).indexOf(t);if(-1!==n)return e[t];let o=r.indexOf(t);for(;o>=0;){const t=r[o];if(e.hasOwnProperty(t)){n=o;break}o-=1}if(-1!==n)return e[r[n]]}(i,n,a)}function Yb(e={}){const{timeout:t=300,preventDefault:r=()=>!0}=e,[n,a]=o.useState([]),i=o.useRef(),s=()=>{i.current&&(clearTimeout(i.current),i.current=null)};return o.useEffect((()=>s),[]),function(e){return o=>{if("Backspace"===o.key){const e=[...n];return e.pop(),void a(e)}if(function(e){const{key:t}=e;return 1===t.length||t.length>1&&/[^a-zA-Z0-9]/.test(t)}(o)){const l=n.concat(o.key);r(o)&&(o.preventDefault(),o.stopPropagation()),a(l),e(l.join("")),s(),i.current=setTimeout((()=>{a([]),i.current=null}),t)}}}}function Kb(e){const t=e.target,{tagName:r,isContentEditable:n}=t;return"INPUT"!==r&&"TEXTAREA"!==r&&!0!==n}function Zb(e={}){const{ref:t,isDisabled:r,isFocusable:n,clickOnEnter:a=!0,clickOnSpace:i=!0,onMouseDown:s,onMouseUp:l,onClick:c,onKeyDown:u,onKeyUp:d,tabIndex:f,onMouseOver:p,onMouseLeave:m,...h}=e,[b,v]=o.useState(!0),[g,y]=o.useState(!1),x=function(){const e=o.useRef(new Map),t=e.current,r=o.useCallback(((t,r,n,o)=>{e.current.set(n,{type:r,el:t,options:o}),t.addEventListener(r,n,o)}),[]),n=o.useCallback(((t,r,n,o)=>{t.removeEventListener(r,n,o),e.current.delete(n)}),[]);return o.useEffect((()=>()=>{t.forEach(((e,t)=>{n(e.el,e.type,t,e.options)}))}),[n,t]),{add:r,remove:n}}(),w=b?f:f||0,k=r&&!n,S=o.useCallback((e=>{if(r)return e.stopPropagation(),void e.preventDefault();e.currentTarget.focus(),null==c||c(e)}),[r,c]),_=o.useCallback((e=>{g&&Kb(e)&&(e.preventDefault(),e.stopPropagation(),y(!1),x.remove(document,"keyup",_,!1))}),[g,x]),C=o.useCallback((e=>{if(null==u||u(e),r||e.defaultPrevented||e.metaKey)return;if(!Kb(e.nativeEvent)||b)return;const t=a&&"Enter"===e.key;if(i&&" "===e.key&&(e.preventDefault(),y(!0)),t){e.preventDefault();e.currentTarget.click()}x.add(document,"keyup",_,!1)}),[r,b,u,a,i,x,_]),E=o.useCallback((e=>{if(null==d||d(e),r||e.defaultPrevented||e.metaKey)return;if(!Kb(e.nativeEvent)||b)return;if(i&&" "===e.key){e.preventDefault(),y(!1);e.currentTarget.click()}}),[i,b,r,d]),j=o.useCallback((e=>{0===e.button&&(y(!1),x.remove(document,"mouseup",j,!1))}),[x]),N=o.useCallback((e=>{if(0!==e.button)return;if(r)return e.stopPropagation(),void e.preventDefault();b||y(!0);e.currentTarget.focus({preventScroll:!0}),x.add(document,"mouseup",j,!1),null==s||s(e)}),[r,b,s,x,j]),O=o.useCallback((e=>{0===e.button&&(b||y(!1),null==l||l(e))}),[l,b]),A=o.useCallback((e=>{r?e.preventDefault():null==p||p(e)}),[r,p]),z=o.useCallback((e=>{g&&(e.preventDefault(),y(!1)),null==m||m(e)}),[g,m]),I=Pd(t,(e=>{e&&"BUTTON"!==e.tagName&&v(!1)}));return b?{...h,ref:I,type:"button","aria-disabled":k?void 0:r,disabled:k,onClick:S,onMouseDown:s,onMouseUp:l,onKeyUp:d,onKeyDown:u,onMouseOver:p,onMouseLeave:m}:{...h,ref:I,role:"button","data-active":Rt(g),"aria-disabled":r?"true":void 0,tabIndex:k?void 0:w,onClick:S,onMouseDown:N,onMouseUp:O,onKeyUp:E,onKeyDown:C,onMouseOver:A,onMouseLeave:z}}function Qb(e){const t=e.current;if(!t)return!1;const r=Fh(t).activeElement;return!!r&&(!t.contains(r)&&!!Vh(r))}Ub.displayName="Flex";var Jb=(e,t)=>({var:e,varRef:t?`var(${e}, ${t})`:`var(${e})`}),ev={arrowShadowColor:Jb("--popper-arrow-shadow-color"),arrowSize:Jb("--popper-arrow-size","8px"),arrowSizeHalf:Jb("--popper-arrow-size-half"),arrowBg:Jb("--popper-arrow-bg"),transformOrigin:Jb("--popper-transform-origin"),arrowOffset:Jb("--popper-arrow-offset")};var tv={top:"bottom center","top-start":"bottom left","top-end":"bottom right",bottom:"top center","bottom-start":"top left","bottom-end":"top right",left:"right center","left-start":"right top","left-end":"right bottom",right:"left center","right-start":"left top","right-end":"left bottom"},rv={scroll:!0,resize:!0};function nv(e){let t;return t="object"==typeof e?{enabled:!0,options:{...rv,...e}}:{enabled:e,options:rv},t}var ov={name:"matchWidth",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:({state:e})=>{e.styles.popper.width=`${e.rects.reference.width}px`},effect:({state:e})=>()=>{const t=e.elements.reference;e.elements.popper.style.width=`${t.offsetWidth}px`}},av={name:"transformOrigin",enabled:!0,phase:"write",fn:({state:e})=>{iv(e)},effect:({state:e})=>()=>{iv(e)}},iv=e=>{var t;e.elements.popper.style.setProperty(ev.transformOrigin.var,(t=e.placement,tv[t]))},sv={name:"positionArrow",enabled:!0,phase:"afterWrite",fn:({state:e})=>{lv(e)}},lv=e=>{var t;if(!e.placement)return;const r=cv(e.placement);if((null==(t=e.elements)?void 0:t.arrow)&&r){Object.assign(e.elements.arrow.style,{[r.property]:r.value,width:ev.arrowSize.varRef,height:ev.arrowSize.varRef,zIndex:-1});const t={[ev.arrowSizeHalf.var]:`calc(${ev.arrowSize.varRef} / 2 - 1px)`,[ev.arrowOffset.var]:`calc(${ev.arrowSizeHalf.varRef} * -1)`};for(const r in t)e.elements.arrow.style.setProperty(r,t[r])}},cv=e=>e.startsWith("top")?{property:"bottom",value:ev.arrowOffset.varRef}:e.startsWith("bottom")?{property:"top",value:ev.arrowOffset.varRef}:e.startsWith("left")?{property:"right",value:ev.arrowOffset.varRef}:e.startsWith("right")?{property:"left",value:ev.arrowOffset.varRef}:void 0,uv={name:"innerArrow",enabled:!0,phase:"main",requires:["arrow"],fn:({state:e})=>{dv(e)},effect:({state:e})=>()=>{dv(e)}},dv=e=>{if(!e.elements.arrow)return;const t=e.elements.arrow.querySelector("[data-popper-arrow-inner]");if(!t)return;const r=(n=e.placement).includes("top")?"1px 1px 0px 0 var(--popper-arrow-shadow-color)":n.includes("bottom")?"-1px -1px 0px 0 var(--popper-arrow-shadow-color)":n.includes("right")?"-1px 1px 0px 0 var(--popper-arrow-shadow-color)":n.includes("left")?"1px -1px 0px 0 var(--popper-arrow-shadow-color)":void 0;var n;r&&t.style.setProperty("--popper-arrow-default-shadow",r),Object.assign(t.style,{transform:"rotate(45deg)",background:ev.arrowBg.varRef,top:0,left:0,width:"100%",height:"100%",position:"absolute",zIndex:"inherit",boxShadow:"var(--popper-arrow-shadow, var(--popper-arrow-default-shadow))"})},fv={"start-start":{ltr:"left-start",rtl:"right-start"},"start-end":{ltr:"left-end",rtl:"right-end"},"end-start":{ltr:"right-start",rtl:"left-start"},"end-end":{ltr:"right-end",rtl:"left-end"},start:{ltr:"left",rtl:"right"},end:{ltr:"right",rtl:"left"}},pv={"auto-start":"auto-end","auto-end":"auto-start","top-start":"top-end","top-end":"top-start","bottom-start":"bottom-end","bottom-end":"bottom-start"};var mv="top",hv="bottom",bv="right",vv="left",gv="auto",yv=[mv,hv,bv,vv],xv="start",wv="end",kv="viewport",Sv="popper",_v=yv.reduce((function(e,t){return e.concat([t+"-"+xv,t+"-"+wv])}),[]),Cv=[].concat(yv,[gv]).reduce((function(e,t){return e.concat([t,t+"-"+xv,t+"-"+wv])}),[]),Ev=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function jv(e){return e?(e.nodeName||"").toLowerCase():null}function Nv(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Ov(e){return e instanceof Nv(e).Element||e instanceof Element}function Av(e){return e instanceof Nv(e).HTMLElement||e instanceof HTMLElement}function zv(e){return"undefined"!=typeof ShadowRoot&&(e instanceof Nv(e).ShadowRoot||e instanceof ShadowRoot)}const Iv={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var r=t.styles[e]||{},n=t.attributes[e]||{},o=t.elements[e];Av(o)&&jv(o)&&(Object.assign(o.style,r),Object.keys(n).forEach((function(e){var t=n[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,r={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,r.popper),t.styles=r,t.elements.arrow&&Object.assign(t.elements.arrow.style,r.arrow),function(){Object.keys(t.elements).forEach((function(e){var n=t.elements[e],o=t.attributes[e]||{},a=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:r[e]).reduce((function(e,t){return e[t]="",e}),{});Av(n)&&jv(n)&&(Object.assign(n.style,a),Object.keys(o).forEach((function(e){n.removeAttribute(e)})))}))}},requires:["computeStyles"]};function Pv(e){return e.split("-")[0]}var Rv=Math.max,Tv=Math.min,Mv=Math.round;function $v(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function Dv(){return!/^((?!chrome|android).)*safari/i.test($v())}function Bv(e,t,r){void 0===t&&(t=!1),void 0===r&&(r=!1);var n=e.getBoundingClientRect(),o=1,a=1;t&&Av(e)&&(o=e.offsetWidth>0&&Mv(n.width)/e.offsetWidth||1,a=e.offsetHeight>0&&Mv(n.height)/e.offsetHeight||1);var i=(Ov(e)?Nv(e):window).visualViewport,s=!Dv()&&r,l=(n.left+(s&&i?i.offsetLeft:0))/o,c=(n.top+(s&&i?i.offsetTop:0))/a,u=n.width/o,d=n.height/a;return{width:u,height:d,top:c,right:l+u,bottom:c+d,left:l,x:l,y:c}}function Fv(e){var t=Bv(e),r=e.offsetWidth,n=e.offsetHeight;return Math.abs(t.width-r)<=1&&(r=t.width),Math.abs(t.height-n)<=1&&(n=t.height),{x:e.offsetLeft,y:e.offsetTop,width:r,height:n}}function Lv(e,t){var r=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(r&&zv(r)){var n=t;do{if(n&&e.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function Wv(e){return Nv(e).getComputedStyle(e)}function Hv(e){return["table","td","th"].indexOf(jv(e))>=0}function qv(e){return((Ov(e)?e.ownerDocument:e.document)||window.document).documentElement}function Vv(e){return"html"===jv(e)?e:e.assignedSlot||e.parentNode||(zv(e)?e.host:null)||qv(e)}function Uv(e){return Av(e)&&"fixed"!==Wv(e).position?e.offsetParent:null}function Gv(e){for(var t=Nv(e),r=Uv(e);r&&Hv(r)&&"static"===Wv(r).position;)r=Uv(r);return r&&("html"===jv(r)||"body"===jv(r)&&"static"===Wv(r).position)?t:r||function(e){var t=/firefox/i.test($v());if(/Trident/i.test($v())&&Av(e)&&"fixed"===Wv(e).position)return null;var r=Vv(e);for(zv(r)&&(r=r.host);Av(r)&&["html","body"].indexOf(jv(r))<0;){var n=Wv(r);if("none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||-1!==["transform","perspective"].indexOf(n.willChange)||t&&"filter"===n.willChange||t&&n.filter&&"none"!==n.filter)return r;r=r.parentNode}return null}(e)||t}function Xv(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Yv(e,t,r){return Rv(e,Tv(t,r))}function Kv(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Zv(e,t){return t.reduce((function(t,r){return t[r]=e,t}),{})}const Qv={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,r=e.state,n=e.name,o=e.options,a=r.elements.arrow,i=r.modifiersData.popperOffsets,s=Pv(r.placement),l=Xv(s),c=[vv,bv].indexOf(s)>=0?"height":"width";if(a&&i){var u=function(e,t){return Kv("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:Zv(e,yv))}(o.padding,r),d=Fv(a),f="y"===l?mv:vv,p="y"===l?hv:bv,m=r.rects.reference[c]+r.rects.reference[l]-i[l]-r.rects.popper[c],h=i[l]-r.rects.reference[l],b=Gv(a),v=b?"y"===l?b.clientHeight||0:b.clientWidth||0:0,g=m/2-h/2,y=u[f],x=v-d[c]-u[p],w=v/2-d[c]/2+g,k=Yv(y,w,x),S=l;r.modifiersData[n]=((t={})[S]=k,t.centerOffset=k-w,t)}},effect:function(e){var t=e.state,r=e.options.element,n=void 0===r?"[data-popper-arrow]":r;null!=n&&("string"!=typeof n||(n=t.elements.popper.querySelector(n)))&&Lv(t.elements.popper,n)&&(t.elements.arrow=n)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Jv(e){return e.split("-")[1]}var eg={top:"auto",right:"auto",bottom:"auto",left:"auto"};function tg(e){var t,r=e.popper,n=e.popperRect,o=e.placement,a=e.variation,i=e.offsets,s=e.position,l=e.gpuAcceleration,c=e.adaptive,u=e.roundOffsets,d=e.isFixed,f=i.x,p=void 0===f?0:f,m=i.y,h=void 0===m?0:m,b="function"==typeof u?u({x:p,y:h}):{x:p,y:h};p=b.x,h=b.y;var v=i.hasOwnProperty("x"),g=i.hasOwnProperty("y"),y=vv,x=mv,w=window;if(c){var k=Gv(r),S="clientHeight",_="clientWidth";if(k===Nv(r)&&"static"!==Wv(k=qv(r)).position&&"absolute"===s&&(S="scrollHeight",_="scrollWidth"),o===mv||(o===vv||o===bv)&&a===wv)x=hv,h-=(d&&k===w&&w.visualViewport?w.visualViewport.height:k[S])-n.height,h*=l?1:-1;if(o===vv||(o===mv||o===hv)&&a===wv)y=bv,p-=(d&&k===w&&w.visualViewport?w.visualViewport.width:k[_])-n.width,p*=l?1:-1}var C,E=Object.assign({position:s},c&&eg),j=!0===u?function(e,t){var r=e.x,n=e.y,o=t.devicePixelRatio||1;return{x:Mv(r*o)/o||0,y:Mv(n*o)/o||0}}({x:p,y:h},Nv(r)):{x:p,y:h};return p=j.x,h=j.y,l?Object.assign({},E,((C={})[x]=g?"0":"",C[y]=v?"0":"",C.transform=(w.devicePixelRatio||1)<=1?"translate("+p+"px, "+h+"px)":"translate3d("+p+"px, "+h+"px, 0)",C)):Object.assign({},E,((t={})[x]=g?h+"px":"",t[y]=v?p+"px":"",t.transform="",t))}const rg={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,r=e.options,n=r.gpuAcceleration,o=void 0===n||n,a=r.adaptive,i=void 0===a||a,s=r.roundOffsets,l=void 0===s||s,c={placement:Pv(t.placement),variation:Jv(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:o,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,tg(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:i,roundOffsets:l})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,tg(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var ng={passive:!0};const og={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,r=e.instance,n=e.options,o=n.scroll,a=void 0===o||o,i=n.resize,s=void 0===i||i,l=Nv(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&c.forEach((function(e){e.addEventListener("scroll",r.update,ng)})),s&&l.addEventListener("resize",r.update,ng),function(){a&&c.forEach((function(e){e.removeEventListener("scroll",r.update,ng)})),s&&l.removeEventListener("resize",r.update,ng)}},data:{}};var ag={left:"right",right:"left",bottom:"top",top:"bottom"};function ig(e){return e.replace(/left|right|bottom|top/g,(function(e){return ag[e]}))}var sg={start:"end",end:"start"};function lg(e){return e.replace(/start|end/g,(function(e){return sg[e]}))}function cg(e){var t=Nv(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function ug(e){return Bv(qv(e)).left+cg(e).scrollLeft}function dg(e){var t=Wv(e),r=t.overflow,n=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(r+o+n)}function fg(e){return["html","body","#document"].indexOf(jv(e))>=0?e.ownerDocument.body:Av(e)&&dg(e)?e:fg(Vv(e))}function pg(e,t){var r;void 0===t&&(t=[]);var n=fg(e),o=n===(null==(r=e.ownerDocument)?void 0:r.body),a=Nv(n),i=o?[a].concat(a.visualViewport||[],dg(n)?n:[]):n,s=t.concat(i);return o?s:s.concat(pg(Vv(i)))}function mg(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function hg(e,t,r){return t===kv?mg(function(e,t){var r=Nv(e),n=qv(e),o=r.visualViewport,a=n.clientWidth,i=n.clientHeight,s=0,l=0;if(o){a=o.width,i=o.height;var c=Dv();(c||!c&&"fixed"===t)&&(s=o.offsetLeft,l=o.offsetTop)}return{width:a,height:i,x:s+ug(e),y:l}}(e,r)):Ov(t)?function(e,t){var r=Bv(e,!1,"fixed"===t);return r.top=r.top+e.clientTop,r.left=r.left+e.clientLeft,r.bottom=r.top+e.clientHeight,r.right=r.left+e.clientWidth,r.width=e.clientWidth,r.height=e.clientHeight,r.x=r.left,r.y=r.top,r}(t,r):mg(function(e){var t,r=qv(e),n=cg(e),o=null==(t=e.ownerDocument)?void 0:t.body,a=Rv(r.scrollWidth,r.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),i=Rv(r.scrollHeight,r.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),s=-n.scrollLeft+ug(e),l=-n.scrollTop;return"rtl"===Wv(o||r).direction&&(s+=Rv(r.clientWidth,o?o.clientWidth:0)-a),{width:a,height:i,x:s,y:l}}(qv(e)))}function bg(e,t,r,n){var o="clippingParents"===t?function(e){var t=pg(Vv(e)),r=["absolute","fixed"].indexOf(Wv(e).position)>=0&&Av(e)?Gv(e):e;return Ov(r)?t.filter((function(e){return Ov(e)&&Lv(e,r)&&"body"!==jv(e)})):[]}(e):[].concat(t),a=[].concat(o,[r]),i=a[0],s=a.reduce((function(t,r){var o=hg(e,r,n);return t.top=Rv(o.top,t.top),t.right=Tv(o.right,t.right),t.bottom=Tv(o.bottom,t.bottom),t.left=Rv(o.left,t.left),t}),hg(e,i,n));return s.width=s.right-s.left,s.height=s.bottom-s.top,s.x=s.left,s.y=s.top,s}function vg(e){var t,r=e.reference,n=e.element,o=e.placement,a=o?Pv(o):null,i=o?Jv(o):null,s=r.x+r.width/2-n.width/2,l=r.y+r.height/2-n.height/2;switch(a){case mv:t={x:s,y:r.y-n.height};break;case hv:t={x:s,y:r.y+r.height};break;case bv:t={x:r.x+r.width,y:l};break;case vv:t={x:r.x-n.width,y:l};break;default:t={x:r.x,y:r.y}}var c=a?Xv(a):null;if(null!=c){var u="y"===c?"height":"width";switch(i){case xv:t[c]=t[c]-(r[u]/2-n[u]/2);break;case wv:t[c]=t[c]+(r[u]/2-n[u]/2)}}return t}function gg(e,t){void 0===t&&(t={});var r=t,n=r.placement,o=void 0===n?e.placement:n,a=r.strategy,i=void 0===a?e.strategy:a,s=r.boundary,l=void 0===s?"clippingParents":s,c=r.rootBoundary,u=void 0===c?kv:c,d=r.elementContext,f=void 0===d?Sv:d,p=r.altBoundary,m=void 0!==p&&p,h=r.padding,b=void 0===h?0:h,v=Kv("number"!=typeof b?b:Zv(b,yv)),g=f===Sv?"reference":Sv,y=e.rects.popper,x=e.elements[m?g:f],w=bg(Ov(x)?x:x.contextElement||qv(e.elements.popper),l,u,i),k=Bv(e.elements.reference),S=vg({reference:k,element:y,placement:o}),_=mg(Object.assign({},y,S)),C=f===Sv?_:k,E={top:w.top-C.top+v.top,bottom:C.bottom-w.bottom+v.bottom,left:w.left-C.left+v.left,right:C.right-w.right+v.right},j=e.modifiersData.offset;if(f===Sv&&j){var N=j[o];Object.keys(E).forEach((function(e){var t=[bv,hv].indexOf(e)>=0?1:-1,r=[mv,hv].indexOf(e)>=0?"y":"x";E[e]+=N[r]*t}))}return E}const yg={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,r=e.options,n=e.name;if(!t.modifiersData[n]._skip){for(var o=r.mainAxis,a=void 0===o||o,i=r.altAxis,s=void 0===i||i,l=r.fallbackPlacements,c=r.padding,u=r.boundary,d=r.rootBoundary,f=r.altBoundary,p=r.flipVariations,m=void 0===p||p,h=r.allowedAutoPlacements,b=t.options.placement,v=Pv(b),g=l||(v===b||!m?[ig(b)]:function(e){if(Pv(e)===gv)return[];var t=ig(e);return[lg(e),t,lg(t)]}(b)),y=[b].concat(g).reduce((function(e,r){return e.concat(Pv(r)===gv?function(e,t){void 0===t&&(t={});var r=t,n=r.placement,o=r.boundary,a=r.rootBoundary,i=r.padding,s=r.flipVariations,l=r.allowedAutoPlacements,c=void 0===l?Cv:l,u=Jv(n),d=u?s?_v:_v.filter((function(e){return Jv(e)===u})):yv,f=d.filter((function(e){return c.indexOf(e)>=0}));0===f.length&&(f=d);var p=f.reduce((function(t,r){return t[r]=gg(e,{placement:r,boundary:o,rootBoundary:a,padding:i})[Pv(r)],t}),{});return Object.keys(p).sort((function(e,t){return p[e]-p[t]}))}(t,{placement:r,boundary:u,rootBoundary:d,padding:c,flipVariations:m,allowedAutoPlacements:h}):r)}),[]),x=t.rects.reference,w=t.rects.popper,k=new Map,S=!0,_=y[0],C=0;C<y.length;C++){var E=y[C],j=Pv(E),N=Jv(E)===xv,O=[mv,hv].indexOf(j)>=0,A=O?"width":"height",z=gg(t,{placement:E,boundary:u,rootBoundary:d,altBoundary:f,padding:c}),I=O?N?bv:vv:N?hv:mv;x[A]>w[A]&&(I=ig(I));var P=ig(I),R=[];if(a&&R.push(z[j]<=0),s&&R.push(z[I]<=0,z[P]<=0),R.every((function(e){return e}))){_=E,S=!1;break}k.set(E,R)}if(S)for(var T=function(e){var t=y.find((function(t){var r=k.get(t);if(r)return r.slice(0,e).every((function(e){return e}))}));if(t)return _=t,"break"},M=m?3:1;M>0;M--){if("break"===T(M))break}t.placement!==_&&(t.modifiersData[n]._skip=!0,t.placement=_,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function xg(e,t,r){return void 0===r&&(r={x:0,y:0}),{top:e.top-t.height-r.y,right:e.right-t.width+r.x,bottom:e.bottom-t.height+r.y,left:e.left-t.width-r.x}}function wg(e){return[mv,bv,hv,vv].some((function(t){return e[t]>=0}))}const kg={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,r=e.options,n=e.name,o=r.offset,a=void 0===o?[0,0]:o,i=Cv.reduce((function(e,r){return e[r]=function(e,t,r){var n=Pv(e),o=[vv,mv].indexOf(n)>=0?-1:1,a="function"==typeof r?r(Object.assign({},t,{placement:e})):r,i=a[0],s=a[1];return i=i||0,s=(s||0)*o,[vv,bv].indexOf(n)>=0?{x:s,y:i}:{x:i,y:s}}(r,t.rects,a),e}),{}),s=i[t.placement],l=s.x,c=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=c),t.modifiersData[n]=i}};const Sg={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,r=e.options,n=e.name,o=r.mainAxis,a=void 0===o||o,i=r.altAxis,s=void 0!==i&&i,l=r.boundary,c=r.rootBoundary,u=r.altBoundary,d=r.padding,f=r.tether,p=void 0===f||f,m=r.tetherOffset,h=void 0===m?0:m,b=gg(t,{boundary:l,rootBoundary:c,padding:d,altBoundary:u}),v=Pv(t.placement),g=Jv(t.placement),y=!g,x=Xv(v),w="x"===x?"y":"x",k=t.modifiersData.popperOffsets,S=t.rects.reference,_=t.rects.popper,C="function"==typeof h?h(Object.assign({},t.rects,{placement:t.placement})):h,E="number"==typeof C?{mainAxis:C,altAxis:C}:Object.assign({mainAxis:0,altAxis:0},C),j=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,N={x:0,y:0};if(k){if(a){var O,A="y"===x?mv:vv,z="y"===x?hv:bv,I="y"===x?"height":"width",P=k[x],R=P+b[A],T=P-b[z],M=p?-_[I]/2:0,$=g===xv?S[I]:_[I],D=g===xv?-_[I]:-S[I],B=t.elements.arrow,F=p&&B?Fv(B):{width:0,height:0},L=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},W=L[A],H=L[z],q=Yv(0,S[I],F[I]),V=y?S[I]/2-M-q-W-E.mainAxis:$-q-W-E.mainAxis,U=y?-S[I]/2+M+q+H+E.mainAxis:D+q+H+E.mainAxis,G=t.elements.arrow&&Gv(t.elements.arrow),X=G?"y"===x?G.clientTop||0:G.clientLeft||0:0,Y=null!=(O=null==j?void 0:j[x])?O:0,K=P+U-Y,Z=Yv(p?Tv(R,P+V-Y-X):R,P,p?Rv(T,K):T);k[x]=Z,N[x]=Z-P}if(s){var Q,J="x"===x?mv:vv,ee="x"===x?hv:bv,te=k[w],re="y"===w?"height":"width",ne=te+b[J],oe=te-b[ee],ae=-1!==[mv,vv].indexOf(v),ie=null!=(Q=null==j?void 0:j[w])?Q:0,se=ae?ne:te-S[re]-_[re]-ie+E.altAxis,le=ae?te+S[re]+_[re]-ie-E.altAxis:oe,ce=p&&ae?(de=Yv(se,te,ue=le))>ue?ue:de:Yv(p?se:ne,te,p?le:oe);k[w]=ce,N[w]=ce-te}var ue,de;t.modifiersData[n]=N}},requiresIfExists:["offset"]};function _g(e,t,r){void 0===r&&(r=!1);var n,o,a=Av(t),i=Av(t)&&function(e){var t=e.getBoundingClientRect(),r=Mv(t.width)/e.offsetWidth||1,n=Mv(t.height)/e.offsetHeight||1;return 1!==r||1!==n}(t),s=qv(t),l=Bv(e,i,r),c={scrollLeft:0,scrollTop:0},u={x:0,y:0};return(a||!a&&!r)&&(("body"!==jv(t)||dg(s))&&(c=(n=t)!==Nv(n)&&Av(n)?{scrollLeft:(o=n).scrollLeft,scrollTop:o.scrollTop}:cg(n)),Av(t)?((u=Bv(t,!0)).x+=t.clientLeft,u.y+=t.clientTop):s&&(u.x=ug(s))),{x:l.left+c.scrollLeft-u.x,y:l.top+c.scrollTop-u.y,width:l.width,height:l.height}}function Cg(e){var t=new Map,r=new Set,n=[];function o(e){r.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!r.has(e)){var n=t.get(e);n&&o(n)}})),n.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){r.has(e.name)||o(e)})),n}var Eg={placement:"bottom",modifiers:[],strategy:"absolute"};function jg(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function Ng(e){void 0===e&&(e={});var t=e,r=t.defaultModifiers,n=void 0===r?[]:r,o=t.defaultOptions,a=void 0===o?Eg:o;return function(e,t,r){void 0===r&&(r=a);var o,i,s={placement:"bottom",orderedModifiers:[],options:Object.assign({},Eg,a),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},l=[],c=!1,u={state:s,setOptions:function(r){var o="function"==typeof r?r(s.options):r;d(),s.options=Object.assign({},a,s.options,o),s.scrollParents={reference:Ov(e)?pg(e):e.contextElement?pg(e.contextElement):[],popper:pg(t)};var i,c,f=function(e){var t=Cg(e);return Ev.reduce((function(e,r){return e.concat(t.filter((function(e){return e.phase===r})))}),[])}((i=[].concat(n,s.options.modifiers),c=i.reduce((function(e,t){var r=e[t.name];return e[t.name]=r?Object.assign({},r,t,{options:Object.assign({},r.options,t.options),data:Object.assign({},r.data,t.data)}):t,e}),{}),Object.keys(c).map((function(e){return c[e]}))));return s.orderedModifiers=f.filter((function(e){return e.enabled})),s.orderedModifiers.forEach((function(e){var t=e.name,r=e.options,n=void 0===r?{}:r,o=e.effect;if("function"==typeof o){var a=o({state:s,name:t,instance:u,options:n}),i=function(){};l.push(a||i)}})),u.update()},forceUpdate:function(){if(!c){var e=s.elements,t=e.reference,r=e.popper;if(jg(t,r)){s.rects={reference:_g(t,Gv(r),"fixed"===s.options.strategy),popper:Fv(r)},s.reset=!1,s.placement=s.options.placement,s.orderedModifiers.forEach((function(e){return s.modifiersData[e.name]=Object.assign({},e.data)}));for(var n=0;n<s.orderedModifiers.length;n++)if(!0!==s.reset){var o=s.orderedModifiers[n],a=o.fn,i=o.options,l=void 0===i?{}:i,d=o.name;"function"==typeof a&&(s=a({state:s,options:l,name:d,instance:u})||s)}else s.reset=!1,n=-1}}},update:(o=function(){return new Promise((function(e){u.forceUpdate(),e(s)}))},function(){return i||(i=new Promise((function(e){Promise.resolve().then((function(){i=void 0,e(o())}))}))),i}),destroy:function(){d(),c=!0}};if(!jg(e,t))return u;function d(){l.forEach((function(e){return e()})),l=[]}return u.setOptions(r).then((function(e){!c&&r.onFirstUpdate&&r.onFirstUpdate(e)})),u}}var Og=Ng({defaultModifiers:[og,{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,r=e.name;t.modifiersData[r]=vg({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})},data:{}},rg,Iv,kg,yg,Sg,Qv,{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,r=e.name,n=t.rects.reference,o=t.rects.popper,a=t.modifiersData.preventOverflow,i=gg(t,{elementContext:"reference"}),s=gg(t,{altBoundary:!0}),l=xg(i,n),c=xg(s,o,a),u=wg(l),d=wg(c);t.modifiersData[r]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":d})}}]});function Ag(e={}){const{enabled:t=!0,modifiers:r,placement:n="bottom",strategy:a="absolute",arrowPadding:i=8,eventListeners:s=!0,offset:l,gutter:c=8,flip:u=!0,boundary:d="clippingParents",preventOverflow:f=!0,matchWidth:p,direction:m="ltr"}=e,h=o.useRef(null),b=o.useRef(null),v=o.useRef(null),g=function(e,t="ltr"){var r,n;const o=(null==(r=fv[e])?void 0:r[t])||e;return"ltr"===t?o:null!=(n=pv[e])?n:o}(n,m),y=o.useRef((()=>{})),x=o.useCallback((()=>{var e;t&&h.current&&b.current&&(null==(e=y.current)||e.call(y),v.current=Og(h.current,b.current,{placement:g,modifiers:[uv,sv,av,{...ov,enabled:!!p},{name:"eventListeners",...nv(s)},{name:"arrow",options:{padding:i}},{name:"offset",options:{offset:null!=l?l:[0,c]}},{name:"flip",enabled:!!u,options:{padding:8}},{name:"preventOverflow",enabled:!!f,options:{boundary:d}},...null!=r?r:[]],strategy:a}),v.current.forceUpdate(),y.current=v.current.destroy)}),[g,t,r,p,s,i,l,c,u,f,d,a]);o.useEffect((()=>()=>{var e;h.current||b.current||(null==(e=v.current)||e.destroy(),v.current=null)}),[]);const w=o.useCallback((e=>{h.current=e,x()}),[x]),k=o.useCallback(((e={},t=null)=>({...e,ref:Pd(w,t)})),[w]),S=o.useCallback((e=>{b.current=e,x()}),[x]),_=o.useCallback(((e={},t=null)=>({...e,ref:Pd(S,t),style:{...e.style,position:a,minWidth:p?void 0:"max-content",inset:"0 auto auto 0"}})),[a,S,p]),C=o.useCallback(((e={},t=null)=>{const{size:r,shadowColor:n,bg:o,style:a,...i}=e;return{...i,ref:t,"data-popper-arrow":"",style:zg(e)}}),[]),E=o.useCallback(((e={},t=null)=>({...e,ref:t,"data-popper-arrow-inner":""})),[]);return{update(){var e;null==(e=v.current)||e.update()},forceUpdate(){var e;null==(e=v.current)||e.forceUpdate()},transformOrigin:ev.transformOrigin.varRef,referenceRef:w,popperRef:S,getPopperProps:_,getArrowProps:C,getArrowInnerProps:E,getReferenceProps:k}}function zg(e){const{size:t,shadowColor:r,bg:n,style:o}=e,a={...o,position:"absolute"};return t&&(a["--popper-arrow-size"]=t),r&&(a["--popper-arrow-shadow-color"]=r),n&&(a["--popper-arrow-bg"]=n),a}function Ig(e={}){const{onClose:t,onOpen:r,isOpen:n,id:a}=e,i=Xu(r),s=Xu(t),[l,c]=o.useState(e.defaultIsOpen||!1),u=void 0!==n?n:l,d=void 0!==n,f=o.useId(),p=null!=a?a:`disclosure-${f}`,m=o.useCallback((()=>{d||c(!1),null==s||s()}),[d,s]),h=o.useCallback((()=>{d||c(!0),null==i||i()}),[d,i]),b=o.useCallback((()=>{u?m():h()}),[u,h,m]);return{isOpen:u,onOpen:h,onClose:m,onToggle:b,isControlled:d,getButtonProps:function(e={}){return{...e,"aria-expanded":u,"aria-controls":p,onClick(t){var r;null==(r=e.onClick)||r.call(e,t),b()}}},getDisclosureProps:function(e={}){return{...e,hidden:!u,id:p}}}}function Pg(e,t){var r;const n=e.target;if(n){if(!Rg(n).contains(n))return!1}return!(null==(r=t.current)?void 0:r.contains(n))}function Rg(e){var t;return null!=(t=null==e?void 0:e.ownerDocument)?t:document}function Tg(e){const{isOpen:t,ref:r}=e,[n,a]=o.useState(t),[i,s]=o.useState(!1);o.useEffect((()=>{i||(a(t),s(!0))}),[t,i,n]),Up((()=>r.current),"animationend",(()=>{a(t)}));return{present:!(!t&&!n),onComplete(){var e;const t=function(e){var t,r;return null!=(r=null==(t=Fh(e))?void 0:t.defaultView)?r:window}(r.current),n=new t.CustomEvent("animationend",{bubbles:!0});null==(e=r.current)||e.dispatchEvent(n)}}}function Mg(e){const{wasSelected:t,enabled:r,isSelected:n,mode:o="unmount"}=e;return!r||(!!n||!("keepMounted"!==o||!t))}var[$g,Dg,Bg,Fg]=$d(),[Lg,Wg]=ot({strict:!1,name:"MenuContext"});function Hg(e){var t;return null!=(t=null==e?void 0:e.ownerDocument)?t:document}function qg(e){return Hg(e).activeElement===e}function Vg(e={}){const{id:t,closeOnSelect:r=!0,closeOnBlur:n=!0,initialFocusRef:a,autoSelect:i=!0,isLazy:s,isOpen:l,defaultIsOpen:c,onClose:u,onOpen:d,placement:f="bottom-start",lazyBehavior:p="unmount",direction:m,computePositionOnMount:h=!1,...b}=e,v=o.useRef(null),g=o.useRef(null),y=Bg(),x=o.useCallback((()=>{requestAnimationFrame((()=>{var e;null==(e=v.current)||e.focus({preventScroll:!1})}))}),[]),w=o.useCallback((()=>{const e=setTimeout((()=>{var e;if(a)null==(e=a.current)||e.focus();else{const e=y.firstEnabled();e&&A(e.index)}}));T.current.add(e)}),[y,a]),k=o.useCallback((()=>{const e=setTimeout((()=>{const e=y.lastEnabled();e&&A(e.index)}));T.current.add(e)}),[y]),S=o.useCallback((()=>{null==d||d(),i?w():x()}),[i,w,x,d]),{isOpen:_,onOpen:C,onClose:E,onToggle:j}=Ig({isOpen:l,defaultIsOpen:c,onClose:u,onOpen:S});!function(e){const{ref:t,handler:r,enabled:n=!0}=e,a=Xu(r),i=o.useRef({isPointerDown:!1,ignoreEmulatedMouseEvents:!1}).current;o.useEffect((()=>{if(!n)return;const e=e=>{Pg(e,t)&&(i.isPointerDown=!0)},o=e=>{i.ignoreEmulatedMouseEvents?i.ignoreEmulatedMouseEvents=!1:i.isPointerDown&&r&&Pg(e,t)&&(i.isPointerDown=!1,a(e))},s=e=>{i.ignoreEmulatedMouseEvents=!0,r&&i.isPointerDown&&Pg(e,t)&&(i.isPointerDown=!1,a(e))},l=Rg(t.current);return l.addEventListener("mousedown",e,!0),l.addEventListener("mouseup",o,!0),l.addEventListener("touchstart",e,!0),l.addEventListener("touchend",s,!0),()=>{l.removeEventListener("mousedown",e,!0),l.removeEventListener("mouseup",o,!0),l.removeEventListener("touchstart",e,!0),l.removeEventListener("touchend",s,!0)}}),[r,t,a,i,n])}({enabled:_&&n,ref:v,handler:e=>{var t;(null==(t=g.current)?void 0:t.contains(e.target))||E()}});const N=Ag({...b,enabled:_||h,placement:f,direction:m}),[O,A]=o.useState(-1);Yu((()=>{_||A(-1)}),[_]),function(e,t){const{visible:r,focusRef:n}=t,o=!r;Yu((()=>{if(!o)return;if(Qb(e))return;const t=(null==n?void 0:n.current)||e.current;let r;return t?(r=requestAnimationFrame((()=>{t.focus({preventScroll:!0})})),()=>{cancelAnimationFrame(r)}):void 0}),[o,e,n])}(v,{focusRef:g,visible:_});const z=Tg({isOpen:_,ref:v}),[I,P]=function(e,...t){const r=o.useId(),n=e||r;return o.useMemo((()=>t.map((e=>`${e}-${n}`))),[n,t])}(t,"menu-button","menu-list"),R=o.useCallback((()=>{C(),x()}),[C,x]),T=o.useRef(new Set([]));!function(e,t=[]){o.useEffect((()=>()=>e()),t)}((()=>{T.current.forEach((e=>clearTimeout(e))),T.current.clear()}));const M=o.useCallback((()=>{C(),w()}),[w,C]),$=o.useCallback((()=>{C(),k()}),[C,k]),D=o.useCallback((()=>{var e,t;const r=Hg(v.current),n=null==(e=v.current)?void 0:e.contains(r.activeElement);if(!(_&&!n))return;const o=null==(t=y.item(O))?void 0:t.node;null==o||o.focus()}),[_,O,y]),B=o.useRef(null);return{openAndFocusMenu:R,openAndFocusFirstItem:M,openAndFocusLastItem:$,onTransitionEnd:D,unstable__animationState:z,descendants:y,popper:N,buttonId:I,menuId:P,forceUpdate:N.forceUpdate,orientation:"vertical",isOpen:_,onToggle:j,onOpen:C,onClose:E,menuRef:v,buttonRef:g,focusedIndex:O,closeOnSelect:r,closeOnBlur:n,autoSelect:i,setFocusedIndex:A,isLazy:s,lazyBehavior:p,initialFocusRef:a,rafId:B}}function Ug(e){var t;return function(e){var t;if(!function(e){return null!=e&&"object"==typeof e&&"nodeType"in e&&e.nodeType===Node.ELEMENT_NODE}(e))return!1;const r=null!=(t=e.ownerDocument.defaultView)?t:window;return e instanceof r.HTMLElement}(e)&&!!(null==(t=null==e?void 0:e.getAttribute("role"))?void 0:t.startsWith("menuitem"))}var[Gg,Xg]=ot({name:"MenuStylesContext",errorMessage:"useMenuStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Menu />\" "}),Yg=e=>{const{children:t}=e,r=yu("Menu",e),n=En(e),{direction:a}=bt(),{descendants:i,...s}=Vg({...n,direction:a}),l=o.useMemo((()=>s),[s]),{isOpen:c,onClose:u,forceUpdate:d}=l;return b.jsx($g,{value:i,children:b.jsx(Lg,{value:l,children:b.jsx(Gg,{value:r,children:It(t,{isOpen:c,onClose:u,forceUpdate:d})})})})};Yg.displayName="Menu";var Kg=Mu(((e,t)=>{const r=Xg();return b.jsx(Tu.span,{ref:t,...e,__css:r.command,className:"chakra-menu__command"})}));Kg.displayName="MenuCommand";var Zg=Mu(((e,t)=>{const{type:r,...n}=e,a=Xg(),i=n.as||r?null!=r?r:void 0:"button",s=o.useMemo((()=>({textDecoration:"none",color:"inherit",userSelect:"none",display:"flex",width:"100%",alignItems:"center",textAlign:"start",flex:"0 0 auto",outline:0,...a.item})),[a.item]);return b.jsx(Tu.button,{ref:t,type:i,...n,__css:s})})),Qg=e=>{const{className:t,children:r,...n}=e,a=Xg(),i=o.Children.only(r),s=o.isValidElement(i)?o.cloneElement(i,{focusable:"false","aria-hidden":!0,className:Ot("chakra-menu__icon",i.props.className)}):null,l=Ot("chakra-menu__icon-wrapper",t);return b.jsx(Tu.span,{className:l,...n,__css:a.icon,children:s})};Qg.displayName="MenuIcon";var Jg=Mu(((e,t)=>{const{icon:r,iconSpacing:n="0.75rem",command:a,commandSpacing:i="0.75rem",children:s,...l}=e,c=function(e={},t=null){const{onMouseEnter:r,onMouseMove:n,onMouseLeave:a,onClick:i,onFocus:s,isDisabled:l,isFocusable:c,closeOnSelect:u,type:d,...f}=e,p=Wg(),{setFocusedIndex:m,focusedIndex:h,closeOnSelect:b,onClose:v,menuRef:g,isOpen:y,menuId:x,rafId:w}=p,k=o.useRef(null),S=`${x}-menuitem-${o.useId()}`,{index:_,register:C}=Fg({disabled:l&&!c}),E=o.useCallback((e=>{null==r||r(e),l||m(_)}),[m,_,l,r]),j=o.useCallback((e=>{null==n||n(e),k.current&&!qg(k.current)&&E(e)}),[E,n]),N=o.useCallback((e=>{null==a||a(e),l||m(-1)}),[m,l,a]),O=o.useCallback((e=>{null==i||i(e),Ug(e.currentTarget)&&(null!=u?u:b)&&v()}),[v,i,b,u]),A=o.useCallback((e=>{null==s||s(e),m(_)}),[m,s,_]),z=_===h,I=l&&!c;Yu((()=>{y&&(z&&!I&&k.current?(w.current&&cancelAnimationFrame(w.current),w.current=requestAnimationFrame((()=>{var e;null==(e=k.current)||e.focus(),w.current=null}))):g.current&&!qg(g.current)&&g.current.focus({preventScroll:!0}))}),[z,I,g,y]);const P=Zb({onClick:O,onFocus:A,onMouseEnter:E,onMouseMove:j,onMouseLeave:N,ref:Pd(C,k,t),isDisabled:l,isFocusable:c});return{...f,...P,type:null!=d?d:P.type,id:S,role:"menuitem",tabIndex:z?0:-1}}(l,t),u=r||a?b.jsx("span",{style:{pointerEvents:"none",flex:1},children:s}):s;return b.jsxs(Zg,{...c,className:Ot("chakra-menu__menuitem",c.className),children:[r&&b.jsx(Qg,{fontSize:"0.8em",marginEnd:n,children:r}),u,a&&b.jsx(Kg,{marginStart:i,children:a})]})}));Jg.displayName="MenuItem";var ey={enter:{visibility:"visible",opacity:1,scale:1,transition:{duration:.2,ease:[.4,0,.2,1]}},exit:{transitionEnd:{visibility:"hidden"},opacity:0,scale:.8,transition:{duration:.1,easings:"easeOut"}}},ty=Tu(l.div),ry=Mu((function(e,t){var r,n;const{rootProps:a,motionProps:i,...s}=e,{isOpen:l,onTransitionEnd:c,unstable__animationState:u}=Wg(),d=function(e={},t=null){const r=Wg();if(!r)throw new Error("useMenuContext: context is undefined. Seems you forgot to wrap component within <Menu>");const{focusedIndex:n,setFocusedIndex:a,menuRef:i,isOpen:s,onClose:l,menuId:c,isLazy:u,lazyBehavior:d,unstable__animationState:f}=r,p=Dg(),m=Yb({preventDefault:e=>" "!==e.key&&Ug(e.target)}),h=o.useCallback((e=>{if(!e.currentTarget.contains(e.target))return;const t=e.key,r={Tab:e=>e.preventDefault(),Escape:l,ArrowDown:()=>{const e=p.nextEnabled(n);e&&a(e.index)},ArrowUp:()=>{const e=p.prevEnabled(n);e&&a(e.index)}}[t];if(r)return e.preventDefault(),void r(e);const o=m((e=>{const t=function(e,t,r,n){if(null==t)return n;if(!n)return e.find((e=>r(e).toLowerCase().startsWith(t.toLowerCase())));const o=e.filter((e=>r(e).toLowerCase().startsWith(t.toLowerCase())));if(o.length>0){let t;return o.includes(n)?(t=o.indexOf(n)+1,t===o.length&&(t=0),o[t]):(t=e.indexOf(o[0]),e[t])}return n}(p.values(),e,(e=>{var t,r;return null!=(r=null==(t=null==e?void 0:e.node)?void 0:t.textContent)?r:""}),p.item(n));if(t){const e=p.indexOf(t.node);a(e)}}));Ug(e.target)&&o(e)}),[p,n,m,l,a]),b=o.useRef(!1);s&&(b.current=!0);const v=Mg({wasSelected:b.current,enabled:u,mode:d,isSelected:f.present});return{...e,ref:Pd(i,t),children:v?e.children:null,tabIndex:-1,role:"menu",id:c,style:{...e.style,transformOrigin:"var(--popper-transform-origin)"},"aria-orientation":"vertical",onKeyDown:Mt(e.onKeyDown,h)}}(s,t),f=function(e={}){const{popper:t,isOpen:r}=Wg();return t.getPopperProps({...e,style:{visibility:r?"visible":"hidden",...e.style}})}(a),p=Xg();return b.jsx(Tu.div,{...f,__css:{zIndex:null!=(n=e.zIndex)?n:null==(r=p.list)?void 0:r.zIndex},children:b.jsx(ty,{variants:ey,initial:!1,animate:l?"enter":"exit",__css:{outline:0,...p.list},...i,className:Ot("chakra-menu__menu-list",d.className),...d,onUpdate:c,onAnimationComplete:$t(u.onComplete,d.onAnimationComplete)})})}));ry.displayName="MenuList";var ny=Mu(((e,t)=>{const r=Xg();return b.jsx(Tu.button,{ref:t,...e,__css:{display:"inline-flex",appearance:"none",alignItems:"center",outline:0,...r.button}})})),oy=Mu(((e,t)=>{const{children:r,as:n,...a}=e,i=function(e={},t=null){const r=Wg(),{onToggle:n,popper:a,openAndFocusFirstItem:i,openAndFocusLastItem:s}=r,l=o.useCallback((e=>{const t=e.key,r={Enter:i,ArrowDown:i,ArrowUp:s}[t];r&&(e.preventDefault(),e.stopPropagation(),r(e))}),[i,s]);return{...e,ref:Pd(r.buttonRef,t,a.referenceRef),id:r.buttonId,"data-active":Rt(r.isOpen),"aria-expanded":r.isOpen,"aria-haspopup":"menu","aria-controls":r.menuId,onClick:Mt(e.onClick,n),onKeyDown:Mt(e.onKeyDown,l)}}(a,t),s=n||ny;return b.jsx(s,{...i,className:Ot("chakra-menu__menu-button",e.className),children:b.jsx(Tu.span,{__css:{pointerEvents:"none",flex:"1 1 auto",minW:0},children:e.children})})}));oy.displayName="MenuButton";var ay=e=>{const{className:t,...r}=e,n=Xg();return b.jsx(Tu.hr,{"aria-orientation":"horizontal",className:Ot("chakra-menu__divider",t),...r,__css:n.divider})};ay.displayName="MenuDivider";var iy={slideInBottom:{...hf,custom:{offsetY:16,reverse:!0}},slideInRight:{...hf,custom:{offsetX:16,reverse:!0}},scale:{...mf,custom:{initialScale:.95,reverse:!0}},none:{}},sy=Tu(l.section),ly=e=>iy[e||"none"],cy=o.forwardRef(((e,t)=>{const{preset:r,motionProps:n=ly(r),...o}=e;return b.jsx(sy,{ref:t,...n,...o})}));cy.displayName="ModalTransition";var uy=Object.defineProperty,dy=(e,t,r)=>(((e,t,r)=>{t in e?uy(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r})(e,t+"",r),r),fy=new class{constructor(){dy(this,"modals"),this.modals=new Map}add(e){return this.modals.set(e,this.modals.size+1),this.modals.size}remove(e){this.modals.delete(e)}isTopModal(e){return!!e&&this.modals.get(e)===this.modals.size}};function py(e,t){const[r,n]=o.useState(0);return o.useEffect((()=>{const r=e.current;if(r){if(t){const e=fy.add(r);n(e)}return()=>{fy.remove(r),n(0)}}}),[t,e]),r}var my=new WeakMap,hy=new WeakMap,by={},vy=0,gy=function(e){return e&&(e.host||gy(e.parentNode))},yy=function(e,t,r,n){var o=function(e,t){return t.map((function(t){if(e.contains(t))return t;var r=gy(t);return r&&e.contains(r)?r:null})).filter((function(e){return Boolean(e)}))}(t,Array.isArray(e)?e:[e]);by[r]||(by[r]=new WeakMap);var a=by[r],i=[],s=new Set,l=new Set(o),c=function(e){e&&!s.has(e)&&(s.add(e),c(e.parentNode))};o.forEach(c);var u=function(e){e&&!l.has(e)&&Array.prototype.forEach.call(e.children,(function(e){if(s.has(e))u(e);else try{var t=e.getAttribute(n),o=null!==t&&"false"!==t,l=(my.get(e)||0)+1,c=(a.get(e)||0)+1;my.set(e,l),a.set(e,c),i.push(e),1===l&&o&&hy.set(e,!0),1===c&&e.setAttribute(r,"true"),o||e.setAttribute(n,"true")}catch(d){}}))};return u(t),s.clear(),vy++,function(){i.forEach((function(e){var t=my.get(e)-1,o=a.get(e)-1;my.set(e,t),a.set(e,o),t||(hy.has(e)||e.removeAttribute(n),hy.delete(e)),o||e.removeAttribute(r)})),--vy||(my=new WeakMap,my=new WeakMap,hy=new WeakMap,by={})}},xy=function(e,t,r){void 0===r&&(r="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),o=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body}(e);return o?(n.push.apply(n,Array.from(o.querySelectorAll("[aria-live], script"))),yy(n,o,r,"aria-hidden")):function(){return null}};function wy(e){const{isOpen:t,onClose:r,id:n,closeOnOverlayClick:a=!0,closeOnEsc:i=!0,useInert:s=!0,onOverlayClick:l,onEsc:c}=e,u=o.useRef(null),d=o.useRef(null),[f,p,m]=function(e,...t){const r=o.useId(),n=e||r;return o.useMemo((()=>t.map((e=>`${e}-${n}`))),[n,t])}(n,"chakra-modal","chakra-modal--header","chakra-modal--body");!function(e,t){const r=e.current;o.useEffect((()=>{if(e.current&&t)return xy(e.current)}),[t,e,r])}(u,t&&s);const h=py(u,t),b=o.useRef(null),v=o.useCallback((e=>{b.current=e.target}),[]),g=o.useCallback((e=>{"Escape"===e.key&&(e.stopPropagation(),i&&(null==r||r()),null==c||c())}),[i,r,c]),[y,x]=o.useState(!1),[w,k]=o.useState(!1),S=o.useCallback(((e={},t=null)=>({role:"dialog",...e,ref:Pd(t,u),id:f,tabIndex:-1,"aria-modal":!0,"aria-labelledby":y?p:void 0,"aria-describedby":w?m:void 0,onClick:Mt(e.onClick,(e=>e.stopPropagation()))})),[m,w,f,p,y]),_=o.useCallback((e=>{e.stopPropagation(),b.current===e.target&&fy.isTopModal(u.current)&&(a&&(null==r||r()),null==l||l())}),[r,a,l]),C=o.useCallback(((e={},t=null)=>({...e,ref:Pd(t,d),onClick:Mt(e.onClick,_),onKeyDown:Mt(e.onKeyDown,g),onMouseDown:Mt(e.onMouseDown,v)})),[g,v,_]);return{isOpen:t,onClose:r,headerId:p,bodyId:m,setBodyMounted:k,setHeaderMounted:x,dialogRef:u,overlayRef:d,getDialogProps:S,getDialogContainerProps:C,index:h}}var[ky,Sy]=ot({name:"ModalStylesContext",errorMessage:"useModalStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Modal />\" "}),[_y,Cy]=ot({strict:!0,name:"ModalContext",errorMessage:"useModalContext: `context` is undefined. Seems you forgot to wrap modal components in `<Modal />`"}),Ey=e=>{const t={scrollBehavior:"outside",autoFocus:!0,trapFocus:!0,returnFocusOnClose:!0,blockScrollOnMount:!0,allowPinchZoom:!1,motionPreset:"scale",lockFocusAcrossFrames:!0,...e},{portalProps:r,children:n,autoFocus:o,trapFocus:a,initialFocusRef:i,finalFocusRef:s,returnFocusOnClose:l,blockScrollOnMount:u,allowPinchZoom:d,preserveScrollBarGap:f,motionPreset:p,lockFocusAcrossFrames:m,onCloseComplete:h}=t,v=yu("Modal",t),g={...wy(t),autoFocus:o,trapFocus:a,initialFocusRef:i,finalFocusRef:s,returnFocusOnClose:l,blockScrollOnMount:u,allowPinchZoom:d,preserveScrollBarGap:f,motionPreset:p,lockFocusAcrossFrames:m};return b.jsx(_y,{value:g,children:b.jsx(ky,{value:v,children:b.jsx(c,{onExitComplete:h,children:g.isOpen&&b.jsx(ht,{...r,children:n})})})})};Ey.displayName="Modal";var jy="right-scroll-bar-position",Ny="width-before-scroll-bar",Oy=am(),Ay=function(){},zy=o.forwardRef((function(e,t){var r=o.useRef(null),n=o.useState({onScrollCapture:Ay,onWheelCapture:Ay,onTouchMoveCapture:Ay}),a=n[0],i=n[1],s=e.forwardProps,l=e.children,c=e.className,u=e.removeScrollBar,d=e.enabled,f=e.shards,p=e.sideCar,m=e.noRelative,h=e.noIsolation,b=e.inert,v=e.allowPinchZoom,g=e.as,y=void 0===g?"div":g,x=e.gapMode,w=tm(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),k=p,S=Qp([r,t]),_=em(em({},w),a);return o.createElement(o.Fragment,null,d&&o.createElement(k,{sideCar:Oy,removeScrollBar:u,shards:f,noRelative:m,noIsolation:h,inert:b,setCallbacks:i,allowPinchZoom:!!v,lockRef:r,gapMode:x}),s?o.cloneElement(o.Children.only(l),em(em({},_),{ref:S})):o.createElement(y,em({},_,{className:c,ref:S}),l))}));zy.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},zy.classNames={fullWidth:Ny,zeroRight:jy};function Iy(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=function(){if("undefined"!=typeof __webpack_nonce__)return __webpack_nonce__}();return t&&e.setAttribute("nonce",t),e}var Py=function(){var e=0,t=null;return{add:function(r){var n,o;0==e&&(t=Iy())&&(o=r,(n=t).styleSheet?n.styleSheet.cssText=o:n.appendChild(document.createTextNode(o)),function(e){(document.head||document.getElementsByTagName("head")[0]).appendChild(e)}(t)),e++},remove:function(){! --e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Ry=function(){var e,t=(e=Py(),function(t,r){o.useEffect((function(){return e.add(t),function(){e.remove()}}),[t&&r])});return function(e){var r=e.styles,n=e.dynamic;return t(r,n),null}},Ty={left:0,top:0,right:0,gap:0},My=function(e){return parseInt(e||"",10)||0},$y=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return Ty;var t=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[My(r),My(n),My(o)]}(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},Dy=Ry(),By="data-scroll-locked",Fy=function(e,t,r,n){var o=e.left,a=e.top,i=e.right,s=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(s,"px ").concat(n,";\n  }\n  body[").concat(By,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(s,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(jy," {\n    right: ").concat(s,"px ").concat(n,";\n  }\n  \n  .").concat(Ny," {\n    margin-right: ").concat(s,"px ").concat(n,";\n  }\n  \n  .").concat(jy," .").concat(jy," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(Ny," .").concat(Ny," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(By,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(s,"px;\n  }\n")},Ly=function(){var e=parseInt(document.body.getAttribute(By)||"0",10);return isFinite(e)?e:0},Wy=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,a=void 0===n?"margin":n;o.useEffect((function(){return document.body.setAttribute(By,(Ly()+1).toString()),function(){var e=Ly()-1;e<=0?document.body.removeAttribute(By):document.body.setAttribute(By,e.toString())}}),[]);var i=o.useMemo((function(){return $y(a)}),[a]);return o.createElement(Dy,{styles:Fy(i,!t,a,r?"":"!important")})},Hy=!1;if("undefined"!=typeof window)try{var qy=Object.defineProperty({},"passive",{get:function(){return Hy=!0,!0}});window.addEventListener("test",qy,qy),window.removeEventListener("test",qy,qy)}catch(gk){Hy=!1}var Vy=!!Hy&&{passive:!1},Uy=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&!(r.overflowY===r.overflowX&&!function(e){return"TEXTAREA"===e.tagName}(e)&&"visible"===r[t])},Gy=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),Xy(e,n)){var o=Yy(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},Xy=function(e,t){return"v"===e?function(e){return Uy(e,"overflowY")}(t):function(e){return Uy(e,"overflowX")}(t)},Yy=function(e,t){return"v"===e?[(r=t).scrollTop,r.scrollHeight,r.clientHeight]:function(e){return[e.scrollLeft,e.scrollWidth,e.clientWidth]}(t);var r},Ky=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Zy=function(e){return[e.deltaX,e.deltaY]},Qy=function(e){return e&&"current"in e?e.current:e},Jy=function(e){return"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")},ex=0,tx=[];function rx(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const nx=(ox=function(e){var t=o.useRef([]),r=o.useRef([0,0]),n=o.useRef(),a=o.useState(ex++)[0],i=o.useState(Ry)[0],s=o.useRef(e);o.useEffect((function(){s.current=e}),[e]),o.useEffect((function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var t=function(e,t,r){if(r||2===arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}([e.lockRef.current],(e.shards||[]).map(Qy),!0).filter(Boolean);return t.forEach((function(e){return e.classList.add("allow-interactivity-".concat(a))})),function(){document.body.classList.remove("block-interactivity-".concat(a)),t.forEach((function(e){return e.classList.remove("allow-interactivity-".concat(a))}))}}}),[e.inert,e.lockRef.current,e.shards]);var l=o.useCallback((function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!s.current.allowPinchZoom;var o,a=Ky(e),i=r.current,l="deltaX"in e?e.deltaX:i[0]-a[0],c="deltaY"in e?e.deltaY:i[1]-a[1],u=e.target,d=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===u.type)return!1;var f=Gy(d,u);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=Gy(d,u)),!f)return!1;if(!n.current&&"changedTouches"in e&&(l||c)&&(n.current=o),!o)return!0;var p=n.current||o;return function(e,t,r,n){var o=function(e,t){return"h"===e&&"rtl"===t?-1:1}(e,window.getComputedStyle(t).direction),a=o*n,i=r.target,s=t.contains(i),l=!1,c=a>0,u=0,d=0;do{var f=Yy(e,i),p=f[0],m=f[1]-f[2]-o*p;(p||m)&&Xy(e,i)&&(u+=m,d+=p),i=i.parentNode.host||i.parentNode}while(!s&&i!==document.body||s&&(t.contains(i)||t===i));return(c&&Math.abs(u)<1||!c&&Math.abs(d)<1)&&(l=!0),l}(p,t,e,"h"===p?l:c)}),[]),c=o.useCallback((function(e){var r=e;if(tx.length&&tx[tx.length-1]===i){var n="deltaY"in r?Zy(r):Ky(r),o=t.current.filter((function(e){return e.name===r.type&&(e.target===r.target||r.target===e.shadowParent)&&(t=e.delta,o=n,t[0]===o[0]&&t[1]===o[1]);var t,o}))[0];if(o&&o.should)r.cancelable&&r.preventDefault();else if(!o){var a=(s.current.shards||[]).map(Qy).filter(Boolean).filter((function(e){return e.contains(r.target)}));(a.length>0?l(r,a[0]):!s.current.noIsolation)&&r.cancelable&&r.preventDefault()}}}),[]),u=o.useCallback((function(e,r,n,o){var a={name:e,delta:r,target:n,should:o,shadowParent:rx(n)};t.current.push(a),setTimeout((function(){t.current=t.current.filter((function(e){return e!==a}))}),1)}),[]),d=o.useCallback((function(e){r.current=Ky(e),n.current=void 0}),[]),f=o.useCallback((function(t){u(t.type,Zy(t),t.target,l(t,e.lockRef.current))}),[]),p=o.useCallback((function(t){u(t.type,Ky(t),t.target,l(t,e.lockRef.current))}),[]);o.useEffect((function(){return tx.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,Vy),document.addEventListener("touchmove",c,Vy),document.addEventListener("touchstart",d,Vy),function(){tx=tx.filter((function(e){return e!==i})),document.removeEventListener("wheel",c,Vy),document.removeEventListener("touchmove",c,Vy),document.removeEventListener("touchstart",d,Vy)}}),[]);var m=e.removeScrollBar,h=e.inert;return o.createElement(o.Fragment,null,h?o.createElement(i,{styles:Jy(a)}):null,m?o.createElement(Wy,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},Oy.useMedium(ox),im);var ox,ax=o.forwardRef((function(e,t){return o.createElement(zy,em({},e,{ref:t,sideCar:nx}))}));function ix(e){const{autoFocus:t,trapFocus:r,dialogRef:n,initialFocusRef:a,blockScrollOnMount:i,allowPinchZoom:s,finalFocusRef:l,returnFocusOnClose:c,preserveScrollBarGap:d,lockFocusAcrossFrames:f,isOpen:p}=Cy(),[m,h]=u();o.useEffect((()=>{!m&&h&&setTimeout(h)}),[m,h]);const v=py(n,p);return b.jsx(Yh,{autoFocus:t,isDisabled:!r,initialFocusRef:a,finalFocusRef:l,restoreFocus:c,contentRef:n,lockFocusAcrossFrames:f,children:b.jsx(ax,{removeScrollBar:!d,allowPinchZoom:s,enabled:1===v&&i,forwardProps:!0,children:e.children})})}ax.classNames=zy.classNames;var sx=Mu(((e,t)=>{const{className:r,children:n,containerProps:o,motionProps:a,...i}=e,{getDialogProps:s,getDialogContainerProps:l}=Cy(),c=s(i,t),u=l(o),d=Ot("chakra-modal__content",r),f=Sy(),p={display:"flex",flexDirection:"column",position:"relative",width:"100%",outline:0,...f.dialog},m={display:"flex",width:"100vw",height:"$100vh",position:"fixed",left:0,top:0,...f.dialogContainer},{motionPreset:h}=Cy();return b.jsx(ix,{children:b.jsx(Tu.div,{...u,className:"chakra-modal__content-container",tabIndex:-1,__css:m,children:b.jsx(cy,{preset:h,motionProps:a,className:d,...c,__css:p,children:n})})})}));function lx(e){const{leastDestructiveRef:t,...r}=e;return b.jsx(Ey,{...r,initialFocusRef:t})}sx.displayName="ModalContent";var cx=Mu(((e,t)=>b.jsx(sx,{ref:t,role:"alertdialog",...e}))),[ux,dx]=ot(),fx={start:{ltr:"left",rtl:"right"},end:{ltr:"right",rtl:"left"}};function px(e){var t;const{isOpen:r,onClose:n,placement:o="right",children:a,...i}=e,s=bt(),l=null==(t=s.components)?void 0:t.Drawer,c=function(e,t){var r,n;if(e)return null!=(n=null==(r=fx[e])?void 0:r[t])?n:e}(o,s.direction);return b.jsx(ux,{value:{placement:c},children:b.jsx(Ey,{isOpen:r,onClose:n,styleConfig:l,...i,children:a})})}var mx=Tu(gf),hx=Mu(((e,t)=>{const{className:r,children:n,motionProps:o,containerProps:a,...i}=e,{getDialogProps:s,getDialogContainerProps:l,isOpen:c}=Cy(),u=s(i,t),d=l(a),f=Ot("chakra-modal__content",r),p=Sy(),m={display:"flex",flexDirection:"column",position:"relative",width:"100%",outline:0,...p.dialog},h={display:"flex",width:"100vw",height:"$100vh",position:"fixed",left:0,top:0,...p.dialogContainer},{placement:v}=dx();return b.jsx(ix,{children:b.jsx(Tu.div,{...d,className:"chakra-modal__content-container",__css:h,children:b.jsx(mx,{motionProps:o,direction:v,in:c,className:f,...u,__css:m,children:n})})})}));hx.displayName="DrawerContent";var bx=Mu(((e,t)=>{const{className:r,...n}=e,o=Ot("chakra-modal__footer",r),a={display:"flex",alignItems:"center",justifyContent:"flex-end",...Sy().footer};return b.jsx(Tu.footer,{ref:t,...n,__css:a,className:o})}));bx.displayName="ModalFooter";var vx=Mu(((e,t)=>{const{className:r,...n}=e,{headerId:a,setHeaderMounted:i}=Cy();o.useEffect((()=>(i(!0),()=>i(!1))),[i]);const s=Ot("chakra-modal__header",r),l={flex:0,...Sy().header};return b.jsx(Tu.header,{ref:t,className:s,id:a,...n,__css:l})}));vx.displayName="ModalHeader";var gx=Tu(l.div),yx=Mu(((e,t)=>{const{className:r,transition:n,motionProps:o,...a}=e,i=Ot("chakra-modal__overlay",r),s={pos:"fixed",left:"0",top:"0",w:"100vw",h:"100vh",...Sy().overlay},{motionPreset:l}=Cy(),c=o||("none"===l?{}:pf);return b.jsx(gx,{...c,__css:s,ref:t,className:i,...a})}));yx.displayName="ModalOverlay";var xx=Mu(((e,t)=>{const{className:r,...n}=e,{bodyId:a,setBodyMounted:i}=Cy();o.useEffect((()=>(i(!0),()=>i(!1))),[i]);const s=Ot("chakra-modal__body",r),l=Sy();return b.jsx(Tu.div,{ref:t,className:s,id:a,...n,__css:l.body})}));xx.displayName="ModalBody";var wx=Mu(((e,t)=>{const{onClick:r,className:n,...o}=e,{onClose:a}=Cy(),i=Ot("chakra-modal__close-btn",n),s=Sy();return b.jsx(hd,{ref:t,__css:s.closeButton,className:i,onClick:Mt(r,(e=>{e.stopPropagation(),a()})),...o})}));wx.displayName="ModalCloseButton",Je({"0%":{strokeDasharray:"1, 400",strokeDashoffset:"0"},"50%":{strokeDasharray:"400, 400",strokeDashoffset:"-100"},"100%":{strokeDasharray:"400, 400",strokeDashoffset:"-260"}}),Je({"0%":{transform:"rotate(0deg)"},"100%":{transform:"rotate(360deg)"}});var kx=Je({"0%":{left:"-40%"},"100%":{left:"100%"}}),Sx=Je({from:{backgroundPosition:"1rem 0"},to:{backgroundPosition:"0 0"}});var[_x,Cx]=ot({name:"ProgressStylesContext",errorMessage:"useProgressStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Progress />\" "}),Ex=Mu(((e,t)=>{const{min:r,max:n,value:o,isIndeterminate:a,role:i,...s}=e,l=function(e){const{value:t=0,min:r,max:n,valueText:o,getValueText:a,isIndeterminate:i,role:s="progressbar"}=e,l=function(e,t,r){return 100*(e-t)/(r-t)}(t,r,n);return{bind:{"data-indeterminate":i?"":void 0,"aria-valuemax":n,"aria-valuemin":r,"aria-valuenow":i?void 0:t,"aria-valuetext":(()=>{if(null!=t)return"function"==typeof a?a(t,l):o})(),role:s},percent:l,value:t}}({value:o,min:r,max:n,isIndeterminate:a,role:i}),c={height:"100%",...Cx().filledTrack};return b.jsx(Tu.div,{ref:t,style:{width:`${l.percent}%`,...s.style},...l.bind,...s,__css:c})})),jx=Mu(((e,t)=>{var r;const{value:n,min:o=0,max:a=100,hasStripe:i,isAnimated:s,children:l,borderRadius:c,isIndeterminate:u,"aria-label":d,"aria-labelledby":f,"aria-valuetext":p,title:m,role:h,...v}=En(e),g=yu("Progress",e),y=null!=c?c:null==(r=g.track)?void 0:r.borderRadius,x={...!u&&i&&s&&{animation:`${Sx} 1s linear infinite`},...u&&{position:"absolute",willChange:"left",minWidth:"50%",animation:`${kx} 1s ease infinite normal none running`}},w={overflow:"hidden",position:"relative",...g.track};return b.jsx(Tu.div,{ref:t,borderRadius:y,__css:w,...v,children:b.jsxs(_x,{value:g,children:[b.jsx(Ex,{"aria-label":d,"aria-labelledby":f,"aria-valuetext":p,min:o,max:a,value:n,isIndeterminate:u,css:x,borderRadius:y,title:m,role:h}),l]})})}));jx.displayName="Progress";var Nx=Mu((function(e,t){const{children:r,placeholder:n,className:o,...a}=e;return b.jsxs(Tu.select,{...a,ref:t,className:Ot("chakra-select",o),children:[n&&b.jsx("option",{value:"",children:n}),r]})}));Nx.displayName="SelectField";var Ox=Mu(((e,t)=>{var r;const n=yu("Select",e),{rootProps:o,placeholder:a,icon:i,color:s,height:l,h:c,minH:u,minHeight:d,iconColor:f,iconSize:p,...m}=En(e),[h,v]=function(e,t){const r={},n={};for(const[o,a]of Object.entries(e))t.includes(o)?r[o]=a:n[o]=a;return[r,n]}(m,bn),g=vp(v),y={width:"100%",height:"fit-content",position:"relative",color:s},x={paddingEnd:"2rem",...n.field,_focus:{zIndex:"unset",...null==(r=n.field)?void 0:r._focus}};return b.jsxs(Tu.div,{className:"chakra-select__wrapper",__css:y,...h,...o,children:[b.jsx(Nx,{ref:t,height:null!=c?c:l,minH:null!=u?u:d,placeholder:a,...g,__css:x,children:e.children}),b.jsx(Ix,{"data-disabled":Rt(g.disabled),...(f||s)&&{color:f||s},__css:n.icon,...p&&{fontSize:p},children:i})]})}));Ox.displayName="Select";var Ax=e=>b.jsx("svg",{viewBox:"0 0 24 24",...e,children:b.jsx("path",{fill:"currentColor",d:"M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"})}),zx=Tu("div",{baseStyle:{position:"absolute",display:"inline-flex",alignItems:"center",justifyContent:"center",pointerEvents:"none",top:"50%",transform:"translateY(-50%)"}}),Ix=e=>{const{children:t=b.jsx(Ax,{}),...r}=e,n=o.cloneElement(t,{role:"presentation",className:"chakra-select__icon",focusable:!1,"aria-hidden":!0,style:{width:"1em",height:"1em",color:"currentColor"}});return b.jsx(zx,{...r,className:"chakra-select__icon-wrapper",children:o.isValidElement(t)?n:null})};Ix.displayName="SelectIcon";var Px=Tu("div",{baseStyle:{boxShadow:"none",backgroundClip:"padding-box",cursor:"default",color:"transparent",pointerEvents:"none",userSelect:"none","&::before, &::after, *":{visibility:"hidden"}}}),Rx=Br("skeleton-start-color"),Tx=Br("skeleton-end-color"),Mx=Je({from:{opacity:0},to:{opacity:1}}),$x=Je({from:{borderColor:Rx.reference,background:Rx.reference},to:{borderColor:Tx.reference,background:Tx.reference}}),Dx=Mu(((e,t)=>{const r={...e,fadeDuration:"number"==typeof e.fadeDuration?e.fadeDuration:.4,speed:"number"==typeof e.speed?e.speed:.8},n=gu("Skeleton",r),a=function(){const e=o.useRef(!0);return o.useEffect((()=>{e.current=!1}),[]),e.current}(),{startColor:i="",endColor:s="",isLoaded:l,fadeDuration:c,speed:u,className:d,fitContent:f,...p}=En(r),[m,h]=function(e,t,r){const n=bt();return Nt(e,t,r)(n)}("colors",[i,s]),v=function(e){const t=o.useRef();return o.useEffect((()=>{t.current=e}),[e]),t.current}(l),g=Ot("chakra-skeleton",d),y={...m&&{[Rx.variable]:m},...h&&{[Tx.variable]:h}};if(l){const e=a||v?"none":`${Mx} ${c}s`;return b.jsx(Tu.div,{ref:t,className:g,__css:{animation:e},...p})}return b.jsx(Px,{ref:t,className:g,...p,__css:{width:f?"fit-content":void 0,...n,...y,_dark:{...n._dark,...y},animation:`${u}s linear infinite alternate ${$x}`}})}));Dx.displayName="Skeleton";var Bx=3,Fx=e=>{const{noOfLines:t=Bx,spacing:r="0.5rem",skeletonHeight:n="0.5rem",className:o,startColor:a,endColor:i,isLoaded:s,fadeDuration:l,speed:c,variant:u,size:d,colorScheme:f,children:p,...m}=e,h=Xb("number"==typeof t?[t]:t)||Bx,v=Array(h).fill(1).map(((e,t)=>t+1));const g=e=>h>1&&e===v.length?"80%":"100%",y=Ot("chakra-skeleton__group",o);return b.jsx(Tu.div,{className:y,...m,children:v.map(((e,t)=>{if(s&&t>0)return null;const o=s?null:{mb:e===v.length?"0":r,width:g(e),height:n};return b.jsx(Dx,{startColor:a,endColor:i,isLoaded:s,fadeDuration:l,speed:c,variant:u,size:d,colorScheme:f,...o,children:0===t?p:void 0},v.length.toString()+e)}))})};Fx.displayName="SkeletonText";var Lx=e=>e?"":void 0,Wx=e=>!!e||void 0,Hx=(...e)=>e.filter(Boolean).join(" ");function qx(...e){return function(t){e.some((e=>(null==e||e(t),null==t?void 0:t.defaultPrevented)))}}function Vx(e){const{orientation:t,vertical:r,horizontal:n}=e;return"vertical"===t?r:n}var Ux={width:0,height:0},Gx=e=>e||Ux;function Xx(e){const t=function(e){var t;return null!=(t=e.view)?t:window}(e);return void 0!==t.PointerEvent&&e instanceof t.PointerEvent?!("mouse"!==e.pointerType):e instanceof t.MouseEvent}function Yx(e){return!!e.touches}function Kx(e,t="page"){return Yx(e)?function(e,t="page"){const r=e.touches[0]||e.changedTouches[0];return{x:r[`${t}X`],y:r[`${t}Y`]}}(e,t):function(e,t="page"){return{x:e[`${t}X`],y:e[`${t}Y`]}}(e,t)}function Zx(e,t=!1){function r(t){e(t,{point:Kx(t)})}const n=t?function(e){return t=>{const r=Xx(t);(!r||r&&0===t.button)&&e(t)}}(r):r;return n}function Qx(e,t,r,n){return function(e,t,r,n){return e.addEventListener(t,r,n),()=>{e.removeEventListener(t,r,n)}}(e,t,Zx(r,"pointerdown"===t),n)}var Jx=Object.defineProperty,ew=(e,t,r)=>(((e,t,r)=>{t in e?Jx(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r})(e,"symbol"!=typeof t?t+"":t,r),r),tw=class{constructor(e,t,r){var n;if(ew(this,"history",[]),ew(this,"startEvent",null),ew(this,"lastEvent",null),ew(this,"lastEventInfo",null),ew(this,"handlers",{}),ew(this,"removeListeners",(()=>{})),ew(this,"threshold",3),ew(this,"win"),ew(this,"updatePoint",(()=>{if(!this.lastEvent||!this.lastEventInfo)return;const e=nw(this.lastEventInfo,this.history),t=null!==this.startEvent,r=function(e,t){if("number"==typeof e&&"number"==typeof t)return iw(e,t);if(sw(e)&&sw(t)){const r=iw(e.x,t.x),n=iw(e.y,t.y);return Math.sqrt(r**2+n**2)}return 0}(e.offset,{x:0,y:0})>=this.threshold;if(!t&&!r)return;const{timestamp:n}=pu();this.history.push({...e.point,timestamp:n});const{onStart:o,onMove:a}=this.handlers;t||(null==o||o(this.lastEvent,e),this.startEvent=this.lastEvent),null==a||a(this.lastEvent,e)})),ew(this,"onPointerMove",((e,t)=>{this.lastEvent=e,this.lastEventInfo=t,lu.update(this.updatePoint,!0)})),ew(this,"onPointerUp",((e,t)=>{const r=nw(t,this.history),{onEnd:n,onSessionEnd:o}=this.handlers;null==o||o(e,r),this.end(),n&&this.startEvent&&(null==n||n(e,r))})),this.win=null!=(n=e.view)?n:window,function(e){return Yx(e)&&e.touches.length>1}(e))return;this.handlers=t,r&&(this.threshold=r),e.stopPropagation(),e.preventDefault();const o={point:Kx(e)},{timestamp:a}=pu();this.history=[{...o.point,timestamp:a}];const{onSessionStart:i}=t;null==i||i(e,nw(o,this.history)),this.removeListeners=function(...e){return t=>e.reduce(((e,t)=>t(e)),t)}(Qx(this.win,"pointermove",this.onPointerMove),Qx(this.win,"pointerup",this.onPointerUp),Qx(this.win,"pointercancel",this.onPointerUp))}updateHandlers(e){this.handlers=e}end(){var e;null==(e=this.removeListeners)||e.call(this),cu.update(this.updatePoint)}};function rw(e,t){return{x:e.x-t.x,y:e.y-t.y}}function nw(e,t){return{point:e.point,delta:rw(e.point,t[t.length-1]),offset:rw(e.point,t[0]),velocity:aw(t,.1)}}var ow=e=>1e3*e;function aw(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,n=null;const o=e[e.length-1];for(;r>=0&&(n=e[r],!(o.timestamp-n.timestamp>ow(t)));)r--;if(!n)return{x:0,y:0};const a=(o.timestamp-n.timestamp)/1e3;if(0===a)return{x:0,y:0};const i={x:(o.x-n.x)/a,y:(o.y-n.y)/a};return i.x===1/0&&(i.x=0),i.y===1/0&&(i.y=0),i}function iw(e,t){return Math.abs(e-t)}function sw(e){return"x"in e&&"y"in e}function lw(e){const t=o.useRef(null);return t.current=e,t}var cw=Boolean(null==globalThis?void 0:globalThis.document)?o.useLayoutEffect:o.useEffect;function uw({getNodes:e,observeMutation:t=!0}){const[r,n]=o.useState([]),[a,i]=o.useState(0);return cw((()=>{const r=e(),o=r.map(((e,t)=>function(e,t){if(!e)return void t(void 0);t({width:e.offsetWidth,height:e.offsetHeight});const r=new(e.ownerDocument.defaultView??window).ResizeObserver((r=>{if(!Array.isArray(r)||!r.length)return;const[n]=r;let o,a;if("borderBoxSize"in n){const e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;o=t.inlineSize,a=t.blockSize}else o=e.offsetWidth,a=e.offsetHeight;t({width:o,height:a})}));return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}(e,(e=>{n((r=>[...r.slice(0,t),e,...r.slice(t+1)]))}))));if(t){const e=r[0];o.push(function(e,t){var r,n;if(!e||!e.parentElement)return;const o=new(null!=(n=null==(r=e.ownerDocument)?void 0:r.defaultView)?n:window).MutationObserver((()=>{t()}));return o.observe(e.parentElement,{childList:!0}),()=>{o.disconnect()}}(e,(()=>{i((e=>e+1))})))}return()=>{o.forEach((e=>{null==e||e()}))}}),[a]),r}function dw(e){var t;const{min:r=0,max:n=100,onChange:a,value:i,defaultValue:s,isReversed:l,direction:c="ltr",orientation:u="horizontal",id:d,isDisabled:f,isReadOnly:p,onChangeStart:m,onChangeEnd:h,step:b=1,getAriaValueText:v,"aria-valuetext":g,"aria-label":y,"aria-labelledby":x,name:w,focusThumbOnChange:k=!0,...S}=e,_=Xu(m),C=Xu(h),E=Xu(v),j=function(e){const{isReversed:t,direction:r,orientation:n}=e;return"ltr"===r||"vertical"===n?t:!t}({isReversed:l,direction:c,orientation:u}),[N,O]=Gd({value:i,defaultValue:null!=s?s:pw(r,n),onChange:a}),[A,z]=o.useState(!1),[I,P]=o.useState(!1),R=!(f||p),T=(n-r)/10,M=b||(n-r)/100,$=Vp(N,r,n),D=Hp(j?n-$+r:$,r,n),B="vertical"===u,F=lw({min:r,max:n,step:b,isDisabled:f,value:$,isInteractive:R,isReversed:j,isVertical:B,eventSource:null,focusThumbOnChange:k,orientation:u}),L=o.useRef(null),W=o.useRef(null),H=o.useRef(null),q=o.useId(),V=null!=d?d:q,[U,G]=[`slider-thumb-${V}`,`slider-track-${V}`],X=o.useCallback((e=>{var t,r;if(!L.current)return;const n=F.current;n.eventSource="pointer";const o=L.current.getBoundingClientRect(),{clientX:a,clientY:i}=null!=(r=null==(t=e.touches)?void 0:t[0])?r:e;let s=(B?o.bottom-i:a-o.left)/(B?o.height:o.width);j&&(s=1-s);let l=function(e,t,r){return(r-t)*e+t}(s,n.min,n.max);return n.step&&(l=parseFloat(qp(l,n.min,n.step))),l=Vp(l,n.min,n.max),l}),[B,j,F]),Y=o.useCallback((e=>{const t=F.current;t.isInteractive&&(e=Vp(e=parseFloat(qp(e,t.min,M)),t.min,t.max),O(e))}),[M,O,F]),K=o.useMemo((()=>({stepUp(e=M){Y(j?$-e:$+e)},stepDown(e=M){Y(j?$+e:$-e)},reset(){Y(s||0)},stepTo(e){Y(e)}})),[Y,j,$,M,s]),Z=o.useCallback((e=>{const t=F.current,r={ArrowRight:()=>K.stepUp(),ArrowUp:()=>K.stepUp(),ArrowLeft:()=>K.stepDown(),ArrowDown:()=>K.stepDown(),PageUp:()=>K.stepUp(T),PageDown:()=>K.stepDown(T),Home:()=>Y(t.min),End:()=>Y(t.max)}[e.key];r&&(e.preventDefault(),e.stopPropagation(),r(e),t.eventSource="keyboard")}),[K,Y,T,F]),Q=null!=(t=null==E?void 0:E($))?t:g,J=function(e){const[t]=uw({observeMutation:!1,getNodes(){var t;return["object"==typeof(t=e)&&null!==t&&"current"in t?e.current:e]}});return t}(W),{getThumbStyle:ee,rootStyle:te,trackStyle:re,innerTrackStyle:ne}=o.useMemo((()=>{const e=F.current,t=null!=J?J:{width:0,height:0};return function(e){const{orientation:t,thumbPercents:r,thumbRects:n,isReversed:o}=e,a="vertical"===t?n.reduce(((e,t)=>Gx(e).height>Gx(t).height?e:t),Ux):n.reduce(((e,t)=>Gx(e).width>Gx(t).width?e:t),Ux),i={position:"relative",touchAction:"none",WebkitTapHighlightColor:"rgba(0,0,0,0)",userSelect:"none",outline:0,...Vx({orientation:t,vertical:a?{paddingLeft:a.width/2,paddingRight:a.width/2}:{},horizontal:a?{paddingTop:a.height/2,paddingBottom:a.height/2}:{}})},s={position:"absolute",...Vx({orientation:t,vertical:{left:"50%",transform:"translateX(-50%)",height:"100%"},horizontal:{top:"50%",transform:"translateY(-50%)",width:"100%"}})},l=1===r.length,c=[0,o?100-r[0]:r[0]],u=l?c:r;let d=u[0];!l&&o&&(d=100-d);const f=Math.abs(u[u.length-1]-u[0]);return{trackStyle:s,innerTrackStyle:{...s,...Vx({orientation:t,vertical:o?{height:`${f}%`,top:`${d}%`}:{height:`${f}%`,bottom:`${d}%`},horizontal:o?{width:`${f}%`,right:`${d}%`}:{width:`${f}%`,left:`${d}%`}})},rootStyle:i,getThumbStyle:e=>{var o;const a=null!=(o=n[e])?o:Ux;return{position:"absolute",userSelect:"none",WebkitUserSelect:"none",MozUserSelect:"none",msUserSelect:"none",touchAction:"none",...Vx({orientation:t,vertical:{bottom:`calc(${r[e]}% - ${a.height/2}px)`},horizontal:{left:`calc(${r[e]}% - ${a.width/2}px)`}})}}}}({isReversed:j,orientation:e.orientation,thumbRects:[t],thumbPercents:[D]})}),[j,J,D,F]),oe=o.useCallback((()=>{F.current.focusThumbOnChange&&setTimeout((()=>{var e;return null==(e=W.current)?void 0:e.focus()}))}),[F]);function ae(e){const t=X(e);null!=t&&t!==F.current.value&&O(t)}Yu((()=>{const e=F.current;oe(),"keyboard"===e.eventSource&&(null==C||C(e.value))}),[$,C]),function(e,t){const{onPan:r,onPanStart:n,onPanEnd:a,onPanSessionStart:i,onPanSessionEnd:s,threshold:l}=t,c=Boolean(r||n||a||i||s),u=o.useRef(null),d=lw({onSessionStart:i,onSessionEnd:s,onStart:n,onMove:r,onEnd(e,t){u.current=null,null==a||a(e,t)}});o.useEffect((()=>{var e;null==(e=u.current)||e.updateHandlers(d.current)})),o.useEffect((()=>{const t=e.current;if(t&&c)return Qx(t,"pointerdown",(function(e){u.current=new tw(e,d.current,l)}))}),[e,c,d,l]),o.useEffect((()=>()=>{var e;null==(e=u.current)||e.end(),u.current=null}),[])}(H,{onPanSessionStart(e){const t=F.current;t.isInteractive&&(z(!0),oe(),ae(e),null==_||_(t.value))},onPanSessionEnd(){const e=F.current;e.isInteractive&&(z(!1),null==C||C(e.value))},onPan(e){F.current.isInteractive&&ae(e)}});const ie=o.useCallback(((e={},t=null)=>({...e,...S,ref:Pd(t,H),tabIndex:-1,"aria-disabled":Wx(f),"data-focused":Lx(I),style:{...e.style,...te}})),[S,f,I,te]),se=o.useCallback(((e={},t=null)=>({...e,ref:Pd(t,L),id:G,"data-disabled":Lx(f),style:{...e.style,...re}})),[f,G,re]),le=o.useCallback(((e={},t=null)=>({...e,ref:t,style:{...e.style,...ne}})),[ne]),ce=o.useCallback(((e={},t=null)=>({...e,ref:Pd(t,W),role:"slider",tabIndex:R?0:void 0,id:U,"data-active":Lx(A),"aria-valuetext":Q,"aria-valuemin":r,"aria-valuemax":n,"aria-valuenow":$,"aria-orientation":u,"aria-disabled":Wx(f),"aria-readonly":Wx(p),"aria-label":y,"aria-labelledby":y?void 0:x,style:{...e.style,...ee(0)},onKeyDown:qx(e.onKeyDown,Z),onFocus:qx(e.onFocus,(()=>P(!0))),onBlur:qx(e.onBlur,(()=>P(!1)))})),[R,U,A,Q,r,n,$,u,f,p,y,x,ee,Z]),ue=o.useCallback(((e,t=null)=>{const o=!(e.value<r||e.value>n),a=$>=e.value,i=Hp(e.value,r,n),s={position:"absolute",pointerEvents:"none",...fw({orientation:u,vertical:{bottom:j?100-i+"%":`${i}%`},horizontal:{left:j?100-i+"%":`${i}%`}})};return{...e,ref:t,role:"presentation","aria-hidden":!0,"data-disabled":Lx(f),"data-invalid":Lx(!o),"data-highlighted":Lx(a),style:{...e.style,...s}}}),[f,j,n,r,u,$]),de=o.useCallback(((e={},t=null)=>({...e,ref:t,type:"hidden",value:$,name:w})),[w,$]);return{state:{value:$,isFocused:I,isDragging:A},actions:K,getRootProps:ie,getTrackProps:se,getInnerTrackProps:le,getThumbProps:ce,getMarkerProps:ue,getInputProps:de}}function fw(e){const{orientation:t,vertical:r,horizontal:n}=e;return"vertical"===t?r:n}function pw(e,t){return t<e?e:e+(t-e)/2}var[mw,hw]=ot({name:"SliderContext",hookName:"useSliderContext",providerName:"<Slider />"}),[bw,vw]=ot({name:"SliderStylesContext",hookName:"useSliderStyles",providerName:"<Slider />"}),gw=Mu(((e,t)=>{var r;const n={...e,orientation:null!=(r=null==e?void 0:e.orientation)?r:"horizontal"},o=yu("Slider",n),a=En(n),{direction:i}=bt();a.direction=i;const{getInputProps:s,getRootProps:l,...c}=dw(a),u=l(),d=s({},t);return b.jsx(mw,{value:c,children:b.jsx(bw,{value:o,children:b.jsxs(Tu.div,{...u,className:Hx("chakra-slider",n.className),__css:o.container,children:[n.children,b.jsx("input",{...d})]})})})}));gw.displayName="Slider";var yw=Mu(((e,t)=>{const{getThumbProps:r}=hw(),n=vw(),o=r(e,t);return b.jsx(Tu.div,{...o,className:Hx("chakra-slider__thumb",e.className),__css:n.thumb})}));yw.displayName="SliderThumb";var xw=Mu(((e,t)=>{const{getTrackProps:r}=hw(),n=vw(),o=r(e,t);return b.jsx(Tu.div,{...o,className:Hx("chakra-slider__track",e.className),__css:n.track})}));xw.displayName="SliderTrack";var ww=Mu(((e,t)=>{const{getInnerTrackProps:r}=hw(),n=vw(),o=r(e,t);return b.jsx(Tu.div,{...o,className:Hx("chakra-slider__filled-track",e.className),__css:n.filledTrack})}));ww.displayName="SliderFilledTrack",Mu(((e,t)=>{const{getMarkerProps:r}=hw(),n=vw(),o=r(e,t);return b.jsx(Tu.div,{...o,className:Hx("chakra-slider__marker",e.className),__css:n.mark})})).displayName="SliderMark";var[kw,Sw]=ot({name:"StatStylesContext",errorMessage:"useStatStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Stat />\" "}),_w=Mu((function(e,t){const r=yu("Stat",e),n={position:"relative",flex:"1 1 0%",...r.container},{className:o,children:a,...i}=En(e);return b.jsx(kw,{value:r,children:b.jsx(Tu.div,{ref:t,...i,className:Ot("chakra-stat",o),__css:n,children:b.jsx("dl",{children:a})})})}));_w.displayName="Stat";var Cw=e=>b.jsx(ed,{color:"red.400",...e,children:b.jsx("path",{fill:"currentColor",d:"M21,5H3C2.621,5,2.275,5.214,2.105,5.553C1.937,5.892,1.973,6.297,2.2,6.6l9,12 c0.188,0.252,0.485,0.4,0.8,0.4s0.611-0.148,0.8-0.4l9-12c0.228-0.303,0.264-0.708,0.095-1.047C21.725,5.214,21.379,5,21,5z"})});function Ew(e){return b.jsx(ed,{color:"green.400",...e,children:b.jsx("path",{fill:"currentColor",d:"M12.8,5.4c-0.377-0.504-1.223-0.504-1.6,0l-9,12c-0.228,0.303-0.264,0.708-0.095,1.047 C2.275,18.786,2.621,19,3,19h18c0.379,0,0.725-0.214,0.895-0.553c0.169-0.339,0.133-0.744-0.095-1.047L12.8,5.4z"})})}function jw(e){const{type:t,"aria-label":r,...n}=e,o=Sw(),a="increase"===t?Ew:Cw,i=r||("increase"===t?"increased by":"decreased by");return b.jsxs(b.Fragment,{children:[b.jsx(Tu.span,{srOnly:!0,children:i}),b.jsx(a,{"aria-hidden":!0,...n,__css:o.icon})]})}Cw.displayName="StatDownArrow",Ew.displayName="StatUpArrow",jw.displayName="StatArrow";var Nw=Mu((function(e,t){return b.jsx(Tu.div,{...e,ref:t,role:"group",className:Ot("chakra-stat__group",e.className),__css:{display:"flex",flexWrap:"wrap",justifyContent:"space-around",alignItems:"flex-start"}})}));Nw.displayName="StatGroup";var Ow=Mu((function(e,t){const r=Sw();return b.jsx(Tu.dd,{ref:t,...e,className:Ot("chakra-stat__help-text",e.className),__css:r.helpText})}));Ow.displayName="StatHelpText";var Aw=Mu((function(e,t){const r=Sw();return b.jsx(Tu.dt,{ref:t,...e,className:Ot("chakra-stat__label",e.className),__css:r.label})}));Aw.displayName="StatLabel";var zw=Mu((function(e,t){const r=Sw();return b.jsx(Tu.dd,{ref:t,...e,className:Ot("chakra-stat__number",e.className),__css:{...r.number,fontFeatureSettings:"pnum",fontVariantNumeric:"proportional-nums"}})}));zw.displayName="StatNumber";var Iw=Mu((function(e,t){const r=yu("Switch",e),{spacing:n="0.5rem",children:a,...i}=En(e),{getIndicatorProps:s,getInputProps:l,getCheckboxProps:c,getRootProps:u,getLabelProps:d}=Rp(i),f=o.useMemo((()=>({display:"inline-block",position:"relative",verticalAlign:"middle",lineHeight:0,...r.container})),[r.container]),p=o.useMemo((()=>({display:"inline-flex",flexShrink:0,justifyContent:"flex-start",boxSizing:"content-box",cursor:"pointer",...r.track})),[r.track]),m=o.useMemo((()=>({userSelect:"none",marginStart:n,...r.label})),[n,r.label]);return b.jsxs(Tu.label,{...u(),className:Ot("chakra-switch",e.className),__css:f,children:[b.jsx("input",{className:"chakra-switch__input",...l({},t)}),b.jsx(Tu.span,{...c(),className:"chakra-switch__track",__css:p,children:b.jsx(Tu.span,{__css:r.thumb,className:"chakra-switch__thumb",...s()})}),a&&b.jsx(Tu.span,{className:"chakra-switch__label",...d(),__css:m,children:a})]})}));Iw.displayName="Switch";var[Pw,Rw]=ot({name:"TableStylesContext",errorMessage:"useTableStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Table />\" "}),Tw=Mu(((e,t)=>{const r=yu("Table",e),{className:n,layout:o,...a}=En(e);return b.jsx(Pw,{value:r,children:b.jsx(Tu.table,{ref:t,__css:{tableLayout:o,...r.table},className:Ot("chakra-table",n),...a})})}));Tw.displayName="Table";var Mw=Mu(((e,t)=>{const r=Rw();return b.jsx(Tu.thead,{...e,ref:t,__css:r.thead})})),$w=Mu(((e,t)=>{const r=Rw();return b.jsx(Tu.tr,{...e,ref:t,__css:r.tr})})),Dw=Mu(((e,t)=>{var r;const{overflow:n,overflowX:o,className:a,...i}=e;return b.jsx(Tu.div,{ref:t,className:Ot("chakra-table__container",a),...i,__css:{display:"block",whiteSpace:"nowrap",WebkitOverflowScrolling:"touch",overflowX:null!=(r=null!=n?n:o)?r:"auto",overflowY:"hidden",maxWidth:"100%"}})})),Bw=Mu(((e,t)=>{const r=Rw();return b.jsx(Tu.tbody,{...e,ref:t,__css:r.tbody})})),Fw=Mu((({isNumeric:e,...t},r)=>{const n=Rw();return b.jsx(Tu.td,{...t,ref:r,__css:n.td,"data-is-numeric":e})})),Lw=Mu((({isNumeric:e,...t},r)=>{const n=Rw();return b.jsx(Tu.th,{...t,ref:r,__css:n.th,"data-is-numeric":e})})),[Ww,Hw,qw,Vw]=$d();var[Uw,Gw]=ot({name:"TabsContext",errorMessage:"useTabsContext: `context` is undefined. Seems you forgot to wrap all tabs components within <Tabs />"});var[Xw,Yw]=ot({});function Kw(e,t){return`${e}--tab-${t}`}function Zw(e,t){return`${e}--tabpanel-${t}`}var[Qw,Jw]=ot({name:"TabsStylesContext",errorMessage:"useTabsStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Tabs />\" "}),ek=Mu((function(e,t){const r=yu("Tabs",e),{children:n,className:a,...i}=En(e),{htmlProps:s,descendants:l,...c}=function(e){var t;const{defaultIndex:r,onChange:n,index:a,isManual:i,isLazy:s,lazyBehavior:l="unmount",orientation:c="horizontal",direction:u="ltr",...d}=e,[f,p]=o.useState(null!=r?r:0),[m,h]=Gd({defaultValue:null!=r?r:0,value:a,onChange:n});o.useEffect((()=>{null!=a&&p(a)}),[a]);const b=qw(),v=o.useId();return{id:`tabs-${null!=(t=e.id)?t:v}`,selectedIndex:m,focusedIndex:f,setSelectedIndex:h,setFocusedIndex:p,isManual:i,isLazy:s,lazyBehavior:l,orientation:c,descendants:b,direction:u,htmlProps:d}}(i),u=o.useMemo((()=>c),[c]),{isFitted:d,...f}=s;return b.jsx(Ww,{value:l,children:b.jsx(Uw,{value:u,children:b.jsx(Qw,{value:r,children:b.jsx(Tu.div,{className:Ot("chakra-tabs",a),ref:t,...f,__css:r.root,children:n})})})})}));ek.displayName="Tabs";var tk=Mu((function(e,t){const r=function(e){const{focusedIndex:t,orientation:r,direction:n}=Gw(),a=Hw(),i=o.useCallback((e=>{const o=()=>{var e;const r=a.nextEnabled(t);r&&(null==(e=r.node)||e.focus())},i=()=>{var e;const r=a.prevEnabled(t);r&&(null==(e=r.node)||e.focus())},s="horizontal"===r,l="vertical"===r,c=e.key,u="ltr"===n?"ArrowLeft":"ArrowRight",d="ltr"===n?"ArrowRight":"ArrowLeft",f={[u]:()=>s&&i(),[d]:()=>s&&o(),ArrowDown:()=>l&&o(),ArrowUp:()=>l&&i(),Home:()=>{var e;const t=a.firstEnabled();t&&(null==(e=t.node)||e.focus())},End:()=>{var e;const t=a.lastEnabled();t&&(null==(e=t.node)||e.focus())}}[c];f&&(e.preventDefault(),f(e))}),[a,t,r,n]);return{...e,role:"tablist","aria-orientation":r,onKeyDown:Mt(e.onKeyDown,i)}}({...e,ref:t}),n={display:"flex",...Jw().tablist};return b.jsx(Tu.div,{...r,className:Ot("chakra-tabs__tablist",e.className),__css:n})}));tk.displayName="TabList";var rk=Mu((function(e,t){const r=function(e){const{children:t,...r}=e,{isLazy:n,lazyBehavior:a}=Gw(),{isSelected:i,id:s,tabId:l}=Yw(),c=o.useRef(!1);return i&&(c.current=!0),{tabIndex:0,...r,children:Mg({wasSelected:c.current,isSelected:i,enabled:n,mode:a})?t:null,role:"tabpanel","aria-labelledby":l,hidden:!i,id:s}}({...e,ref:t}),n=Jw();return b.jsx(Tu.div,{outline:"0",...r,className:Ot("chakra-tabs__tab-panel",e.className),__css:n.tabpanel})}));rk.displayName="TabPanel";var nk=Mu((function(e,t){const r=function(e){const t=Gw(),{id:r,selectedIndex:n}=t,a=Pf(e.children).map(((e,t)=>o.createElement(Xw,{key:t,value:{isSelected:t===n,id:Zw(r,t),tabId:Kw(r,t),selectedIndex:n}},e)));return{...e,children:a}}(e),n=Jw();return b.jsx(Tu.div,{...r,width:"100%",ref:t,className:Ot("chakra-tabs__tab-panels",e.className),__css:n.tabpanels})}));nk.displayName="TabPanels";var ok=Mu((function(e,t){const r=Jw(),n=function(e){const{isDisabled:t=!1,isFocusable:r=!1,...n}=e,{setSelectedIndex:o,isManual:a,id:i,setFocusedIndex:s,selectedIndex:l}=Gw(),{index:c,register:u}=Vw({disabled:t&&!r}),d=c===l;return{...Zb({...n,ref:Pd(u,e.ref),isDisabled:t,isFocusable:r,onClick:Mt(e.onClick,(()=>{o(c)}))}),id:Kw(i,c),role:"tab",tabIndex:d?0:-1,type:"button","aria-selected":d,"aria-controls":Zw(i,c),onFocus:t?void 0:Mt(e.onFocus,(()=>{s(c),!a&&(!t||!r)&&o(c)}))}}({...e,ref:t}),o={outline:"0",display:"flex",alignItems:"center",justifyContent:"center",...r.tab};return b.jsx(Tu.button,{...n,className:Ot("chakra-tabs__tab",e.className),__css:o})}));ok.displayName="Tab";var[ak,ik]=ot({name:"TagStylesContext",errorMessage:"useTagStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Tag />\" "}),sk=Mu(((e,t)=>{const r=yu("Tag",e),n=En(e),o={display:"inline-flex",verticalAlign:"top",alignItems:"center",maxWidth:"100%",...r.container};return b.jsx(ak,{value:r,children:b.jsx(Tu.span,{ref:t,...n,__css:o})})}));sk.displayName="Tag",Mu(((e,t)=>{const r=ik();return b.jsx(Tu.span,{ref:t,noOfLines:1,...e,__css:r.label})})).displayName="TagLabel",Mu(((e,t)=>b.jsx(ed,{ref:t,verticalAlign:"top",marginEnd:"0.5rem",...e}))).displayName="TagLeftIcon",Mu(((e,t)=>b.jsx(ed,{ref:t,verticalAlign:"top",marginStart:"0.5rem",...e}))).displayName="TagRightIcon";var lk=e=>b.jsx(ed,{verticalAlign:"inherit",viewBox:"0 0 512 512",...e,children:b.jsx("path",{fill:"currentColor",d:"M289.94 256l95-95A24 24 0 00351 127l-95 95-95-95a24 24 0 00-34 34l95 95-95 95a24 24 0 1034 34l95-95 95 95a24 24 0 0034-34z"})});lk.displayName="TagCloseIcon",Mu(((e,t)=>{const{isDisabled:r,children:n,...o}=e,a={display:"flex",alignItems:"center",justifyContent:"center",outline:"0",...ik().closeButton};return b.jsx(Tu.button,{ref:t,"aria-label":"close",...o,type:"button",disabled:r,__css:a,children:n||b.jsx(lk,{})})})).displayName="TagCloseButton";var ck=["h","minH","height","minHeight"],uk=Mu(((e,t)=>{const r=gu("Textarea",e),{className:n,rows:o,...a}=En(e),i=vp(a),s=o?function(e,t=[]){const r=Object.assign({},e);for(const n of t)n in r&&delete r[n];return r}(r,ck):r;return b.jsx(Tu.textarea,{ref:t,rows:o,...i,className:Ot("chakra-textarea",n),__css:s})}));uk.displayName="Textarea";var dk={exit:{scale:.85,opacity:0,transition:{opacity:{duration:.15,easings:"easeInOut"},scale:{duration:.2,easings:"easeInOut"}}},enter:{scale:1,opacity:1,transition:{opacity:{easings:"easeOut",duration:.2},scale:{duration:.2,ease:[.175,.885,.4,1.1]}}}},fk=e=>{var t;return(null==(t=e.current)?void 0:t.ownerDocument)||document},pk=e=>{var t,r;return(null==(r=null==(t=e.current)?void 0:t.ownerDocument)?void 0:r.defaultView)||window};function mk(e={}){const{openDelay:t=0,closeDelay:r=0,closeOnClick:n=!0,closeOnMouseDown:a,closeOnScroll:i,closeOnPointerDown:s=a,closeOnEsc:l=!0,onOpen:c,onClose:u,placement:d,id:f,isOpen:p,defaultIsOpen:m,arrowSize:h=10,arrowShadowColor:b,arrowPadding:v,modifiers:g,isDisabled:y,gutter:x,offset:w,direction:k,...S}=e,{isOpen:_,onOpen:C,onClose:E}=Ig({isOpen:p,defaultIsOpen:m,onOpen:c,onClose:u}),{referenceRef:j,getPopperProps:N,getArrowInnerProps:O,getArrowProps:A}=Ag({enabled:_,placement:d,arrowPadding:v,modifiers:g,gutter:x,offset:w,direction:k}),z=o.useId(),I=`tooltip-${null!=f?f:z}`,P=o.useRef(null),R=o.useRef(),T=o.useCallback((()=>{R.current&&(clearTimeout(R.current),R.current=void 0)}),[]),M=o.useRef(),$=o.useCallback((()=>{M.current&&(clearTimeout(M.current),M.current=void 0)}),[]),D=o.useCallback((()=>{$(),E()}),[E,$]),B=function(e,t){return o.useEffect((()=>{const r=fk(e);return r.addEventListener(hk,t),()=>r.removeEventListener(hk,t)}),[t,e]),()=>{const t=fk(e),r=pk(e);t.dispatchEvent(new r.CustomEvent(hk))}}(P,D),F=o.useCallback((()=>{if(!y&&!R.current){_&&B();const e=pk(P);R.current=e.setTimeout(C,t)}}),[B,y,_,C,t]),L=o.useCallback((()=>{T();const e=pk(P);M.current=e.setTimeout(D,r)}),[r,D,T]),W=o.useCallback((()=>{_&&n&&L()}),[n,L,_]),H=o.useCallback((()=>{_&&s&&L()}),[s,L,_]),q=o.useCallback((e=>{_&&"Escape"===e.key&&L()}),[_,L]);Up((()=>fk(P)),"keydown",l?q:void 0),Up((()=>{const e=P.current;if(!e)return null;const t=Lh(e);return"body"===t.localName?pk(P):t}),"scroll",(()=>{_&&i&&D()}),{passive:!0,capture:!0}),o.useEffect((()=>{y&&(T(),_&&E())}),[y,_,E,T]),o.useEffect((()=>()=>{T(),$()}),[T,$]),Up((()=>P.current),"pointerleave",L);const V=o.useCallback(((e={},t=null)=>({...e,ref:Pd(P,t,j),onPointerEnter:Mt(e.onPointerEnter,(e=>{"touch"!==e.pointerType&&F()})),onClick:Mt(e.onClick,W),onPointerDown:Mt(e.onPointerDown,H),onFocus:Mt(e.onFocus,F),onBlur:Mt(e.onBlur,L),"aria-describedby":_?I:void 0})),[F,L,H,_,I,W,j]),U=o.useCallback(((e={},t=null)=>N({...e,style:{...e.style,[ev.arrowSize.var]:h?`${h}px`:void 0,[ev.arrowShadowColor.var]:b}},t)),[N,h,b]),G=o.useCallback(((e={},t=null)=>{const r={...e.style,position:"relative",transformOrigin:ev.transformOrigin.varRef};return{ref:t,...S,...e,id:I,role:"tooltip",style:r}}),[S,I]);return{isOpen:_,show:F,hide:L,getTriggerProps:V,getTooltipProps:G,getTooltipPositionerProps:U,getArrowProps:A,getArrowInnerProps:O}}var hk="chakra-ui:close-tooltip";var bk=Tu(l.div),vk=Mu(((e,t)=>{var r,n;const a=gu("Tooltip",e),i=En(e),s=bt(),{children:l,label:u,shouldWrapChildren:d,"aria-label":f,hasArrow:p,bg:m,portalProps:h,background:v,backgroundColor:g,bgColor:y,motionProps:x,...w}=i,k=null!=(n=null!=(r=null!=v?v:g)?r:m)?n:y;if(k){a.bg=k;const e=function(e,t,r){var n,o,a;return null!=(a=null==(o=null==(n=e.__cssMap)?void 0:n[`${t}.${r}`])?void 0:o.varRef)?a:r}(s,"colors",k);a[ev.arrowBg.var]=e}const S=mk({...w,direction:s.direction});let _;if("string"==typeof l||d)_=b.jsx(Tu.span,{display:"inline-block",tabIndex:0,...S.getTriggerProps(),children:l});else{const e=o.Children.only(l);_=o.cloneElement(e,S.getTriggerProps(e.props,e.ref))}const C=!!f,E=S.getTooltipProps({},t),j=C?function(e,t=[]){const r=Object.assign({},e);for(const n of t)n in r&&delete r[n];return r}(E,["role","id"]):E,N=function(e,t){const r={};for(const n of t)n in e&&(r[n]=e[n]);return r}(E,["role","id"]);return u?b.jsxs(b.Fragment,{children:[_,b.jsx(c,{children:S.isOpen&&b.jsx(ht,{...h,children:b.jsx(Tu.div,{...S.getTooltipPositionerProps(),__css:{zIndex:a.zIndex,pointerEvents:"none"},children:b.jsxs(bk,{variants:dk,initial:"exit",animate:"enter",exit:"exit",...x,...j,__css:a,children:[u,C&&b.jsx(Tu.span,{srOnly:!0,...N,children:f}),p&&b.jsx(Tu.div,{"data-popper-arrow":!0,className:"chakra-tooltip__arrow-wrapper",children:b.jsx(Tu.div,{"data-popper-arrow-inner":!0,className:"chakra-tooltip__arrow",__css:{bg:a.bg}})})]})})})})]}):b.jsx(b.Fragment,{children:l})}));vk.displayName="Tooltip";export{yw as $,pd as A,Gf as B,ff as C,Cb as D,qf as E,dp as F,Lp as G,Tb as H,Yf as I,ed as J,_b as K,vk as L,Ey as M,Fb as N,pb as O,jx as P,Vb as Q,g as R,Ox as S,Eb as T,tp as U,zb as V,wb as W,qb as X,gw as Y,xw as Z,ww as _,Mb as a,mb as a0,Xb as a1,Lb as a2,od as a3,If as a4,Jf as a5,Db as a6,_w as a7,Aw as a8,zw as a9,Jd as aA,Ud as aB,sk as aC,Qd as aD,yf as aE,Nw as aF,Yg as aG,oy as aH,ry as aI,Jg as aJ,Tw as aK,Mw as aL,$w as aM,Lw as aN,Bw as aO,Fw as aP,Bf as aQ,Df as aR,Mf as aS,px as aT,hx as aU,Dw as aV,ay as aW,Ow as aa,jw as ab,ep as ac,Rb as ad,ab as ae,ek as af,tk as ag,ok as ah,nk as ai,rk as aj,lb as ak,db as al,fb as am,Dx as an,Fx as ao,gt as ap,Iw as aq,Ab as ar,gb as as,yb as at,Bb as au,jb as av,Nb as aw,vb as ax,xb as ay,xf as az,Sd as b,td as c,dd as d,qc as e,fd as f,ud as g,Ib as h,Hb as i,b as j,yt as k,yx as l,sx as m,vx as n,wx as o,xx as p,hp as q,mp as r,uk as s,Of as t,rb as u,bx as v,Cd as w,Ub as x,lx as y,cx as z};
