const e=window.fetch,n=console.error,o=console.warn;console.error=(...e)=>{const o=e.join(" ");(o.includes("401")||o.includes("Unauthorized"))&&(o.includes("/auth/me")||o.includes("fetchInterceptor")||o.includes("Failed to load resource"))||n.apply(console,e)},console.warn=(...e)=>{const n=e.join(" ");(n.includes("401")||n.includes("Unauthorized"))&&(n.includes("/auth/me")||n.includes("Failed to load resource"))||o.apply(console,e)};const t=console.log;console.log=(...e)=>{const n=e.join(" ");(n.includes("401")||n.includes("Unauthorized"))&&n.includes("/auth/me")||t.apply(console,e)},window.fetch=async(...o)=>{try{const n=await e(...o);return 401===n.status&&o[0]?.toString().includes("/auth/me"),n}catch(t){throw o[0]?.toString().includes("/auth/me")||n("Fetch error:",t),t}},window.addEventListener("error",(e=>{if(e.message&&(e.message.includes("401")||e.message.includes("Unauthorized"))&&e.message.includes("/auth/me"))return e.preventDefault(),e.stopPropagation(),!1})),window.addEventListener("unhandledrejection",(e=>{if(e.reason&&e.reason.toString&&(e.reason.toString().includes("401")||e.reason.toString().includes("Unauthorized"))&&e.reason.toString().includes("/auth/me"))return e.preventDefault(),!1}));
