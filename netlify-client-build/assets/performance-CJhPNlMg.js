var e=Object.defineProperty,t=(t,r,i)=>((t,r,i)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[r]=i)(t,"symbol"!=typeof r?r+"":r,i);import{d as r}from"./vendor-BJbfb-22.js";import{M as i}from"./index-48bDQXfm.js";import"./ui-CxDIvMgK.js";import"./icons-DRFAkztb.js";import"./utils-DuWhH-qE.js";import"./charts-Bmy0KzL9.js";const n=new class{constructor(e={}){t(this,"metrics",[]),t(this,"webVitals",{}),t(this,"isEnabled"),t(this,"reportingEndpoint"),t(this,"isProduction",!0),this.isEnabled=e.enabled??!1,this.reportingEndpoint=e.reportingEndpoint??"https://careerdart-server.fly.dev/api/operations/report-performance-metrics",this.isEnabled&&"undefined"!=typeof window&&this.initializeMonitoring(),this.isProduction&&"undefined"!=typeof window&&(this.initializeWebVitals(),this.initializeImageOptimizations(),this.initializePrefetching())}initializeMonitoring(){this.observeCLS(),this.observeINP(),this.observeFCP(),this.observeLCP(),this.observeTTFB(),this.observeNavigationTiming(),this.observeResourceTiming(),this.observeLongTasks(),this.startPeriodicReporting(),this.setupUnloadReporting()}async initializeWebVitals(){}reportMetric(e,t){}initializeImageOptimizations(){if("IntersectionObserver"in window){const e=new IntersectionObserver((t=>{t.forEach((t=>{if(t.isIntersecting){const r=t.target,i=r.dataset.src;i&&(r.src=i,r.classList.remove("lazy"),e.unobserve(r))}}))}));document.querySelectorAll("img[data-src]").forEach((t=>{e.observe(t)}))}}initializePrefetching(){const e=["/cover-letters","/resume","/jobs"];"requestIdleCallback"in window&&requestIdleCallback((()=>{e.forEach((e=>{const t=document.createElement("link");t.rel="prefetch",t.href=e,document.head.appendChild(t)}))}))}preloadCriticalCSS(e){const t=document.createElement("link");t.rel="preload",t.as="style",t.href=e,t.onload=()=>{t.rel="stylesheet"},document.head.appendChild(t)}optimizeFonts(){[].forEach((e=>{const t=document.createElement("link");t.rel="preload",t.as="font",t.type="font/woff2",t.href=e,t.crossOrigin="anonymous",document.head.appendChild(t)}))}addResourceHints(){[{rel:"dns-prefetch",href:"//fonts.googleapis.com"},{rel:"dns-prefetch",href:"//api.openai.com"},{rel:"preconnect",href:"https://fonts.gstatic.com",crossorigin:!0}].forEach((e=>{const t=document.createElement("link");t.rel=e.rel,t.href=e.href,e.crossorigin&&(t.crossOrigin="anonymous"),document.head.appendChild(t)}))}observeCLS(){if(!("LayoutShift"in window))return;let e=0;new PerformanceObserver((t=>{for(const r of t.getEntries())r.hadRecentInput||(e+=r.value);this.recordVital("CLS",e,this.getRating("CLS",e))})).observe({type:"layout-shift",buffered:!0})}observeINP(){if(!("PerformanceEventTiming"in window))return;new PerformanceObserver((e=>{for(const t of e.getEntries())if("first-input"===t.name){const e=t.processingStart-t.startTime;this.recordVital("INP",e,this.getRating("INP",e))}})).observe({type:"first-input",buffered:!0})}observeFCP(){new PerformanceObserver((e=>{for(const t of e.getEntries())"first-contentful-paint"===t.name&&this.recordVital("FCP",t.startTime,this.getRating("FCP",t.startTime))})).observe({type:"paint",buffered:!0})}observeLCP(){new PerformanceObserver((e=>{const t=e.getEntries(),r=t[t.length-1];this.recordVital("LCP",r.startTime,this.getRating("LCP",r.startTime))})).observe({type:"largest-contentful-paint",buffered:!0})}observeTTFB(){new PerformanceObserver((e=>{for(const t of e.getEntries()){const e=t.responseStart-t.requestStart;this.recordVital("TTFB",e,this.getRating("TTFB",e))}})).observe({type:"navigation",buffered:!0})}observeNavigationTiming(){new PerformanceObserver((e=>{for(const t of e.getEntries())this.recordMetric("DOM_CONTENT_LOADED",t.domContentLoadedEventEnd-t.domContentLoadedEventStart),this.recordMetric("LOAD_EVENT",t.loadEventEnd-t.loadEventStart),this.recordMetric("DNS_LOOKUP",t.domainLookupEnd-t.domainLookupStart),this.recordMetric("TCP_CONNECT",t.connectEnd-t.connectStart),this.recordMetric("REQUEST_RESPONSE",t.responseEnd-t.requestStart)})).observe({type:"navigation",buffered:!0})}observeResourceTiming(){new PerformanceObserver((e=>{for(const t of e.getEntries()){const e=t.responseEnd-t.startTime;e>1e3&&this.recordMetric("SLOW_RESOURCE",e,{url:t.name,type:t.initiatorType,size:t.transferSize})}})).observe({type:"resource",buffered:!0})}observeLongTasks(){if(!("PerformanceLongTaskTiming"in window))return;new PerformanceObserver((e=>{for(const t of e.getEntries())this.recordMetric("LONG_TASK",t.duration,{startTime:t.startTime,attribution:t.attribution})})).observe({type:"longtask",buffered:!0})}getRating(e,t){const r={CLS:{good:.1,poor:.25},INP:{good:100,poor:300},FCP:{good:1800,poor:3e3},LCP:{good:2500,poor:4e3},TTFB:{good:800,poor:1800}}[e];return r?t<=r.good?"good":t<=r.poor?"needs-improvement":"poor":"good"}recordMetric(e,t,r){const i={name:e,value:t,timestamp:Date.now(),url:window.location.href,userAgent:navigator.userAgent,...r};this.metrics.push(i)}recordVital(e,t,r){const i={name:e,value:t,rating:r,timestamp:Date.now(),url:window.location.href,userAgent:navigator.userAgent};this.metrics.push(i)}startPeriodicReporting(){setInterval((()=>{this.reportMetrics()}),3e4)}setupUnloadReporting(){window.addEventListener("beforeunload",(()=>{this.reportMetrics(!0)})),document.addEventListener("visibilitychange",(()=>{"hidden"===document.visibilityState&&this.reportMetrics(!0)}))}async reportMetrics(e=!1){if(0===this.metrics.length)return;const t=[...this.metrics];this.metrics=[];try{e&&"sendBeacon"in navigator?navigator.sendBeacon(this.reportingEndpoint,JSON.stringify({metrics:t})):await i({metrics:t})}catch(r){this.metrics.unshift(...t)}}recordCustomMetric(e,t,r){this.recordMetric(`CUSTOM_${e}`,t,r)}measureFunction(e,t){const r=performance.now(),i=t(),n=performance.now()-r;return this.recordMetric(`FUNCTION_${e}`,n),i}async measureAsyncFunction(e,t){const r=performance.now(),i=await t(),n=performance.now()-r;return this.recordMetric(`ASYNC_FUNCTION_${e}`,n),i}getMetrics(){return[...this.metrics]}clearMetrics(){this.metrics=[]}setEnabled(e){this.isEnabled=e}};function o(){return{recordMetric:(e,t,r)=>{n.recordCustomMetric(e,t,r)},measureRender:e=>{const t=performance.now();return()=>{const r=performance.now()-t;n.recordCustomMetric(`RENDER_${e}`,r)}},measureFunction:n.measureFunction.bind(n),measureAsyncFunction:n.measureAsyncFunction.bind(n)}}function s(e,t){const i=t||e.displayName||e.name||"Unknown";return function(t){const{measureRender:n}=o();return r.useEffect((()=>n(i))),r.createElement(e,t)}}const a=async e=>{try{return await e()}catch(t){return null}},c=(e,t)=>{let r;return(...i)=>{clearTimeout(r),r=setTimeout((()=>e(...i)),t)}},d=(e,t)=>r.memo(e,t);export{c as debounce,a as lazyImport,d as memoizeComponent,n as performanceMonitor,o as usePerformanceMonitoring,s as withPerformanceMonitoring};
