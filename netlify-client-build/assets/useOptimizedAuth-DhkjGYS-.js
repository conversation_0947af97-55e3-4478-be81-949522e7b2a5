import"./ui-CxDIvMgK.js";import{u as t}from"./index-D-VERaZz.js";import{b as e}from"./vendor-BJbfb-22.js";import"./icons-DRFAkztb.js";import"./utils-DuWhH-qE.js";import"./charts-Bmy0KzL9.js";let o=null,s=0;const n=console.error;function r(){const n=t(),[r,a]=e.useState((()=>o||n)),i=e.useRef(s);return e.useEffect((()=>{const t=Date.now();(t-i.current>3e4||n.data?.id!==r.data?.id||n.isLoading!==r.isLoading||n.data&&!r.data||!n.data&&r.data)&&(a(n),i.current=t,o=n,s=t)}),[n,r]),e.useMemo((()=>r),[r])}function a(){const[o,s]=e.useState(null),[n,r]=e.useState(!1),a=t();return e.useEffect((()=>{n||a.isLoading||(s(a),r(!0))}),[a,n]),n?o:a}console.error=(...t)=>{const e=t.join(" ");e.includes("401")&&(e.includes("/auth/me")||e.includes("Unauthorized"))||n.apply(console,t)};export{r as useOptimizedAuth,a as useStaticAuth};
