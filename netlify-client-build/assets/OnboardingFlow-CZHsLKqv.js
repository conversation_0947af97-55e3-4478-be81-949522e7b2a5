import{k as e,j as i,M as r,l as s,m as l,p as n,V as o,y as t,a,B as c,P as d,T as u,H as h,K as x,h as g,v as p,L as m,U as j,a5 as b}from"./ui-CxDIvMgK.js";import{b as f}from"./vendor-BJbfb-22.js";import{q as S,r as y,s as z,t as v,v as C,e as w,h as k,w as W,x as Y,y as A,z as D,d as R}from"./index-Bu-v5N9o.js";import"./icons-DRFAkztb.js";import"./utils-DuWhH-qE.js";import"./charts-Bmy0KzL9.js";const I=({isOpen:I,onClose:L,onComplete:P,onSkip:$})=>{const[B,E]=f.useState(0),[O,T]=f.useState({careerStage:"",industries:[],goals:[],experience:""}),_=e("white","gray.800"),F=e("gray.50","gray.700"),G=e("gray.200","gray.600"),H=e("gray.800","white"),M=e("gray.600","gray.400"),q=[{title:"Welcome to CareerDart!",subtitle:"Let's create your personalized career toolkit",content:"WelcomeStep"},{title:"What's your career stage?",subtitle:"This helps us tailor our recommendations",content:"CareerStageStep"},{title:"Which industries interest you?",subtitle:"Select all that apply",content:"IndustriesStep"},{title:"What are your main goals?",subtitle:"We'll customize features based on your priorities",content:"GoalsStep"},{title:"You're all set! 🎉",subtitle:"Your personalized CareerDart experience awaits",content:"CompletionStep"}],J=[{id:"student",label:"Student/Recent Graduate",icon:C,color:"blue"},{id:"entry",label:"Entry Level (0-2 years)",icon:y,color:"green"},{id:"mid",label:"Mid Level (3-7 years)",icon:w,color:"purple"},{id:"senior",label:"Senior Level (8+ years)",icon:k,color:"orange"},{id:"executive",label:"Executive/Leadership",icon:W,color:"red"}],K=[{id:"tech",label:"Technology",icon:Y},{id:"finance",label:"Finance & Banking",icon:k},{id:"healthcare",label:"Healthcare",icon:A},{id:"education",label:"Education",icon:C},{id:"consulting",label:"Consulting",icon:W},{id:"retail",label:"Retail & E-commerce",icon:D},{id:"nonprofit",label:"Non-profit",icon:A},{id:"manufacturing",label:"Manufacturing",icon:D}],N=[{id:"findJob",label:"Find my next job",icon:w},{id:"improveResume",label:"Improve my resume",icon:R},{id:"interviewPrep",label:"Prepare for interviews",icon:W},{id:"careerChange",label:"Change career paths",icon:y},{id:"networking",label:"Build my network",icon:W},{id:"skillDev",label:"Develop new skills",icon:k}],U=(e,i)=>{T((r=>({...r,[e]:r[e].includes(i)?r[e].filter((e=>e!==i)):[...r[e],i]})))};return i.jsxs(r,{isOpen:I,onClose:L,size:"2xl",closeOnOverlayClick:!1,children:[i.jsx(s,{backdropFilter:"blur(4px)"}),i.jsx(l,{bg:_,maxW:"600px",mx:4,children:i.jsx(n,{p:8,children:i.jsxs(o,{spacing:6,align:"stretch",children:[i.jsxs(t,{justify:"space-between",align:"center",children:[i.jsx(a,{}),i.jsx(c,{variant:"ghost",size:"sm",color:M,onClick:()=>{$?$():L()},_hover:{color:H},children:"Skip for now"})]}),i.jsxs(o,{spacing:2,children:[i.jsx(d,{value:B/(q.length-1)*100,colorScheme:"blue",size:"sm",w:"full",borderRadius:"full"}),i.jsxs(u,{fontSize:"xs",color:M,alignSelf:"flex-end",children:["Step ",B+1," of ",q.length]})]}),i.jsxs(o,{spacing:2,textAlign:"center",children:[i.jsx(h,{size:"lg",color:H,children:q[B].title}),i.jsx(u,{color:M,children:q[B].subtitle})]}),i.jsx(a,{minH:"300px",children:(()=>{switch(B){case 0:return i.jsx(o,{spacing:6,textAlign:"center",children:i.jsx(a,{p:6,bg:F,borderRadius:"xl",border:"1px",borderColor:G,w:"full",children:i.jsxs(o,{spacing:3,children:[i.jsx(u,{fontSize:"sm",fontWeight:"medium",color:H,children:"🎯 What we'll set up together:"}),i.jsxs(o,{spacing:2,align:"start",w:"full",children:[i.jsxs(g,{spacing:3,children:[i.jsx(u,{fontSize:"sm",color:"blue.500",children:"✨"}),i.jsx(u,{fontSize:"sm",color:M,children:"Your career stage and experience level"})]}),i.jsxs(g,{spacing:3,children:[i.jsx(u,{fontSize:"sm",color:"blue.500",children:"🏢"}),i.jsx(u,{fontSize:"sm",color:M,children:"Industry preferences and interests"})]}),i.jsxs(g,{spacing:3,children:[i.jsx(u,{fontSize:"sm",color:"blue.500",children:"📈"}),i.jsx(u,{fontSize:"sm",color:M,children:"Career goals and priorities"})]})]}),i.jsx(u,{fontSize:"xs",color:M,fontStyle:"italic",mt:2,children:"⏱️ Takes just 2 minutes • Completely customizes your dashboard"}),i.jsx(a,{mt:3,p:3,bg:e("blue.50","blue.900"),borderRadius:"md",w:"full",children:i.jsx(u,{fontSize:"xs",color:"blue.600",textAlign:"center",fontWeight:"medium",children:"💡 Completing setup unlocks personalized job recommendations, tailored cover letter templates, and industry-specific interview tips!"})})]})})});case 1:return i.jsx(o,{spacing:4,children:i.jsx(m,{columns:{base:1,md:2},spacing:3,w:"full",children:J.map((e=>i.jsx(j,{variant:O.careerStage===e.id?"filled":"outline",cursor:"pointer",onClick:()=>{return i="careerStage",r=e.id,void T((e=>({...e,[i]:r})));var i,r},bg:O.careerStage===e.id?`${e.color}.50`:F,borderColor:O.careerStage===e.id?`${e.color}.300`:G,_hover:{borderColor:`${e.color}.400`,transform:"translateY(-2px)"},transition:"all 0.2s",children:i.jsx(b,{children:i.jsxs(g,{spacing:3,children:[i.jsx(x,{as:e.icon,boxSize:5,color:`${e.color}.500`}),i.jsx(o,{align:"start",spacing:0,children:i.jsx(u,{fontWeight:"medium",fontSize:"sm",color:H,children:e.label})}),O.careerStage===e.id&&i.jsx(x,{as:v,color:`${e.color}.500`,ml:"auto"})]})})},e.id)))})});case 2:return i.jsxs(o,{spacing:4,children:[i.jsx(m,{columns:{base:2,md:3},spacing:3,w:"full",children:K.map((e=>i.jsx(j,{variant:O.industries.includes(e.id)?"filled":"outline",cursor:"pointer",onClick:()=>U("industries",e.id),bg:O.industries.includes(e.id)?"blue.50":F,borderColor:O.industries.includes(e.id)?"blue.300":G,_hover:{borderColor:"blue.400",transform:"translateY(-2px)"},transition:"all 0.2s",size:"sm",children:i.jsx(b,{children:i.jsxs(o,{spacing:2,align:"center",children:[i.jsx(x,{as:e.icon,boxSize:5,color:"blue.500"}),i.jsx(u,{fontWeight:"medium",fontSize:"xs",textAlign:"center",color:H,children:e.label}),O.industries.includes(e.id)&&i.jsx(x,{as:v,color:"blue.500",boxSize:3})]})})},e.id)))}),i.jsxs(u,{fontSize:"sm",color:M,textAlign:"center",children:["Selected: ",O.industries.length," industries"]})]});case 3:return i.jsxs(o,{spacing:4,children:[i.jsx(m,{columns:{base:1,md:2},spacing:3,w:"full",children:N.map((e=>i.jsx(j,{variant:O.goals.includes(e.id)?"filled":"outline",cursor:"pointer",onClick:()=>U("goals",e.id),bg:O.goals.includes(e.id)?"purple.50":F,borderColor:O.goals.includes(e.id)?"purple.300":G,_hover:{borderColor:"purple.400",transform:"translateY(-2px)"},transition:"all 0.2s",children:i.jsx(b,{children:i.jsxs(g,{spacing:3,children:[i.jsx(x,{as:e.icon,boxSize:5,color:"purple.500"}),i.jsx(u,{fontWeight:"medium",fontSize:"sm",color:H,children:e.label}),O.goals.includes(e.id)&&i.jsx(x,{as:v,color:"purple.500",ml:"auto"})]})})},e.id)))}),i.jsxs(u,{fontSize:"sm",color:M,textAlign:"center",children:["Selected: ",O.goals.length," goals"]})]});case 4:return i.jsxs(o,{spacing:6,textAlign:"center",children:[i.jsx(x,{as:v,boxSize:16,color:"green.500"}),i.jsxs(o,{spacing:3,children:[i.jsx(u,{fontSize:"lg",color:H,children:"Perfect! Your CareerDart is ready 🎯"}),i.jsx(u,{color:M,children:"Based on your preferences, we've customized your dashboard with relevant tools and recommendations."})]}),i.jsx(a,{p:4,bg:F,borderRadius:"lg",border:"1px",borderColor:G,w:"full",children:i.jsxs(o,{spacing:2,align:"start",children:[i.jsx(u,{fontSize:"sm",fontWeight:"medium",color:H,children:"Your Profile:"}),i.jsxs(g,{flexWrap:"wrap",spacing:2,children:[i.jsx(p,{colorScheme:"blue",children:J.find((e=>e.id===O.careerStage))?.label}),O.industries.slice(0,3).map((e=>i.jsx(p,{colorScheme:"green",variant:"outline",children:K.find((i=>i.id===e))?.label},e))),O.industries.length>3&&i.jsxs(p,{colorScheme:"gray",children:["+",O.industries.length-3," more"]})]})]})})]});default:return null}})()}),i.jsxs(t,{justify:"space-between",align:"center",children:[i.jsx(c,{variant:"ghost",onClick:()=>{B>0&&E(B-1)},isDisabled:0===B,leftIcon:i.jsx(S,{}),size:"sm",children:"Previous"}),B===q.length-1?i.jsx(c,{colorScheme:"blue",onClick:()=>{P(O),L()},rightIcon:i.jsx(y,{}),size:"md",children:"Get Started!"}):i.jsx(c,{colorScheme:"blue",onClick:()=>{B<q.length-1&&E(B+1)},isDisabled:!(()=>{switch(B){case 0:case 4:return!0;case 1:return""!==O.careerStage;case 2:return O.industries.length>0;case 3:return O.goals.length>0;default:return!1}})(),rightIcon:i.jsx(z,{}),size:"md",children:0===B?"Start Setup":"Continue"})]})]})})})]})};export{I as default};
