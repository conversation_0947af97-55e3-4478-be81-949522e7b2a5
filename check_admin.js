const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkAndCreateAdmin() {
  try {
    // Check if admin user exists
    let user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        auth: {
          include: {
            identities: true
          }
        }
      }
    });

    if (!user) {
      console.log('Creating admin user...');
      
      // Create user
      user = await prisma.user.create({
        data: {
          username: 'admin',
          email: '<EMAIL>',
          hasPaid: true,
          gptModel: 'gpt-4o-mini',
          notifyPaymentExpires: false,
          credits: 1000,
          yearsOfExperience: 0,
          autoSave: true,
          darkMode: false,
          featureUpdates: true,
          jobReminders: true,
          marketingEmails: false,
          subscriptionReminders: true,
          wordCountDisplay: true
        }
      });
      
      console.log('User created with ID:', user.id);
    } else {
      console.log('Admin user already exists with ID:', user.id);
    }

    // Check if auth exists
    let auth = await prisma.auth.findUnique({
      where: { userId: user.id },
      include: { identities: true }
    });

    if (!auth) {
      console.log('Creating auth record...');
      auth = await prisma.auth.create({
        data: {
          userId: user.id,
          identities: {
            create: {
              providerName: 'email',
              providerUserId: user.id.toString(),
              providerData: JSON.stringify({ isVerified: true })
            }
          }
        },
        include: { identities: true }
      });
    } else {
      // Update existing auth identity to set verified status
      const emailIdentity = auth.identities.find(i => i.providerName === 'email');
      if (emailIdentity) {
        console.log('Updating existing auth identity...');
        await prisma.authIdentity.update({
          where: {
            providerName_providerUserId: {
              providerName: 'email',
              providerUserId: emailIdentity.providerUserId
            }
          },
          data: {
            providerData: JSON.stringify({ isVerified: true })
          }
        });
      } else {
        console.log('Creating email auth identity...');
        await prisma.authIdentity.create({
          data: {
            providerName: 'email',
            providerUserId: user.id.toString(),
            providerData: JSON.stringify({ isVerified: true }),
            authId: auth.id
          }
        });
      }
    }

    // Verify the result
    const finalUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        auth: {
          include: {
            identities: true
          }
        }
      }
    });

    console.log('Final user state:');
    console.log('User ID:', finalUser.id);
    console.log('Email:', finalUser.email);
    console.log('Username:', finalUser.username);
    if (finalUser.auth?.identities) {
      finalUser.auth.identities.forEach(identity => {
        console.log(`Provider: ${identity.providerName}, Data: ${identity.providerData}`);
      });
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAndCreateAdmin(); 