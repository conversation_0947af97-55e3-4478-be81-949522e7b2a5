# flyctl launch added from .gitignore
**/node_modules

# Ignore all dotenv files by default to prevent accidentally committing any secrets.
# To include specific dotenv files, use the `!` operator or adjust these rules.
**/.env
**/.env.*

# Don't ignore example dotenv files.
!**/.env.example
!**/.env.*.example

**/.DS_Store
# Local Netlify folder
**/.netlify
**/.vercel
**/.env*.local

# flyctl launch added from .wasp/build/server/.gitignore
.wasp/build/server/**/node_modules

.wasp/build/server/**/npm-debug.log
.wasp/build/server/**/*.log

.wasp/build/server/**/.env

# flyctl launch added from .wasp/build/web-app/.gitignore
# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
.wasp/build/web-app/node_modules
.wasp/build/web-app/.pnp
.wasp/build/web-app/**/.pnp.js

# testing
.wasp/build/web-app/coverage

# production
.wasp/build/web-app/build

# misc
.wasp/build/web-app/**/.DS_Store
.wasp/build/web-app/**/.env.local
.wasp/build/web-app/**/.env.development.local
.wasp/build/web-app/**/.env.test.local
.wasp/build/web-app/**/.env.production.local

.wasp/build/web-app/**/npm-debug.log*
.wasp/build/web-app/**/yarn-debug.log*
.wasp/build/web-app/**/yarn-error.log*
fly.toml
