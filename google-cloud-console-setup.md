# Google Cloud Console Setup for CareerDart Production

## 🎯 Exact Configuration Required

### 1. Go to Google Cloud Console
**URL**: https://console.cloud.google.com/apis/credentials

### 2. Find Your OAuth 2.0 Client ID
Look for your existing OAuth client or create a new one for production.

### 3. Edit OAuth Client Configuration

**Application type**: Web application
**Name**: CareerDart Production

### 4. Set Authorized JavaScript Origins
```
https://careerdart.com
```

**Important**: 
- ✅ Use `https://careerdart.com` (your frontend domain)
- ❌ Do NOT use `https://api.careerdart.com` here
- ❌ Do NOT include trailing slashes

### 5. Set Authorized Redirect URIs
```
https://api.careerdart.com/auth/google/callback
```

**Important**:
- ✅ Use `https://api.careerdart.com/auth/google/callback` (your backend domain + callback path)
- ❌ Do NOT use `https://careerdart.com/auth/google/callback`
- ✅ Include the exact path `/auth/google/callback`

### 6. <PERSON><PERSON><PERSON> Consent Screen
Make sure your OAuth consent screen is configured:
- **User Type**: External (for public use)
- **App name**: CareerDart
- **User support email**: Your email
- **App domain**: https://careerdart.com
- **Developer contact information**: Your email

### 7. Save Configuration
Click "Save" to apply the changes.

## 🔍 Common Mistakes to Avoid

### ❌ Wrong JavaScript Origins
```
https://api.careerdart.com  # WRONG - this is your backend
https://careerdart-prod.fly.dev  # WRONG - this is internal Fly.io URL
```

### ✅ Correct JavaScript Origins
```
https://careerdart.com  # CORRECT - this is your frontend
```

### ❌ Wrong Redirect URIs
```
https://careerdart.com/auth/google/callback  # WRONG - frontend domain
https://careerdart-prod.fly.dev/auth/google/callback  # WRONG - internal URL
```

### ✅ Correct Redirect URIs
```
https://api.careerdart.com/auth/google/callback  # CORRECT - backend domain
```

## 🧪 Testing Your Configuration

### 1. Test OAuth Flow
1. Go to https://careerdart.com/login
2. Click "Continue with Google"
3. Check browser network tab
4. Verify redirect goes to: `https://api.careerdart.com/auth/google/callback`

### 2. Check for Errors
Common error messages and solutions:

**Error**: `redirect_uri_mismatch`
**Solution**: Check that redirect URI exactly matches what's in Google Cloud Console

**Error**: `invalid_client`
**Solution**: Verify Client ID and Client Secret are correct in Fly.io secrets

**Error**: `unauthorized_client`
**Solution**: Check that JavaScript origins are correctly set

## 📋 Final Checklist

- [ ] Google Cloud Console has `https://careerdart.com` in JavaScript origins
- [ ] Google Cloud Console has `https://api.careerdart.com/auth/google/callback` in redirect URIs
- [ ] OAuth consent screen is properly configured
- [ ] Client ID and Secret are copied to Fly.io secrets
- [ ] Netlify has `REACT_APP_API_URL=https://api.careerdart.com`
- [ ] Both domains have valid SSL certificates
- [ ] No CORS errors in browser console

## 🆘 If Still Not Working

1. **Check Fly.io logs**: `fly logs -a careerdart-prod`
2. **Verify domain resolution**: `nslookup api.careerdart.com`
3. **Test API directly**: `curl https://api.careerdart.com/auth/me`
4. **Check browser console** for detailed error messages
5. **Verify SSL certificates**: `fly certs list -a careerdart-prod`
